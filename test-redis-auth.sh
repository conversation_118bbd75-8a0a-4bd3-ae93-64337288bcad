#!/bin/bash

# Better Auth Redis Integration Testing Script
# This script tests the Redis-enhanced authentication system

set -e

# Configuration
BASE_URL="http://localhost:5174"
API_BASE="$BASE_URL/api/auth"
HEALTH_URL="$BASE_URL/api/health/redis"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test Redis health
test_redis_health() {
    log_info "Testing Redis health..."
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$HEALTH_URL")
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')
    
    if [ "$http_code" -eq 200 ]; then
        log_success "Redis health check passed"
        echo "$body" | jq '.'
    else
        log_error "Redis health check failed (HTTP $http_code)"
        echo "$body"
        return 1
    fi
}

# Test detailed Redis health
test_redis_health_detailed() {
    log_info "Testing detailed Redis health..."
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$HEALTH_URL?detailed=true")
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')
    
    if [ "$http_code" -eq 200 ]; then
        log_success "Detailed Redis health check passed"
        echo "$body" | jq '.'
    else
        log_error "Detailed Redis health check failed (HTTP $http_code)"
        echo "$body"
        return 1
    fi
}

# Test rate limiting for login
test_login_rate_limiting() {
    log_info "Testing login rate limiting..."
    
    local email="<EMAIL>"
    local password="wrongpassword"
    local attempts=0
    local rate_limited=false
    
    # Make multiple failed login attempts
    for i in {1..7}; do
        attempts=$((attempts + 1))
        log_info "Login attempt $attempts..."
        
        response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
            -X POST "$API_BASE/sign-in/email" \
            -H "Content-Type: application/json" \
            -d "{\"email\":\"$email\",\"password\":\"$password\"}")
        
        http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
        body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')
        
        if [ "$http_code" -eq 429 ]; then
            log_success "Rate limiting triggered after $attempts attempts"
            echo "$body" | jq '.'
            rate_limited=true
            break
        elif [ "$http_code" -eq 401 ]; then
            log_info "Login failed as expected (attempt $attempts)"
        else
            log_warning "Unexpected response code: $http_code"
            echo "$body"
        fi
        
        # Small delay between attempts
        sleep 1
    done
    
    if [ "$rate_limited" = false ]; then
        log_warning "Rate limiting was not triggered after $attempts attempts"
    fi
}

# Test registration rate limiting
test_registration_rate_limiting() {
    log_info "Testing registration rate limiting..."
    
    local base_email="test-reg-rate-limit"
    local password="testpassword123"
    local attempts=0
    local rate_limited=false
    
    # Make multiple registration attempts
    for i in {1..5}; do
        attempts=$((attempts + 1))
        local email="${base_email}${i}@example.com"
        log_info "Registration attempt $attempts with email: $email"
        
        response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
            -X POST "$API_BASE/sign-up/email" \
            -H "Content-Type: application/json" \
            -d "{\"email\":\"$email\",\"password\":\"$password\",\"name\":\"Test User $i\"}")
        
        http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
        body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')
        
        if [ "$http_code" -eq 429 ]; then
            log_success "Registration rate limiting triggered after $attempts attempts"
            echo "$body" | jq '.'
            rate_limited=true
            break
        elif [ "$http_code" -eq 200 ] || [ "$http_code" -eq 409 ]; then
            log_info "Registration attempt $attempts completed (status: $http_code)"
        else
            log_warning "Unexpected response code: $http_code"
            echo "$body"
        fi
        
        # Small delay between attempts
        sleep 1
    done
    
    if [ "$rate_limited" = false ]; then
        log_warning "Registration rate limiting was not triggered after $attempts attempts"
    fi
}

# Test session endpoint rate limiting
test_session_rate_limiting() {
    log_info "Testing session endpoint rate limiting..."
    
    local requests=0
    local rate_limited=false
    
    # Make many session requests quickly
    for i in {1..105}; do
        requests=$((requests + 1))
        
        if [ $((requests % 20)) -eq 0 ]; then
            log_info "Session request $requests..."
        fi
        
        response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
            -X GET "$API_BASE/get-session" \
            -H "Content-Type: application/json")
        
        http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
        
        if [ "$http_code" -eq 429 ]; then
            log_success "Session rate limiting triggered after $requests requests"
            body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')
            echo "$body" | jq '.'
            rate_limited=true
            break
        fi
    done
    
    if [ "$rate_limited" = false ]; then
        log_warning "Session rate limiting was not triggered after $requests requests"
    fi
}

# Test rate limit headers
test_rate_limit_headers() {
    log_info "Testing rate limit headers..."
    
    response=$(curl -s -I "$API_BASE/get-session")
    
    if echo "$response" | grep -q "X-RateLimit-Limit"; then
        log_success "Rate limit headers are present"
        echo "$response" | grep "X-RateLimit"
    else
        log_warning "Rate limit headers not found"
        echo "$response"
    fi
}

# Test CORS headers
test_cors_headers() {
    log_info "Testing CORS headers..."
    
    response=$(curl -s -I \
        -H "Origin: http://localhost:3000" \
        -H "Access-Control-Request-Method: POST" \
        -H "Access-Control-Request-Headers: Content-Type" \
        -X OPTIONS "$API_BASE/sign-in/email")
    
    if echo "$response" | grep -q "Access-Control-Allow-Origin"; then
        log_success "CORS headers are present"
        echo "$response" | grep "Access-Control"
    else
        log_warning "CORS headers not found"
        echo "$response"
    fi
}

# Main test execution
main() {
    log_info "Starting Better Auth Redis Integration Tests"
    echo "========================================"
    
    # Check if server is running
    if ! curl -s "$BASE_URL" > /dev/null; then
        log_error "Server is not running at $BASE_URL"
        log_info "Please start the server with: bun run dev"
        exit 1
    fi
    
    log_success "Server is running at $BASE_URL"
    echo ""
    
    # Run tests
    test_redis_health
    echo ""
    
    test_redis_health_detailed
    echo ""
    
    test_rate_limit_headers
    echo ""
    
    test_cors_headers
    echo ""
    
    test_login_rate_limiting
    echo ""
    
    test_registration_rate_limiting
    echo ""
    
    test_session_rate_limiting
    echo ""
    
    log_success "All tests completed!"
    log_info "Check the Upstash console for rate limiting analytics"
}

# Check dependencies
check_dependencies() {
    if ! command -v curl &> /dev/null; then
        log_error "curl is required but not installed"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_warning "jq is not installed - JSON output will not be formatted"
    fi
}

# Script execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    check_dependencies
    main "$@"
fi
