{"name": "financial-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "db:seed": "tsx src/lib/db/seed.ts"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-router": "^1.124.2", "@tanstack/react-router-devtools": "^1.124.2", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dexie": "^4.0.11", "emoji-picker-react": "^4.13.2", "i18next": "^25.3.2", "little-date": "^1.0.0", "lucide-react": "^0.525.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-day-picker": "^9.8.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-i18next": "^15.6.0", "recharts": "^3.0.2", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "zod": "^3.25.74", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.29.0", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/router-plugin": "^1.124.2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "prettier-plugin-tailwindcss": "^0.6.13", "tailwindcss": "^4.1.11", "tsx": "^4.20.3", "tw-animate-css": "^1.3.5", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0"}}