{"name": "kwaci-grow", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix vite:build", "dev": "remix vite:dev", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "start": "remix-serve ./build/server/index.js", "typecheck": "tsc", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "db:push": "drizzle-kit push", "db:seed": "bun run app/lib/db/seed.ts", "db:reset": "bun run db:migrate && bun run db:seed", "dev:auth": "bun run scripts/dev-auth-helper.ts", "dev:users": "bun run scripts/dev-auth-helper.ts list", "dev:clear-rate-limits": "bun run scripts/clear-rate-limits.ts", "dev:rate-limit-status": "bun run scripts/clear-rate-limits.ts status", "test:session": "node scripts/test-session.js", "test:session:full": "node tests/session-management.test.js", "test:products": "vitest run app/components/inventory/__tests__/ProductManagement.test.tsx", "test:products:watch": "vitest app/components/inventory/__tests__/ProductManagement.test.tsx", "test:products:fix": "vitest run app/components/inventory/__tests__/ProductManagementSheet.fix.test.tsx", "test:products:create": "vitest run app/components/inventory/__tests__/ProductCreateSheet.test.tsx"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@remix-run/node": "^2.16.8", "@remix-run/react": "^2.16.8", "@remix-run/serve": "^2.16.8", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.35.1", "better-auth": "^1.3.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.3", "drizzle-seed": "^0.3.1", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "isbot": "^4.1.0", "lucide-react": "^0.525.0", "pg": "^8.16.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.60.0", "react-i18next": "^15.6.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@remix-run/dev": "^2.16.8", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/vite": "^4.1.11", "@types/pg": "^8.15.4", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "autoprefixer": "^10.4.21", "eslint": "^8.38.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "typescript": "^5.1.6", "vite": "^6.0.0", "vite-tsconfig-paths": "^4.2.1"}, "engines": {"node": ">=20.0.0"}}