import { db } from './app/lib/db/connection.ts';
import { users, userRoles, roles, rolePermissions, permissions, businesses } from './app/lib/db/schema.ts';
import { eq, and } from 'drizzle-orm';
import { RBACService } from './app/lib/services/rbacService.server.ts';
import process from 'process';

console.log('=== Debug Sarah\'s Permissions ===');

// Find <PERSON>'s user record
const sarahUser = await db
  .select()
  .from(users)
  .where(eq(users.email, '<EMAIL>'))
  .limit(1);

if (sarahUser.length === 0) {
  console.log('❌ Sarah user not found!');
  process.exit(1);
}

console.log('✅ Sarah user found:', {
  id: sarahUser[0].id,
  name: sarah<PERSON><PERSON>[0].name,
  email: sarahUser[0].email
});

// Find the first business (KWACI Coffee House)
const firstBusiness = await db
  .select()
  .from(businesses)
  .limit(1);

if (firstBusiness.length === 0) {
  console.log('❌ No businesses found!');
  process.exit(1);
}

const businessId = firstBusiness[0].id;
console.log('🏢 Using business:', {
  id: businessId,
  name: firstBusiness[0].name
});

// Check Sarah's roles for this business
const sarahRoles = await db
  .select({
    roleName: roles.name,
    businessId: userRoles.businessId,
    isActive: userRoles.isActive
  })
  .from(userRoles)
  .innerJoin(roles, eq(userRoles.roleId, roles.id))
  .where(
    and(
      eq(userRoles.userId, sarahUser[0].id),
      eq(userRoles.businessId, businessId),
      eq(userRoles.isActive, true)
    )
  );

console.log('👤 Sarah\'s roles for this business:', sarahRoles);

// Get Sarah's permissions using RBACService
const userPermissions = await RBACService.getUserPermissions(sarahUser[0].id, businessId);
console.log('🔐 Sarah\'s permissions:', {
  permissions: userPermissions.permissions,
  roles: userPermissions.roles
});

// Check specific permissions
const hasCogsRead = await RBACService.hasPermission(sarahUser[0].id, 'cogs.read', businessId);
const hasProductsRead = await RBACService.hasPermission(sarahUser[0].id, 'products.read', businessId);
const hasIngredientsRead = await RBACService.hasPermission(sarahUser[0].id, 'ingredients.read', businessId);

console.log('🔍 Specific permission checks:');
console.log('  - cogs.read:', hasCogsRead ? '✅' : '❌');
console.log('  - products.read:', hasProductsRead ? '✅' : '❌');
console.log('  - ingredients.read:', hasIngredientsRead ? '✅' : '❌');

// Check if viewer role has cogs.read permission
const viewerRole = await db
  .select()
  .from(roles)
  .where(eq(roles.name, 'viewer'))
  .limit(1);

if (viewerRole.length > 0) {
  const viewerPermissions = await db
    .select({ permission: permissions.name })
    .from(rolePermissions)
    .innerJoin(permissions, eq(rolePermissions.permissionId, permissions.id))
    .where(eq(rolePermissions.roleId, viewerRole[0].id));
  
  console.log('👁️ Viewer role permissions:', viewerPermissions.map(p => p.permission));
  
  const hasCogsReadInRole = viewerPermissions.some(p => p.permission === 'cogs.read');
  console.log('🔍 Viewer role has cogs.read:', hasCogsReadInRole ? '✅' : '❌');
}

console.log('=== Debug Complete ===');
process.exit(0);