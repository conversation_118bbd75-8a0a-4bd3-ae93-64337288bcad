# Database Configuration
# Replace with your actual Supabase PostgreSQL connection string
DATABASE_URL="postgresql://username:password@hostname:port/database"

# Better Auth Configuration
# Generate a random secret key for session encryption
# You can use: openssl rand -base64 32
BETTER_AUTH_SECRET="your-secret-key-here"

# Application URL
# Set this to your application's URL in production
BETTER_AUTH_URL="http://localhost:3000"

# Optional: Social Authentication Providers
# Uncomment and configure if you want to enable social login

# GitHub OAuth (optional)
# GITHUB_CLIENT_ID="your-github-client-id"
# GITHUB_CLIENT_SECRET="your-github-client-secret"

# Google OAuth (optional)
# GOOGLE_CLIENT_ID="your-google-client-id"
# GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Email Configuration (optional)
# Configure if you want to enable email verification
# SMTP_HOST="smtp.gmail.com"
# SMTP_PORT="587"
# SMTP_USER="<EMAIL>"
# SMTP_PASS="your-app-password"
