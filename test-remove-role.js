import { db } from './app/lib/db/connection.ts';
import { userRoles, roles } from './app/lib/db/schema.ts';
import { eq, and } from 'drizzle-orm';

async function testRemoveRole() {
  try {
    console.log('🔍 Testing removeRole method...');
    
    const roleName = 'test_custom_1753508430396';
    const businessId = 'bbeb56d2-d094-42ec-bff7-b1ad420d493d';
    
    // Get the role
    const role = await db
      .select()
      .from(roles)
      .where(eq(roles.name, roleName))
      .limit(1);

    if (!role.length) {
      console.log('❌ Role not found');
      return;
    }
    
    console.log('✅ Role found:', role[0].id);
    
    // Check current assignment
    const currentAssignment = await db
      .select()
      .from(userRoles)
      .where(
        and(
          eq(userRoles.roleId, role[0].id),
          eq(userRoles.businessId, businessId)
        )
      );
    
    console.log('Current assignments:', currentAssignment.length);
    currentAssignment.forEach(assignment => {
      console.log(`  - User: ${assignment.userId}, Active: ${assignment.isActive}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testRemoveRole();