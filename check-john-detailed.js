import { db } from './app/lib/db/connection.ts';
import { userRoles, roles, users, businesses } from './app/lib/db/schema.ts';
import { eq, isNull } from 'drizzle-orm';

console.log('=== John\'s Business-Specific Roles ===');
const businessRoles = await db
  .select({
    userId: userRoles.userId,
    userName: users.name,
    userEmail: users.email,
    roleName: roles.name,
    businessId: userRoles.businessId,
    businessName: businesses.name,
    isActive: userRoles.isActive
  })
  .from(userRoles)
  .innerJoin(users, eq(userRoles.userId, users.id))
  .innerJoin(roles, eq(userRoles.roleId, roles.id))
  .leftJoin(businesses, eq(userRoles.businessId, businesses.id))
  .where(eq(users.email, '<EMAIL>'));

console.log('Business-specific roles:', JSON.stringify(businessRoles, null, 2));

console.log('\n=== Checking for Global Roles (businessId = null) ===');
const globalRoles = await db
  .select({
    userId: userRoles.userId,
    userName: users.name,
    userEmail: users.email,
    roleName: roles.name,
    businessId: userRoles.businessId,
    isActive: userRoles.isActive
  })
  .from(userRoles)
  .innerJoin(users, eq(userRoles.userId, users.id))
  .innerJoin(roles, eq(userRoles.roleId, roles.id))
  .where(eq(users.email, '<EMAIL>'))
  .where(isNull(userRoles.businessId));

console.log('Global roles:', JSON.stringify(globalRoles, null, 2));