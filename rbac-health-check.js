#!/usr/bin/env bun
/**
 * RBAC Health Check Script
 * 
 * This script performs a comprehensive health check of the RBAC system,
 * validating that roles, permissions, and user assignments are working correctly.
 * 
 * Usage: bun run rbac-health-check.js
 */

import { db } from './app/lib/db/connection';
import { roles, permissions, rolePermissions, userRoles, users, businesses } from './app/lib/db/schema';
import { eq } from 'drizzle-orm';
import { RBACService } from './app/lib/services/rbacService.server';

async function healthCheck() {
  console.log('🔍 RBAC Health Check Starting...');
  console.log('=' .repeat(50));
  
  let hasErrors = false;
  
  try {
    // 1. Check basic RBAC data
    console.log('\n📊 Basic RBAC Data:');
    const allRoles = await db.select().from(roles);
    const allPermissions = await db.select().from(permissions);
    const allRolePermissions = await db.select().from(rolePermissions);
    const allUserRoles = await db.select().from(userRoles);
    
    console.log(`   - Roles: ${allRoles.length}`);
    console.log(`   - Permissions: ${allPermissions.length}`);
    console.log(`   - Role-Permission Assignments: ${allRolePermissions.length}`);
    console.log(`   - User-Role Assignments: ${allUserRoles.length}`);
    
    if (allRoles.length === 0) {
      console.log('   ❌ ERROR: No roles found!');
      hasErrors = true;
    }
    
    if (allPermissions.length === 0) {
      console.log('   ❌ ERROR: No permissions found!');
      hasErrors = true;
    }
    
    // 2. Check business_owner role specifically
    console.log('\n👑 Business Owner Role Analysis:');
    const businessOwnerRole = allRoles.find(r => r.name === 'business_owner');
    
    if (businessOwnerRole) {
      console.log(`   ✅ Role found (ID: ${businessOwnerRole.id})`);
      
      const businessOwnerPerms = await db
        .select({ 
          permission: permissions.name,
          resource: permissions.resource,
          action: permissions.action
        })
        .from(rolePermissions)
        .innerJoin(permissions, eq(rolePermissions.permissionId, permissions.id))
        .where(eq(rolePermissions.roleId, businessOwnerRole.id));
      
      console.log(`   - Permissions assigned: ${businessOwnerPerms.length}`);
      
      if (businessOwnerPerms.length === 0) {
        console.log('   ❌ CRITICAL ERROR: Business owner has no permissions!');
        hasErrors = true;
      } else {
        console.log('   ✅ Business owner has permissions assigned');
        
        // Check for essential permissions
        const essentialPermissions = [
          'business.read',
          'business.update', 
          'business.manage_users',
          'inventory.read',
          'categories.read'
        ];
        
        const assignedPermissionNames = businessOwnerPerms.map(p => p.permission);
        const missingEssential = essentialPermissions.filter(ep => !assignedPermissionNames.includes(ep));
        
        if (missingEssential.length > 0) {
          console.log(`   ⚠️  WARNING: Missing essential permissions: ${missingEssential.join(', ')}`);
        } else {
          console.log('   ✅ All essential permissions present');
        }
        
        // Show permission breakdown by resource
        const permissionsByResource = {};
        businessOwnerPerms.forEach(p => {
          if (!permissionsByResource[p.resource]) {
            permissionsByResource[p.resource] = [];
          }
          permissionsByResource[p.resource].push(p.action);
        });
        
        console.log('   📋 Permissions by resource:');
        Object.entries(permissionsByResource).forEach(([resource, actions]) => {
          console.log(`      - ${resource}: ${actions.join(', ')}`);
        });
      }
    } else {
      console.log('   ❌ CRITICAL ERROR: Business owner role not found!');
      hasErrors = true;
    }
    
    // 3. Check test users and their permissions
    console.log('\n👤 Test User Analysis:');
    const testUsers = await db.select().from(users).where(eq(users.email, '<EMAIL>'));
    
    if (testUsers.length > 0) {
      const testUser = testUsers[0];
      console.log(`   ✅ Test user found: ${testUser.name} (${testUser.email})`);
      
      const userBusinesses = await db.select().from(businesses).where(eq(businesses.userId, testUser.id));
      console.log(`   - Owns ${userBusinesses.length} business(es)`);
      
      if (userBusinesses.length > 0) {
        const business = userBusinesses[0];
        console.log(`   - Business: ${business.name} (ID: ${business.id})`);
        
        try {
          // Check role assignment
          const hasBusinessOwnerRole = await RBACService.hasRole(testUser.id, 'business_owner', business.id);
          console.log(`   - Has business_owner role: ${hasBusinessOwnerRole ? '✅ Yes' : '❌ No'}`);
          
          if (!hasBusinessOwnerRole) {
            hasErrors = true;
          }
          
          // Check permissions
          const userPermissions = await RBACService.getUserPermissions(testUser.id, business.id);
          console.log(`   - Total permissions: ${userPermissions.permissions.length}`);
          
          if (userPermissions.permissions.length === 0) {
            console.log('   ❌ CRITICAL ERROR: User has no permissions!');
            hasErrors = true;
          } else {
            console.log('   ✅ User has permissions');
            
            // Test specific permission checks
            const testPermissions = [
              'business.read',
              'business.update',
              'inventory.read',
              'categories.read'
            ];
            
            console.log('   🧪 Permission tests:');
            for (const perm of testPermissions) {
              const hasPermission = await RBACService.hasPermission(testUser.id, perm, business.id);
              console.log(`      - ${perm}: ${hasPermission ? '✅' : '❌'}`);
              if (!hasPermission) {
                hasErrors = true;
              }
            }
          }
        } catch (error) {
          console.log(`   ❌ ERROR checking user permissions: ${error instanceof Error ? error.message : String(error)}`);
          hasErrors = true;
        }
      } else {
        console.log('   ⚠️  WARNING: Test user has no businesses');
      }
    } else {
      console.log('   ⚠️  WARNING: Test user (<EMAIL>) not found');
    }
    
    // 4. Check for orphaned data
    console.log('\n🔍 Data Integrity Checks:');
    
    // Check for role-permission assignments with invalid role IDs
    const orphanedRolePerms = await db
      .select({ rolePermissionId: rolePermissions.id })
      .from(rolePermissions)
      .leftJoin(roles, eq(rolePermissions.roleId, roles.id))
      .where(eq(roles.id, null));
    
    if (orphanedRolePerms.length > 0) {
      console.log(`   ⚠️  WARNING: ${orphanedRolePerms.length} orphaned role-permission assignments`);
    } else {
      console.log('   ✅ No orphaned role-permission assignments');
    }
    
    // Check for user-role assignments with invalid user IDs
    const orphanedUserRoles = await db
      .select({ userRoleId: userRoles.id })
      .from(userRoles)
      .leftJoin(users, eq(userRoles.userId, users.id))
      .where(eq(users.id, null));
    
    if (orphanedUserRoles.length > 0) {
      console.log(`   ⚠️  WARNING: ${orphanedUserRoles.length} orphaned user-role assignments`);
    } else {
      console.log('   ✅ No orphaned user-role assignments');
    }
    
    // 5. Summary
    console.log('\n' + '=' .repeat(50));
    if (hasErrors) {
      console.log('❌ RBAC HEALTH CHECK FAILED');
      console.log('\n🔧 Recommended Actions:');
      console.log('   1. Run: bun run db:reset');
      console.log('   2. Or run: bun run app/lib/db/rbac-seed.ts');
      console.log('   3. Or use manual fix script from troubleshooting guide');
      console.log('\n📖 See: docs/rbac-troubleshooting-guide.md');
    } else {
      console.log('✅ RBAC HEALTH CHECK PASSED');
      console.log('\n🎉 All RBAC components are working correctly!');
      console.log('   - Roles and permissions are properly configured');
      console.log('   - User assignments are valid');
      console.log('   - No data integrity issues found');
    }
    
  } catch (error) {
    console.error('\n💥 Health check failed with error:', error);
    hasErrors = true;
  }
  
  console.log('\n🔍 Health check completed!');
  
  if (hasErrors) {
    throw new Error('RBAC health check failed');
  }
}

// Run the health check
healthCheck()
  .then(() => {
    console.log('\n✅ Health check completed successfully!');
  })
  .catch((error) => {
    console.error('💥 Fatal error during health check:', error);
    throw error;
  });