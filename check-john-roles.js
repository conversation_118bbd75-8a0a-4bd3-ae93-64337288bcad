import { db } from './app/lib/db/connection.ts';
import { userRoles, roles, users } from './app/lib/db/schema.ts';
import { eq } from 'drizzle-orm';

const result = await db
  .select({
    userId: userRoles.userId,
    userName: users.name,
    userEmail: users.email,
    roleName: roles.name,
    businessId: userRoles.businessId,
    isActive: userRoles.isActive
  })
  .from(userRoles)
  .innerJoin(users, eq(userRoles.userId, users.id))
  .innerJoin(roles, eq(userRoles.roleId, roles.id))
  .where(eq(users.email, '<EMAIL>'));

console.log('<PERSON> roles:', JSON.stringify(result, null, 2));