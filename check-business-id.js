import { db } from './app/lib/db/connection.ts';
import { businesses } from './app/lib/db/schema.ts';
import { eq } from 'drizzle-orm';

async function checkBusinessId() {
  try {
    console.log('🔍 Finding KWACI Coffee House business...');
    
    const business = await db.select()
      .from(businesses)
      .where(eq(businesses.name, 'KWACI Coffee House'))
      .limit(1);
    
    if (business[0]) {
      console.log('✅ Found KWACI Coffee House:');
      console.log(`   Business ID: ${business[0].id}`);
      console.log(`   Business Name: ${business[0].name}`);
      console.log(`   Created At: ${business[0].createdAt}`);
    } else {
      console.log('❌ KWACI Coffee House not found');
    }
    
    // Also list all businesses
    console.log('\n📋 All businesses:');
    const allBusinesses = await db.select().from(businesses);
    allBusinesses.forEach(b => {
      console.log(`   ${b.name}: ${b.id}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

await checkBusinessId();