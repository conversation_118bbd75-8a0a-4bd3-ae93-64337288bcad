# Better Auth REST API Integration Guide

## Overview

This guide provides comprehensive information for integrating your Better Auth implementation with external applications like Flutter mobile clients.

## 1. Authentication Endpoints

### Base Configuration
- **Base URL**: `https://your-domain.com/api/auth`
- **Content-Type**: `application/json`
- **Authentication**: Session-based (cookies) or Bearer tokens

### Core Endpoints

#### **User Registration**
```http
POST /api/auth/sign-up/email
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "<PERSON>",
  "image": "https://example.com/avatar.jpg" // optional
}
```

**Response (Success - 200)**:
```json
{
  "user": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "name": "<PERSON>",
    "emailVerified": false,
    "image": "https://example.com/avatar.jpg",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "session": {
    "id": "session-uuid",
    "userId": "user-uuid",
    "expiresAt": "2024-01-08T00:00:00.000Z",
    "token": "session-token",
    "ipAddress": "***********",
    "userAgent": "Mozilla/5.0..."
  }
}
```

**Response (Error - 400/409)**:
```json
{
  "message": "Email already exists",
  "code": "USER_ALREADY_EXISTS"
}
```

#### **User Login**
```http
POST /api/auth/sign-in/email
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "rememberMe": true // optional, default: true
}
```

**Response (Success - 200)**:
```json
{
  "user": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "name": "John Doe",
    "emailVerified": true,
    "image": "https://example.com/avatar.jpg",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "session": {
    "id": "session-uuid",
    "userId": "user-uuid",
    "expiresAt": "2024-01-08T00:00:00.000Z",
    "token": "session-token",
    "ipAddress": "***********",
    "userAgent": "Mozilla/5.0..."
  }
}
```

**Response (Error - 401)**:
```json
{
  "message": "Invalid email or password",
  "code": "INVALID_CREDENTIALS"
}
```

#### **Get Current Session**
```http
GET /api/auth/get-session
Cookie: better-auth.session_token=your-session-token
// OR
Authorization: Bearer your-session-token
```

**Response (Success - 200)**:
```json
{
  "user": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "name": "John Doe",
    "emailVerified": true,
    "image": "https://example.com/avatar.jpg"
  },
  "session": {
    "id": "session-uuid",
    "userId": "user-uuid",
    "expiresAt": "2024-01-08T00:00:00.000Z",
    "token": "session-token"
  }
}
```

**Response (Error - 401)**:
```json
{
  "message": "Unauthorized",
  "code": "UNAUTHORIZED"
}
```

#### **User Logout**
```http
POST /api/auth/sign-out
Cookie: better-auth.session_token=your-session-token
// OR
Authorization: Bearer your-session-token
```

**Response (Success - 200)**:
```json
{
  "success": true
}
```

#### **Password Reset Request**
```http
POST /api/auth/forget-password
Content-Type: application/json

{
  "email": "<EMAIL>",
  "redirectTo": "https://your-app.com/reset-password" // optional
}
```

**Response (Success - 200)**:
```json
{
  "message": "Password reset email sent"
}
```

#### **Password Reset Confirmation**
```http
POST /api/auth/reset-password
Content-Type: application/json

{
  "token": "reset-token-from-email",
  "password": "newpassword123"
}
```

**Response (Success - 200)**:
```json
{
  "message": "Password reset successfully"
}
```

## 2. Cross-Platform API Support

### Session Management for Mobile Apps

Better Auth supports headless/API-only authentication through:

1. **Session Tokens**: Can be used as Bearer tokens
2. **Cookie-based Sessions**: For web applications
3. **Custom Headers**: For API authentication

### Configuration for External Clients

Your current setup in `app/lib/auth.server.ts` already supports external clients. To enhance it for mobile apps:

```typescript
// Add to your auth.server.ts
export const auth = betterAuth({
  // ... existing configuration
  
  // Enable CORS for mobile apps
  cors: {
    origin: ["http://localhost:3000", "https://your-production-domain.com"],
    credentials: true,
  },
  
  // Configure session for API usage (optimized configuration)
  session: {
    expiresIn: 60 * 60 * 24 * 30, // 30 days - Extended for better user experience
    updateAge: 60 * 60 * 6, // 6 hours - More frequent updates to keep sessions fresh
    disableSessionRefresh: false, // Explicitly enable session refresh for active users
    cookieCache: {
      enabled: true,
      maxAge: 60 * 60 * 24, // 24 hours - Eliminates frequent validation calls
    },
  },
  
  // Advanced configuration for API clients
  advanced: {
    generateId: () => crypto.randomUUID(),
    crossSubDomainCookies: {
      enabled: true,
      domain: ".your-domain.com", // Allow subdomains
    },
  },
});
```

## 3. Flutter Integration Examples

### HTTP Client Setup

```dart
// lib/services/auth_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class AuthService {
  static const String baseUrl = 'https://your-domain.com/api/auth';
  
  // Get stored session token
  Future<String?> getSessionToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('session_token');
  }
  
  // Store session token
  Future<void> setSessionToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('session_token', token);
  }
  
  // Remove session token
  Future<void> removeSessionToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('session_token');
  }
  
  // Get headers with authentication
  Future<Map<String, String>> getHeaders() async {
    final token = await getSessionToken();
    return {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }
}
```

### User Registration

```dart
// User registration
Future<Map<String, dynamic>?> register({
  required String email,
  required String password,
  required String name,
  String? image,
}) async {
  try {
    final response = await http.post(
      Uri.parse('$baseUrl/sign-up/email'),
      headers: await getHeaders(),
      body: jsonEncode({
        'email': email,
        'password': password,
        'name': name,
        if (image != null) 'image': image,
      }),
    );
    
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      
      // Store session token
      if (data['session']?['token'] != null) {
        await setSessionToken(data['session']['token']);
      }
      
      return data;
    } else {
      // Handle error
      final error = jsonDecode(response.body);
      throw AuthException(error['message'] ?? 'Registration failed');
    }
  } catch (e) {
    throw AuthException('Network error: $e');
  }
}
```

### User Login

```dart
// User login
Future<Map<String, dynamic>?> login({
  required String email,
  required String password,
  bool rememberMe = true,
}) async {
  try {
    final response = await http.post(
      Uri.parse('$baseUrl/sign-in/email'),
      headers: await getHeaders(),
      body: jsonEncode({
        'email': email,
        'password': password,
        'rememberMe': rememberMe,
      }),
    );
    
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      
      // Store session token
      if (data['session']?['token'] != null) {
        await setSessionToken(data['session']['token']);
      }
      
      return data;
    } else {
      // Handle error
      final error = jsonDecode(response.body);
      throw AuthException(error['message'] ?? 'Login failed');
    }
  } catch (e) {
    throw AuthException('Network error: $e');
  }
}
```

### Session Management

```dart
// Get current session
Future<Map<String, dynamic>?> getCurrentSession() async {
  try {
    final response = await http.get(
      Uri.parse('$baseUrl/get-session'),
      headers: await getHeaders(),
    );
    
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else if (response.statusCode == 401) {
      // Session expired, remove token
      await removeSessionToken();
      return null;
    } else {
      throw AuthException('Failed to get session');
    }
  } catch (e) {
    throw AuthException('Network error: $e');
  }
}

// Logout
Future<void> logout() async {
  try {
    await http.post(
      Uri.parse('$baseUrl/sign-out'),
      headers: await getHeaders(),
    );
  } finally {
    // Always remove local token
    await removeSessionToken();
  }
}
```

### Error Handling

```dart
// Custom exception class
class AuthException implements Exception {
  final String message;
  AuthException(this.message);
  
  @override
  String toString() => 'AuthException: $message';
}

// Error handling helper
Map<String, String> getErrorMessage(String errorCode) {
  switch (errorCode) {
    case 'USER_ALREADY_EXISTS':
      return {'title': 'Account Exists', 'message': 'An account with this email already exists.'};
    case 'INVALID_CREDENTIALS':
      return {'title': 'Invalid Credentials', 'message': 'Please check your email and password.'};
    case 'EMAIL_NOT_VERIFIED':
      return {'title': 'Email Not Verified', 'message': 'Please verify your email before signing in.'};
    default:
      return {'title': 'Error', 'message': 'An unexpected error occurred.'};
  }
}
```

## 4. CORS Configuration

### Current Setup
Your current Remix setup doesn't explicitly configure CORS, but Better Auth handles it automatically. For external clients, you may need to add CORS headers.

### Enhanced CORS Setup

Create a CORS middleware for your Remix app:

```typescript
// app/lib/cors.server.ts
export function addCorsHeaders(response: Response, origin?: string): Response {
  const allowedOrigins = [
    'http://localhost:3000',
    'https://your-production-domain.com',
    // Add your mobile app's custom scheme if needed
    'your-app://auth-callback',
  ];

  const requestOrigin = origin || '*';
  const isAllowed = allowedOrigins.includes(requestOrigin) || requestOrigin === '*';

  if (isAllowed) {
    response.headers.set('Access-Control-Allow-Origin', requestOrigin);
  }

  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, Cookie');
  response.headers.set('Access-Control-Allow-Credentials', 'true');
  response.headers.set('Access-Control-Max-Age', '86400');

  return response;
}
```

Update your auth route to handle CORS:

```typescript
// app/routes/api.auth.$.ts
import type { LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node";
import { auth } from "~/lib/auth.server";
import { addCorsHeaders } from "~/lib/cors.server";

export async function loader({ request }: LoaderFunctionArgs) {
  // Handle preflight requests
  if (request.method === 'OPTIONS') {
    return addCorsHeaders(new Response(null, { status: 200 }), request.headers.get('Origin'));
  }

  const response = await auth.handler(request);
  return addCorsHeaders(response, request.headers.get('Origin'));
}

export async function action({ request }: ActionFunctionArgs) {
  // Handle preflight requests
  if (request.method === 'OPTIONS') {
    return addCorsHeaders(new Response(null, { status: 200 }), request.headers.get('Origin'));
  }

  const response = await auth.handler(request);
  return addCorsHeaders(response, request.headers.get('Origin'));
}
```

## 5. Security Considerations

### API Security Best Practices

1. **Rate Limiting**: Implement rate limiting for authentication endpoints
2. **HTTPS Only**: Always use HTTPS in production
3. **Token Security**: Store session tokens securely on mobile devices
4. **Input Validation**: Validate all input data
5. **Error Handling**: Don't expose sensitive information in error messages

### Enhanced Security Configuration

```typescript
// app/lib/auth.server.ts - Enhanced security
export const auth = betterAuth({
  // ... existing configuration

  // Security headers
  advanced: {
    generateId: () => crypto.randomUUID(),
    useSecureCookies: process.env.NODE_ENV === 'production',
    crossSubDomainCookies: {
      enabled: false, // Disable for security unless needed
    },
  },

  // Session security (optimized configuration)
  session: {
    expiresIn: 60 * 60 * 24 * 30, // 30 days - Extended for better user experience
    updateAge: 60 * 60 * 6, // 6 hours - More frequent updates to keep sessions fresh
    disableSessionRefresh: false, // Explicitly enable session refresh for active users
    cookieCache: {
      enabled: true,
      maxAge: 60 * 60 * 24, // 24 hours - Eliminates frequent validation calls
    },
  },
});
```

### Mobile App Security

```dart
// lib/services/secure_storage.dart
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class SecureStorage {
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: IOSAccessibility.first_unlock_this_device,
    ),
  );

  static Future<void> storeToken(String token) async {
    await _storage.write(key: 'session_token', value: token);
  }

  static Future<String?> getToken() async {
    return await _storage.read(key: 'session_token');
  }

  static Future<void> deleteToken() async {
    await _storage.delete(key: 'session_token');
  }
}
```

## 6. Testing the API

### Using cURL

```bash
# Register a new user
curl -X POST https://your-domain.com/api/auth/sign-up/email \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "name": "Test User"
  }'

# Login
curl -X POST https://your-domain.com/api/auth/sign-in/email \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'

# Get session (replace TOKEN with actual session token)
curl -X GET https://your-domain.com/api/auth/get-session \
  -H "Authorization: Bearer TOKEN"

# Logout
curl -X POST https://your-domain.com/api/auth/sign-out \
  -H "Authorization: Bearer TOKEN"
```

### Using Postman

1. Create a new collection for "Better Auth API"
2. Add requests for each endpoint
3. Set up environment variables for base URL and tokens
4. Use tests to automatically extract and store session tokens

## 7. Error Codes Reference

| Code | Description | HTTP Status |
|------|-------------|-------------|
| `USER_ALREADY_EXISTS` | Email already registered | 409 |
| `INVALID_CREDENTIALS` | Wrong email/password | 401 |
| `EMAIL_NOT_VERIFIED` | Email verification required | 403 |
| `WEAK_PASSWORD` | Password doesn't meet requirements | 400 |
| `INVALID_EMAIL` | Invalid email format | 400 |
| `RATE_LIMITED` | Too many requests | 429 |
| `UNAUTHORIZED` | Invalid or expired session | 401 |
| `SERVER_ERROR` | Internal server error | 500 |

## 8. Implementation Checklist

### Backend (Your Remix App)
- [x] Better Auth configured with PostgreSQL
- [x] Authentication endpoints exposed via `/api/auth/*`
- [ ] CORS headers configured for mobile apps
- [ ] Rate limiting implemented
- [ ] Error handling enhanced with specific error codes
- [ ] Session token extraction for Bearer auth

### Flutter App
- [ ] HTTP client service created
- [ ] Secure token storage implemented
- [ ] Authentication state management
- [ ] Error handling with user-friendly messages
- [ ] Network error handling and retry logic
- [ ] Session refresh logic

## 9. Next Steps

1. **Test the endpoints** using the provided cURL examples
2. **Implement CORS** if needed for your mobile app
3. **Add rate limiting** for production security
4. **Set up proper error handling** in your Flutter app
5. **Implement secure token storage** using flutter_secure_storage
6. **Add refresh token logic** if implementing long-lived sessions

## 10. Additional Resources

- [Better Auth Documentation](https://www.better-auth.com/docs)
- [Flutter HTTP Package](https://pub.dev/packages/http)
- [Flutter Secure Storage](https://pub.dev/packages/flutter_secure_storage)
- [Remix CORS Handling](https://remix.run/docs/en/main/guides/cors)
