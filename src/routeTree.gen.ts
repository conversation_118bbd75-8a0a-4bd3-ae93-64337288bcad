/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as WarehouseRouteImport } from './routes/warehouse'
import { Route as TestTargetsRouteImport } from './routes/test-targets'
import { Route as TestMenusRouteImport } from './routes/test-menus'
import { Route as TestIngredientFixRouteImport } from './routes/test-ingredient-fix'
import { Route as TestI18nRouteImport } from './routes/test-i18n'
import { Route as TestEmployeesRouteImport } from './routes/test-employees'
import { Route as TestDashboardRouteImport } from './routes/test-dashboard'
import { Route as TestBusinessIsolationRouteImport } from './routes/test-business-isolation'
import { Route as TestAccountingIntegrationRouteImport } from './routes/test-accounting-integration'
import { Route as SettingsRouteImport } from './routes/settings'
import { Route as SeedMultiBusinessRouteImport } from './routes/seed-multi-business'
import { Route as SalesTargetsRouteImport } from './routes/sales-targets'
import { Route as RecurringExpensesRouteImport } from './routes/recurring-expenses'
import { Route as ProductsRouteImport } from './routes/products'
import { Route as ProductionRouteImport } from './routes/production'
import { Route as PlanRouteImport } from './routes/plan'
import { Route as PeopleRouteImport } from './routes/people'
import { Route as OperationsRouteImport } from './routes/operations'
import { Route as MenusRouteImport } from './routes/menus'
import { Route as LearnRouteImport } from './routes/learn'
import { Route as KwaciDemoRouteImport } from './routes/kwaci-demo'
import { Route as IngredientsRouteImport } from './routes/ingredients'
import { Route as FixedAssetsRouteImport } from './routes/fixed-assets'
import { Route as DebugAccountingRouteImport } from './routes/debug-accounting'
import { Route as CogsRouteImport } from './routes/cogs'
import { Route as AnalyticsRouteImport } from './routes/analytics'
import { Route as AccountingRouteImport } from './routes/accounting'
import { Route as AccountRouteImport } from './routes/account'
import { Route as IndexRouteImport } from './routes/index'
import { Route as ReportsIndexRouteImport } from './routes/reports/index'
import { Route as ReportsProfitRouteImport } from './routes/reports/profit'
import { Route as ReportsFinancialRouteImport } from './routes/reports/financial'
import { Route as ReportsCostsRouteImport } from './routes/reports/costs'
import { Route as PlanDetailPlanIdRouteImport } from './routes/plan-detail.$planId'

const WarehouseRoute = WarehouseRouteImport.update({
  id: '/warehouse',
  path: '/warehouse',
  getParentRoute: () => rootRouteImport,
} as any)
const TestTargetsRoute = TestTargetsRouteImport.update({
  id: '/test-targets',
  path: '/test-targets',
  getParentRoute: () => rootRouteImport,
} as any)
const TestMenusRoute = TestMenusRouteImport.update({
  id: '/test-menus',
  path: '/test-menus',
  getParentRoute: () => rootRouteImport,
} as any)
const TestIngredientFixRoute = TestIngredientFixRouteImport.update({
  id: '/test-ingredient-fix',
  path: '/test-ingredient-fix',
  getParentRoute: () => rootRouteImport,
} as any)
const TestI18nRoute = TestI18nRouteImport.update({
  id: '/test-i18n',
  path: '/test-i18n',
  getParentRoute: () => rootRouteImport,
} as any)
const TestEmployeesRoute = TestEmployeesRouteImport.update({
  id: '/test-employees',
  path: '/test-employees',
  getParentRoute: () => rootRouteImport,
} as any)
const TestDashboardRoute = TestDashboardRouteImport.update({
  id: '/test-dashboard',
  path: '/test-dashboard',
  getParentRoute: () => rootRouteImport,
} as any)
const TestBusinessIsolationRoute = TestBusinessIsolationRouteImport.update({
  id: '/test-business-isolation',
  path: '/test-business-isolation',
  getParentRoute: () => rootRouteImport,
} as any)
const TestAccountingIntegrationRoute =
  TestAccountingIntegrationRouteImport.update({
    id: '/test-accounting-integration',
    path: '/test-accounting-integration',
    getParentRoute: () => rootRouteImport,
  } as any)
const SettingsRoute = SettingsRouteImport.update({
  id: '/settings',
  path: '/settings',
  getParentRoute: () => rootRouteImport,
} as any)
const SeedMultiBusinessRoute = SeedMultiBusinessRouteImport.update({
  id: '/seed-multi-business',
  path: '/seed-multi-business',
  getParentRoute: () => rootRouteImport,
} as any)
const SalesTargetsRoute = SalesTargetsRouteImport.update({
  id: '/sales-targets',
  path: '/sales-targets',
  getParentRoute: () => rootRouteImport,
} as any)
const RecurringExpensesRoute = RecurringExpensesRouteImport.update({
  id: '/recurring-expenses',
  path: '/recurring-expenses',
  getParentRoute: () => rootRouteImport,
} as any)
const ProductsRoute = ProductsRouteImport.update({
  id: '/products',
  path: '/products',
  getParentRoute: () => rootRouteImport,
} as any)
const ProductionRoute = ProductionRouteImport.update({
  id: '/production',
  path: '/production',
  getParentRoute: () => rootRouteImport,
} as any)
const PlanRoute = PlanRouteImport.update({
  id: '/plan',
  path: '/plan',
  getParentRoute: () => rootRouteImport,
} as any)
const PeopleRoute = PeopleRouteImport.update({
  id: '/people',
  path: '/people',
  getParentRoute: () => rootRouteImport,
} as any)
const OperationsRoute = OperationsRouteImport.update({
  id: '/operations',
  path: '/operations',
  getParentRoute: () => rootRouteImport,
} as any)
const MenusRoute = MenusRouteImport.update({
  id: '/menus',
  path: '/menus',
  getParentRoute: () => rootRouteImport,
} as any)
const LearnRoute = LearnRouteImport.update({
  id: '/learn',
  path: '/learn',
  getParentRoute: () => rootRouteImport,
} as any)
const KwaciDemoRoute = KwaciDemoRouteImport.update({
  id: '/kwaci-demo',
  path: '/kwaci-demo',
  getParentRoute: () => rootRouteImport,
} as any)
const IngredientsRoute = IngredientsRouteImport.update({
  id: '/ingredients',
  path: '/ingredients',
  getParentRoute: () => rootRouteImport,
} as any)
const FixedAssetsRoute = FixedAssetsRouteImport.update({
  id: '/fixed-assets',
  path: '/fixed-assets',
  getParentRoute: () => rootRouteImport,
} as any)
const DebugAccountingRoute = DebugAccountingRouteImport.update({
  id: '/debug-accounting',
  path: '/debug-accounting',
  getParentRoute: () => rootRouteImport,
} as any)
const CogsRoute = CogsRouteImport.update({
  id: '/cogs',
  path: '/cogs',
  getParentRoute: () => rootRouteImport,
} as any)
const AnalyticsRoute = AnalyticsRouteImport.update({
  id: '/analytics',
  path: '/analytics',
  getParentRoute: () => rootRouteImport,
} as any)
const AccountingRoute = AccountingRouteImport.update({
  id: '/accounting',
  path: '/accounting',
  getParentRoute: () => rootRouteImport,
} as any)
const AccountRoute = AccountRouteImport.update({
  id: '/account',
  path: '/account',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const ReportsIndexRoute = ReportsIndexRouteImport.update({
  id: '/reports/',
  path: '/reports/',
  getParentRoute: () => rootRouteImport,
} as any)
const ReportsProfitRoute = ReportsProfitRouteImport.update({
  id: '/reports/profit',
  path: '/reports/profit',
  getParentRoute: () => rootRouteImport,
} as any)
const ReportsFinancialRoute = ReportsFinancialRouteImport.update({
  id: '/reports/financial',
  path: '/reports/financial',
  getParentRoute: () => rootRouteImport,
} as any)
const ReportsCostsRoute = ReportsCostsRouteImport.update({
  id: '/reports/costs',
  path: '/reports/costs',
  getParentRoute: () => rootRouteImport,
} as any)
const PlanDetailPlanIdRoute = PlanDetailPlanIdRouteImport.update({
  id: '/plan-detail/$planId',
  path: '/plan-detail/$planId',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/account': typeof AccountRoute
  '/accounting': typeof AccountingRoute
  '/analytics': typeof AnalyticsRoute
  '/cogs': typeof CogsRoute
  '/debug-accounting': typeof DebugAccountingRoute
  '/fixed-assets': typeof FixedAssetsRoute
  '/ingredients': typeof IngredientsRoute
  '/kwaci-demo': typeof KwaciDemoRoute
  '/learn': typeof LearnRoute
  '/menus': typeof MenusRoute
  '/operations': typeof OperationsRoute
  '/people': typeof PeopleRoute
  '/plan': typeof PlanRoute
  '/production': typeof ProductionRoute
  '/products': typeof ProductsRoute
  '/recurring-expenses': typeof RecurringExpensesRoute
  '/sales-targets': typeof SalesTargetsRoute
  '/seed-multi-business': typeof SeedMultiBusinessRoute
  '/settings': typeof SettingsRoute
  '/test-accounting-integration': typeof TestAccountingIntegrationRoute
  '/test-business-isolation': typeof TestBusinessIsolationRoute
  '/test-dashboard': typeof TestDashboardRoute
  '/test-employees': typeof TestEmployeesRoute
  '/test-i18n': typeof TestI18nRoute
  '/test-ingredient-fix': typeof TestIngredientFixRoute
  '/test-menus': typeof TestMenusRoute
  '/test-targets': typeof TestTargetsRoute
  '/warehouse': typeof WarehouseRoute
  '/plan-detail/$planId': typeof PlanDetailPlanIdRoute
  '/reports/costs': typeof ReportsCostsRoute
  '/reports/financial': typeof ReportsFinancialRoute
  '/reports/profit': typeof ReportsProfitRoute
  '/reports': typeof ReportsIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/account': typeof AccountRoute
  '/accounting': typeof AccountingRoute
  '/analytics': typeof AnalyticsRoute
  '/cogs': typeof CogsRoute
  '/debug-accounting': typeof DebugAccountingRoute
  '/fixed-assets': typeof FixedAssetsRoute
  '/ingredients': typeof IngredientsRoute
  '/kwaci-demo': typeof KwaciDemoRoute
  '/learn': typeof LearnRoute
  '/menus': typeof MenusRoute
  '/operations': typeof OperationsRoute
  '/people': typeof PeopleRoute
  '/plan': typeof PlanRoute
  '/production': typeof ProductionRoute
  '/products': typeof ProductsRoute
  '/recurring-expenses': typeof RecurringExpensesRoute
  '/sales-targets': typeof SalesTargetsRoute
  '/seed-multi-business': typeof SeedMultiBusinessRoute
  '/settings': typeof SettingsRoute
  '/test-accounting-integration': typeof TestAccountingIntegrationRoute
  '/test-business-isolation': typeof TestBusinessIsolationRoute
  '/test-dashboard': typeof TestDashboardRoute
  '/test-employees': typeof TestEmployeesRoute
  '/test-i18n': typeof TestI18nRoute
  '/test-ingredient-fix': typeof TestIngredientFixRoute
  '/test-menus': typeof TestMenusRoute
  '/test-targets': typeof TestTargetsRoute
  '/warehouse': typeof WarehouseRoute
  '/plan-detail/$planId': typeof PlanDetailPlanIdRoute
  '/reports/costs': typeof ReportsCostsRoute
  '/reports/financial': typeof ReportsFinancialRoute
  '/reports/profit': typeof ReportsProfitRoute
  '/reports': typeof ReportsIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/account': typeof AccountRoute
  '/accounting': typeof AccountingRoute
  '/analytics': typeof AnalyticsRoute
  '/cogs': typeof CogsRoute
  '/debug-accounting': typeof DebugAccountingRoute
  '/fixed-assets': typeof FixedAssetsRoute
  '/ingredients': typeof IngredientsRoute
  '/kwaci-demo': typeof KwaciDemoRoute
  '/learn': typeof LearnRoute
  '/menus': typeof MenusRoute
  '/operations': typeof OperationsRoute
  '/people': typeof PeopleRoute
  '/plan': typeof PlanRoute
  '/production': typeof ProductionRoute
  '/products': typeof ProductsRoute
  '/recurring-expenses': typeof RecurringExpensesRoute
  '/sales-targets': typeof SalesTargetsRoute
  '/seed-multi-business': typeof SeedMultiBusinessRoute
  '/settings': typeof SettingsRoute
  '/test-accounting-integration': typeof TestAccountingIntegrationRoute
  '/test-business-isolation': typeof TestBusinessIsolationRoute
  '/test-dashboard': typeof TestDashboardRoute
  '/test-employees': typeof TestEmployeesRoute
  '/test-i18n': typeof TestI18nRoute
  '/test-ingredient-fix': typeof TestIngredientFixRoute
  '/test-menus': typeof TestMenusRoute
  '/test-targets': typeof TestTargetsRoute
  '/warehouse': typeof WarehouseRoute
  '/plan-detail/$planId': typeof PlanDetailPlanIdRoute
  '/reports/costs': typeof ReportsCostsRoute
  '/reports/financial': typeof ReportsFinancialRoute
  '/reports/profit': typeof ReportsProfitRoute
  '/reports/': typeof ReportsIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/account'
    | '/accounting'
    | '/analytics'
    | '/cogs'
    | '/debug-accounting'
    | '/fixed-assets'
    | '/ingredients'
    | '/kwaci-demo'
    | '/learn'
    | '/menus'
    | '/operations'
    | '/people'
    | '/plan'
    | '/production'
    | '/products'
    | '/recurring-expenses'
    | '/sales-targets'
    | '/seed-multi-business'
    | '/settings'
    | '/test-accounting-integration'
    | '/test-business-isolation'
    | '/test-dashboard'
    | '/test-employees'
    | '/test-i18n'
    | '/test-ingredient-fix'
    | '/test-menus'
    | '/test-targets'
    | '/warehouse'
    | '/plan-detail/$planId'
    | '/reports/costs'
    | '/reports/financial'
    | '/reports/profit'
    | '/reports'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/account'
    | '/accounting'
    | '/analytics'
    | '/cogs'
    | '/debug-accounting'
    | '/fixed-assets'
    | '/ingredients'
    | '/kwaci-demo'
    | '/learn'
    | '/menus'
    | '/operations'
    | '/people'
    | '/plan'
    | '/production'
    | '/products'
    | '/recurring-expenses'
    | '/sales-targets'
    | '/seed-multi-business'
    | '/settings'
    | '/test-accounting-integration'
    | '/test-business-isolation'
    | '/test-dashboard'
    | '/test-employees'
    | '/test-i18n'
    | '/test-ingredient-fix'
    | '/test-menus'
    | '/test-targets'
    | '/warehouse'
    | '/plan-detail/$planId'
    | '/reports/costs'
    | '/reports/financial'
    | '/reports/profit'
    | '/reports'
  id:
    | '__root__'
    | '/'
    | '/account'
    | '/accounting'
    | '/analytics'
    | '/cogs'
    | '/debug-accounting'
    | '/fixed-assets'
    | '/ingredients'
    | '/kwaci-demo'
    | '/learn'
    | '/menus'
    | '/operations'
    | '/people'
    | '/plan'
    | '/production'
    | '/products'
    | '/recurring-expenses'
    | '/sales-targets'
    | '/seed-multi-business'
    | '/settings'
    | '/test-accounting-integration'
    | '/test-business-isolation'
    | '/test-dashboard'
    | '/test-employees'
    | '/test-i18n'
    | '/test-ingredient-fix'
    | '/test-menus'
    | '/test-targets'
    | '/warehouse'
    | '/plan-detail/$planId'
    | '/reports/costs'
    | '/reports/financial'
    | '/reports/profit'
    | '/reports/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AccountRoute: typeof AccountRoute
  AccountingRoute: typeof AccountingRoute
  AnalyticsRoute: typeof AnalyticsRoute
  CogsRoute: typeof CogsRoute
  DebugAccountingRoute: typeof DebugAccountingRoute
  FixedAssetsRoute: typeof FixedAssetsRoute
  IngredientsRoute: typeof IngredientsRoute
  KwaciDemoRoute: typeof KwaciDemoRoute
  LearnRoute: typeof LearnRoute
  MenusRoute: typeof MenusRoute
  OperationsRoute: typeof OperationsRoute
  PeopleRoute: typeof PeopleRoute
  PlanRoute: typeof PlanRoute
  ProductionRoute: typeof ProductionRoute
  ProductsRoute: typeof ProductsRoute
  RecurringExpensesRoute: typeof RecurringExpensesRoute
  SalesTargetsRoute: typeof SalesTargetsRoute
  SeedMultiBusinessRoute: typeof SeedMultiBusinessRoute
  SettingsRoute: typeof SettingsRoute
  TestAccountingIntegrationRoute: typeof TestAccountingIntegrationRoute
  TestBusinessIsolationRoute: typeof TestBusinessIsolationRoute
  TestDashboardRoute: typeof TestDashboardRoute
  TestEmployeesRoute: typeof TestEmployeesRoute
  TestI18nRoute: typeof TestI18nRoute
  TestIngredientFixRoute: typeof TestIngredientFixRoute
  TestMenusRoute: typeof TestMenusRoute
  TestTargetsRoute: typeof TestTargetsRoute
  WarehouseRoute: typeof WarehouseRoute
  PlanDetailPlanIdRoute: typeof PlanDetailPlanIdRoute
  ReportsCostsRoute: typeof ReportsCostsRoute
  ReportsFinancialRoute: typeof ReportsFinancialRoute
  ReportsProfitRoute: typeof ReportsProfitRoute
  ReportsIndexRoute: typeof ReportsIndexRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/warehouse': {
      id: '/warehouse'
      path: '/warehouse'
      fullPath: '/warehouse'
      preLoaderRoute: typeof WarehouseRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/test-targets': {
      id: '/test-targets'
      path: '/test-targets'
      fullPath: '/test-targets'
      preLoaderRoute: typeof TestTargetsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/test-menus': {
      id: '/test-menus'
      path: '/test-menus'
      fullPath: '/test-menus'
      preLoaderRoute: typeof TestMenusRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/test-ingredient-fix': {
      id: '/test-ingredient-fix'
      path: '/test-ingredient-fix'
      fullPath: '/test-ingredient-fix'
      preLoaderRoute: typeof TestIngredientFixRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/test-i18n': {
      id: '/test-i18n'
      path: '/test-i18n'
      fullPath: '/test-i18n'
      preLoaderRoute: typeof TestI18nRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/test-employees': {
      id: '/test-employees'
      path: '/test-employees'
      fullPath: '/test-employees'
      preLoaderRoute: typeof TestEmployeesRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/test-dashboard': {
      id: '/test-dashboard'
      path: '/test-dashboard'
      fullPath: '/test-dashboard'
      preLoaderRoute: typeof TestDashboardRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/test-business-isolation': {
      id: '/test-business-isolation'
      path: '/test-business-isolation'
      fullPath: '/test-business-isolation'
      preLoaderRoute: typeof TestBusinessIsolationRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/test-accounting-integration': {
      id: '/test-accounting-integration'
      path: '/test-accounting-integration'
      fullPath: '/test-accounting-integration'
      preLoaderRoute: typeof TestAccountingIntegrationRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/settings': {
      id: '/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof SettingsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/seed-multi-business': {
      id: '/seed-multi-business'
      path: '/seed-multi-business'
      fullPath: '/seed-multi-business'
      preLoaderRoute: typeof SeedMultiBusinessRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/sales-targets': {
      id: '/sales-targets'
      path: '/sales-targets'
      fullPath: '/sales-targets'
      preLoaderRoute: typeof SalesTargetsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/recurring-expenses': {
      id: '/recurring-expenses'
      path: '/recurring-expenses'
      fullPath: '/recurring-expenses'
      preLoaderRoute: typeof RecurringExpensesRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/products': {
      id: '/products'
      path: '/products'
      fullPath: '/products'
      preLoaderRoute: typeof ProductsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/production': {
      id: '/production'
      path: '/production'
      fullPath: '/production'
      preLoaderRoute: typeof ProductionRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/plan': {
      id: '/plan'
      path: '/plan'
      fullPath: '/plan'
      preLoaderRoute: typeof PlanRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/people': {
      id: '/people'
      path: '/people'
      fullPath: '/people'
      preLoaderRoute: typeof PeopleRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/operations': {
      id: '/operations'
      path: '/operations'
      fullPath: '/operations'
      preLoaderRoute: typeof OperationsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/menus': {
      id: '/menus'
      path: '/menus'
      fullPath: '/menus'
      preLoaderRoute: typeof MenusRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/learn': {
      id: '/learn'
      path: '/learn'
      fullPath: '/learn'
      preLoaderRoute: typeof LearnRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/kwaci-demo': {
      id: '/kwaci-demo'
      path: '/kwaci-demo'
      fullPath: '/kwaci-demo'
      preLoaderRoute: typeof KwaciDemoRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/ingredients': {
      id: '/ingredients'
      path: '/ingredients'
      fullPath: '/ingredients'
      preLoaderRoute: typeof IngredientsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/fixed-assets': {
      id: '/fixed-assets'
      path: '/fixed-assets'
      fullPath: '/fixed-assets'
      preLoaderRoute: typeof FixedAssetsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/debug-accounting': {
      id: '/debug-accounting'
      path: '/debug-accounting'
      fullPath: '/debug-accounting'
      preLoaderRoute: typeof DebugAccountingRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/cogs': {
      id: '/cogs'
      path: '/cogs'
      fullPath: '/cogs'
      preLoaderRoute: typeof CogsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/analytics': {
      id: '/analytics'
      path: '/analytics'
      fullPath: '/analytics'
      preLoaderRoute: typeof AnalyticsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/accounting': {
      id: '/accounting'
      path: '/accounting'
      fullPath: '/accounting'
      preLoaderRoute: typeof AccountingRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/account': {
      id: '/account'
      path: '/account'
      fullPath: '/account'
      preLoaderRoute: typeof AccountRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/reports/': {
      id: '/reports/'
      path: '/reports'
      fullPath: '/reports'
      preLoaderRoute: typeof ReportsIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/reports/profit': {
      id: '/reports/profit'
      path: '/reports/profit'
      fullPath: '/reports/profit'
      preLoaderRoute: typeof ReportsProfitRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/reports/financial': {
      id: '/reports/financial'
      path: '/reports/financial'
      fullPath: '/reports/financial'
      preLoaderRoute: typeof ReportsFinancialRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/reports/costs': {
      id: '/reports/costs'
      path: '/reports/costs'
      fullPath: '/reports/costs'
      preLoaderRoute: typeof ReportsCostsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/plan-detail/$planId': {
      id: '/plan-detail/$planId'
      path: '/plan-detail/$planId'
      fullPath: '/plan-detail/$planId'
      preLoaderRoute: typeof PlanDetailPlanIdRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AccountRoute: AccountRoute,
  AccountingRoute: AccountingRoute,
  AnalyticsRoute: AnalyticsRoute,
  CogsRoute: CogsRoute,
  DebugAccountingRoute: DebugAccountingRoute,
  FixedAssetsRoute: FixedAssetsRoute,
  IngredientsRoute: IngredientsRoute,
  KwaciDemoRoute: KwaciDemoRoute,
  LearnRoute: LearnRoute,
  MenusRoute: MenusRoute,
  OperationsRoute: OperationsRoute,
  PeopleRoute: PeopleRoute,
  PlanRoute: PlanRoute,
  ProductionRoute: ProductionRoute,
  ProductsRoute: ProductsRoute,
  RecurringExpensesRoute: RecurringExpensesRoute,
  SalesTargetsRoute: SalesTargetsRoute,
  SeedMultiBusinessRoute: SeedMultiBusinessRoute,
  SettingsRoute: SettingsRoute,
  TestAccountingIntegrationRoute: TestAccountingIntegrationRoute,
  TestBusinessIsolationRoute: TestBusinessIsolationRoute,
  TestDashboardRoute: TestDashboardRoute,
  TestEmployeesRoute: TestEmployeesRoute,
  TestI18nRoute: TestI18nRoute,
  TestIngredientFixRoute: TestIngredientFixRoute,
  TestMenusRoute: TestMenusRoute,
  TestTargetsRoute: TestTargetsRoute,
  WarehouseRoute: WarehouseRoute,
  PlanDetailPlanIdRoute: PlanDetailPlanIdRoute,
  ReportsCostsRoute: ReportsCostsRoute,
  ReportsFinancialRoute: ReportsFinancialRoute,
  ReportsProfitRoute: ReportsProfitRoute,
  ReportsIndexRoute: ReportsIndexRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
