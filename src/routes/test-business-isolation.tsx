import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { CheckCircle, XCircle, Loader2, Alert<PERSON>riangle } from 'lucide-react'
import { WarehouseService } from '@/lib/services/warehouseService'
import { ProductionService } from '@/lib/services/productionService'
import { BusinessService } from '@/lib/services/businessService'
import { useBusinessStore } from '@/lib/stores/businessStore'

interface TestResult {
  name: string
  status: 'pending' | 'success' | 'warning' | 'error'
  message: string
  data?: any
}

function BusinessIsolationTest() {
  const [tests, setTests] = useState<TestResult[]>([
    { name: 'Business Context Check', status: 'pending', message: 'Checking business context...' },
    { name: 'Warehouse Data Isolation', status: 'pending', message: 'Waiting...' },
    { name: 'Production Data Isolation', status: 'pending', message: 'Waiting...' },
    { name: 'Cross-Business Data Verification', status: 'pending', message: 'Waiting...' },
  ])
  const [isRunning, setIsRunning] = useState(false)
  const [testResults, setTestResults] = useState<any>({})
  const { currentBusiness, businesses } = useBusinessStore()

  const updateTest = (index: number, updates: Partial<TestResult>) => {
    setTests(prev => prev.map((test, i) => i === index ? { ...test, ...updates } : test))
  }

  const runTests = async () => {
    setIsRunning(true)
    setTestResults({})

    try {
      // Test 1: Business Context Check
      updateTest(0, { status: 'pending', message: 'Checking business context...' })
      
      if (!currentBusiness) {
        updateTest(0, { 
          status: 'error', 
          message: 'No business selected. Please select a business first.' 
        })
        setIsRunning(false)
        return
      }

      const allBusinesses = await BusinessService.getAll()
      updateTest(0, { 
        status: 'success', 
        message: `Business context OK. Current: ${currentBusiness.name}, Total businesses: ${allBusinesses.length}`,
        data: { currentBusiness, totalBusinesses: allBusinesses.length }
      })

      // Test 2: Warehouse Data Isolation
      updateTest(1, { status: 'pending', message: 'Testing warehouse data isolation...' })
      
      try {
        const warehouseBatches = await WarehouseService.getAllBatchesWithItems()
        const warehouseStats = await WarehouseService.getWarehouseStats()
        
        updateTest(1, { 
          status: 'success', 
          message: `Warehouse isolation OK. Found ${warehouseBatches.length} batches for business "${currentBusiness.name}"`,
          data: { batches: warehouseBatches.length, stats: warehouseStats }
        })
      } catch (error) {
        updateTest(1, { 
          status: 'error', 
          message: `Warehouse test failed: ${error instanceof Error ? error.message : String(error)}` 
        })
      }

      // Test 3: Production Data Isolation
      updateTest(2, { status: 'pending', message: 'Testing production data isolation...' })
      
      try {
        const productionBatches = await ProductionService.getAllBatchesWithItems()
        const productionStats = await ProductionService.getProductionStats()
        
        updateTest(2, { 
          status: 'success', 
          message: `Production isolation OK. Found ${productionBatches.length} batches for business "${currentBusiness.name}"`,
          data: { batches: productionBatches.length, stats: productionStats }
        })
      } catch (error) {
        updateTest(2, { 
          status: 'error', 
          message: `Production test failed: ${error instanceof Error ? error.message : String(error)}` 
        })
      }

      // Test 4: Cross-Business Data Verification
      updateTest(3, { status: 'pending', message: 'Verifying cross-business data isolation...' })
      
      if (allBusinesses.length > 1) {
        // Test with different businesses to ensure isolation
        const currentBusinessData = {
          warehouseBatches: await WarehouseService.getAllBatchesWithItems(),
          productionBatches: await ProductionService.getAllBatchesWithItems()
        }

        setTestResults({
          currentBusiness: currentBusiness.name,
          warehouseBatches: currentBusinessData.warehouseBatches.length,
          productionBatches: currentBusinessData.productionBatches.length,
          allBusinesses: allBusinesses.map(b => b.name)
        })

        updateTest(3, { 
          status: 'success', 
          message: `Cross-business verification complete. Data properly isolated for "${currentBusiness.name}"`,
          data: currentBusinessData
        })
      } else {
        updateTest(3, { 
          status: 'warning', 
          message: 'Only one business exists. Create multiple businesses to test cross-business isolation.' 
        })
      }

    } catch (error) {
      console.error('Test execution failed:', error)
      updateTest(tests.findIndex(t => t.status === 'pending'), { 
        status: 'error', 
        message: `Test failed: ${error instanceof Error ? error.message : String(error)}` 
      })
    } finally {
      setIsRunning(false)
    }
  }

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case 'pending':
        return <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Business Isolation Test</h1>
          <p className="text-muted-foreground mt-2">
            Test warehouse and production data isolation between businesses
          </p>
        </div>
        <Button 
          onClick={runTests} 
          disabled={isRunning}
          className="min-w-[120px]"
        >
          {isRunning ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Running...
            </>
          ) : (
            'Run Tests'
          )}
        </Button>
      </div>

      <div className="grid gap-4">
        {tests.map((test, index) => (
          <Card key={index}>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                {getStatusIcon(test.status)}
                {test.name}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-2">{test.message}</p>
              {test.data && (
                <pre className="text-xs bg-muted p-2 rounded overflow-auto">
                  {JSON.stringify(test.data, null, 2)}
                </pre>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {Object.keys(testResults).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-sm bg-muted p-4 rounded overflow-auto">
              {JSON.stringify(testResults, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export const Route = createFileRoute('/test-business-isolation')({
  component: BusinessIsolationTest,
})
