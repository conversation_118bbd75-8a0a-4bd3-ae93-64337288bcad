{"navigation": {"dashboard": "Dashboard", "plan": "Plan", "products": "Products", "menus": "Menus", "salesTargets": "Sales Targets", "operations": "Operations", "people": "People", "accounting": "Accounting", "recurringExpenses": "Recurring Expenses", "analytics": "Analytics", "ingredients": "Ingredients", "cogsCalculator": "COGS Calculator", "warehouse": "Warehouse", "production": "Production", "fixedAssets": "Fixed Assets", "reports": "Reports", "kwaciDemo": "KWACI Demo", "learningHub": "Learning Hub", "settings": "Settings", "account": "Account"}, "navigationDescriptions": {"dashboard": "Main financial dashboard", "plan": "Guided onboarding and operational planning", "products": "Manage products and their compositions", "menus": "Create and manage coffee shop menus", "salesTargets": "Set daily sales targets for menu products", "operations": "Sales recording, analytics, and operational tracking", "people": "Manage employees and point-of-contact assignments", "accounting": "Comprehensive financial transaction management and accounting", "recurringExpenses": "Manage monthly and yearly recurring operational expenses", "analytics": "Product-specific income projections and profit analysis", "ingredients": "Manage ingredients and their properties", "cogsCalculator": "Calculate cost of goods sold", "warehouse": "Manage warehouse stock and inventory", "production": "Manage production batches and allocations", "fixedAssets": "Manage fixed assets and depreciation", "reports": "Financial reports and analytics", "kwaciDemo": "Explore KWACI acronym meanings and animations"}, "reportsSubMenu": {"financialOverview": "Financial Overview", "profitAnalysis": "Profit Analysis", "costBreakdown": "Cost Breakdown"}, "sidebarGroups": {"navigation": "Navigation", "learningSupport": "Learning & Support", "quickActions": "Quick Actions", "devTools": "<PERSON>"}, "devTools": {"multiBusinessSeed": "Multi-Business Seed", "debugAccounting": "Debug Accounting"}, "userMenu": {"businessOwner": "KWACI Business Owner", "email": "<EMAIL>", "keyboardShortcuts": "Keyboard Shortcuts", "signOut": "Sign out"}, "common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "create": "Create", "update": "Update", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "export": "Export", "import": "Import", "refresh": "Refresh", "retry": "Retry", "remove": "Remove"}, "language": {"switchLanguage": "Switch Language", "currentLanguage": "Current Language", "english": "English", "indonesian": "Bahasa Indonesia"}, "operationsStatus": {"status": {"pending": "Pending", "inProgress": "In Progress", "completed": "Completed"}}, "kwaci": {"brandName": "KWACI Grow", "acronyms": {"mixed": {"name": "Mixed (Indonesian-English)", "k": "<PERSON><PERSON><PERSON>", "kDesc": "Finance", "w": "<PERSON><PERSON><PERSON><PERSON>", "wDesc": "Entrepreneur", "a": "Automated", "aDesc": "Automated processes", "c": "Commerce", "cDesc": "Commerce management", "i": "Insights", "iDesc": "Business insights"}, "english": {"name": "All English Alternative", "k": "Knowledge", "kDesc": "Business knowledge management", "w": "Warehouse", "wDesc": "Inventory and stock management", "a": "Analytics", "aDesc": "Data analytics and reporting", "c": "Commerce", "cDesc": "Business commerce operations", "i": "Intelligence", "iDesc": "Business intelligence"}, "indonesian": {"name": "All Bahasa Indonesia Alternative", "k": "<PERSON><PERSON>", "kDesc": "Cashier/POS", "w": "<PERSON><PERSON>", "wDesc": "Small business/shop", "a": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aDesc": "Accounting", "c": "<PERSON><PERSON><PERSON>", "cDesc": "Smart/intelligent", "i": "<PERSON><PERSON><PERSON>", "iDesc": "Innovation"}}}, "plan": {"title": "Plan", "description": "Guided onboarding and operational planning for your coffee shop", "tabs": {"journey": "Journey Map", "planningDashboard": "Planning Dashboard"}, "templatePreviewSheet": {"previewDescription": "Preview template details and create a new operational plan", "overview": "Template Overview", "type": {"daily": "Daily", "weekly": "Weekly", "monthly": "Monthly"}, "difficulty": {"beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced"}, "default": "<PERSON><PERSON><PERSON>", "estimatedDuration": "Estimated Duration: {{duration}} hours", "loading": "Loading template details...", "goals": "Goals ({{count}})", "goalTarget": "Target: {{value}} {{unit}}", "tasks": "Tasks ({{count}})", "priority": {"low": "Low", "medium": "Medium", "high": "High"}, "taskEst": "Est. {{duration}}min", "metrics": "Metrics ({{count}})", "metricCategory": {"performance": "Performance", "quality": "Quality", "efficiency": "Efficiency"}, "metricTarget": "Target: {{value}} {{unit}}", "metricTrack": "Track: {{frequency}}", "createPlan": "Create Plan from Template", "close": "Close", "backToPreview": "Back to Preview"}, "journeyMap": {"loading": "Loading journey progress...", "error": "Error loading journey progress", "title": "Coffee Shop Setup Journey", "description": "Complete all 9 steps to fully set up your coffee shop operations", "autoCheck": "Auto Check", "reset": "Reset", "overallProgress": "Overall Progress", "complete": "{{percentage}}% Complete", "nextStep": "Next Step: {{title}}", "start": "Start", "completeTitle": "🎉 Journey Complete!", "completeDescription": "Congratulations! You've completed all setup steps for your coffee shop.", "tabs": {"map": "Journey Map", "guided": "Guided Steps"}, "stepsMap": {"title": "Journey Steps Map", "description": "Click on any unlocked step to view instructions and get started", "bonus": "Bonus", "status": {"completed": "Completed", "ready": "Ready", "locked": "Locked"}}, "modal": {"stepLabel": "Step {{number}}: {{title}}", "instructions": "Instructions", "goTo": "Go to {{title}}", "completePrev": "Complete Previous Steps First", "close": "Close", "markComplete": "Mark as Complete", "markingComplete": "Marking Complete...", "tipsTitle": "💡 Tips for Success", "tips": {"createIngredient": ["Start with basic ingredients like Coffee Beans, Milk, and Sugar", "Set realistic base unit costs and quantities", "Use clear, descriptive names for easy identification"], "createProduct": ["Create simple products first (e.g., Espresso, Latte)", "Ensure COGS calculations are accurate", "Add detailed descriptions for clarity"], "createMenu": ["Give your menu a clear, descriptive name", "Set the status to \"Active\" when ready", "Add notes about the menu's purpose or target audience"], "createBranch": ["Use specific location names (e.g., \"Downtown Store\")", "Include address or area information", "Set branch as active for operations"], "addProductToMenu": ["Set competitive pricing for your products", "Organize products by categories", "Use display order to highlight popular items"], "addItemToWarehouse": ["Start with sufficient quantities for production", "Use the COGS calculator for accurate costing", "Add batch notes for tracking purposes"], "createProductionAllocation": ["Allocate realistic quantities based on demand", "Check stock levels before allocation", "Add production notes for reference"], "changeProductionBatchStatus": ["Follow the workflow: Pending → In Progress → Completed", "Monitor stock level changes after completion", "Record actual output quantities when completing"], "recordSales": ["Record sales with accurate timestamps", "Ensure sufficient stock before recording sales", "Monitor inventory deductions after sales"]}, "status": {"completed": "Completed", "ready": "Ready to Start", "locked": "Locked", "unknown": "Unknown"}}, "guidedSteps": {"checkingRequirements": "Checking requirements...", "requirementsMet": "Requirements met! You can mark this step as complete.", "validation": {"createIngredient": {"message": "Please create at least one ingredient to proceed.", "suggestion1": "Go to the Ingredients section and add a new ingredient.", "suggestion2": "Ensure you have filled in all required ingredient details.", "suggestion3": "Save your ingredient before continuing."}, "createProduct": {"message": "Please create at least one product to proceed.", "suggestion1": "Go to the Products section and add a new product.", "suggestion2": "Ensure you have filled in all required product details.", "suggestion3": "Save your product before continuing."}, "createMenu": {"message": "Please create at least one menu to proceed.", "suggestion1": "Go to the Menus section and add a new menu.", "suggestion2": "Ensure you have filled in all required menu details.", "suggestion3": "Save your menu before continuing."}, "createBranch": {"message": "Please create at least one branch to proceed.", "suggestion1": "Go to the Branches section and add a new branch.", "suggestion2": "Ensure you have filled in all required branch details.", "suggestion3": "Save your branch before continuing."}, "addProductToMenu": {"message": "Please add at least one product to a menu to proceed.", "suggestion1": "Go to the Menus section and add products to a menu.", "suggestion2": "Ensure each menu has at least one product.", "suggestion3": "Save your menu after adding products."}, "addItemToWarehouse": {"message": "Please add at least one item to the warehouse to proceed.", "suggestion1": "Go to the Warehouse section and add a new item.", "suggestion2": "Ensure you have filled in all required item details.", "suggestion3": "Save your warehouse item before continuing."}, "createProductionAllocation": {"message": "Please create at least one production allocation to proceed.", "suggestion1": "Go to the Production Allocation section and add a new allocation.", "suggestion2": "Ensure you have filled in all required allocation details.", "suggestion3": "Save your allocation before continuing."}, "changeProductionBatchStatus": {"message": "Please update the production batch status to proceed.", "suggestion1": "Go to the Production Batches section and update the status.", "suggestion2": "Ensure the batch status reflects the current progress.", "suggestion3": "Save your changes after updating the status."}, "recordSales": {"message": "Please record at least one sale to proceed.", "suggestion1": "Go to the Sales section and record a new sale.", "suggestion2": "Ensure you have filled in all required sales details.", "suggestion3": "Save your sales record before continuing."}}, "validationNotConfigured": "Step validation not configured.", "validationError": "Error checking step requirements.", "tryAgain": "Please try again or contact support.", "progress": "Progress", "instructions": "Instructions", "showSuggestions": "Show suggestions", "hideSuggestions": "Hide suggestions", "goTo": "Go to {{title}}", "markComplete": "Mark as Complete", "completing": "Completing...", "status": {"completed": "Completed", "ready": "Ready to Complete", "inProgress": "In Progress", "locked": "Locked", "unknown": "Unknown"}}}, "type": {"daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "daily_id": "Daily Plan", "weekly_id": "Weekly Plan", "monthly_id": "Monthly Plan"}, "category": {"sales": "Sales", "production": "Production", "efficiency": "Efficiency", "quality": "Quality", "cost": "Cost"}, "status": {"draft": "Draft", "active": "Active", "completed": "Completed", "archived": "Archived"}, "planningDashboard": {"loading": "Loading dashboard data...", "title": "Planning Dashboard", "description": "Create and manage daily, weekly, and monthly operational plans for your coffee shop", "stats": {"activePlans": "Active Plans", "goalsAchieved": "Goals Achieved", "tasksCompleted": "Tasks Completed", "completionRate": "Completion Rate"}, "templates": {"title": "Planning Templates", "description": "Quick start templates for different planning scenarios", "default": "<PERSON><PERSON><PERSON>", "duration": "Est. {{hours}}h duration", "useTemplate": "Use Template"}, "journey": {"title": "Setup Journey Progress", "description": "Complete your coffee shop setup journey to unlock advanced planning features", "overallProgress": "Overall Progress", "incompleteTitle": "Complete Setup Journey", "incompleteDescription": "Finish setting up your coffee shop to unlock all planning features and templates.", "completeTitle": "🎉 Setup Complete!", "completeDescription": "Your coffee shop is fully set up. You can now create comprehensive operational plans.", "createIngredient": {"title": "Add Your First Ingredient", "description": "Start by adding an ingredient to your inventory. Ingredients are the building blocks for your products.", "instructions": "Go to the Ingredients section and click 'Add Ingredient'. Fill in the details and save."}, "createProduct": {"title": "Create Your First Product", "description": "Now create a product using your ingredients. Products are what you sell to customers.", "instructions": "Go to the Products section, click 'Add Product', and define its composition."}, "createMenu": {"title": "Build Your Menu", "description": "Add your products to a menu so customers can order them.", "instructions": "Navigate to Menus, create a new menu, and add products."}, "createBranch": {"title": "Add a Branch", "description": "Set up your first branch location for sales and operations.", "instructions": "Go to Branches, click 'Add Branch', and enter the location details."}, "addProductToMenu": {"title": "Add Product to Menu", "description": "Link your products to the menu for customer ordering.", "instructions": "Edit your menu and add the products you want to offer."}, "addItemToWarehouse": {"title": "Stock Your Warehouse", "description": "Add items to your warehouse to track inventory levels.", "instructions": "Go to Warehouse, click 'Add Item', and specify quantities."}, "createProductionAllocation": {"title": "Set Up Production Allocation", "description": "Allocate ingredients for production batches.", "instructions": "Navigate to Production, create a batch, and allocate ingredients."}, "changeProductionBatchStatus": {"title": "Update Production Batch Status", "description": "Track the progress of your production batches.", "instructions": "Go to Production, select a batch, and update its status."}, "recordSales": {"title": "Record Your First Sale", "description": "Start tracking sales to monitor business performance.", "instructions": "Go to Sales, click 'Record Sale', and enter transaction details."}, "createSalesTarget": {"title": "Set a Sales Target", "description": "Define daily sales goals for your team.", "instructions": "Go to Sales Targets, click 'Add Target', and set your goals."}}, "recentPlans": {"title": "Recent Plans", "noPlansTitle": "No Plans Yet", "noPlansDescription": "Create your first operational plan to get started with structured planning.", "createFirst": "Create Your First Plan", "view": "View Plan"}, "analytics": {"plans": "Plans", "title": "Planning Analytics", "description": "Overview of your planning performance and insights", "planDistribution": "Plan Distribution", "statusOverview": "Status Overview", "performanceMetrics": "Performance Metrics", "averageTaskDuration": "Average Task Duration", "mostUsedTemplate": "Most Used Template", "totalPlansCreated": "Total Plans Created", "goalCategories": "Goal Categories"}}}, "dashboard": {"title": "Business Dashboard", "description": "Comprehensive overview of your business operations and performance", "lastUpdated": "Last updated", "refresh": "Refresh", "realTimeData": "Real-time data", "dataUpdatesAutomatically": "Dashboard data updates automatically when you switch business contexts", "welcome": {"title": "Welcome to KWACI Grow", "description": "Your comprehensive business management dashboard", "selectBusiness": "Please select a business from the sidebar to view your dashboard and start managing your operations.", "multiBusinessEnabled": "Multi-business support enabled"}, "noBusinessSelected": "No business selected", "selectBusinessToView": "Please select a business to view", "errorLoading": "Error loading", "tryAgain": "Try again", "salesAnalytics": {"title": "Sales Analytics", "description": "Revenue and transaction insights for {{period}}", "selectPeriod": "Select period", "periods": {"today": "Today", "week": "This Week", "month": "This Month", "quarter": "Last 3 Months"}, "metrics": {"totalRevenue": "Total Revenue", "transactions": "Transactions", "avgOrderValue": "Avg Order Value", "topProduct": "Top Product", "perTransactionAverage": "Per transaction average", "sold": "sold", "noSales": "No sales", "active": "Active"}, "hourlyChart": {"title": "Hourly Sales Today", "description": "Revenue and transaction distribution throughout the day", "peakHours": "Peak Hours"}, "noData": {"title": "No Sales Data", "description": "No sales recorded for {{period}}. Start recording sales to see analytics here."}}, "financialOverview": {"title": "Financial Overview", "description": "Current financial position and cash flow insights", "metrics": {"availableCash": "Available Cash", "monthlyExpenses": "Monthly Expenses", "netPosition": "Net Position", "cashRunway": "Cash Runway", "burnRate": "Burn rate", "months": "months", "atCurrentBurnRate": "At current burn rate", "positive": "Positive", "negative": "Negative"}, "healthStatus": {"healthy": "Healthy", "caution": "Caution", "critical": "Critical"}, "summary": {"title": "Financial Health Summary", "description": "Overview of your business financial position", "cashFlowStatus": "Cash Flow Status", "positiveCashFlow": "Positive cash flow - business is generating more revenue than expenses", "negativeCashFlow": "Negative cash flow - expenses exceed revenue, monitor closely", "revenueVsExpenses": "Revenue vs Expenses", "monthlyRevenue": "Monthly Revenue", "monthlyExpenses": "Monthly Expenses", "netResult": "Net Result", "recommendations": "Financial Recommendations", "focusOnRevenue": "• Focus on increasing revenue or reducing expenses", "considerOptimization": "• Consider cost optimization - runway is less than 3 months", "considerReinvestment": "• Consider reinvesting profits for business growth", "immediateAttention": "• Immediate attention needed - negative cash position"}, "error": {"title": "Unable to Load Financial Data"}}, "operationsStatus": {"title": "Operations Status", "description": "Production batches and operational workflow status", "overview": "Operations Overview", "metrics": {"incompleteBatches": "Incomplete Batches", "overdueBatches": "Overdue Batches", "urgentBatches": "Urgent Batches", "onTimeRate": "On-Time Rate", "batchesCompletedOnTime": "Batches completed on time"}, "status": {"pending": "Pending", "inProgress": "In Progress", "completed": "Completed", "cancelled": "Cancelled"}, "priority": {"overdue": "Overdue", "urgent": "<PERSON><PERSON>", "normal": "Normal"}, "badges": {"active": "Active", "allComplete": "All Complete", "attentionNeeded": "Attention Needed", "onTrack": "On Track", "monitorClosely": "Monitor Closely"}, "columns": {"batchNumber": "Batch #", "product": "Product", "quantity": "Quantity", "status": "Status", "priority": "Priority", "created": "Created", "age": "Age", "target": "Target", "notSpecified": "Not specified"}, "incompleteBatches": {"title": "Incomplete Production Batches", "description": "Production batches that require attention or completion"}, "emptyState": {"title": "All Batches Complete", "description": "No incomplete production batches. Great job keeping up with production!"}, "error": {"title": "Unable to Load Operations Data", "loading": "Error loading", "tryAgain": "Try again"}, "noBusinessSelected": {"title": "No business selected", "description": "Please select a business to view operations status."}}, "inventoryAlerts": {"title": "Inventory Alerts", "description": "Stock levels and low inventory warnings", "overview": "Inventory Overview", "metrics": {"criticalAlerts": "Critical Alerts", "lowStockAlerts": "Low Stock Alerts", "itemsBelowThreshold": "Items Below Threshold", "stockHealth": "Stock Health"}, "summary": {"title": "Inventory Summary", "criticalItems": "Critical Items", "lowStockItems": "Low Stock Items", "totalItems": "Total Items"}, "alertLevels": {"critical": "Critical", "lowStock": "Low Stock", "normal": "Normal"}, "badges": {"immediateAction": "Immediate Action", "noCriticalIssues": "No Critical Issues", "monitorClosely": "Monitor Closely", "stockLevelsGood": "Stock Levels Good", "requireAttention": "Require Attention", "allWellStocked": "All Well-Stocked", "excellentHealth": "Excellent Health", "goodOverall": "Good Overall"}, "columns": {"ingredient": "Ingredient", "currentStock": "Current Stock", "threshold": "<PERSON><PERSON><PERSON><PERSON>", "stockLevel": "Stock Level", "alertLevel": "Alert <PERSON>", "actionNeeded": "Action Needed"}, "actions": {"outOfStock": "Out of stock - Order immediately", "reorderUrgently": "Reorder urgently", "planReorderSoon": "Plan reorder soon", "monitorLevels": "Monitor levels"}, "progress": {"ofThreshold": "% of threshold"}, "lowStockIngredients": {"title": "Low Stock Ingredients", "description": "Ingredients that are below their low stock threshold"}, "emptyState": {"title": "All Stock Levels Good", "description": "No ingredients are below their low stock threshold. Keep up the good inventory management!"}, "error": {"title": "Unable to Load Inventory Data", "loading": "Error loading", "tryAgain": "Try again"}, "noBusinessSelected": {"title": "No business selected", "description": "Please select a business to view inventory alerts."}}, "branchPerformance": {"title": "Branch Performance", "description": "Sales performance comparison across branches for {{period}}", "periods": {"today": "today", "week": "this week", "month": "this month", "quarter": "last 3 months"}, "periodLabels": {"today": "Today", "week": "This Week", "month": "This Month", "quarter": "Last 3 Months"}, "metrics": {"activeBranches": "Active Branches", "totalRevenue": "Total Revenue", "avgPerBranch": "Avg per Branch", "topPerformer": "Top Performer"}, "summary": {"title": "Performance Summary", "topPerformer": "Top Performer", "totalBranches": "Total Branches", "avgRevenue": "Avg Revenue"}, "badges": {"operational": "Operational", "noBranches": "No branches"}, "labels": {"acrossAllBranches": "Across all branches", "revenuePerBranch": "Revenue per branch", "noData": "No data", "aboveAverage": "Above average", "belowAverage": "Below average"}, "columns": {"rank": "Rank", "branch": "Branch", "revenue": "Revenue", "transactions": "Transactions", "avgOrderValue": "Avg Order", "performance": "Performance"}, "comparison": {"title": "Branch Performance Comparison", "description": "Detailed performance metrics for each branch ({{period}})"}, "performanceLabels": {"excellent": "Excellent", "good": "Good", "average": "Average", "belowAverage": "Below Average"}, "emptyState": {"title": "No Branch Data", "description": "No branches found or no sales data available for the selected period."}, "error": {"title": "Unable to Load Branch Data", "loading": "Error loading", "tryAgain": "Try again"}, "noBusinessSelected": {"title": "No business selected", "description": "Please select a business to view branch performance."}}}, "cogs": {"playground": {"title": "COGS Playground", "sandboxBadge": "Sandbox Environment", "saveToDatabase": "Save to Database", "description": {"welcome": "Welcome to the COGS Playground!", "line1": "This is a sandbox environment where you can experiment with ingredient and product combinations", "line2": "to calculate Cost of Goods Sold (COGS) before committing data to your database.", "line3": "All data here is temporary until you choose to save it."}, "badges": {"realTime": "Real-time COGS calculations", "temporaryIngredients": "Temporary ingredients", "productExperimentation": "Product experimentation", "optionalSave": "Optional database save"}, "tabs": {"ingredients": "Ingredients", "products": "Products"}, "gettingStarted": {"title": "Getting Started", "description": "Follow these steps to start experimenting with COGS calculations:", "steps": {"createIngredients": {"title": "Create Ingredients", "description": "Add temporary ingredients with costs, quantities, and units"}, "buildProducts": {"title": "Build Products", "description": "Combine ingredients to create products and see real-time COGS"}, "saveWhenReady": {"title": "Save When Ready", "description": "When satisfied with your experiments, save to database"}}}}, "saveDialog": {"title": "Save to Database", "description": "This will permanently save your temporary ingredients and products to the database. Only active items will be saved.", "noDataTitle": "No active data to save", "noDataDescription": "Create some active ingredients or products first.", "ingredientsToSave": "Ingredients to Save", "productsToSave": "Products to Save", "saveSummary": "Save Summary", "totalIngredients": "Total Ingredients", "totalProducts": "Total Products", "saving": "Saving...", "success": "Successfully saved to database!", "failure": "Failed to save to database"}, "tempIngredientList": {"title": "Temporary Ingredients", "sandboxBadge": "Sandbox", "addIngredient": "Add Ingredient", "description": "Create ingredients for COGS experimentation. These won't be saved to the database until you choose to save them.", "emptyMessage": "No temporary ingredients yet", "addFirst": "Add Your First Ingredient", "table": {"name": "Name", "category": "Category", "baseCost": "Base Cost", "baseQuantity": "Base Quantity", "unitCost": "Unit Cost", "unit": "Unit", "status": "Status", "actions": "Actions", "active": "Active", "inactive": "Inactive"}}, "tempProductList": {"title": "Temporary Products", "sandboxBadge": "Sandbox", "addProduct": "Add Product", "description": "Create products with ingredient compositions for COGS experimentation. These won't be saved to the database until you choose to save them.", "noIngredientsWarning": "Create some active ingredients first before adding products.", "emptyMessage": "No temporary products yet", "addFirst": "Add Your First Product", "table": {"productName": "Product Name", "description": "Description", "ingredients": "Ingredients", "cogsPerCup": "COGS per Cup", "status": "Status", "actions": "Actions", "noIngredientsBadge": "No ingredients", "ingredientCount": "{{count}} ingredient", "ingredientCount_plural": "{{count}} ingredients", "active": "Active", "inactive": "Inactive"}, "summary": {"title": "Playground Summary", "totalProducts": "Total Products", "activeProducts": "Active Products", "avgCogsPerCup": "Avg COGS per Cup", "totalIngredientsUsed": "Total Ingredients Used"}}, "tempIngredientForm": {"addTitle": "Add Temporary Ingredient", "editTitle": "Edit Temporary Ingredient", "description": "Create ingredients for COGS experimentation. This data is temporary and won't be saved to the database until you choose to save it.", "fields": {"name": "Ingredient Name *", "baseUnitCost": "Base Unit Cost *", "baseUnitQuantity": "Base Unit Quantity *", "unit": "Unit *", "category": "Category", "supplierInfo": "Supplier Information", "note": "Notes", "isActive": "Active ingredient", "selectUnit": "Select unit", "selectCategory": "Select category", "noCategory": "No Category", "costBaseHelp": "Cost for the base quantity", "quantityHelp": "Quantity in the base unit", "unitHelp": "Unit of measurement", "supplierPlaceholder": "Enter supplier details", "notePlaceholder": "Additional notes about this ingredient"}, "buttons": {"add": "Add Ingredient", "update": "Update Ingredient", "adding": "Adding...", "updating": "Updating...", "cancel": "Cancel"}}, "tempProductForm": {"addTitle": "Add Temporary Product", "editTitle": "Edit Temporary Product", "description": "Create products with ingredient compositions for COGS experimentation. This data is temporary and won't be saved to the database until you choose to save it.", "fields": {"name": "Product Name *", "description": "Description", "note": "Notes", "isActive": "Active product", "usagePerCup": "Usage per Cup", "ingredient": "Ingredient", "noteOptional": "Note (optional)", "selectIngredient": "Select ingredient", "additionalNotes": "Additional notes", "totalCogsPerCup": "Total COGS per Cup"}, "buttons": {"add": "Add Product", "update": "Update Product", "adding": "Adding...", "updating": "Updating...", "cancel": "Cancel", "addIngredient": "Add Ingredient"}, "table": {"ingredient": "Ingredient", "usagePerCup": "Usage per Cup", "costPerCup": "Cost per Cup", "note": "Note", "actions": "Actions"}, "emptyIngredients": "No ingredients added yet"}}, "recurringExpenses": {"page": {"title": "Recurring Expenses", "description": "Manage your monthly and yearly recurring operational expenses", "showInactive": "Show inactive"}, "sheet": {"createTitle": "Add New Recurring Expense", "editTitle": "Edit Recurring Expense", "createDescription": "Add a new recurring expense to track your operational costs.", "editDescription": "Update the expense information and settings."}, "summary": {"monthlyTotal": "Monthly Total", "yearlyTotal": "Yearly Total", "activeExpenses": "Active Expenses", "totalExpenses": "Total Expenses", "expensesByCategory": "Expenses by Category", "monthlyExpenses": "Monthly Expenses", "yearlyExpenses": "Yearly Expenses", "quickStats": "Quick Stats", "annualCost": "Annual cost (monthly × 12)", "dailyAverageCost": "Daily average cost", "expenseCategories": "Expense categories"}, "table": {"title": "Recurring Expenses", "searchPlaceholder": "Search expenses...", "categoryFilterAll": "All Categories", "frequencyFilterAll": "All", "noExpenses": "No recurring expenses found. Create your first expense to get started.", "noMatch": "No expenses match your current filters.", "headers": {"name": "Name", "amount": "Amount", "frequency": "Frequency", "category": "Category", "startDate": "Start Date", "endDate": "End Date", "status": "Status", "actions": "Actions"}, "ongoing": "Ongoing", "status": {"active": "Active", "inactive": "Inactive"}, "actions": {"edit": "Edit", "delete": "Delete", "activate": "Activate", "deactivate": "Deactivate", "openMenu": "Open menu", "clear": "Clear"}}, "form": {"fields": {"name": "Expense Name", "description": "Description (Optional)", "amount": "Amount ({{currency}})", "frequency": "Frequency", "category": "Category", "startDate": "Start Date", "endDate": "End Date (Optional)", "note": "Note (Optional)"}, "placeholders": {"name": "e.g., Office Rent, Employee Salary", "description": "Additional details about this expense...", "category": "Select or create category...", "searchCategories": "Search categories...", "frequency": "Select frequency", "pickDate": "Pick a date", "noEndDate": "No end date", "clearDate": "Clear Date", "note": "Additional notes or comments..."}, "descriptions": {"leaveEmpty": "Leave empty for ongoing expenses"}, "frequency": {"monthly": "Monthly", "yearly": "Yearly"}, "buttons": {"cancel": "Cancel", "create": "Create Expense", "update": "Update Expense", "saving": "Saving..."}}, "messages": {"createSuccess": "The new recurring expense has been created successfully.", "updateSuccess": "The recurring expense has been updated successfully.", "deleteSuccess": "The recurring expense has been permanently deleted.", "deleteError": "Failed to delete the expense. Please try again.", "deactivateSuccess": "The recurring expense has been deactivated.", "activateSuccess": "The recurring expense has been activated.", "statusError": "Failed to update the expense status. Please try again."}}, "products": {"loading": "Loading products...", "errorLoading": "Error loading products: {{error}}", "title": "Product Management", "description": "Manage your products and their ingredient compositions", "create": "Create Product", "createNewTitle": "Create New Product", "createNewDescription": "Add a new product to your catalog", "tabs": {"overview": "Overview", "products": "Products"}, "overview": {"totalProducts": "Total Products", "totalProductsDesc": "Active products in catalog", "totalIngredients": "Total Ingredients", "totalIngredientsDesc": "Across all products", "avgIngredients": "Avg. Ingredients", "avgIngredientsDesc": "Per product", "recentProducts": "Recent Products", "noProducts": "No products yet", "noProductsDesc": "Create your first product to get started", "viewDetails": "View Details", "viewAll": "View All Products ({{count}})", "ingredientsCount": "{{count}} ingredients"}, "filters": {"title": "Product Filters", "showInactive": "Show inactive products"}, "list": {"title": "Products ({{count}})", "searchPlaceholder": "Search products...", "noProductsFound": "No products found", "noProducts": "No products yet", "tryAdjustSearch": "Try adjusting your search terms", "createFirst": "Create your first product to get started", "table": {"name": "Name", "description": "Description", "ingredients": "Ingredients", "cogsPerCup": "COGS per Cup", "status": "Status", "created": "Created", "actions": "Actions"}, "ingredientCount": "{{count}} ingredients", "badge": {"active": "Active", "inactive": "Inactive"}, "deleteTitle": "Delete Product", "deleteDescription": "Are you sure you want to delete \"{{name}}\"? This action cannot be undone.", "editSheetTitle": "Edit Product", "editSheetDescription": "Update product information", "viewSheetTitle": "Product Details", "viewSheetDescription": "View and manage product ingredients"}, "form": {"nameRequired": "Product name is required", "saveFailed": "Failed to save product", "unexpectedError": "An unexpected error occurred", "fields": {"name": "Product Name *", "description": "Description", "note": "Notes", "activeStatus": "Active Status"}, "placeholders": {"name": "Enter product name", "description": "Enter product description", "note": "Additional notes about this product"}, "active": "Product is active", "inactive": "Product is inactive", "buttons": {"create": "Create Product", "update": "Update Product", "creating": "Creating...", "updating": "Updating..."}, "nextSteps": {"title": "Next steps:", "step1": "After creating the product, you can add ingredients", "step2": "Set usage amounts per cup for each ingredient", "step3": "Use the product in COGS calculations and production"}}, "ingredients": {"loadingDetails": "Loading product details...", "notFound": "Product not found", "title": "Ingredients", "productInfo": {"description": "Description", "noDescription": "No description", "totalCogsPerCup": "Total COGS per Cup"}, "form": {"ingredient": "Ingredient", "selectIngredient": "Select ingredient", "usagePerCup": "Usage per Cup", "noteOptional": "Note (optional)", "additionalNotes": "Additional notes", "notePlaceholder": "Note"}, "buttons": {"addIngredient": "Add Ingredient", "add": "Add"}, "messages": {"selectIngredient": "Please select an ingredient and enter usage amount", "invalidUsage": "Usage per cup must be greater than 0", "addSuccess": "Ingredient added successfully", "addFailure": "Failed to add ingredient", "updateSuccess": "Ingredient updated successfully", "updateFailure": "Failed to update ingredient", "removeSuccess": "Ingredient removed successfully", "removeFailure": "Failed to remove ingredient"}, "noIngredients": "No ingredients yet", "noIngredientsDescription": "Add ingredients to define this product's composition", "table": {"ingredient": "Ingredient", "usagePerCup": "Usage per Cup", "costPerCup": "Cost per Cup", "unitCost": "Unit Cost", "note": "Note", "actions": "Actions"}, "missingIngredient": "Missing Ingredient (ID: {{id}})", "removeDialog": {"title": "Remove Ingredient", "description": "Remove \"{{name}}\" from this product?", "remove": "Remove"}}, "cogs": {"title": "COGS Calculation", "loading": "Calculating COGS...", "noIngredients": "No ingredients added to this product yet.", "addIngredientsPrompt": "Add ingredients to see COGS calculation.", "titleFor": "COGS Calculation for {{name}}", "whatIs": {"title": "What is COGS?", "text": "COGS (Cost of Goods Sold) represents the total cost of ingredients needed to make one cup of this product. It's calculated by multiplying each ingredient's cost per unit by the amount used per cup."}, "totalCogsPerCup": "Total COGS per Cup", "ingredientCount": "{{count}} ingredient{{count !== 1 ? 's' : ''}}", "ingredientBreakdown": "Ingredient Breakdown", "table": {"ingredient": "Ingredient", "usagePerCup": "Usage per Cup", "costPerCup": "Cost per Cup", "percentOfTotal": "% of Total"}, "formula": {"title": "Calculation Formula:", "formula": "Cost per Cup = (Base Unit Cost ÷ Base Unit Quantity) × Usage per Cup", "example": "Example: If milk costs 20,000 per 1000ml and you use 100ml per cup, then cost per cup = (20,000 ÷ 1000) × 100 = 2,000"}}}, "ingredients": {"loading": "Loading ingredients...", "errorLoading": "Error loading ingredients: {{error}}", "title": "Ingredient Management", "description": "Manage your ingredients and their properties", "create": "Create Ingredient", "createNewTitle": "Create New Ingredient", "createNewDescription": "Add a new ingredient to your inventory", "tabs": {"overview": "Overview", "ingredients": "Ingredients"}, "overview": {"totalIngredients": "Total Ingredients", "totalIngredientsDesc": "Active ingredients", "categories": "Categories", "categoriesDesc": "Ingredient categories", "totalUsages": "Total Usages", "totalUsagesDesc": "Across all products", "avgUsage": "Avg. <PERSON>", "avgUsageDesc": "Per ingredient", "recentIngredients": "Recent Ingredients", "noIngredients": "No ingredients yet", "noIngredientsDesc": "Create your first ingredient to get started", "viewDetails": "View Details", "viewAll": "View All Ingredients ({{count}})", "noCategory": "No category", "usedInProducts": "Used in {{count}} products"}, "filters": {"title": "Ingredient Filters", "showInactive": "Show inactive ingredients"}, "list": {"title": "Ingredients ({{count}})", "searchPlaceholder": "Search ingredients...", "allCategories": "All Categories", "noIngredientsFound": "No ingredients found", "tryAdjustSearch": "Try adjusting your search terms or filters", "createFirst": "Create your first ingredient to get started", "table": {"name": "Name", "category": "Category", "unit": "Unit", "unitCost": "Unit Cost", "usage": "Usage", "status": "Status", "actions": "Actions"}, "badge": {"active": "Active", "inactive": "Inactive"}, "deleteTitle": "Delete Ingredient", "deleteDescription": "Are you sure you want to delete \"{{name}}\"?", "deleteInUse": "This ingredient is used in {{count}} products and cannot be deleted.", "editSheetTitle": "Edit Ingredient", "editSheetDescription": "Update ingredient information", "viewSheetTitle": "Ingredient Usage", "viewSheetDescription": "View which products use this ingredient"}, "categoryCombobox": {"searchPlaceholder": "Search categories...", "emptyText": "No categories found.", "deleteTitle": "Delete Category", "alertDescription": "Select a category to delete. You can only delete categories that are not in use.", "confirmTitle": "Delete Category \"{{name}}\"", "inUseMessage": "This category is used by {{count}} ingredient(s). You cannot delete a category that is in use.", "confirmMessage": "Are you sure you want to delete this category? This action cannot be undone."}, "unitCombobox": {"searchPlaceholder": "Search units...", "emptyText": "No unit found."}, "form": {"fields": {"name": "Ingredient Name *", "category": "Category *", "baseUnitCost": "Base Unit Cost *", "baseUnitQuantity": "Base Unit Quantity *", "unit": "Unit *", "supplierInfo": "Supplier Information", "note": "Notes", "activeStatus": "Active Status"}, "placeholders": {"name": "Enter ingredient name", "selectCategory": "Select or create category...", "baseUnitCost": "0.00", "baseUnitQuantity": "1.00", "selectUnit": "Select or enter unit...", "supplierInfo": "Supplier name, contact, etc.", "note": "Additional notes about this ingredient", "costBaseHelp": "Enter the cost per base unit (e.g., per gram, per ml)", "quantityHelp": "Enter the quantity in base units (e.g., grams, ml)"}, "costPreview": "Cost Preview:", "activeStatus": "Active Status", "activeIngredient": "Ingredient is active", "inactiveIngredient": "Ingredient is inactive", "buttons": {"create": "Create Ingredient", "update": "Update Ingredient", "creating": "Creating...", "updating": "Updating...", "cancel": "Cancel"}, "exampleTitle": "Example:", "exampleItem1": "• Milk: Base cost 20,000 for 1,000 ml = 20 per ml", "exampleItem2": "• Coffee beans: Base cost 200,000 for 1,000 g = 200 per g", "exampleItem3": "• Cups: Base cost 850 for 1 piece = 850 per piece"}, "validation": {"nameRequired": "Ingredient name is required", "categoryRequired": "Category is required", "baseUnitCostRequired": "Base unit cost is required", "baseUnitCostPositive": "Base unit cost must be positive", "baseUnitCostNumber": "Base unit cost must be a valid number", "baseUnitQuantityRequired": "Base unit quantity is required", "baseUnitQuantityPositive": "Base unit quantity must be positive", "baseUnitQuantityNumber": "Base unit quantity must be a valid number", "unitRequired": "Unit is required"}, "usage": {"loading": "Loading ingredient details...", "notFound": "Ingredient not found", "close": "Close", "category": "Category", "unit": "Unit", "baseUnitCost": "Base Unit Cost", "baseUnitQuantity": "Base Unit Quantity", "costPerUnit": "Cost per Unit", "supplier": "Supplier", "noSupplier": "No supplier info", "notes": "Notes", "productUsageTitle": "Product Usage ({{count}})", "notUsed": "Not used in any products", "notUsedDesc": "This ingredient is not currently used in any products", "table": {"product": "Product", "usagePerCup": "Usage per Cup", "costPerCup": "Cost per Cup", "note": "Note", "status": "Status"}, "badge": {"active": "Active", "inactive": "Inactive"}, "costAnalysis": "Cost Analysis", "totalProductsUsing": "Total Products Using", "avgUsagePerCup": "Average Usage per Cup", "avgCostPerCup": "Average Cost per Cup"}}, "menus": {"management": {"title": "Menu Management", "description": "Create and manage your coffee shop menus with products and pricing", "createMenu": "Create Menu", "tabs": {"menus": "Menus", "branches": "Branches"}, "menuFiltersView": "Menu Filters & View", "showInactive": "Show inactive menus", "noMenus": "No menus found", "noMenusHint": "Get started by creating your first menu", "branch": {"title": "Branch Management", "description": "Manage your coffee shop locations and their menu assignments", "create": "Create Branch", "filtersTitle": "Branch Filters & View", "showInactive": "Show inactive branches"}, "editMenuSheet": {"title": "<PERSON>", "description": "Update menu details and settings"}, "createMenuSheet": {"title": "Create New Menu", "description": "Add a new menu to your coffee shop catalog"}, "assignBranchesSheet": {"title": "Assign <PERSON><PERSON>", "description": "Select which branches this menu should be available at"}, "menuDetailsSheet": {"title": "<PERSON><PERSON>", "description": "Manage products and settings for this menu"}, "branchMenuSheet": {"title": "Branch Menu Management", "description": "Manage menu assignments for this branch"}, "createBranchSheet": {"title": "Create New Branch", "description": "Add a new branch location to your coffee shop network"}, "editBranchSheet": {"title": "Edit Branch", "description": "Update branch details and settings"}}, "branchAssignment": {"loading": "Loading branches...", "assigningTo": "Assigning branches to:", "selectBranches": "Select Branches *", "searchPlaceholder": "Search branches...", "noBranchesFound": "No branches found.", "help": "Select which branches this menu should be available at", "selectedBranches": "Selected Branches", "remove": "Remove", "assign": "Assign <PERSON><PERSON>", "cancel": "Cancel", "helpTitle": "Branch Assignment:", "helpItems": ["Menus can be assigned to multiple branches", "Each branch can have multiple menus", "Use this to control which menus are available at each location", "You can set different sales targets per branch later"], "assigning": "Assigning..."}, "branchForm": {"name": "Branch Name *", "location": "Location", "businessHours": "Business Hours", "startTime": "Start Time *", "endTime": "End Time *", "activeStatus": "Active Status", "note": "Note", "placeholders": {"name": "Enter branch name", "location": "Enter branch location or address", "note": "Add any additional notes or comments"}, "help": {"name": "A descriptive name for your branch (e.g., \"Main Location\")", "location": "Physical location or address of this branch", "businessHours": "Set the operating hours for this branch", "businessHoursInfo": "Business hours are used for calculating time-based progress in target analysis", "note": "Optional notes for internal reference"}, "activeHelp": "Branch is active and available for menu assignments", "inactiveHelp": "Branch is inactive and hidden from operations", "buttons": {"create": "Create Branch", "update": "Update Branch", "creating": "Creating...", "updating": "Updating...", "cancel": "Cancel"}, "helpTitle": "Branch Management:", "helpItems": ["Branches represent physical locations where you serve customers", "Each branch can have multiple menus assigned to it", "Use branches to manage location-specific offerings and targets", "Inactive branches are hidden but can be reactivated later"]}, "menuForm": {"fields": {"name": "Menu Name *", "description": "Description", "note": "Note", "activeStatus": "Active Status"}, "placeholders": {"name": "Enter menu name", "description": "Enter menu description", "note": "Add any additional notes or comments"}, "help": {"name": "A descriptive name for your menu (e.g., \"Morning Coffee Menu\", \"Afternoon Specials\")", "description": "Optional description of the menu and its purpose", "note": "Optional notes for internal reference"}, "statusHelp": {"active": "Menu is active and available for use", "inactive": "<PERSON><PERSON> is inactive and hidden from operations"}, "buttons": {"create": "Create Menu", "update": "Update Menu", "creating": "Creating...", "updating": "Updating...", "cancel": "Cancel"}, "nextSteps": {"title": "Next steps:", "step1": "After creating the menu, you can add products with pricing", "step2": "Assign the menu to specific branches/locations", "step3": "Set daily sales targets for each branch", "step4": "Use the menu in your daily operations"}}, "branchList": {"noBranches": "No branches yet", "noBranchesHint": "Create a branch to start assigning menus"}}, "salesTargets": {"errors": {"loadBranches": "Failed to load branches. Please refresh the page.", "loadTargets": "Failed to load sales targets. Please try again.", "updateTarget": "Failed to update target. Please try again.", "loadMonthlyTargets": "Failed to load monthly targets. Please try again."}, "dailyPage": {"title": "Daily Sales Targets", "description": "Set and manage daily sales targets for your menu products", "selectBranchPlaceholder": "Select branch", "summary": {"totalItems": "Total Items", "estRevenue": "Est. Revenue", "activeProducts": "Active Products", "activeMenus": "Active Menus"}, "noActiveMenusTitle": "No Active Menus", "noActiveMenusDescription": "No active menus found for the selected branch and date.", "selectBranchTitle": "Select a Branch", "selectBranchDescription": "Please select a branch to view and manage sales targets"}, "calendar": {"noBusiness": "Please select a business to view sales targets", "noBranch": "Please select a branch to view sales targets", "invalidBranch": "Selected branch is not available in the current business", "totalTargets": "Total Targets", "estRevenue": "Est. Revenue", "items": "{{count}} items", "noMenusForDate": "No active menus found for this date", "menusCount": "Menus ({{count}})", "menuSummary": "{{products}} products • {{total}} target items"}, "menuSection": {"productsBadge": "{{active}}/{{total}} products", "itemsBadge": "{{count}} items", "noProductsTitle": "No Products", "noProductsDescription": "This menu doesn't have any products assigned yet.", "table": {"product": "Product", "price": "Price", "targetQty": "Target Qty", "estRevenue": "Est. Revenue", "actions": "Actions"}, "summary": {"menuTotal": "Menu Total", "activeLabel": "{{count}} active"}}, "productRow": {"notePlaceholder": "Add a note (optional)...", "saveTooltip": "Save (Ctrl+Enter)", "cancelTooltip": "Cancel (Esc)", "editTooltip": "Edit target", "noteLabel": "Note: {{note}}", "targetMinError": "Target must be 0 or greater"}}, "operations": {"page": {"title": "Operations", "description": "Transform daily sales targets into operational pipeline with real-time tracking and analytics"}, "tabs": {"recording": "Sales Recording", "analysis": "Target Analysis", "analytics": "Analytics", "projections": "Projections"}, "salesRecording": {"filtersTitle": "Filters", "date": "Date", "branch": "Branch", "allBranches": "All branches", "summary": {"totalSales": "Total Sales", "totalRevenue": "Total Revenue", "avgOrderValue": "Avg Order Value", "topProduct": "Top Product", "none": "None", "sold": "{{count}} sold"}, "recordsTitle": "Sales Records", "recordsDescription": "Recent sales for {{date}} at {{branch}}", "loading": "Loading sales records...", "noRecords": "No sales records found for this date", "addButton": "Add sales", "sheetTitle": "Record New Sale", "sheetDescription": "Record a new sale with product details, quantity, and pricing information."}, "salesRecordForm": {"fields": {"branch": "Branch", "menu": "<PERSON><PERSON>", "product": "Product", "saleDate": "Sale Date", "saleTime": "Sale Time", "quantity": "Quantity", "unitPrice": "Unit Price ({{currency}})", "totalAmount": "Total Amount", "note": "Note (Optional)"}, "placeholders": {"branch": "Select a branch", "menu": "Select a menu", "product": "Select a product", "pickDate": "Pick a date", "note": "Additional notes about this sale..."}, "buttons": {"setCurrentTime": "Set current time", "record": "Record Sale"}}, "targetAnalysis": {"title": "Target vs Actual Analysis", "description": "Real-time comparison between daily sales targets and actual performance", "date": "Date", "branch": "Branch", "allBranches": "All branches", "loading": "Loading analysis...", "noTargets": "No targets found for this date", "progress": "Progress", "expected": "Expected: {{value}}%", "timeRemaining": "Time remaining: {{value}}", "target": "Target", "actual": "Actual", "variance": "<PERSON><PERSON><PERSON>", "salesCount": "Sales Count: {{count}} items", "unknownMenu": "Unknown Menu", "unknownBranch": "Unknown Branch"}, "analytics": {"filtersTitle": "Analytics Filters", "filtersDescription": "Filter analytics data by time range and branch", "timeRange": "Time Range", "range": {"today": "Today", "week": "Last 7 Days", "month": "Last 30 Days", "custom": "Custom Range"}, "startDate": "Start Date", "endDate": "End Date", "branch": "Branch", "allBranches": "All branches", "tabs": {"hourly": "Hourly Analysis", "products": "Product Popularity", "progress": "Daily Progress"}, "hourly": {"title": "Hourly Profitability Analysis", "description": "Revenue and profit breakdown by hour of day"}, "products": {"title": "Product Popularity", "description": "Top-selling products by quantity and revenue", "byQuantity": "By Quantity", "byRevenue": "By Revenue", "noData": "No product data available"}, "progress": {"title": "Daily Progress Tracking", "description": "Real-time progress towards daily sales targets", "requireSingleDate": "Progress tracking requires a single date selection", "noTargets": "No sales targets found for this date", "selectSpecificDate": "Please select a specific date to view daily progress", "createTargets": "Create sales targets for {{date}} to track progress"}, "loadingChart": "Loading chart..."}, "projections": {"title": "Enhanced Income Projections", "description": "Multi-product projections based on actual sales performance and targets", "branch": "Branch", "selectBranch": "Select a branch", "menu": "<PERSON><PERSON>", "selectMenu": "Select a menu", "referenceDate": "Reference Date", "daysPerMonth": "Days Per Month", "useActual": "Use Actual Sales Data", "usingActual": "Using actual sales", "usingTargets": "Using targets only", "loading": "Loading projections...", "selectBranchMenu": "Please select a branch and menu to view projections", "noProducts": "No products found for the selected menu", "summary": {"dailyRevenue": "Daily Revenue", "dailyProfit": "Daily Profit", "monthlyRevenue": "Monthly Revenue", "avgPerformance": "Avg Performance"}, "tableTitle": "Product Projections", "tableDescription": "Individual product performance and revenue projections", "table": {"product": "Product", "targetQty": "Target Qty", "actualSales": "Actual Sales", "performance": "Performance", "unitPrice": "Unit Price", "dailyRevenue": "Daily Revenue", "dailyProfit": "Daily Profit", "monthlyRevenue": "Monthly Revenue", "monthlyProfit": "Monthly Profit"}}}, "accounting": {"quickActions": {"title": "Quick Actions", "description": "Common accounting tasks and financial management tools", "badges": {"income": "Income", "expense": "Expense", "capital": "Capital", "asset": "<PERSON><PERSON>"}, "actions": {"recordSale": {"title": "Record Sale", "description": "Add sales income transaction"}, "addExpense": {"title": "Add Expense", "description": "Record operating expense"}, "capitalInvestment": {"title": "Capital Investment", "description": "Record capital injection"}, "assetPurchase": {"title": "Asset Purchase", "description": "Add fixed asset"}, "inventory": {"title": "Inventory", "description": "Manage stock & purchases"}, "cogsCalculator": {"title": "COGS Calculator", "description": "Calculate product costs"}, "reports": {"title": "Financial Reports", "description": "View detailed reports"}}, "sheet": {"title": "Add Transaction", "description": "Record a new financial transaction"}, "gettingStarted": {"title": "Getting Started", "items": ["Record sales and income transactions", "Add operating and recurring expenses", "Track capital investments and assets", "Monitor financial health and cash flow"]}}, "dashboard": {"title": "Accounting", "tagline": "Comprehensive financial transaction management", "selectBusiness": "Please select a business to view accounting information.", "description": "Financial management for {{business}}", "tryAgain": "Try Again", "filters": "Filters", "refresh": "Refresh", "export": "Export", "updated": "Updated: {{time}}", "totalTransactions": "Total Transactions", "allTypes": "All transaction types", "salesIncome": "Sales Income", "salesRevenue": "Revenue from sales", "operatingExpenses": "Operating Expenses", "operationalCosts": "Operational costs", "netPosition": "Net Position", "incomeMinusExpenses": "Income minus expenses", "recentTransactions": "Recent Transactions", "allTransactions": "All financial transactions across your business", "filtered": "Filtered", "clear": "Clear"}, "summary": {"financialHealth": "Financial Health:", "healthy": "Healthy", "needsAttention": "Needs Attention", "profitMargin": "<PERSON><PERSON>", "totalIncome": "Total Income", "sales": "Sales", "capital": "Capital", "totalExpenses": "Total Expenses", "operating": "Operating", "fixed": "Fixed", "variable": "Variable", "netIncome": "Net Income", "grossProfit": "Gross Profit", "operatingProfit": "Operating Profit", "cashFlow": "Cash Flow", "burnRate": "Burn Rate", "margin": "<PERSON><PERSON>", "costStructure": "Cost Structure", "variableCosts": "Variable Costs", "fixedCosts": "Fixed Costs", "operatingCosts": "Operating", "revenueMix": "Revenue Mix", "salesRevenue": "Sales Revenue", "capitalInvestment": "Capital Investment", "performance": "Performance", "status": "Status"}, "transactions": {"searchPlaceholder": "Search transactions...", "date": "Date", "type": "Type", "description": "Description", "category": "Category", "amount": "Amount", "noMatch": "No transactions match your search.", "none": "No transactions found.", "viewDetails": "View Details", "edit": "Edit", "delete": "Delete", "showing": "Showing {{count}} of {{total}} transactions{{search}}"}, "filters": {"title": "Filter Transactions", "clearAll": "Clear All", "search": "Search", "searchPlaceholder": "Search descriptions, categories, notes...", "startDate": "Start Date", "endDate": "End Date", "pickStart": "Pick start date", "pickEnd": "Pick end date", "minAmount": "Min Amount ({{currency}})", "maxAmount": "Max Amount ({{currency}})", "types": "Transaction Types", "status": "Status", "categories": "Categories", "clear": "Clear", "apply": "Apply Filters", "active": "Active Filters", "searchLabel": "Search: \"{{term}}\"", "dateRange": "Date Range", "amountRange": "Amount Range"}, "health": {"title": "Financial Health", "loading": "Loading health indicators...", "noData": "No data available for health analysis", "score": "Financial Health Score", "overallAssessment": "Overall business financial health assessment", "keyMetrics": "Key Metrics", "quickStats": "Quick Stats", "profitability": "Profitability", "liquidity": "Liquidity", "efficiency": "Efficiency", "growth": "Growth", "profitMargin": "<PERSON><PERSON>", "burnRate": "Monthly Burn Rate", "cashFlow": "Cash Flow", "netIncome": "Net Income", "alertsTitle": "Health Alerts & Recommendations", "badges": {"excellent": "Excellent", "good": "Good", "fair": "Fair", "poor": "Poor"}, "noDataPrompt": "Record some transactions to see your business health indicators.", "alerts": {"negativeProfitMargin": {"title": "Negative Profit <PERSON>", "message": "Your business is operating at a {{value}}% loss.", "recommendation": "Review expenses and consider increasing prices or reducing costs."}, "lowProfitMargin": {"title": "Low Profit <PERSON>gin", "message": "Profit margin of {{value}}% is below healthy levels.", "recommendation": "Aim for at least 10-15% profit margin for sustainable growth."}, "excellentProfitability": {"title": "Excellent Profitability", "message": "Strong profit margin of {{value}}%.", "recommendation": "Consider reinvesting profits for growth opportunities."}, "negativeCashFlow": {"title": "Negative Cash Flow", "message": "Cash outflow of {{value}}.", "recommendation": "Focus on increasing income and managing expenses."}, "lowCashReserves": {"title": "Low Cash Reserves", "message": "Cash flow covers less than 3 months of expenses.", "recommendation": "Build cash reserves for financial stability."}, "highVariableCosts": {"title": "High Variable Costs", "message": "Variable costs are {{value}}% of income.", "recommendation": "Review supplier costs and operational efficiency."}, "highFixedCosts": {"title": "High Fixed Costs", "message": "Fixed costs are {{value}}% of income.", "recommendation": "Consider reducing fixed expenses or increasing revenue."}, "growthOpportunity": {"title": "Growth Opportunity", "message": "Strong financial position enables expansion.", "recommendation": "Consider investing in marketing, inventory, or new locations."}}, "sheets": {"healthScore": {"title": "Financial Health Score Explained", "description": "Understanding your overall business health scoring methodology", "cards": {"whatIs": "What is Financial Health Score?", "scoreRanges": "Score Ranges & Meanings", "scoringFactors": "Scoring Factors", "currentScore": "Your Current Score", "componentScores": "Component Scores", "improving": "Improving Your Score"}}, "keyMetrics": {"title": "Key Metrics Explained", "description": "Understanding the four pillars of financial health assessment", "cards": {"whatAre": "What are Key Metrics?", "profitability": "1. Profitability", "liquidity": "2. Liquidity", "efficiency": "3. Efficiency", "growth": "4. Growth", "improving": "Improving Key Metrics"}}, "quickStats": {"title": "Quick Stats Explained", "description": "Understanding key performance indicators at a glance", "cards": {"whatAre": "What are Quick Stats?", "profitMargin": "1. Profit <PERSON> %", "burnRate": "2. Monthly Burn Rate", "cashFlow": "3. Cash Flow", "netIncome": "4. Net Income", "benchmarks": "Coffee Shop Benchmarks", "using": "Using Quick Stats for Decisions"}}}}, "transactionForm": {"fields": {"transactionType": "Transaction Type", "category": "Category", "amount": "Amount ({{currency}})", "description": "Description", "date": "Date", "quantity": "Quantity", "unitPrice": "Unit Price ({{currency}})", "frequency": "Frequency", "usefulLife": "Estimated Useful Life (Years)", "unit": "Unit", "baseUnitCost": "Base Unit Cost ({{currency}})", "usagePerCup": "Usage Per Cup", "note": "Note (Optional)"}, "placeholders": {"transactionType": "Select transaction type", "category": "Select category", "description": "Transaction description", "date": "Pick a date", "frequency": "Select frequency", "unit": "e.g., ml, g, piece", "note": "Additional notes about this transaction"}, "descriptions": {"unitPrice": "Price per unit sold", "usefulLife": "Used for depreciation calculations", "baseUnitCost": "Cost per base unit (e.g., per liter, per kg)", "usagePerCup": "Amount used per cup/serving"}, "frequency": {"monthly": "Monthly", "yearly": "Yearly"}, "other": "Other", "buttons": {"create": "Create Transaction", "update": "Update Transaction"}}}, "people": {"page": {"title": "People Management", "description": "Manage employees and point-of-contact assignments", "noBusiness": {"title": "No Business Selected", "description": "Please select a business to manage employees."}}, "employees": {"title": "Employees", "description": "Manage employee information and details", "addButton": "Add Employee", "editTitle": "Edit Employee", "createTitle": "Add New Employee", "editDescription": "Update employee information and details", "createDescription": "Add a new employee with complete information", "listTitle": "Employee List", "listDescription": "All employees in your organization", "loading": "Loading employees...", "empty": "No employees found. Add your first employee to get started.", "table": {"employee": "Employee", "position": "Position", "department": "Department", "status": "Status", "hireDate": "Hire Date", "salary": "Salary", "actions": "Actions"}, "badges": {"active": "Active", "inactive": "Inactive", "terminated": "Terminated"}, "notSpecified": "Not specified", "deleting": "Deleting..."}, "toast": {"loadError": "Failed to load employees", "createSuccess": "Employee created successfully", "createError": "Failed to create employee", "updateSuccess": "Employee updated successfully", "updateError": "Failed to update employee", "deleteSuccess": "Employee deleted successfully", "deleteError": "Failed to delete employee"}, "poc": {"sectionTitle": "Point of Contact Management", "description": "Assign employees as points of contact for specific branches", "assignButton": "Assign POC", "sheetTitle": "Assign Point of Contact", "sheetDescription": "Assign an employee as a point of contact for a branch", "managerTitle": "Current POC Assignments", "managerDescription": "Manage point of contact assignments for branches", "noAssignments": "No POC assignments found. Assign employees as points of contact for branches.", "table": {"employee": "Employee", "position": "Position", "branch": "Branch", "date": "Assigned Date", "notes": "Notes", "actions": "Actions"}, "noNotes": "No notes", "removing": "Removing...", "loading": "Loading POC assignments..."}, "forms": {"employee": {"sections": {"personal": "Personal Information", "professional": "Professional Information", "contact": "Contact Information"}, "fields": {"fullName": "Full Name *", "companyIdNumber": "Company ID Number *", "nationalIdNumber": "National ID Number", "dateOfBirth": "Date of Birth", "position": "Position/Job Title *", "department": "Department *", "jobLevel": "Job Level", "salary": "Salary ({{currency}})", "hireDate": "Hire Date *", "employmentStatus": "Employment Status", "phone": "Phone Number", "email": "Email Address", "note": "Additional Notes"}, "placeholders": {"fullName": "Enter full name", "companyIdNumber": "e.g., EMP001", "nationalIdNumber": "e.g., 1234567890123456", "position": "e.g., <PERSON><PERSON>, Manager", "department": "e.g., Operations, Management", "jobLevel": "e.g., Junior, Senior, Manager", "salary": "e.g., 5000000", "phone": "e.g., +62812345678", "email": "e.g., <EMAIL>", "note": "Any additional information about the employee..."}, "pickDate": "Pick a date", "buttons": {"cancel": "Cancel", "create": "Create Employee", "update": "Update Employee", "saving": "Saving..."}}, "poc": {"title": "Assign Point of Contact", "fields": {"employee": "Employee *", "branch": "Branch *", "assignedDate": "Assignment Date *", "note": "Notes"}, "placeholders": {"searchEmployees": "Search employees...", "searchBranches": "Search branches...", "notes": "Any additional notes about this assignment..."}, "selectEmployee": "Select employee", "selectBranch": "Select branch", "pickDate": "Pick a date", "noEmployee": "No employee found.", "noBranch": "No branch found.", "loading": "Loading employees and branches...", "buttons": {"assign": "Assign POC", "assigning": "Assigning...", "cancel": "Cancel"}}}}, "analytics": {"page": {"title": "<PERSON>u Analytics", "description": "Menu-based income projections and profit analysis organized by menu structure"}, "controls": {"daysPerMonth": "Days per Month"}, "loading": "Loading analytics...", "table": {"title": "Menu-Based Income Projections & Profits", "info": "{{menus}} menus • {{products}} products • Individual targets", "loadingTargets": "Loading targets...", "refresh": "Refresh", "noDataTitle": "No menu data found", "noDataDescription": "Make sure you have:\n• Active menus with products\n• Products with ingredients and COGS data\n• Product pricing set in menus", "headers": {"menuProduct": "Menu / Product & Target", "cogs": "COGS/Unit", "price": "<PERSON><PERSON>", "dailyRevenue": "Daily Revenue", "weeklyRevenue": "Weekly Revenue", "monthlyRevenue": "Monthly Revenue", "dailyProfit": "Daily Profit", "weeklyProfit": "Weekly Profit", "monthlyProfit": "Monthly Profit"}, "targetPerDay": "Target/day:", "units": "units"}, "explanation": {"defaultTitle": "<PERSON>u Analytics", "clickPrompt": "Click on a product row to see detailed calculations and analysis.", "understandingTitle": "Understanding Menu-Based Analytics", "understanding": {"menuStructure": "Menu Structure: Analytics organized by menu with products underneath", "menuPrice": "Menu Price: Actual price set for this product in this specific menu", "cogs": "COGS/Unit: Cost of Goods Sold per unit (ingredients cost)", "revenue": "Revenue: Total income from sales (Menu Price × Quantity)", "profit": "Profit: Revenue minus COGS (excludes fixed costs)"}, "benefitsTitle": "Menu-<PERSON><PERSON><PERSON>", "benefits": {"seePerformance": "• See how the same product performs in different menus", "comparePricing": "• Compare pricing strategies across menus", "identifyProfitable": "• Identify which menus are most profitable", "optimizePlacement": "• Optimize product placement and pricing"}, "menuContext": "<PERSON><PERSON> Context", "unitEconomics": "Unit Economics", "dailyProjections": "Daily Projections", "monthlyProjections": "Monthly Projections", "calculationFormula": "Calculation Formula", "formula": {"revenue": "Revenue = Menu Price × Quantity × Days", "profit": "Profit = (Menu Price - COGS) × Quantity × Days", "margin": "Margin = (Profit ÷ Revenue) × 100%"}, "performanceInsights": "Performance Insights", "insights": {"excellent": "✓ Excellent profit margin in this menu", "good": "✓ Good profit margin in this menu", "moderate": "⚠ Moderate profit margin in this menu", "low": "⚠ Low profit margin in this menu", "negative": "⚠ Negative margin - review pricing for this menu"}, "compareHint": "💡 Compare this product's performance across different menus to optimize pricing strategy", "inMenu": "in {{menuName}}", "daysPerMonth": "Days per Month:", "totalQuantity": "Total Quantity:", "targetQuantity": "Target Quantity:", "revenueLabel": "Revenue:", "profitLabel": "Profit:", "profitMargin": "Profit <PERSON>:"}}, "warehouse": {"sheet": {"triggerAria": "Add items to warehouse", "title": "Add to Warehouse", "description": "Add ingredient items to warehouse stock by selecting products and their ingredients"}, "form": {"header": "Add Items to Warehouse", "productQuantity": "Product & Quantity", "fields": {"product": "Product", "numberOfCups": "Number of Cups to Prepare", "smartStock": "Smart Stock Calculation", "batchNote": "Batch Note (Optional)"}, "placeholders": {"product": "Choose a product to calculate ingredients for...", "numberOfCups": "Enter number of cups...", "batchNote": "Add a note for this warehouse batch..."}, "autoCalcHint": "Ingredient quantities will be auto-calculated based on the product recipe", "smartStockHint": "Only add the deficit amount needed to reach target quantities based on current stock levels", "calculated": {"titleSmart": "Smart Stock Calculation", "titleDefault": "Calculated Ingredients", "forCups": "for {{cups}} cups of {{product}}", "explanationSmart": "Only deficit amounts needed to reach target quantities will be added.", "explanation": "Quantities and costs are automatically calculated based on the product recipe.", "roundedInfo": "Actual quantities are rounded up to minimum purchase units based on ingredient packaging."}, "table": {"ingredient": "Ingredient", "currentStock": "Current Stock", "usagePerCup": "Usage per Cup", "requiredQuantity": "Required Quantity", "theoreticalNeed": "Theoretical Need", "actualQuantityToAdd": "Actual Quantity to Add", "purchaseInfo": "Purchase Info", "costPerUnit": "Cost per Unit", "totalCost": "Total Cost", "sufficient": "Sufficient", "noPurchaseNeeded": "No purchase needed", "roundedUpFrom": "Rounded up from {{quantity}} {{unit}}"}, "totalCost": {"label": "Total Batch Cost:", "costForDeficit": "Cost for deficit amounts to reach {{cups}} cups of {{product}}", "costFor": "Cost for {{cups}} cups of {{product}}", "sufficient": "Current stock levels are sufficient for the selected quantity"}, "buttons": {"submitAdding": "Adding to Warehouse...", "submitNoItems": "No Items Need to be Added", "submitAdd": "Add {{count}} Ingredients to Warehouse"}, "messages": {"failedLoadProducts": "Failed to load products", "failedLoadIngredients": "Failed to load product ingredients", "noIngredients": "No ingredients found for the selected product", "stockSufficient": "Current stock levels are sufficient for the selected quantity. No items need to be added.", "successAdd": "Successfully added {{count}} ingredients to warehouse batch #{{batch}} for {{cups}} cups of {{product}}", "failedAdd": "Failed to add items to warehouse"}}, "stock": {"loading": "Loading stock levels...", "error": "Error loading stock levels", "title": "Current Stock Levels", "description": "Real-time inventory levels and low stock thresholds", "noData": {"title": "No Stock Data", "description": "Stock levels will appear here after you add items to the warehouse."}, "alerts": "Low Stock Alerts ({{count}})", "alert": {"remaining": "{{count}} {{unit}} remaining", "threshold": "Threshold: {{threshold}} {{unit}}"}, "reservation": {"negative": "Reservation quantity cannot be negative", "updated": "Reservation updated successfully. Available stock: {{stock}}", "failed": "Failed to update reservation", "unexpected": "An unexpected error occurred while updating reservation", "reserved": "Reserved {{quantity}} {{unit}} successfully. Available stock: {{stock}}", "reserveFailed": "Failed to reserve stock", "reserveUnexpected": "An unexpected error occurred while reserving stock", "unreserved": "Unreserved {{quantity}} {{unit}} successfully. Available stock: {{stock}}", "unreserveFailed": "Failed to unreserve stock", "unreserveUnexpected": "An unexpected error occurred while unreserving stock"}, "table": {"ingredient": "Ingredient", "currentStock": "Current Stock", "reserved": "Reserved", "available": "Available", "status": "Status", "lowStockAlert": "Low Stock Alert", "actions": "Actions", "low": "Low Stock", "good": "Good", "moderate": "Moderate", "editReservation": "Edit reservation", "editThreshold": "Edit low stock threshold", "releaseReservations": "Release all reservations", "quickReserve": "Quick reserve {{quantity}} {{unit}}"}, "summary": {"totalIngredients": "Total Ingredients", "lowStockItems": "Low Stock Items", "wellStocked": "Well Stocked"}}, "batchList": {"noBatches": {"title": "No Warehouse Batches", "description": "You haven't added any items to the warehouse yet.", "hint": "Use the COGS Calculator to create your first warehouse batch."}, "title": "Warehouse Batches ({{count}})", "searchPlaceholder": "Search batches, ingredients, or notes...", "batchItems": "Batch Items", "table": {"ingredient": "Ingredient", "quantity": "Quantity", "unitCost": "Unit Cost", "totalCost": "Total Cost"}, "items": "{{count}} items", "ingredients": "{{count}} ingredients", "noResults": {"title": "No Results Found", "description": "No batches match your search term \"{{term}}\"", "clear": "Clear Search"}}, "calendar": {"title": "Warehouse Calendar", "batchesFor": "Batches for {{date}}", "noActivity": {"title": "No Warehouse Activity", "description": "No warehouse batches have been created yet. Use the COGS Calculator to add your first batch."}, "days": {"sun": "Sun", "mon": "Mon", "tue": "<PERSON><PERSON>", "wed": "Wed", "thu": "<PERSON>hu", "fri": "<PERSON><PERSON>", "sat": "Sat"}, "batchCount": "{{count}} batch{{plural}}"}, "management": {"title": "Warehouse Management", "description": "Manage your inventory and track stock additions", "tabs": {"overview": "Overview", "stock": "Stock Levels", "production": "Quick Production Allocation", "batches": "Batches", "calendar": "Calendar"}, "overview": {"recentBatches": "Recent Batches", "noBatches": "No warehouse batches yet", "addFirstBatch": "Use the COGS Calculator to add your first batch", "inventorySummary": "Inventory Summary", "noInventory": "No inventory data available", "mostStockedIngredients": "Most Stocked Ingredients", "viewAllBatches": "View All Batches ({{count}})"}}, "stats": {"totalBatches": "Total Batches", "totalItems": "Total Items", "totalValue": "Total Value", "latestBatch": "Latest Batch", "none": "None"}}, "production": {"allocation": {"loading": "Loading production allocation...", "title": "Quick Production Allocation", "description": "Allocate ingredients for production batches and reserve stock", "fields": {"product": "Product to Produce", "cups": "Number of Cups to Produce", "note": "Production Note (Optional)"}, "placeholders": {"selectProduct": "Select product", "cups": "Enter number of cups to produce", "note": "Add a note about this production batch..."}, "legacy": "Legacy COGS Items", "productSuffix": " of {{name}}", "selectedProductInfo": "{{count}} ingredients configured", "hints": {"maxPossible": "Maximum possible with current stock: {{count}} cups", "noProduction": "No production possible - insufficient stock for all ingredients"}, "ingredientsTitle": "Ingredients to be allocated:", "stockSummary": {"insufficient": "Insufficient stock for {{cups}} cups", "useMaximum": "Use maximum", "addStock": "Add more stock to warehouse before creating production batch."}, "buttons": {"creating": "Creating Production Batch...", "insufficient": "Insufficient Stock for Production", "allocate": "Allocate for {{count}} Cups{{product}}"}, "messages": {"invalidCups": "Please enter a valid number of cups to allocate", "noValidIngredients": "No valid ingredients found for production allocation", "insufficientStock": "Insufficient stock for production: {{details}}{{maxCupsMessage}}", "success": "Successfully created Production Batch #{{batch}} for {{cups}} cups of {{product}}. Ingredients allocated from stock.", "failed": "Failed to create production batch: {{error}}", "unexpected": "An unexpected error occurred while creating the production batch", "noteDefault": "Production allocation for {{cups}} cups of {{product}}"}, "warningNoIngredients": "No valid ingredients found. Please add ingredients with usage per cup data in the COGS Calculator.", "info": {"howTitle": "How it works:", "step1": "Enter the number of cups to produce", "step2": "System validates stock availability for all ingredients", "step3": "Calculates ingredient requirements based on COGS data", "step4": "Creates production batch and reserves stock (if sufficient)", "step5": "Stock shows as \"Reserved\" until production is completed", "step6": "Manage production batches in the Production page", "stockTitle": "Stock validation:", "stockStep1": "Red highlighting indicates insufficient stock", "stockStep2": "System suggests maximum possible production quantity", "stockStep3": "<PERSON><PERSON> disabled when stock is insufficient"}}, "batchStatus": {"loading": "Loading production batches...", "error": "Error loading production batches: {{error}}", "title": "Production Batch Status", "description": "Manage active production batches and update their status", "noActive": "No active production batches. Create one using Quick Production Allocation.", "complete": "Complete", "completeTitle": "Complete Production Batch", "completeConfirm": "Are you sure you want to mark <PERSON><PERSON> #{{batch}} as completed? This will permanently consume the allocated ingredients from stock.", "completeBatch": "Complete Batch", "table": {"batch": "<PERSON><PERSON>", "status": "Status", "items": "Items", "created": "Created", "actions": "Actions"}, "showing": {"partial": "Showing {{shown}} of {{total}} active batches.", "all": "Showing {{shown}} active batch{{plural}}."}, "viewAll": "View all {{count}} batch{{plural}} in Production page."}, "management": {"title": "Production Management", "description": "Manage production batches and track ingredient allocations", "tabs": {"overview": "Overview", "batches": "Production Batches", "reservations": "Reserved Operations"}, "quickActions": {"title": "Quick Actions", "description": "Common production management tasks", "viewAllBatches": "View All Production Batches", "manageReservations": "Manage Stock Reservations"}, "stats": {"totalBatches": "Total Batches", "totalBatchesDesc": "Production batches created", "pending": "Pending", "pendingDesc": "Awaiting production start", "inProgress": "In Progress", "inProgressDesc": "Currently in production", "completed": "Completed", "completedDesc": "Production finished", "latestBatch": "Latest Production Batch", "batchNumber": "Batch Number:", "status": "Status:", "created": "Created:", "note": "Note:"}}, "batchList": {"filter": "Filter by Status:", "allStatuses": "All Statuses", "showing": "Showing {{shown}} of {{total}} batches", "noBatches": {"all": "No production batches found. Create your first batch using Quick Production Allocation.", "status": "No {{status}} batches found."}, "table": {"batch": "Batch #", "status": "Status", "created": "Created", "items": "Items", "note": "Note", "actions": "Actions"}, "viewDetails": "View and manage production batch details", "batchNote": "Batch Note", "updateNote": "Update the production batch note", "save": "Save Changes", "cancel": "Cancel", "deleteTitle": "Delete Production Batch", "deleteConfirm": "Are you sure you want to delete Production Batch #{{batch}}? {{releaseStock}} This action cannot be undone.", "releaseStock": "This will release all reserved stock.", "delete": "Delete Batch", "noNote": "No note", "allocated": "Allocated Ingredients:"}, "reservations": {"stats": {"total": "Total Reservations", "manual": "Manual Reservations", "production": "Production Reservations"}, "title": "Reserved Operations", "description": "Manage all stock reservations and their purposes", "create": "Create Reservation", "createTitle": "Create Manual Reservation", "createDescription": "Reserve stock for manual purposes or future orders", "selectStock": "Select Ingredient from Stock", "chooseStock": "Choose an ingredient from available stock", "noStockItems": "No stock items available for reservation", "selected": "Selected: {{ingredient}}", "available": "Available for reservation:", "unit": "Unit", "quantity": "Quantity to Reserve", "reason": "Reason (Optional)", "reasonPlaceholder": "Reason for this reservation...", "creating": "Creating...", "selectFirst": "Select Ingredient First", "invalidQuantity": "Invalid Quantity", "invalidQuantityButton": "Invalid Quantity", "validQuantity": "✓ Valid reservation quantity", "createAction": "Create Reservation", "noStockWarning": "No stock available for reservation. Add items to warehouse first.", "filter": "Filter by Purpose:", "allPurposes": "All Purposes", "manual": "Manual Reservations", "productionPurpose": "Production Batches", "showing": "Showing {{shown}} of {{total}} reservations", "noReservations": {"all": "No stock reservations found.", "purpose": "No {{purpose}} reservations found."}, "table": {"ingredient": "Ingredient", "quantity": "Quantity", "purpose": "Purpose", "date": "Reserved Date", "actions": "Actions"}, "releaseTitle": "Release Reservation", "releaseConfirm": "Are you sure you want to release the reservation of {{quantity}} {{unit}} of {{ingredient}}? This will make the stock available again.", "release": "Release Reservation"}}, "fixedAssets": {"title": "Fixed Assets", "description": "Manage fixed assets and automatic depreciation calculations.", "assetInventory": "Asset Inventory", "sheet": {"addTitle": "Add New Fixed Asset", "editTitle": "Edit Fixed Asset", "addDescription": "Add a new fixed asset with automatic depreciation tracking.", "editDescription": "Update the asset information and depreciation settings."}, "summary": {"totalAssets": "Total Assets", "assetsDesc": "Fixed assets in inventory", "totalPurchaseCost": "Total Purchase Cost", "purchaseCostDesc": "Original investment value", "currentValue": "Current Value", "currentValueDesc": "After depreciation calculation", "totalDepreciation": "Total Depreciation", "totalDepreciationDesc": "{{rate}}% of original value", "explanationTitle": "Understanding Fixed Assets & Depreciation", "explanation": {"p1": "<strong>Fixed Assets</strong> are long-term items your business owns, like equipment, furniture, or technology.", "p2": "<strong>Depreciation</strong> spreads the cost of these assets over their useful life. For example, a ₹60,000 coffee machine used for 5 years costs ₹12,000 per year (₹1,000 per month) in depreciation.", "p3": "<strong>Current Value</strong> shows what the asset is worth today after accounting for wear and age.", "p4": "This system automatically creates monthly depreciation entries in your Fixed Costs to accurately reflect your business expenses."}}, "table": {"emptyTitle": "No fixed assets found.", "emptyDescription": "Click the + button to add your first asset.", "columns": {"name": "Asset Name", "category": "Category", "purchaseDate": "Purchase Date", "purchaseCost": "Purchase Cost", "currentValue": "Current Value", "depreciation": "Depreciation Period", "status": "Status", "actions": "Actions"}, "status": {"fully": "Fully Depreciated", "mostly": "Mostly Depreciated", "half": "Half Depreciated", "recent": "Recently Purchased", "percent": "{{percent}}% depreciated"}, "menu": "Open menu", "edit": "Edit", "delete": "Delete", "deleteTitle": "Delete Asset", "deleteConfirm": "Are you sure you want to delete \"{{name}}\"? This will also remove the corresponding depreciation entry from Fixed Costs. This action cannot be undone.", "deleting": "Deleting..."}, "form": {"fields": {"name": "Asset Name", "category": "Category", "purchaseDate": "Purchase Date", "purchaseCost": "Purchase Cost ({{currency}})", "depreciation": "Depreciation Period", "customDuration": "Custom Duration (Months)", "note": "Note (Optional)"}, "placeholders": {"name": "e.g., Coffee Machine, POS System", "selectCategory": "Select or create category...", "pickDate": "Pick a date", "purchaseCost": "0", "customDuration": "36", "note": "Additional information about this asset..."}, "preview": {"title": "Depreciation Preview", "currentValue": "Current Value", "totalDepreciation": "Total Depreciation", "monthlyDepreciation": "Monthly Depreciation", "remainingMonths": "Remaining Months"}, "buttons": {"create": "Create Asset", "update": "Update Asset", "saving": "Saving...", "cancel": "Cancel"}}, "categoryCombobox": {"searchPlaceholder": "Search categories...", "emptyText": "No categories found.", "manage": "Manage Categories", "deleteTitle": "Delete Category", "alertDescription": "Select a category to delete. You can only delete categories that are not in use.", "confirmTitle": "Delete Category \"{{name}}\"", "inUseMessage": "Cannot delete \"{{name}}\". It is currently used by {{count}} asset(s). Please reassign or delete those assets first.", "confirmMessage": "Are you sure you want to delete \"{{name}}\"? This action cannot be undone.", "deleting": "Deleting...", "creating": "Creating category..."}}, "learn": {"page": {"title": "Learning Hub", "description": "Master financial concepts and business management skills for your coffee shop"}, "searchPlaceholder": "Search learning topics...", "modules": {"accounting": {"title": "Accounting & Financial Management", "description": "Learn essential accounting concepts for your coffee shop business"}, "operations": {"title": "Operations & Sales Management", "description": "Understanding sales targets, recording, and operational metrics"}, "warehouse": {"title": "Inventory & Warehouse Management", "description": "Stock management, inventory tracking, and warehouse operations"}, "analytics": {"title": "Business Analytics & Reporting", "description": "Understanding financial reports, metrics, and business intelligence"}}, "available": "Available", "comingSoon": "Coming Soon", "startLearning": "Start Learning", "viewSection": "View {{section}} Section →", "quickLinks": {"title": "Ready to apply what you've learned?", "goToAccounting": "Go to Accounting", "cogsCalculator": "COGS Calculator", "viewAnalytics": "View Analytics", "financialDashboard": "Financial Dashboard"}, "comingSoonMessage": {"title": "Learning Module Coming Soon", "description": "We're working on comprehensive learning materials for this module."}, "tabs": {"basics": "Basics", "transactions": "Transactions", "financialHealth": "Financial Health", "costManagement": "Cost Management"}, "section": {"definition": "Definition", "example": "Coffee Shop Example", "context": "How it works in our app"}, "accounting": {"title": "Accounting & Financial Management Learning", "description": "Essential financial concepts every coffee shop owner should understand"}, "accountingTopics": {"basicConcepts": {"title": "Basic Accounting Concepts", "description": "Fundamental accounting principles for coffee shop owners", "transactions": {"term": "Transactions", "definition": "Any business activity that involves money coming in or going out of your coffee shop.", "example": "When you sell a latte for $5, that's a sales transaction. When you buy coffee beans for $50, that's an expense transaction.", "context": "In our app, all your business activities are recorded as transactions in the Accounting section."}, "revenue": {"term": "Revenue (Income)", "definition": "All the money your coffee shop earns from selling products and services.", "example": "If you sell 100 lattes at $5 each, your revenue is $500 for that period.", "context": "Track your daily sales revenue in the Operations section and view totals in Accounting."}, "expenses": {"term": "Expenses", "definition": "All the money your coffee shop spends to operate the business.", "example": "Rent ($2000/month), coffee beans ($500/month), staff wages ($3000/month) are all expenses.", "context": "Manage recurring expenses and track all business costs in the Accounting section."}, "netIncome": {"term": "Net Income (Profit/Loss)", "definition": "What's left after subtracting all expenses from your revenue. Positive = profit, negative = loss.", "example": "Revenue $10,000 - Expenses $8,000 = Net Income $2,000 (profit)", "context": "View your net income in the Financial Summary cards on the Accounting dashboard."}}, "transactionTypes": {"title": "Types of Business Transactions", "description": "Understanding different transaction categories in your coffee shop", "salesIncome": {"term": "Sales Income", "definition": "Money earned from selling coffee, pastries, and other products to customers.", "example": "Daily sales of $500 from selling lattes, cappuccinos, and muffins.", "context": "Automatically tracked when you record sales in the Operations section."}, "operatingExpenses": {"term": "Operating Expenses", "definition": "Day-to-day costs needed to run your coffee shop.", "example": "Staff wages, utilities, cleaning supplies, marketing costs.", "context": "Add these in the Accounting section or set up recurring expenses for monthly costs."}, "fixedCosts": {"term": "Fixed Costs", "definition": "Expenses that stay the same each month regardless of how much you sell.", "example": "Rent ($2000/month), insurance ($200/month), loan payments ($500/month).", "context": "Set these up in the Financial Terms sheet or as recurring expenses."}, "variableCosts": {"term": "Variable Costs (COGS)", "definition": "Costs that change based on how much you produce or sell.", "example": "Coffee beans, milk, sugar - the more drinks you make, the more you spend on these.", "context": "Calculate these using the COGS Calculator and track in Variable COGS sheet."}, "capitalInvestment": {"term": "Capital Investment", "definition": "Money invested to start or expand your business, including equipment purchases.", "example": "Buying an espresso machine ($5000), furniture ($2000), initial inventory ($1000).", "context": "Record these in the Initial Capital sheet or Fixed Assets section."}}, "financialHealth": {"title": "Understanding Financial Health", "description": "Key indicators to monitor your coffee shop's financial performance", "grossProfit": {"term": "Gross Profit", "definition": "Revenue minus the direct cost of making your products (COGS).", "example": "If you sell $1000 worth of coffee and the beans/milk cost $300, gross profit is $700.", "context": "View this in your Financial Summary - it shows how efficiently you're producing."}, "profitMargin": {"term": "<PERSON><PERSON>", "definition": "What percentage of each sale becomes profit after all expenses.", "example": "If you make $2 profit on a $5 latte, your profit margin is 40%.", "context": "Higher margins mean your business is more profitable per sale."}, "breakEven": {"term": "Break-even Point", "definition": "The amount of sales needed to cover all your expenses (no profit, no loss).", "example": "If your monthly expenses are $8000 and average profit per sale is $2, you need 4000 sales to break even.", "context": "Use the Financial Dashboard to see if you're above or below break-even."}, "cashFlow": {"term": "Cash Flow", "definition": "The timing of money coming in versus money going out.", "example": "You might be profitable but have cash flow problems if customers pay late but you pay suppliers immediately.", "context": "Monitor this in the Financial Health indicators on your dashboard."}}, "costManagement": {"title": "Cost Management & Control", "description": "Managing and optimizing your coffee shop costs", "cogs": {"term": "Cost of Goods Sold (COGS)", "definition": "The direct cost of ingredients and materials used to make your products.", "example": "For a latte: coffee beans ($0.50), milk ($0.30), cup ($0.10) = COGS of $0.90", "context": "Calculate precise COGS using our COGS Calculator for each menu item."}, "foodCostPct": {"term": "Food Cost Percentage", "definition": "What percentage of your sales goes to ingredient costs.", "example": "If you sell $1000 and spend $300 on ingredients, your food cost percentage is 30%.", "context": "Industry standard for coffee shops is typically 25-35%."}, "laborCostPct": {"term": "Labor Cost Percentage", "definition": "What percentage of your sales goes to staff wages and benefits.", "example": "If you sell $1000 and pay $250 in wages, your labor cost percentage is 25%.", "context": "Track this by recording staff costs in Operating Expenses."}, "overheadCosts": {"term": "Overhead Costs", "definition": "Indirect costs needed to run your business but not directly tied to making products.", "example": "Rent, utilities, insurance, accounting fees, marketing.", "context": "These are typically recorded as Fixed Costs or Operating Expenses."}}}}}