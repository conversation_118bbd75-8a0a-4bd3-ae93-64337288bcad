{"navigation": {"dashboard": "<PERSON><PERSON>", "plan": "Rencan<PERSON>", "products": "Produk", "menus": "<PERSON><PERSON>", "salesTargets": "Target Penjualan", "operations": "Operasional", "people": "<PERSON><PERSON><PERSON>", "accounting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recurringExpenses": "Biaya <PERSON>lang", "analytics": "<PERSON><PERSON><PERSON>", "ingredients": "Bahan Baku", "cogsCalculator": "Kalkulator HPP", "warehouse": "<PERSON><PERSON><PERSON>", "production": "<PERSON><PERSON><PERSON><PERSON>", "fixedAssets": "<PERSON><PERSON>", "reports": "<PERSON><PERSON><PERSON>", "kwaciDemo": "Demo KWACI", "learningHub": "<PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON><PERSON>", "account": "<PERSON><PERSON><PERSON>"}, "navigationDescriptions": {"dashboard": "<PERSON><PERSON> k<PERSON>n utama", "plan": "Pan<PERSON><PERSON> onboarding dan perencanaan operasional", "products": "<PERSON><PERSON><PERSON> produk dan komposisinya", "menus": "Buat dan kelola menu kedai kopi", "salesTargets": "Tetapkan target penjualan harian untuk produk menu", "operations": "Pencatatan pen<PERSON>, analitik, dan pelacakan operasional", "people": "<PERSON><PERSON><PERSON> karyawan dan penugasan point-of-contact", "accounting": "Manajemen transaksi keuangan dan akuntansi komprehensif", "recurringExpenses": "<PERSON><PERSON><PERSON> biaya operasional berulang bulanan dan tahunan", "analytics": "Proyeksi pendapatan spesifik produk dan analisis keuntungan", "ingredients": "<PERSON><PERSON><PERSON> bahan baku dan <PERSON><PERSON>ya", "cogsCalculator": "Hitung harga pokok penjualan", "warehouse": "<PERSON><PERSON>la stok dan inventori gudang", "production": "Kelola batch produksi dan alokasi", "fixedAssets": "<PERSON><PERSON><PERSON> aset tetap dan dep<PERSON><PERSON>i", "reports": "<PERSON><PERSON><PERSON> keuangan dan analitik", "kwaciDemo": "<PERSON><PERSON><PERSON><PERSON> makna dan animasi ak<PERSON><PERSON> K<PERSON>CI"}, "reportsSubMenu": {"financialOverview": "<PERSON><PERSON><PERSON>", "profitAnalysis": "<PERSON><PERSON><PERSON>", "costBreakdown": "Rincian <PERSON>ya"}, "sidebarGroups": {"navigation": "Na<PERSON><PERSON><PERSON>", "learningSupport": "Belajar & Dukungan", "quickActions": "<PERSON><PERSON><PERSON>", "devTools": "Alat Pengembang"}, "devTools": {"multiBusinessSeed": "Seed Multi-Bisnis", "debugAccounting": "Debug Akuntansi"}, "userMenu": {"businessOwner": "Pemilik Bisnis KWACI", "email": "<EMAIL>", "keyboardShortcuts": "Pintasan Keyboard", "signOut": "<PERSON><PERSON><PERSON>"}, "common": {"loading": "Memuat...", "error": "<PERSON><PERSON><PERSON>", "success": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "save": "Simpan", "delete": "Hapus", "edit": "Edit", "create": "Buat", "update": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "back": "Kembali", "next": "Selanjutnya", "previous": "Sebelumnya", "search": "<PERSON><PERSON>", "filter": "Filter", "sort": "Urut<PERSON>", "export": "Ekspor", "import": "Impor", "refresh": "Segarkan", "retry": "<PERSON><PERSON>", "remove": "Hapus"}, "language": {"switchLanguage": "Ganti Bahasa", "currentLanguage": "Bahasa Saat Ini", "english": "English", "indonesian": "Bahasa Indonesia"}, "operationsStatus": {"status": {"pending": "<PERSON><PERSON><PERSON>", "inProgress": "Sedang Berlangsung", "completed": "Se<PERSON><PERSON>"}}, "kwaci": {"brandName": "KWACI Grow", "acronyms": {"mixed": {"name": "<PERSON><PERSON><PERSON> (Indonesia-Inggris)", "k": "<PERSON><PERSON><PERSON>", "kDesc": "<PERSON><PERSON><PERSON>", "w": "<PERSON><PERSON><PERSON><PERSON>", "wDesc": "<PERSON><PERSON><PERSON><PERSON>", "a": "Automated", "aDesc": "Proses otomatis", "c": "Commerce", "cDesc": "<PERSON><PERSON><PERSON><PERSON> per<PERSON>", "i": "Insights", "iDesc": "<PERSON><PERSON><PERSON> bisnis"}, "english": {"name": "<PERSON><PERSON><PERSON><PERSON>", "k": "Knowledge", "kDesc": "<PERSON><PERSON><PERSON><PERSON> pen<PERSON>an bisnis", "w": "Warehouse", "wDesc": "Manajemen inventori dan stok", "a": "Analytics", "aDesc": "Analitik data dan pelaporan", "c": "Commerce", "cDesc": "Operasi perdagangan bisnis", "i": "Intelligence", "iDesc": "Kecerdasan bisnis"}, "indonesian": {"name": "<PERSON><PERSON><PERSON><PERSON>", "k": "<PERSON><PERSON>", "kDesc": "Kasir/POS", "w": "<PERSON><PERSON>", "wDesc": "<PERSON><PERSON><PERSON> kecil/toko", "a": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "c": "<PERSON><PERSON><PERSON>", "cDesc": "Cerdas/pintar", "i": "<PERSON><PERSON><PERSON>", "iDesc": "<PERSON><PERSON><PERSON>"}}}, "plan": {"title": "Rencan<PERSON>", "description": "<PERSON><PERSON><PERSON> onboarding dan perencanaan operasional untuk kedai kopi Anda", "tabs": {"journey": "<PERSON><PERSON>", "planningDashboard": "<PERSON><PERSON>"}, "templatePreviewSheet": {"previewDescription": "Pratinjau detail template dan buat rencana operasional baru", "overview": "<PERSON><PERSON><PERSON>", "type": {"daily": "<PERSON><PERSON>", "weekly": "Mingguan", "monthly": "Bulanan"}, "difficulty": {"beginner": "<PERSON><PERSON><PERSON>", "intermediate": "<PERSON><PERSON><PERSON>", "advanced": "Lanjutan"}, "default": "<PERSON><PERSON><PERSON>", "estimatedDuration": "<PERSON><PERSON><PERSON><PERSON>: {{duration}} jam", "loading": "Memuat detail template...", "goals": "<PERSON><PERSON><PERSON> ({{count}})", "goalTarget": "Target: {{value}} {{unit}}", "tasks": "Tugas ({{count}})", "priority": {"low": "Rendah", "medium": "Sedang", "high": "Tingg<PERSON>"}, "taskEst": "Perk. {{duration}}menit", "metrics": "<PERSON><PERSON> ({{count}})", "metricCategory": {"performance": "Performa", "quality": "<PERSON><PERSON><PERSON>", "efficiency": "E<PERSON><PERSON>nsi"}, "metricTarget": "Target: {{value}} {{unit}}", "metricTrack": "Lacak: {{frequency}}", "createPlan": "Buat Rencana da<PERSON>", "close": "<PERSON><PERSON><PERSON>", "backToPreview": "<PERSON><PERSON><PERSON> ke Pratinjau"}, "journeyMap": {"loading": "Memuat progres perjalanan...", "error": "Gagal memuat progres perjalanan", "title": "<PERSON><PERSON><PERSON><PERSON> Kedai Ko<PERSON>", "description": "Selesaikan 9 langkah untuk menyiapkan operasional kedai kopi Anda", "autoCheck": "<PERSON><PERSON>", "reset": "<PERSON><PERSON>", "overallProgress": "<PERSON><PERSON><PERSON>", "complete": "{{percentage}}% <PERSON><PERSON><PERSON>", "nextStep": "<PERSON><PERSON><PERSON>: {{title}}", "start": "<PERSON><PERSON>", "completeTitle": "🎉 <PERSON><PERSON><PERSON><PERSON>!", "completeDescription": "Selamat! Anda telah menyelesaikan semua langkah penyiapan kedai kopi.", "tabs": {"map": "<PERSON><PERSON>", "guided": "<PERSON><PERSON><PERSON>"}, "stepsMap": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON> langkah yang terbuka untuk melihat instruksi dan memulai", "bonus": "Bonus", "status": {"completed": "Se<PERSON><PERSON>", "ready": "Siap", "locked": "Terkun<PERSON>"}}, "modal": {"stepLabel": "Langkah {{number}}: {{title}}", "instructions": "Instruksi", "goTo": "<PERSON>u<PERSON> {{title}}", "completePrev": "<PERSON><PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "markComplete": "Tandai Selesai", "markingComplete": "<PERSON><PERSON><PERSON>...", "tipsTitle": "💡 <PERSON><PERSON><PERSON>", "tips": {"createIngredient": ["<PERSON><PERSON>gan bahan dasar se<PERSON><PERSON>, <PERSON><PERSON>, da<PERSON>", "Tetapkan biaya satuan dan jumlah dasar yang realistis", "<PERSON><PERSON><PERSON> nama yang jelas dan deskriptif untuk memudahkan identifikasi"], "createProduct": ["Buat produk se<PERSON><PERSON> terlebih da<PERSON>u (misal: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>)", "<PERSON><PERSON><PERSON> HPP akurat", "Tambahkan deskripsi detail untuk kejelasan"], "createMenu": ["Beri nama menu yang jelas dan deskriptif", "Setel status ke \"Aktif\" jika sudah siap", "Tambahkan catatan tentang tujuan atau target menu"], "createBranch": ["Gunakan nama lokasi yang spesifik (misal: \"<PERSON><PERSON>\")", "Sertakan alamat atau informasi area", "Setel cabang sebagai aktif untuk operasional"], "addProductToMenu": ["Tetapkan harga yang kompetitif untuk produk Anda", "Kelompokkan produk berdasarkan kategori", "Gunakan urutan tampilan untuk menyorot item populer"], "addItemToWarehouse": ["<PERSON><PERSON> dengan jumlah yang cukup untuk produksi", "<PERSON><PERSON><PERSON> kalk<PERSON> HPP untuk perhitungan biaya yang akurat", "Tambahkan catatan batch untuk keperluan pelacakan"], "createProductionAllocation": ["Alokasikan jumlah realistis be<PERSON><PERSON> per<PERSON>an", "Periksa stok sebelum alokasi", "Tambahkan catatan produksi untuk referensi"], "changeProductionBatchStatus": ["<PERSON><PERSON><PERSON> alur kerja: <PERSON><PERSON> → <PERSON><PERSON> → <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> stok setelah se<PERSON>ai", "Catat jumlah output aktual saat menyelesaikan"], "recordSales": ["Catat penjualan dengan waktu yang akurat", "Pastikan stok cukup sebelum mencatat penjualan", "<PERSON><PERSON><PERSON> pen<PERSON>an inventori setelah pen<PERSON>"]}, "status": {"completed": "Se<PERSON><PERSON>", "ready": "Siap Dimulai", "locked": "Terkun<PERSON>", "unknown": "Tidak Diketahui"}}, "guidedSteps": {"checkingRequirements": "Memeriksa <PERSON>...", "requirementsMet": "Persyaratan terpenuhi! Anda dapat menandai langkah ini sebagai selesai.", "validation": {"createIngredient": {"message": "<PERSON><PERSON><PERSON> buat minimal satu bahan untuk melanjutkan.", "suggestion1": "<PERSON><PERSON>ian <PERSON>han dan tambahkan bahan baru.", "suggestion2": "Pastikan semua detail bahan yang diperlukan sudah diisi.", "suggestion3": "<PERSON><PERSON><PERSON> bahan Anda sebelum melanjutkan."}, "createProduct": {"message": "<PERSON><PERSON><PERSON> buat minimal satu produk untuk melanjutkan.", "suggestion1": "<PERSON><PERSON> bagian Produk dan tambahkan produk baru.", "suggestion2": "Pastikan semua detail produk yang diperlukan sudah diisi.", "suggestion3": "Simpan produk Anda sebelum melanjutkan."}, "createMenu": {"message": "<PERSON><PERSON>an buat minimal satu menu untuk melanjutkan.", "suggestion1": "<PERSON>uka bagian Menu dan tambahkan menu baru.", "suggestion2": "Pastikan semua detail menu yang diperlukan sudah diisi.", "suggestion3": "Simpan menu Anda sebelum melanjutkan."}, "createBranch": {"message": "<PERSON><PERSON>an buat minimal satu cabang untuk melanjutkan.", "suggestion1": "<PERSON><PERSON> bagian <PERSON>ng dan tambahkan cabang baru.", "suggestion2": "Pastikan semua detail cabang yang diperlukan sudah diisi.", "suggestion3": "Simpan cabang Anda sebelum melanjutkan."}, "addProductToMenu": {"message": "<PERSON><PERSON><PERSON> tambahkan minimal satu produk ke menu untuk melanjutkan.", "suggestion1": "<PERSON>uka bagian Menu dan tambahkan produk ke menu.", "suggestion2": "Pastikan setiap menu memiliki minimal satu produk.", "suggestion3": "Simpan menu Anda setelah menambahkan produk."}, "addItemToWarehouse": {"message": "<PERSON><PERSON>an tambahkan minimal satu item ke gudang untuk melanjutkan.", "suggestion1": "Buka bagian Gudang dan tambahkan item baru.", "suggestion2": "Pastikan semua detail item yang diperlukan sudah diisi.", "suggestion3": "Simpan item gudang Anda sebelum melanjutkan."}, "createProductionAllocation": {"message": "<PERSON><PERSON><PERSON> buat minimal satu alokasi produksi untuk melanjutkan.", "suggestion1": "<PERSON><PERSON> bagian Alokasi Produksi dan tambahkan alokasi baru.", "suggestion2": "Pastikan semua detail alokasi yang diperlukan sudah diisi.", "suggestion3": "<PERSON><PERSON><PERSON> alokasi Anda sebelum melanjutkan."}, "changeProductionBatchStatus": {"message": "<PERSON><PERSON><PERSON> perbarui status batch produksi untuk melanjutkan.", "suggestion1": "<PERSON><PERSON> <PERSON>ian Batch Produksi dan perbarui <PERSON>nya.", "suggestion2": "Pastikan status batch mencerminkan progres saat ini.", "suggestion3": "<PERSON><PERSON><PERSON> per<PERSON>han Anda setelah memperbarui status."}, "recordSales": {"message": "Silakan catat minimal satu penjualan untuk melanjutkan.", "suggestion1": "<PERSON><PERSON> bagian <PERSON>alan dan catat penjualan baru.", "suggestion2": "Pastikan semua detail penjualan yang diperlukan sudah diisi.", "suggestion3": "Simpan catatan penjualan Anda sebelum melanjutkan."}}, "validationNotConfigured": "Validasi langkah belum dikonfigurasi.", "validationError": "<PERSON><PERSON><PERSON><PERSON> kesalahan saat memeriksa persyaratan langkah.", "tryAgain": "Silakan coba lagi atau hubungi dukungan.", "progress": "Progres", "instructions": "Instruksi", "showSuggestions": "<PERSON><PERSON><PERSON><PERSON> saran", "hideSuggestions": "Sembunyikan saran", "goTo": "<PERSON>u<PERSON> {{title}}", "markComplete": "Tandai Selesai", "completing": "Menyelesaikan...", "status": {"completed": "Se<PERSON><PERSON>", "ready": "Siap Diselesaikan", "inProgress": "Sedang Be<PERSON>lan", "locked": "Terkun<PERSON>", "unknown": "Tidak Diketahui"}}}, "type": {"daily": "<PERSON><PERSON>", "weekly": "Mingguan", "monthly": "Bulanan", "daily_id": "<PERSON><PERSON><PERSON>", "weekly_id": "<PERSON><PERSON><PERSON>", "monthly_id": "<PERSON><PERSON><PERSON>"}, "category": {"sales": "Penjualan", "production": "<PERSON><PERSON><PERSON><PERSON>", "efficiency": "E<PERSON><PERSON>nsi", "quality": "<PERSON><PERSON><PERSON>", "cost": "Biaya"}, "status": {"draft": "Draf", "active": "Aktif", "completed": "Se<PERSON><PERSON>", "archived": "Diarsip<PERSON>"}, "planningDashboard": {"loading": "Memuat data dasbor...", "title": "<PERSON><PERSON>", "description": "Buat dan kelola rencana <PERSON><PERSON> harian, min<PERSON><PERSON>, dan bulanan untuk kedai kopi Anda", "stats": {"activePlans": "Rencana Aktif", "goalsAchieved": "Target Tercapai", "tasksCompleted": "<PERSON><PERSON>", "completionRate": "<PERSON><PERSON><PERSON>"}, "templates": {"title": "Template Perencanaan", "description": "Template kilat untuk berbagai skenario perencanaan", "default": "<PERSON><PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON><PERSON> durasi {{hours}}j", "useTemplate": "<PERSON><PERSON><PERSON>"}, "journey": {"title": "Progres Per<PERSON>", "description": "Selesaikan perjalanan penyiapan kedai kopi untuk membuka fitur perencanaan lanjutan", "overallProgress": "<PERSON><PERSON><PERSON>", "incompleteTitle": "<PERSON><PERSON><PERSON><PERSON>", "incompleteDescription": "Selesaikan penyiapan kedai kopi Anda untuk membuka semua fitur dan template perencanaan.", "completeTitle": "🎉 Penyiapan Selesai!", "completeDescription": "Kedai kopi Anda sepen<PERSON>nya siap. Anda dapat membuat rencana operasional komprehensif.", "createIngredient": {"title": "Buat Bahan Baku", "description": "<PERSON><PERSON><PERSON> bahan baku utama <PERSON>, <PERSON><PERSON>, dan <PERSON><PERSON> untuk memulai perencan<PERSON>.", "instructions": "<PERSON><PERSON> dengan bahan dasar se<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Tetapkan biaya satuan dan jumlah dasar yang realistis. Gunakan nama yang jelas dan deskriptif untuk setiap bahan."}, "createProduct": {"title": "Buat Produk", "description": "Buat produk kopi utama seperti Espresso, <PERSON><PERSON>, da<PERSON>.", "instructions": "Tambahkan produk utama yang akan dijual di kedai kopi Anda. Pastikan setiap produk memiliki komposisi bahan yang jelas."}, "createMenu": {"title": "<PERSON><PERSON><PERSON>u", "description": "Susun menu kedai kopi Anda dengan produk yang telah dibuat.", "instructions": "Buat menu yang menarik dan mudah dipahami pelanggan. Sertakan semua produk utama."}, "createBranch": {"title": "Buat Cabang", "description": "Tambahkan cabang atau lokasi kedai kopi Anda.", "instructions": "Masukkan detail lokasi dan informasi penting untuk setiap cabang."}, "addProductToMenu": {"title": "Tambahkan Produk ke Menu", "description": "Hubungkan produk ke menu yang tersedia.", "instructions": "Pastikan setiap produk yang dibuat sudah masuk ke dalam menu yang sesuai."}, "addItemToWarehouse": {"title": "Tambahkan Item ke Gudang", "description": "Masukkan stok awal bahan baku ke gudang.", "instructions": "Catat jumlah stok awal untuk setiap bahan baku di gudang."}, "createProductionAllocation": {"title": "Buat Alokasi Produksi", "description": "<PERSON><PERSON><PERSON><PERSON> alokasi bahan baku untuk produksi harian.", "instructions": "Tentukan bahan baku yang akan digunakan untuk setiap batch produksi."}, "changeProductionBatchStatus": {"title": "Ubah Status Batch Produksi", "description": "Perbarui status batch produksi set<PERSON>h se<PERSON>.", "instructions": "Tandai batch produksi sebagai selesai setelah proses produksi berakhir."}, "recordSales": {"title": "Catat Penjualan", "description": "Rekam transaksi penjualan harian kedai kopi Anda.", "instructions": "Masukkan data penjualan setiap hari untuk memantau performa bisnis."}, "createSalesTarget": {"title": "Buat Target Penjualan", "description": "Tetapkan target penju<PERSON> harian untuk setiap produk.", "instructions": "Tentukan target penjualan yang realistis untuk setiap produk di menu."}}, "recentPlans": {"title": "<PERSON><PERSON><PERSON>", "noPlansTitle": "Belum Ada Rencana", "noPlansDescription": "Buat rencana operasional pertama Anda untuk memulai perencanaan terstruktur.", "createFirst": "Buat Rencana <PERSON>", "view": "<PERSON><PERSON>"}, "analytics": {"plans": "Rencan<PERSON>", "title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> performa dan wawasan peren<PERSON>a", "planDistribution": "Distribusi Rencana", "statusOverview": "Ringkasan Status", "performanceMetrics": "<PERSON><PERSON>", "averageTaskDuration": "<PERSON><PERSON><PERSON>-<PERSON>a", "mostUsedTemplate": "Template <PERSON><PERSON>", "totalPlansCreated": "Total Rencana Dibuat", "goalCategories": "Kategori Target"}}}, "dashboard": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> kom<PERSON><PERSON><PERSON><PERSON><PERSON> dan kinerja bisnis And<PERSON>", "lastUpdated": "<PERSON><PERSON><PERSON>", "refresh": "Segarkan", "realTimeData": "Data real-time", "dataUpdatesAutomatically": "Data dasbor diperbarui secara otomatis saat Anda beralih konteks bisnis", "welcome": {"title": "Selamat Datang di KWACI Grow", "description": "<PERSON>bor manajemen bisnis komprehen<PERSON><PERSON>", "selectBusiness": "<PERSON><PERSON>an pilih bisnis dari sidebar untuk melihat dasbor Anda dan mulai mengelola <PERSON>i.", "multiBusinessEnabled": "Dukungan multi-bisnis diaktifkan"}, "noBusinessSelected": "Tidak ada bisnis yang dipilih", "selectBusinessToView": "<PERSON><PERSON>an pilih bisnis untuk melihat", "errorLoading": "Kesalahan memuat", "tryAgain": "Coba lagi", "salesAnalytics": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> pendapatan dan transaksi untuk {{period}}", "selectPeriod": "<PERSON><PERSON><PERSON> periode", "periods": {"today": "<PERSON>", "week": "<PERSON><PERSON>", "month": "<PERSON><PERSON><PERSON>", "quarter": "3 <PERSON><PERSON><PERSON>"}, "metrics": {"totalRevenue": "Total Pendapatan", "transactions": "Transaksi", "avgOrderValue": "<PERSON><PERSON>-<PERSON><PERSON>", "topProduct": "<PERSON><PERSON><PERSON>", "perTransactionAverage": "Rata-rata per <PERSON><PERSON>i", "sold": "<PERSON><PERSON><PERSON><PERSON>", "noSales": "Tidak ada pen<PERSON>", "active": "Aktif"}, "hourlyChart": {"title": "Penjualan Per <PERSON>", "description": "Distribusi pendapatan dan transaksi sepanjang hari", "peakHours": "Jam Sibuk"}, "noData": {"title": "Tidak Ada Data Penjualan", "description": "Tidak ada penjualan tercatat untuk {{period}}. <PERSON>lai catat penjualan untuk melihat analitik di sini."}}, "financialOverview": {"title": "<PERSON><PERSON><PERSON>", "description": "Posisi keuangan saat ini dan wawasan arus kas", "metrics": {"availableCash": "<PERSON><PERSON>", "monthlyExpenses": "<PERSON><PERSON><PERSON>", "netPosition": "Posisi Bersih", "cashRunway": "Runway Kas", "burnRate": "Tingkat pembakaran", "months": "bulan", "atCurrentBurnRate": "Pada tingkat pembakaran saat ini", "positive": "<PERSON><PERSON><PERSON><PERSON>", "negative": "<PERSON><PERSON><PERSON><PERSON>"}, "healthStatus": {"healthy": "<PERSON><PERSON>", "caution": "Hati-hati", "critical": "<PERSON><PERSON><PERSON>"}, "summary": {"title": "<PERSON><PERSON><PERSON>", "description": "Ringkasan posisi keuangan bisnis And<PERSON>", "cashFlowStatus": "Status Arus Kas", "positiveCashFlow": "<PERSON><PERSON> kas positif - bisnis men<PERSON><PERSON>an lebih banyak pendapatan daripada biaya", "negativeCashFlow": "<PERSON><PERSON> kas negatif - biaya mele<PERSON> penda<PERSON>, pantau dengan cermat", "revenueVsExpenses": "Pendapatan vs Biaya", "monthlyRevenue": "Pendapatan Bulanan", "monthlyExpenses": "<PERSON><PERSON><PERSON>", "netResult": "<PERSON><PERSON>", "recommendations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "focusOnRevenue": "• Fokus pada peningkatan pendapatan atau pengurangan biaya", "considerOptimization": "• Pertimbangkan optimisasi biaya - runway kurang dari 3 bulan", "considerReinvestment": "• Pertimbangkan reinvestasi keuntungan untuk pertumbuhan bisnis", "immediateAttention": "• Perhatian segera dip<PERSON> - posisi kas negatif"}, "error": {"title": "Tidak Dapat Memuat Data Keuangan"}}, "operationsStatus": {"title": "Status Operasional", "description": "Status batch produksi dan alur kerja operasional", "overview": "Ringkasan Operasional", "metrics": {"incompleteBatches": "Batch Belum Selesai", "overdueBatches": "<PERSON><PERSON>", "urgentBatches": "<PERSON><PERSON>", "onTimeRate": "Tingkat Tepat Waktu", "batchesCompletedOnTime": "Batch diselesaikan tepat waktu"}, "status": {"pending": "<PERSON><PERSON><PERSON>", "inProgress": "Sedang Berlangsung", "completed": "Se<PERSON><PERSON>", "cancelled": "Di<PERSON><PERSON><PERSON>"}, "priority": {"overdue": "Terlambat", "urgent": "Mendesak", "normal": "Normal"}, "badges": {"active": "Aktif", "allComplete": "<PERSON><PERSON><PERSON>", "attentionNeeded": "<PERSON><PERSON>", "onTrack": "<PERSON><PERSON><PERSON>", "monitorClosely": "Pan<PERSON><PERSON>"}, "columns": {"batchNumber": "No. Batch", "product": "Produk", "quantity": "Kuantitas", "status": "Status", "priority": "Prioritas", "created": "Dibuat", "age": "<PERSON><PERSON>", "target": "Target", "notSpecified": "Tidak ditentukan"}, "incompleteBatches": {"title": "Batch Produksi Belum Selesai", "description": "<PERSON>ch produksi yang memer<PERSON>an per<PERSON>ian atau penyelesaian"}, "emptyState": {"title": "<PERSON><PERSON><PERSON>", "description": "Tidak ada batch produksi yang belum selesai. Kerja bagus dalam menjaga produksi!"}, "error": {"title": "Tidak Dapat Memuat Data Operasional", "loading": "<PERSON><PERSON> memuat", "tryAgain": "Coba lagi"}, "noBusinessSelected": {"title": "Tidak ada bisnis dipilih", "description": "<PERSON><PERSON>an pilih bisnis untuk melihat status operasional."}}, "inventoryAlerts": {"title": "Peringatan Inventori", "description": "Level stok dan peringatan inventori rendah", "overview": "<PERSON><PERSON><PERSON>", "metrics": {"criticalAlerts": "<PERSON><PERSON><PERSON>", "lowStockAlerts": "Peringatan Stok Rendah", "itemsBelowThreshold": "<PERSON><PERSON>", "stockHealth": "<PERSON><PERSON><PERSON><PERSON>"}, "summary": {"title": "<PERSON><PERSON><PERSON>", "criticalItems": "<PERSON><PERSON>", "lowStockItems": "Item Stok Rendah", "totalItems": "Total Item"}, "alertLevels": {"critical": "<PERSON><PERSON><PERSON>", "lowStock": "Stok Rendah", "normal": "Normal"}, "badges": {"immediateAction": "<PERSON><PERSON><PERSON>", "noCriticalIssues": "Tidak Ada Masalah Kritis", "monitorClosely": "Pan<PERSON><PERSON>", "stockLevelsGood": "Level Stok Baik", "requireAttention": "<PERSON><PERSON>", "allWellStocked": "<PERSON><PERSON><PERSON>", "excellentHealth": "<PERSON><PERSON><PERSON><PERSON>", "goodOverall": "Baik Secara <PERSON>"}, "columns": {"ingredient": "<PERSON><PERSON>", "currentStock": "Stok Saat Ini", "threshold": "Ambang Bat<PERSON>", "stockLevel": "Level Stok", "alertLevel": "Level Peringatan", "actionNeeded": "<PERSON><PERSON><PERSON>"}, "actions": {"outOfStock": "Stok habis - <PERSON><PERSON> segera", "reorderUrgently": "<PERSON>esan ulang mendesak", "planReorderSoon": "Rencanakan pesan ulang segera", "monitorLevels": "Pantau level"}, "progress": {"ofThreshold": "% dari ambang batas"}, "lowStockIngredients": {"title": "Bahan Stok Rendah", "description": "<PERSON><PERSON> yang berada di bawah ambang batas stok rendah"}, "emptyState": {"title": "Semua Level Stok Baik", "description": "Tidak ada bahan yang berada di bawah ambang batas stok rendah. Pertahankan manajemen inventori yang baik!"}, "error": {"title": "Tidak Dapat Memuat Data Inventori", "loading": "<PERSON><PERSON> memuat", "tryAgain": "Coba lagi"}, "noBusinessSelected": {"title": "Tidak ada bisnis dipilih", "description": "<PERSON><PERSON>an pilih bisnis untuk melihat peringatan inventori."}}, "branchPerformance": {"title": "<PERSON><PERSON><PERSON>", "description": "Perbandingan kinerja penjualan di semua cabang untuk {{period}}", "periods": {"today": "hari ini", "week": "minggu ini", "month": "bulan ini", "quarter": "3 bulan te<PERSON>hir"}, "periodLabels": {"today": "<PERSON>", "week": "<PERSON><PERSON>", "month": "<PERSON><PERSON><PERSON>", "quarter": "3 <PERSON><PERSON><PERSON>"}, "metrics": {"activeBranches": "Cabang Aktif", "totalRevenue": "Total Pendapatan", "avgPerBranch": "Rata-rata per <PERSON>abang", "topPerformer": "<PERSON><PERSON><PERSON>"}, "summary": {"title": "<PERSON><PERSON><PERSON>", "topPerformer": "<PERSON><PERSON><PERSON>", "totalBranches": "Total Cabang", "avgRevenue": "<PERSON>a-<PERSON>a <PERSON>"}, "badges": {"operational": "Operasional", "noBranches": "Tidak ada cabang"}, "labels": {"acrossAllBranches": "Di semua cabang", "revenuePerBranch": "Pendapatan per cabang", "noData": "Tidak ada data", "aboveAverage": "Di atas rata-rata", "belowAverage": "<PERSON> bawah rata-rata"}, "columns": {"rank": "<PERSON><PERSON><PERSON>", "branch": "Cabang", "revenue": "Pendapatan", "transactions": "Transaksi", "avgOrderValue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "performance": "<PERSON><PERSON><PERSON>"}, "comparison": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> kinerja detail untuk setiap cabang ({{period}})"}, "performanceLabels": {"excellent": "Sangat Baik", "good": "Baik", "average": "<PERSON>a-rata", "belowAverage": "<PERSON>a"}, "emptyState": {"title": "Tidak Ada Data Cabang", "description": "Tidak ada cabang ditemukan atau tidak ada data penjualan tersedia untuk periode yang dipilih."}, "error": {"title": "Tidak Dapat Memuat Data Cabang", "loading": "<PERSON><PERSON> memuat", "tryAgain": "Coba lagi"}, "noBusinessSelected": {"title": "Tidak ada bisnis dipilih", "description": "<PERSON><PERSON>an pilih bisnis untuk melihat kinerja cabang."}}}, "cogs": {"playground": {"title": "Playground HPP", "sandboxBadge": "Lingkungan Sandbox", "saveToDatabase": "Simpan ke Database", "description": {"welcome": "Selamat datang di COGS Playground!", "line1": "Ini adalah lingkungan sandbox untuk bereksperimen dengan kombinasi bahan dan produk", "line2": "untuk menghitung Harga Pokok Penjualan (HPP) sebelum menyimpan ke database.", "line3": "Semua data di sini bersifat sementara sampai Anda memilih untuk menyimpannya."}, "badges": {"realTime": "Perhitungan HPP real-time", "temporaryIngredients": "<PERSON><PERSON> sementara", "productExperimentation": "Eksperimen produk", "optionalSave": "Opsional simpan ke database"}, "tabs": {"ingredients": "<PERSON><PERSON>", "products": "Produk"}, "gettingStarted": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> langkah berikut untuk mulai bereksperimen dengan perhitungan HPP:", "steps": {"createIngredients": {"title": "<PERSON><PERSON><PERSON>", "description": "Tambahkan bahan sementara dengan biaya, jumlah, dan unit"}, "buildProducts": {"title": "Buat Produk", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> bahan untuk membuat produk dan lihat HPP secara real-time"}, "saveWhenReady": {"title": "Simpan Jika Siap", "description": "<PERSON>ka sudah puas dengan per<PERSON>baan Anda, simpan ke database"}}}}, "saveDialog": {"title": "Simpan ke Database", "description": "Ini akan menyimpan bahan dan produk sementara ke database secara permanen. Hanya item aktif yang akan disimpan.", "noDataTitle": "Tidak ada data aktif untuk disimpan", "noDataDescription": "<PERSON><PERSON><PERSON> bahan atau produk aktif terle<PERSON>h dahulu.", "ingredientsToSave": "<PERSON><PERSON> yang <PERSON>", "productsToSave": "<PERSON><PERSON><PERSON> yang <PERSON>", "saveSummary": "<PERSON><PERSON><PERSON>", "totalIngredients": "Total Bahan", "totalProducts": "Total Produk", "saving": "Menyimpan...", "success": "Berhasil disimpan ke database!", "failure": "Gagal menyimpan ke database"}, "tempIngredientList": {"title": "<PERSON><PERSON>", "sandboxBadge": "Sandbox", "addIngredient": "Tambah Bahan", "description": "<PERSON><PERSON>t bahan untuk percobaan HPP. Data ini tidak akan disimpan ke database sampai Anda memilih untuk menyimpannya.", "emptyMessage": "Belum ada bahan sementara", "addFirst": "Tambah Bahan <PERSON>", "table": {"name": "<PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "baseCost": "<PERSON><PERSON><PERSON>", "baseQuantity": "<PERSON><PERSON><PERSON>", "unitCost": "Biaya per Unit", "unit": "Unit", "status": "Status", "actions": "<PERSON><PERSON><PERSON>", "active": "Aktif", "inactive": "<PERSON><PERSON><PERSON><PERSON>"}}, "tempProductList": {"title": "Produk Sementara", "sandboxBadge": "Sandbox", "addProduct": "Tambah Produk", "description": "Buat produk dengan komposisi bahan untuk percobaan HPP. Data ini tidak akan disimpan ke database sampai Anda memilih untuk menyimpannya.", "noIngredientsWarning": "<PERSON><PERSON>t beberapa bahan aktif terlebih dahulu sebelum menambah produk.", "emptyMessage": "Belum ada produk sementara", "addFirst": "Tambah Produk Pertama Anda", "table": {"productName": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "ingredients": "<PERSON><PERSON>", "cogsPerCup": "HPP per Cangkir", "status": "Status", "actions": "<PERSON><PERSON><PERSON>", "noIngredientsBadge": "Tidak ada bahan", "ingredientCount": "{{count}} bahan", "ingredientCount_plural": "{{count}} bahan", "active": "Aktif", "inactive": "<PERSON><PERSON><PERSON><PERSON>"}, "summary": {"title": "Ringkasan Playground", "totalProducts": "Total Produk", "activeProducts": "Produk Aktif", "avgCogsPerCup": "Rata-rata HPP per Cangkir", "totalIngredientsUsed": "Total Bahan Digunakan"}}, "tempIngredientForm": {"addTitle": "Tambah Bahan Sementara", "editTitle": "<PERSON>", "description": "<PERSON><PERSON>t bahan untuk percobaan HPP. Data ini bersifat sementara dan tidak akan disimpan ke database sampai Anda memilih untuk menyimpannya.", "fields": {"name": "<PERSON><PERSON> *", "baseUnitCost": "Biaya Unit Dasar *", "baseUnitQuantity": "Jumlah Unit Dasar *", "unit": "Unit *", "category": "<PERSON><PERSON><PERSON>", "supplierInfo": "Informasi Pemasok", "note": "Catatan", "isActive": "Bahan aktif", "selectUnit": "Pilih unit", "selectCategory": "<PERSON><PERSON><PERSON> ka<PERSON>i", "noCategory": "Tidak Ada Kategori", "costBaseHelp": "Biaya untuk jumlah dasar", "quantityHelp": "Jumlah dalam unit dasar", "unitHelp": "<PERSON><PERSON><PERSON>n", "supplierPlaceholder": "Masukkan detail pemasok", "notePlaceholder": "Catatan tambahan tentang bahan ini"}, "buttons": {"add": "Tambah Bahan", "update": "<PERSON><PERSON><PERSON>", "adding": "Menambah...", "updating": "Memperbarui...", "cancel": "<PERSON><PERSON>"}}, "tempProductForm": {"addTitle": "Tambah Produk Sementara", "editTitle": "Edit Produk <PERSON>", "description": "Buat produk dengan komposisi bahan untuk percobaan HPP. Data ini bersifat sementara dan tidak akan disimpan ke database sampai Anda memilih untuk menyimpannya.", "fields": {"name": "<PERSON><PERSON> *", "description": "<PERSON><PERSON><PERSON><PERSON>", "note": "Catatan", "isActive": "Produk aktif", "usagePerCup": "<PERSON><PERSON><PERSON><PERSON> per <PERSON>gkir", "ingredient": "<PERSON><PERSON>", "noteOptional": "Catatan (opsional)", "selectIngredient": "<PERSON><PERSON><PERSON> bahan", "additionalNotes": "Catatan tambahan", "totalCogsPerCup": "Total HPP per Cangkir"}, "buttons": {"add": "Tambah Produk", "update": "<PERSON><PERSON><PERSON>", "adding": "Menambah...", "updating": "Memperbarui...", "cancel": "<PERSON><PERSON>", "addIngredient": "Tambah Bahan"}, "table": {"ingredient": "<PERSON><PERSON>", "usagePerCup": "<PERSON><PERSON><PERSON><PERSON> per <PERSON>gkir", "costPerCup": "<PERSON><PERSON><PERSON> per <PERSON>", "note": "Catatan", "actions": "<PERSON><PERSON><PERSON>"}, "emptyIngredients": "Belum ada bahan yang ditambahkan"}}, "recurringExpenses": {"page": {"title": "Biaya <PERSON>lang", "description": "<PERSON><PERSON><PERSON> biaya operasional berulang bulanan dan tahunan", "showInactive": "Tampilkan yang tidak aktif"}, "sheet": {"createTitle": "Tambah Biaya Berulang", "editTitle": "<PERSON> <PERSON><PERSON><PERSON>", "createDescription": "Tambahkan biaya berulang baru untuk melacak biaya operasional.", "editDescription": "Perbarui informasi dan pengaturan biaya."}, "summary": {"monthlyTotal": "Total Bulanan", "yearlyTotal": "Total Tahunan", "activeExpenses": "Biaya Aktif", "totalExpenses": "Total Biaya", "expensesByCategory": "<PERSON><PERSON><PERSON>", "monthlyExpenses": "<PERSON><PERSON><PERSON>", "yearlyExpenses": "<PERSON><PERSON><PERSON>", "quickStats": "Statistik Singkat", "annualCost": "<PERSON><PERSON><PERSON> (bulanan × 12)", "dailyAverageCost": "<PERSON>a-rata biaya harian", "expenseCategories": "<PERSON><PERSON><PERSON>"}, "table": {"title": "Biaya <PERSON>lang", "searchPlaceholder": "<PERSON>i biaya...", "categoryFilterAll": "<PERSON><PERSON><PERSON>", "frequencyFilterAll": "<PERSON><PERSON><PERSON>", "noExpenses": "Belum ada biaya berulang. Buat biaya pertama Anda.", "noMatch": "Tidak ada biaya yang cocok dengan filter Anda.", "headers": {"name": "<PERSON><PERSON>", "amount": "<PERSON><PERSON><PERSON>", "frequency": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "startDate": "<PERSON><PERSON>", "endDate": "<PERSON><PERSON>", "status": "Status", "actions": "<PERSON><PERSON><PERSON>"}, "ongoing": "<PERSON><PERSON><PERSON><PERSON>g", "status": {"active": "Aktif", "inactive": "Tidak Aktif"}, "actions": {"edit": "Edit", "delete": "Hapus", "activate": "Aktifkan", "deactivate": "Nonaktifkan", "openMenu": "Buka menu", "clear": "<PERSON><PERSON><PERSON><PERSON>"}}, "form": {"fields": {"name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> (Opsional)", "amount": "<PERSON><PERSON><PERSON> ({{currency}})", "frequency": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "startDate": "<PERSON><PERSON>", "endDate": "<PERSON><PERSON> (Opsional)", "note": "Catatan (Opsional)"}, "placeholders": {"name": "mis. <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "description": "Detail tambahan tentang biaya ini...", "category": "<PERSON><PERSON><PERSON> atau buat kategori...", "searchCategories": "<PERSON>i kategori...", "frequency": "<PERSON><PERSON><PERSON>", "pickDate": "<PERSON><PERSON><PERSON> tanggal", "noEndDate": "<PERSON><PERSON> tanggal akhir", "clearDate": "<PERSON><PERSON>", "note": "Catatan tambahan..."}, "descriptions": {"leaveEmpty": "Biarkan kosong untuk biaya berkelanjutan"}, "frequency": {"monthly": "Bulanan", "yearly": "<PERSON><PERSON><PERSON>"}, "buttons": {"cancel": "<PERSON><PERSON>", "create": "Buat Biaya", "update": "<PERSON><PERSON><PERSON>", "saving": "Menyimpan..."}}, "messages": {"createSuccess": "Biaya berulang baru berhasil dibuat.", "updateSuccess": "<PERSON><PERSON><PERSON> berulang ber<PERSON>.", "deleteSuccess": "<PERSON><PERSON><PERSON> berulang berhasil di<PERSON>pus permanen.", "deleteError": "<PERSON><PERSON> mengh<PERSON>us biaya. Silakan coba lagi.", "deactivateSuccess": "<PERSON><PERSON>ya berulang ber<PERSON>il din<PERSON>.", "activateSuccess": "<PERSON><PERSON><PERSON> be<PERSON>lang ber<PERSON>.", "statusError": "Gagal memperbarui status biaya. Silakan coba lagi."}}, "products": {"loading": "Memuat produk...", "errorLoading": "<PERSON><PERSON>ahan memuat produk: {{error}}", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> produk dan komposisi bahannya", "create": "Buat Produk", "createNewTitle": "Buat Produk Baru", "createNewDescription": "Tambahkan produk baru ke katalog Anda", "tabs": {"overview": "<PERSON><PERSON><PERSON>", "products": "Produk"}, "overview": {"totalProducts": "Total Produk", "totalProductsDesc": "Produk aktif di katalog", "totalIngredients": "Total Bahan", "totalIngredientsDesc": "Di semua produk", "avgIngredients": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avgIngredientsDesc": "Per produk", "recentProducts": "Produk Terbaru", "noProducts": "Belum ada produk", "noProductsDesc": "Buat produk pertama Anda untuk memulai", "viewDetails": "<PERSON><PERSON>", "viewAll": "<PERSON><PERSON> ({{count}})", "ingredientsCount": "{{count}} bahan"}, "filters": {"title": "<PERSON><PERSON>", "showInactive": "Tampilkan produk tidak aktif"}, "list": {"title": "Produk ({{count}})", "searchPlaceholder": "Cari produk...", "noProductsFound": "Produk tidak ditemukan", "noProducts": "Belum ada produk", "tryAdjustSearch": "Coba ubah kata pencarian", "createFirst": "Buat produk pertama Anda untuk memulai", "table": {"name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "ingredients": "<PERSON><PERSON>", "cogsPerCup": "HPP per Cangkir", "status": "Status", "created": "Dibuat", "actions": "<PERSON><PERSON><PERSON>"}, "ingredientCount": "{{count}} bahan", "badge": {"active": "Aktif", "inactive": "<PERSON><PERSON><PERSON><PERSON>"}, "deleteTitle": "Hapus Produk", "deleteDescription": "Yakin ingin menghapus \"{{name}}\"? Tindakan ini tidak dapat dibatalkan.", "editSheetTitle": "Edit Produk", "editSheetDescription": "Perbarui informasi produk", "viewSheetTitle": "Detail Produk", "viewSheetDescription": "<PERSON><PERSON> dan kelola bahan produk"}, "form": {"nameRequired": "<PERSON><PERSON> produk wajib diisi", "saveFailed": "<PERSON>l menyimpan produk", "unexpectedError": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han tak terduga", "fields": {"name": "<PERSON><PERSON> *", "description": "<PERSON><PERSON><PERSON><PERSON>", "note": "Catatan", "activeStatus": "Status Aktif"}, "placeholders": {"name": "<PERSON><PERSON><PERSON><PERSON> nama produk", "description": "<PERSON><PERSON><PERSON><PERSON> produk", "note": "Catatan tambahan tentang produk"}, "active": "Produk aktif", "inactive": "Produk tidak aktif", "buttons": {"create": "Buat Produk", "update": "<PERSON><PERSON><PERSON>", "creating": "Membuat...", "updating": "Memperbarui..."}, "nextSteps": {"title": "Langkah selanju<PERSON>nya:", "step1": "<PERSON><PERSON><PERSON> memb<PERSON>t produk, <PERSON><PERSON> bisa menambah bahan", "step2": "<PERSON>ur jumlah penggunaan per cangkir untuk tiap bahan", "step3": "<PERSON><PERSON>n produk untuk perhitungan HPP dan produksi"}}, "ingredients": {"loadingDetails": "Memuat detail produk...", "notFound": "Produk tidak ditemukan", "title": "<PERSON><PERSON>", "productInfo": {"description": "<PERSON><PERSON><PERSON><PERSON>", "noDescription": "Tidak ada <PERSON>", "totalCogsPerCup": "Total HPP per Cangkir"}, "form": {"ingredient": "<PERSON><PERSON>", "selectIngredient": "<PERSON><PERSON><PERSON> bahan", "usagePerCup": "<PERSON><PERSON><PERSON><PERSON> per <PERSON>gkir", "noteOptional": "Catatan (opsional)", "additionalNotes": "Catatan tambahan", "notePlaceholder": "Catatan"}, "buttons": {"addIngredient": "Tambah Bahan", "add": "Tambah"}, "messages": {"selectIngredient": "<PERSON><PERSON><PERSON> bahan dan masukkan jumlah penggunaan", "invalidUsage": "Pengg<PERSON>an per cangkir harus lebih dari 0", "addSuccess": "<PERSON><PERSON><PERSON><PERSON> bahan", "addFailure": "<PERSON><PERSON> bahan", "updateSuccess": "<PERSON><PERSON><PERSON><PERSON> bahan", "updateFailure": "<PERSON><PERSON> bahan", "removeSuccess": "<PERSON><PERSON><PERSON><PERSON> bahan", "removeFailure": "<PERSON><PERSON><PERSON> bahan"}, "noIngredients": "Belum ada bahan", "noIngredientsDescription": "Tambahkan bahan untuk menentukan komposisi produk", "table": {"ingredient": "<PERSON><PERSON>", "usagePerCup": "<PERSON><PERSON><PERSON><PERSON> per <PERSON>gkir", "costPerCup": "<PERSON><PERSON><PERSON> per <PERSON>", "unitCost": "<PERSON><PERSON><PERSON>", "note": "Catatan", "actions": "<PERSON><PERSON><PERSON>"}, "missingIngredient": "<PERSON><PERSON> hi<PERSON> (ID: {{id}})", "removeDialog": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON> \"{{name}}\" dari produk ini?", "remove": "Hapus"}}, "cogs": {"title": "Perhitungan HPP", "loading": "Menghitung HPP...", "noIngredients": "Belum ada bahan pada produk ini.", "addIngredientsPrompt": "<PERSON><PERSON><PERSON> bahan untuk melihat perhitungan HPP.", "titleFor": "Perhitungan HPP untuk {{name}}", "whatIs": {"title": "Apa itu HPP?", "text": "HPP (Harga Pokok Penjualan) adalah total biaya bahan yang dibutuhkan untuk membuat satu cangkir produk ini."}, "totalCogsPerCup": "Total HPP per Cangkir", "ingredientCount": "{{count}} bahan", "ingredientBreakdown": "<PERSON><PERSON><PERSON>", "table": {"ingredient": "<PERSON><PERSON>", "usagePerCup": "<PERSON><PERSON><PERSON><PERSON>", "costPerCup": "<PERSON><PERSON><PERSON> per <PERSON>", "percentOfTotal": "% dari Total"}, "formula": {"title": "<PERSON><PERSON><PERSON>:", "formula": "Biaya per <PERSON>ki<PERSON> = (<PERSON><PERSON><PERSON> ÷ <PERSON><PERSON><PERSON>) × <PERSON><PERSON><PERSON><PERSON>", "example": "Contoh: <PERSON><PERSON> susu seharga Rp20.000 per 1000ml dan digunakan 100ml per cangkir, maka biaya per cangkir = (20.000 ÷ 1000) × 100 = Rp2.000"}}}, "ingredients": {"loading": "<PERSON><PERSON><PERSON> bahan...", "errorLoading": "<PERSON><PERSON><PERSON> memuat bahan: {{error}}", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> bahan baku dan <PERSON><PERSON>ya", "create": "<PERSON><PERSON><PERSON>", "createNewTitle": "<PERSON><PERSON><PERSON>", "createNewDescription": "<PERSON><PERSON><PERSON> bahan baru ke inventori", "tabs": {"overview": "<PERSON><PERSON><PERSON>", "ingredients": "<PERSON><PERSON>"}, "overview": {"totalIngredients": "Total Bahan", "totalIngredientsDesc": "Bahan aktif", "categories": "<PERSON><PERSON><PERSON>", "categoriesDesc": "<PERSON><PERSON><PERSON> bahan", "totalUsages": "Total Penggunaan", "totalUsagesDesc": "Di semua produk", "avgUsage": "<PERSON><PERSON><PERSON><PERSON>a <PERSON>", "avgUsageDesc": "<PERSON> bahan", "recentIngredients": "<PERSON><PERSON>", "noIngredients": "Belum ada bahan", "noIngredientsDesc": "<PERSON><PERSON>t bahan pertama Anda untuk memulai", "viewDetails": "<PERSON><PERSON>", "viewAll": "<PERSON><PERSON> ({{count}})", "noCategory": "<PERSON><PERSON> kategori", "usedInProducts": "<PERSON><PERSON><PERSON><PERSON> di {{count}} produk"}, "filters": {"title": "<PERSON><PERSON>", "showInactive": "<PERSON><PERSON><PERSON><PERSON> bahan tidak aktif"}, "list": {"title": "<PERSON><PERSON> ({{count}})", "searchPlaceholder": "<PERSON><PERSON> bahan...", "allCategories": "<PERSON><PERSON><PERSON>", "noIngredientsFound": "<PERSON><PERSON> tidak di<PERSON>n", "tryAdjustSearch": "Coba ubah kata pencarian atau filter", "createFirst": "<PERSON><PERSON>t bahan pertama Anda untuk memulai", "table": {"name": "<PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "unit": "Unit", "unitCost": "<PERSON><PERSON><PERSON>", "usage": "<PERSON><PERSON><PERSON><PERSON>", "status": "Status", "actions": "<PERSON><PERSON><PERSON>"}, "badge": {"active": "Aktif", "inactive": "<PERSON><PERSON><PERSON><PERSON>"}, "deleteTitle": "<PERSON><PERSON>", "deleteDescription": "Yakin ingin menghapus \"{{name}}\"?", "deleteInUse": "<PERSON>han ini digunakan di {{count}} produk dan tidak dapat dihapus.", "editSheetTitle": "<PERSON>", "editSheetDescription": "<PERSON><PERSON><PERSON> informasi bahan", "viewSheetTitle": "<PERSON><PERSON><PERSON><PERSON>", "viewSheetDescription": "Lihat produk yang menggunakan bahan ini"}, "categoryCombobox": {"searchPlaceholder": "<PERSON>i kategori...", "emptyText": "<PERSON><PERSON>i tidak <PERSON>.", "deleteTitle": "<PERSON><PERSON>", "alertDescription": "<PERSON><PERSON><PERSON> kategori yang ingin dihapus. <PERSON><PERSON> kategori yang tidak digunakan yang bisa dihapus.", "confirmTitle": "<PERSON><PERSON> \"{{name}}\"", "inUseMessage": "<PERSON><PERSON>i ini digunakan oleh {{count}} bahan dan tidak dapat di<PERSON>pus.", "confirmMessage": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus kategori ini? Tindakan ini tidak dapat dibatalkan."}, "unitCombobox": {"searchPlaceholder": "Cari unit...", "emptyText": "Unit tidak ditemukan."}, "form": {"fields": {"name": "<PERSON><PERSON> *", "category": "Kategori *", "baseUnitCost": "<PERSON><PERSON><PERSON> *", "baseUnitQuantity": "<PERSON><PERSON><PERSON> *", "unit": "Unit *", "supplierInfo": "Informasi Supplier", "note": "Catatan", "activeStatus": "Status Aktif"}, "placeholders": {"name": "<PERSON><PERSON><PERSON><PERSON> nama bahan", "selectCategory": "<PERSON><PERSON><PERSON> atau buat kategori...", "baseUnitCost": "0.00", "baseUnitQuantity": "1.00", "selectUnit": "Pilih atau masukkan unit...", "supplierInfo": "Nama supplier, kontak, dll.", "note": "Catatan tambahan tentang bahan ini", "costBaseHelp": "Masukkan biaya per satuan dasar (misal, per gram, per ml)", "quantityHelp": "<PERSON><PERSON><PERSON><PERSON> jumlah dalam satuan dasar (misal, gram, ml)"}, "costPreview": "<PERSON><PERSON><PERSON><PERSON><PERSON>:", "activeStatus": "Status Aktif", "activeIngredient": "Bahan aktif", "inactiveIngredient": "Bahan tidak aktif", "buttons": {"create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "creating": "Membuat...", "updating": "Memperbarui...", "cancel": "<PERSON><PERSON>"}, "exampleTitle": "Contoh:", "exampleItem1": "• Susu: Biaya 20.000 untuk 1.000 ml = 20 per ml", "exampleItem2": "• Biji kopi: Biaya 200.000 untuk 1.000 g = 200 per g", "exampleItem3": "• Gelas: Biaya 850 per pcs"}, "validation": {"nameRequired": "<PERSON><PERSON> bahan wajib diisi", "categoryRequired": "<PERSON><PERSON><PERSON> waji<PERSON>", "baseUnitCostRequired": "Biaya satuan dasar wajib diisi", "baseUnitCostPositive": "Biaya satuan dasar harus positif", "baseUnitCostNumber": "Biaya satuan dasar harus berupa angka", "baseUnitQuantityRequired": "<PERSON><PERSON><PERSON> satuan dasar wajib diisi", "baseUnitQuantityPositive": "<PERSON><PERSON><PERSON> satuan dasar harus positif", "baseUnitQuantityNumber": "<PERSON><PERSON><PERSON> satuan dasar harus berupa angka", "unitRequired": "Unit wajib diisi"}, "usage": {"loading": "Memuat detail bahan...", "notFound": "<PERSON><PERSON> tidak di<PERSON>n", "close": "<PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "unit": "Unit", "baseUnitCost": "<PERSON><PERSON><PERSON>", "baseUnitQuantity": "<PERSON><PERSON><PERSON>", "costPerUnit": "Biaya per Unit", "supplier": "Supplier", "noSupplier": "Tidak ada info supplier", "notes": "Catatan", "productUsageTitle": "<PERSON><PERSON><PERSON><PERSON> ({{count}})", "notUsed": "Tidak digunakan di produk manapun", "notUsedDesc": "Bahan ini belum digunakan di produk manapun", "table": {"product": "Produk", "usagePerCup": "<PERSON><PERSON><PERSON><PERSON> per <PERSON>gkir", "costPerCup": "<PERSON><PERSON><PERSON> per <PERSON>", "note": "Catatan", "status": "Status"}, "badge": {"active": "Aktif", "inactive": "<PERSON><PERSON><PERSON><PERSON>"}, "costAnalysis": "<PERSON><PERSON><PERSON>", "totalProductsUsing": "Total Produk Menggunakan", "avgUsagePerCup": "<PERSON>a-<PERSON>a <PERSON> per Cangkir", "avgCostPerCup": "<PERSON>a-rata <PERSON> per <PERSON>gkir"}}, "menus": {"management": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Buat dan kelola menu kedai kopi beserta harga produknya", "createMenu": "<PERSON><PERSON><PERSON>u", "tabs": {"menus": "<PERSON><PERSON>", "branches": "Cabang"}, "menuFiltersView": "Filter & Tampilan <PERSON>u", "showInactive": "Tampilkan menu tidak aktif", "noMenus": "Tidak ada menu", "noMenusHint": "<PERSON><PERSON> dengan membuat menu pertama Anda", "branch": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> lokasi kedai kopi dan penugasan menunya", "create": "Buat Cabang", "filtersTitle": "Filter & Tampilan Cabang", "showInactive": "Tampilkan cabang tidak aktif"}, "editMenuSheet": {"title": "<PERSON>", "description": "Perbarui detail dan pengaturan menu"}, "createMenuSheet": {"title": "Buat Menu Baru", "description": "Tambahkan menu baru ke katalog kedai kopi Anda"}, "assignBranchesSheet": {"title": "Tetapkan Cabang", "description": "Pilih cabang tempat menu ini tersedia"}, "menuDetailsSheet": {"title": "Det<PERSON>", "description": "Ke<PERSON>la produk dan pengaturan untuk menu ini"}, "branchMenuSheet": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Kelola penugasan menu untuk cabang ini"}, "createBranchSheet": {"title": "Buat Cabang Baru", "description": "Tambahkan lokasi cabang kedai kopi"}, "editBranchSheet": {"title": "<PERSON>", "description": "Perbarui detail dan pengaturan cabang"}}, "branchAssignment": {"loading": "Memuat cabang...", "assigningTo": "Menetapkan cabang untuk:", "selectBranches": "<PERSON><PERSON><PERSON> *", "searchPlaceholder": "Cari cabang...", "noBranchesFound": "Cabang tidak di<PERSON>n.", "help": "Pilih cabang tempat menu ini tersedia", "selectedBranches": "Cabang <PERSON>", "remove": "Hapus", "assign": "Tetapkan Cabang", "cancel": "<PERSON><PERSON>", "helpTitle": "Penetapan <PERSON>ng:", "helpItems": ["Menu dapat ditetapkan ke beberapa cabang", "Setiap cabang dapat memiliki banyak menu", "Gunakan untuk mengontrol menu di setiap lokasi", "Anda dapat menetapkan target penjualan per cabang nanti"], "assigning": "Menetapkan..."}, "branchForm": {"name": "<PERSON><PERSON> *", "location": "<PERSON><PERSON>", "businessHours": "Jam Operasional", "startTime": "<PERSON> *", "endTime": "<PERSON> *", "activeStatus": "Status Aktif", "note": "Catatan", "placeholders": {"name": "<PERSON><PERSON><PERSON>n nama cabang", "location": "Masukkan lokasi atau alamat cabang", "note": "Tambahkan catatan"}, "help": {"name": "<PERSON><PERSON><PERSON><PERSON>f untuk cabang", "location": "Lokasi fisik atau alamat cabang", "businessHours": "Atur jam operasional cabang", "businessHoursInfo": "Jam operasional digunakan untuk analisis target", "note": "Catatan opsional"}, "activeHelp": "Cabang aktif dan dapat digunakan", "inactiveHelp": "Cabang tidak aktif", "buttons": {"create": "Buat Cabang", "update": "<PERSON><PERSON><PERSON>", "creating": "Membuat...", "updating": "Memperbarui...", "cancel": "<PERSON><PERSON>"}, "helpTitle": "<PERSON><PERSON><PERSON><PERSON>:", "helpItems": ["Cabang merepresentasikan lokasi fisik tempat Anda melayani pelanggan", "Setiap cabang dapat memiliki beberapa menu", "Gunakan cabang untuk mengatur penawaran dan target per lokasi", "Cabang tidak aktif disembunyikan namun bisa diaktifkan kembali"]}, "menuForm": {"fields": {"name": "<PERSON><PERSON> *", "description": "<PERSON><PERSON><PERSON><PERSON>", "note": "Catatan", "activeStatus": "Status Aktif"}, "placeholders": {"name": "Ma<PERSON>kkan nama menu", "description": "Masukkan deskripsi menu", "note": "Tambahkan catatan"}, "help": {"name": "<PERSON><PERSON> deskriptif untuk menu (mis. \"Menu Pagi\", \"Menu Sore\")", "description": "Deskripsi opsional tentang menu dan tujuannya", "note": "Catatan opsional untuk referensi"}, "statusHelp": {"active": "Menu aktif dan dapat digunakan", "inactive": "Menu tidak aktif dan disembunyikan"}, "buttons": {"create": "<PERSON><PERSON><PERSON>u", "update": "<PERSON><PERSON><PERSON>", "creating": "Membuat...", "updating": "Memperbarui...", "cancel": "<PERSON><PERSON>"}, "nextSteps": {"title": "Langkah selanju<PERSON>nya:", "step1": "<PERSON><PERSON><PERSON> membuat menu, <PERSON>a dapat menambah produk beserta harga", "step2": "Tetapkan menu ke cabang tertentu", "step3": "Atur target penjualan harian untuk tiap cabang", "step4": "Gunakan menu dalam operasi harian <PERSON>"}}, "branchList": {"noBranches": "Belum ada cabang", "noBranchesHint": "Buat cabang untuk mulai menetapkan menu"}}, "salesTargets": {"errors": {"loadBranches": "Gagal memuat cabang. <PERSON><PERSON>an segarkan halaman.", "loadTargets": "Gagal memuat target penjualan. Coba lagi.", "updateTarget": "<PERSON><PERSON> me<PERSON> target. Coba lagi.", "loadMonthlyTargets": "Gagal memuat target bulanan. Coba lagi."}, "dailyPage": {"title": "Target Penjualan Harian", "description": "Tetapkan dan kelola target penjualan harian untuk produk menu", "selectBranchPlaceholder": "<PERSON><PERSON><PERSON> cabang", "summary": {"totalItems": "Total Item", "estRevenue": "Perk. Pendapatan", "activeProducts": "Produk Aktif", "activeMenus": "Menu Aktif"}, "noActiveMenusTitle": "Tidak Ada Menu Aktif", "noActiveMenusDescription": "Tidak ada menu aktif untuk cabang dan tanggal ini.", "selectBranchTitle": "<PERSON><PERSON><PERSON>", "selectBranchDescription": "<PERSON><PERSON>an pilih cabang untuk melihat dan mengelola target penjualan"}, "calendar": {"noBusiness": "<PERSON><PERSON>h bisnis untuk melihat target penjualan", "noBranch": "Pilih cabang untuk melihat target penjualan", "invalidBranch": "Cabang tidak tersedia di bisnis saat ini", "totalTargets": "Total Target", "estRevenue": "Perk. Pendapatan", "items": "{{count}} item", "noMenusForDate": "Tidak ada menu aktif pada tanggal ini", "menusCount": "Menu ({{count}})", "menuSummary": "{{products}} produk • {{total}} target item"}, "menuSection": {"productsBadge": "{{active}}/{{total}} produk", "itemsBadge": "{{count}} item", "noProductsTitle": "Tidak Ada Produk", "noProductsDescription": "Menu ini belum memiliki produk.", "table": {"product": "Produk", "price": "<PERSON><PERSON>", "targetQty": "Target Qty", "estRevenue": "Perk. Pendapatan", "actions": "<PERSON><PERSON><PERSON>"}, "summary": {"menuTotal": "Total Menu", "activeLabel": "{{count}} aktif"}}, "productRow": {"notePlaceholder": "Tambahkan catatan (opsional)...", "saveTooltip": "Simpan (Ctrl+Enter)", "cancelTooltip": "Batal (Esc)", "editTooltip": "Edit target", "noteLabel": "Catatan: {{note}}", "targetMinError": "Target harus 0 atau lebih"}}, "operations": {"page": {"title": "Operasional", "description": "Ubah target penjualan harian menjadi pipeline operasional dengan pelacakan dan analitik real-time"}, "tabs": {"recording": "Pencatatan Penjualan", "analysis": "<PERSON><PERSON><PERSON>", "analytics": "<PERSON><PERSON><PERSON>", "projections": "Proyeksi"}, "salesRecording": {"filtersTitle": "Filter", "date": "Tanggal", "branch": "Cabang", "allBranches": "<PERSON><PERSON><PERSON> cabang", "summary": {"totalSales": "Total Penjualan", "totalRevenue": "Total Pendapatan", "avgOrderValue": "<PERSON>a-<PERSON>a <PERSON>", "topProduct": "<PERSON><PERSON><PERSON>", "none": "Tidak ada", "sold": "terjual {{count}}"}, "recordsTitle": "Catatan Penjualan", "recordsDescription": "Penjualan terbaru untuk {{date}} di {{branch}}", "loading": "Memuat catatan penjualan...", "noRecords": "Tidak ada catatan penjualan untuk tanggal ini", "addButton": "Tambah penjualan", "sheetTitle": "Catat Penjualan Baru", "sheetDescription": "Catat penjualan baru dengan detail produk, jum<PERSON>, dan harga"}, "salesRecordForm": {"fields": {"branch": "Cabang", "menu": "<PERSON><PERSON>", "product": "Produk", "saleDate": "Tanggal Penju<PERSON>", "saleTime": "<PERSON><PERSON><PERSON>", "quantity": "<PERSON><PERSON><PERSON>", "unitPrice": "<PERSON><PERSON> ({{currency}})", "totalAmount": "Total", "note": "Catatan (Opsional)"}, "placeholders": {"branch": "<PERSON><PERSON><PERSON> cabang", "menu": "Pilih menu", "product": "<PERSON><PERSON><PERSON> produk", "pickDate": "<PERSON><PERSON><PERSON> tanggal", "note": "Catatan tambahan tentang penjualan ini..."}, "buttons": {"setCurrentTime": "<PERSON><PERSON><PERSON>", "record": "Catat Penjualan"}}, "targetAnalysis": {"title": "Analisis Target vs Aktual", "description": "Perbandingan real-time antara target penju<PERSON> harian dan kinerja aktual", "date": "Tanggal", "branch": "Cabang", "allBranches": "<PERSON><PERSON><PERSON> cabang", "loading": "Memuat analisis...", "noTargets": "Tidak ada target untuk tanggal ini", "progress": "Progres", "expected": "Harapan: {{value}}%", "timeRemaining": "Sisa waktu: {{value}}", "target": "Target", "actual": "Aktual", "variance": "<PERSON><PERSON><PERSON>", "salesCount": "<PERSON><PERSON><PERSON>: {{count}} item", "unknownMenu": "Menu Tidak Dikenal", "unknownBranch": "Cabang Tidak Dikenal"}, "analytics": {"filtersTitle": "<PERSON><PERSON>", "filtersDescription": "Saring data analitik berdasarkan rentang waktu dan cabang", "timeRange": "Rentang <PERSON>", "range": {"today": "<PERSON> ini", "week": "7 <PERSON>", "month": "30 <PERSON>", "custom": "Ren<PERSON><PERSON>"}, "startDate": "<PERSON><PERSON>", "endDate": "<PERSON><PERSON>", "branch": "Cabang", "allBranches": "<PERSON><PERSON><PERSON> cabang", "tabs": {"hourly": "<PERSON><PERSON><PERSON>", "products": "Popularitas Produk", "progress": "<PERSON><PERSON><PERSON>"}, "hourly": {"title": "Analisis Profitabilitas Per Jam", "description": "Rincian pendapatan dan profit per jam"}, "products": {"title": "Popularitas Produk", "description": "<PERSON>duk terlaris berdasarkan jumlah dan pendapatan", "byQuantity": "<PERSON><PERSON><PERSON><PERSON>", "byRevenue": "Berdasarkan Pendapatan", "noData": "Tidak ada data produk"}, "progress": {"title": "Pelacakan Progres Harian", "description": "Progres real-time menuju target penju<PERSON> harian", "requireSingleDate": "Pelacakan progres memerlukan pilihan satu tanggal", "noTargets": "Tidak ada target penjualan untuk tanggal ini", "selectSpecificDate": "<PERSON><PERSON><PERSON> tanggal khusus untuk melihat progres harian", "createTargets": "Buat target penjualan untuk {{date}} untuk melacak progres"}, "loadingChart": "Memuat grafik..."}, "projections": {"title": "Proyeksi Pendapatan Lanjutan", "description": "Proyeksi multi-produk berdasarkan kinerja penjualan aktual dan target", "branch": "Cabang", "selectBranch": "<PERSON><PERSON><PERSON> cabang", "menu": "<PERSON><PERSON>", "selectMenu": "Pilih menu", "referenceDate": "<PERSON>gal <PERSON>", "daysPerMonth": "<PERSON>", "useActual": "Gunakan Data Penjualan Aktual", "usingActual": "Menggunakan penjualan aktual", "usingTargets": "<PERSON><PERSON> target", "loading": "Memuat proyeksi...", "selectBranchMenu": "<PERSON><PERSON>h cabang dan menu untuk melihat proyeksi", "noProducts": "Tidak ada produk untuk menu yang dipilih", "summary": {"dailyRevenue": "<PERSON><PERSON><PERSON><PERSON>", "dailyProfit": "<PERSON><PERSON>", "monthlyRevenue": "Pendapatan Bulanan", "avgPerformance": "<PERSON><PERSON>-<PERSON>a <PERSON>"}, "tableTitle": "Proyeksi Produk", "tableDescription": "<PERSON><PERSON>ja produk individu dan proyeksi pendapatan", "table": {"product": "Produk", "targetQty": "Target Qty", "actualSales": "Penjualan Aktual", "performance": "Performa", "unitPrice": "<PERSON><PERSON>", "dailyRevenue": "<PERSON><PERSON><PERSON><PERSON>", "dailyProfit": "<PERSON><PERSON>", "monthlyRevenue": "Pendapatan Bulanan", "monthlyProfit": "<PERSON><PERSON>"}}}, "accounting": {"quickActions": {"title": "Tindakan Cepat", "description": "Tugas akuntansi umum dan alat manajemen keuangan", "badges": {"income": "<PERSON><PERSON><PERSON><PERSON>", "expense": "<PERSON><PERSON><PERSON><PERSON>", "capital": "Modal", "asset": "<PERSON><PERSON>"}, "actions": {"recordSale": {"title": "Catat Penjualan", "description": "Tambah transaksi pendapatan penjualan"}, "addExpense": {"title": "Tambah Pengeluaran", "description": "Catat biaya operasional"}, "capitalInvestment": {"title": "Investasi Modal", "description": "Catat injeksi modal"}, "assetPurchase": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Tambah aset tetap"}, "inventory": {"title": "Inventori", "description": "<PERSON><PERSON>la stok & pem<PERSON>ian"}, "cogsCalculator": {"title": "Kalkulator HPP", "description": "Hitung biaya produk"}, "reports": {"title": "<PERSON><PERSON><PERSON>", "description": "Lihat laporan detail"}}, "sheet": {"title": "Tambah Transaksi", "description": "Catat transaksi keuangan baru"}, "gettingStarted": {"title": "<PERSON><PERSON><PERSON>", "items": ["Catat transaksi penjualan dan pemasukan", "Tambahkan biaya operasional dan berulang", "Lacak investasi modal dan aset", "<PERSON><PERSON><PERSON> kesehatan keuangan dan arus kas"]}}, "dashboard": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tagline": "Manajemen transaksi keuangan komprehensif", "selectBusiness": "<PERSON><PERSON>an pilih bisnis untuk melihat informasi akuntansi.", "description": "Manajemen keuangan untuk {{business}}", "tryAgain": "<PERSON><PERSON>", "filters": "Filter", "refresh": "<PERSON><PERSON>", "export": "Ekspor", "updated": "Di<PERSON><PERSON><PERSON>: {{time}}", "totalTransactions": "Total Transaksi", "allTypes": "<PERSON><PERSON><PERSON> j<PERSON>", "salesIncome": "Pendapatan Penjualan", "salesRevenue": "Pendapatan dari pen<PERSON>", "operatingExpenses": "Biaya Operasional", "operationalCosts": "Biaya operasional", "netPosition": "Posisi Bersih", "incomeMinusExpenses": "Pendapatan dikurangi pengeluaran", "recentTransactions": "Transaksi Terbaru", "allTransactions": "<PERSON><PERSON><PERSON>i k<PERSON>angan di bisnis Anda", "filtered": "Tersaring", "clear": "<PERSON><PERSON><PERSON><PERSON>"}, "summary": {"financialHealth": "<PERSON><PERSON><PERSON><PERSON>:", "healthy": "<PERSON><PERSON>", "needsAttention": "<PERSON><PERSON>", "profitMargin": "<PERSON><PERSON>", "totalIncome": "Total Pemasukan", "sales": "Penjualan", "capital": "Modal", "totalExpenses": "Total Pengeluaran", "operating": "Operasional", "fixed": "Tetap", "variable": "Variabel", "netIncome": "Lab<PERSON> Be<PERSON>", "grossProfit": "<PERSON><PERSON>", "operatingProfit": "Laba Operasional", "cashFlow": "<PERSON><PERSON>", "burnRate": "Burn Rate", "margin": "<PERSON><PERSON>", "costStructure": "Struktur Biaya", "variableCosts": "Biaya Variabel", "fixedCosts": "Biaya <PERSON>", "operatingCosts": "Operasional", "revenueMix": "Komposisi Pendapatan", "salesRevenue": "Pendapatan Penjualan", "capitalInvestment": "Investasi Modal", "performance": "Performa", "status": "Status"}, "transactions": {"searchPlaceholder": "<PERSON>i trans<PERSON>i...", "date": "Tanggal", "type": "Tipe", "description": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON><PERSON>", "noMatch": "Tidak ada transaksi yang cocok dengan pencarian <PERSON>.", "none": "Tidak ada transaksi di<PERSON>.", "viewDetails": "<PERSON><PERSON>", "edit": "Ubah", "delete": "Hapus", "showing": "Menampilkan {{count}} dari {{total}} transaksi{{search}}"}, "filters": {"title": "<PERSON><PERSON>", "clearAll": "<PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON>", "searchPlaceholder": "<PERSON><PERSON>, kate<PERSON>i, catatan...", "startDate": "<PERSON><PERSON>", "endDate": "<PERSON><PERSON>", "pickStart": "<PERSON><PERSON><PERSON> tanggal mulai", "pickEnd": "<PERSON><PERSON><PERSON> tanggal akhir", "minAmount": "Ju<PERSON>lah <PERSON> ({{currency}})", "maxAmount": "<PERSON><PERSON><PERSON> ({{currency}})", "types": "<PERSON><PERSON><PERSON>", "status": "Status", "categories": "<PERSON><PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON><PERSON>", "apply": "Terap<PERSON> Filter", "active": "Filter Aktif", "searchLabel": "Pencarian: \"{{term}}\"", "dateRange": "<PERSON><PERSON><PERSON>", "amountRange": "<PERSON><PERSON><PERSON>"}, "health": {"title": "<PERSON><PERSON><PERSON><PERSON>", "loading": "<PERSON><PERSON><PERSON> in<PERSON> k<PERSON>an...", "noData": "Tidak ada data untuk analisis kesehatan", "score": "<PERSON><PERSON>", "overallAssessment": "<PERSON><PERSON><PERSON> kesehatan keuangan bisnis secara keseluruhan", "keyMetrics": "<PERSON><PERSON>", "quickStats": "Statistik Singkat", "profitability": "Profitabilitas", "liquidity": "Likuiditas", "efficiency": "E<PERSON><PERSON>nsi", "growth": "<PERSON><PERSON><PERSON><PERSON>", "profitMargin": "<PERSON><PERSON>", "burnRate": "Burn Rate B<PERSON>nan", "cashFlow": "<PERSON><PERSON>", "netIncome": "Lab<PERSON> Be<PERSON>", "alertsTitle": "Peringatan & Rekomendasi", "badges": {"excellent": "Sangat Baik", "good": "Baik", "fair": "<PERSON><PERSON><PERSON>", "poor": "Buruk"}, "noDataPrompt": "Catat beberapa transaksi untuk melihat indikator kesehatan bisnis.", "alerts": {"negativeProfitMargin": {"title": "<PERSON><PERSON> Laba Negatif", "message": "<PERSON><PERSON><PERSON> kerugian {{value}}%.", "recommendation": "<PERSON><PERSON><PERSON> pengeluaran dan pertimbangkan menaikkan harga atau menu<PERSON>an biaya."}, "lowProfitMargin": {"title": "<PERSON><PERSON>", "message": "Margin laba {{value}}% berada di bawah tingkat sehat.", "recommendation": "Usahakan margin laba minimal 10-15% untuk pertumbuhan berkelanjutan."}, "excellentProfitability": {"title": "Profitabilitas Sangat Baik", "message": "Margin laba kuat sebesar {{value}}%.", "recommendation": "Pertimbangkan reinvestasi laba untuk peluang pertum<PERSON>han."}, "negativeCashFlow": {"title": "<PERSON><PERSON> Negatif", "message": "<PERSON><PERSON> kas keluar sebesar {{value}}.", "recommendation": "Fokus pada peningkatan pendapatan dan pengelolaan pengeluaran."}, "lowCashReserves": {"title": "Cadangan Kas Rendah", "message": "Arus kas hanya menutup kurang dari 3 bulan pengeluaran.", "recommendation": "Bangun cadangan kas untuk kestabilan keuangan."}, "highVariableCosts": {"title": "Biaya V<PERSON>", "message": "Biaya variabel sebesar {{value}}% dari pendapatan.", "recommendation": "Tinjau biaya pemasok dan efisiensi operasional."}, "highFixedCosts": {"title": "Biaya Tetap <PERSON>i", "message": "Biaya tetap sebesar {{value}}% dari pendapatan.", "recommendation": "Pertimbangkan mengurangi biaya tetap atau meningkatkan pendapatan."}, "growthOpportunity": {"title": "<PERSON><PERSON><PERSON>", "message": "Posisi keuangan yang kuat memungkinkan ekspansi.", "recommendation": "Pertimbangkan investasi di pema<PERSON>, inventori, atau lokasi baru."}}, "sheets": {"healthScore": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Me<PERSON>ami metodologi penilaian kesehatan bisnis secara keseluruhan", "cards": {"whatIs": "Apa itu S<PERSON> Ke<PERSON>hat<PERSON>?", "scoreRanges": "Rentang Skor & Artinya", "scoringFactors": "<PERSON><PERSON><PERSON>", "currentScore": "Skor Anda Saat Ini", "componentScores": "Skor Komponen", "improving": "Meningkatkan Skor Anda"}}, "keyMetrics": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Memahami empat pilar penilaian kesehatan keuangan", "cards": {"whatAre": "Apa itu <PERSON>?", "profitability": "1. Profitabilitas", "liquidity": "2. <PERSON><PERSON><PERSON><PERSON>", "efficiency": "3. <PERSON><PERSON><PERSON><PERSON><PERSON>", "growth": "4. <PERSON><PERSON><PERSON><PERSON>", "improving": "<PERSON><PERSON><PERSON><PERSON>"}}, "quickStats": {"title": "<PERSON><PERSON><PERSON>an Statistik Singkat", "description": "<PERSON><PERSON><PERSON> indikator kinerja utama secara sekilas", "cards": {"whatAre": "Apa itu Statistik Singkat?", "profitMargin": "1. <PERSON><PERSON><PERSON>", "burnRate": "2. <PERSON>", "cashFlow": "3. <PERSON><PERSON>", "netIncome": "4. <PERSON><PERSON>", "benchmarks": "Benchmark Kedai Kopi", "using": "Menggunakan Statistik Singkat untuk Keputusan"}}}}, "transactionForm": {"fields": {"transactionType": "<PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON><PERSON> ({{currency}})", "description": "<PERSON><PERSON><PERSON><PERSON>", "date": "Tanggal", "quantity": "Kuantitas", "unitPrice": "<PERSON><PERSON> ({{currency}})", "frequency": "<PERSON><PERSON><PERSON><PERSON>", "usefulLife": "<PERSON><PERSON> (Tahun)", "unit": "Satuan", "baseUnitCost": "<PERSON><PERSON><PERSON> ({{currency}})", "usagePerCup": "Penggunaan Per Cup", "note": "Catatan (Opsional)"}, "placeholders": {"transactionType": "<PERSON><PERSON><PERSON> tipe transaksi", "category": "<PERSON><PERSON><PERSON> ka<PERSON>i", "description": "Deskrip<PERSON>", "date": "<PERSON><PERSON><PERSON> tanggal", "frequency": "<PERSON><PERSON><PERSON>", "unit": "misal: ml, g, pcs", "note": "Catatan tambahan mengenai transaksi"}, "descriptions": {"unitPrice": "Harga per unit terjual", "usefulLife": "Digunakan untuk perhitungan depresiasi", "baseUnitCost": "Biaya per satuan dasar (misal per liter, per kg)", "usagePerCup": "<PERSON><PERSON><PERSON> yang digunakan per sajian"}, "frequency": {"monthly": "Bulanan", "yearly": "<PERSON><PERSON><PERSON>"}, "other": "<PERSON><PERSON><PERSON>", "buttons": {"create": "Buat Transaksi", "update": "<PERSON><PERSON><PERSON>"}}}, "people": {"page": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> karyawan dan penugasan point-of-contact", "noBusiness": {"title": "Tidak Ada Bisnis <PERSON>", "description": "<PERSON><PERSON>an pilih bisnis untuk mengelola karyawan"}}, "employees": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>la informasi dan detail karyawan", "addButton": "<PERSON><PERSON> Karyawan", "editTitle": "<PERSON>", "createTitle": "Tambah Karyawan Baru", "editDescription": "Perbarui informasi dan detail karyawan", "createDescription": "Tambahkan karyawan baru dengan informasi lengkap", "listTitle": "<PERSON><PERSON><PERSON>", "listDescription": "<PERSON><PERSON><PERSON> ka<PERSON> di organisasi Anda", "loading": "<PERSON><PERSON><PERSON> karyawan...", "empty": "<PERSON>um ada karyawan. <PERSON><PERSON><PERSON> karyawan pertama Anda.", "table": {"employee": "<PERSON><PERSON><PERSON>", "position": "<PERSON><PERSON><PERSON>", "department": "Departemen", "status": "Status", "hireDate": "<PERSON><PERSON>", "salary": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>"}, "badges": {"active": "Aktif", "inactive": "Tidak Aktif", "terminated": "Diberhentikan"}, "notSpecified": "Tidak ditentukan", "deleting": "Mengh<PERSON>us..."}, "toast": {"loadError": "<PERSON><PERSON> memuat karyawan", "createSuccess": "<PERSON><PERSON><PERSON> ber<PERSON><PERSON> dibuat", "createError": "<PERSON><PERSON> memb<PERSON>t karyawan", "updateSuccess": "<PERSON><PERSON><PERSON> be<PERSON><PERSON><PERSON>", "updateError": "<PERSON><PERSON> ka<PERSON>", "deleteSuccess": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "deleteError": "<PERSON><PERSON><PERSON>"}, "poc": {"sectionTitle": "Manajemen POC", "description": "Tetapkan karyawan sebagai point of contact untuk cabang", "assignButton": "Tetapkan POC", "sheetTitle": "Tetapkan Point of Contact", "sheetDescription": "Tetapkan karyawan sebagai point of contact untuk cabang", "managerTitle": "Daftar POC Saat Ini", "managerDescription": "Kelola penugasan point of contact untuk cabang", "noAssignments": "Belum ada penugasan POC. Tetapkan karyawan sebagai point of contact untuk cabang.", "table": {"employee": "<PERSON><PERSON><PERSON>", "position": "<PERSON><PERSON><PERSON>", "branch": "Cabang", "date": "<PERSON><PERSON>", "notes": "Catatan", "actions": "<PERSON><PERSON><PERSON>"}, "noNotes": "Tidak ada catatan", "removing": "Mengh<PERSON>us...", "loading": "Memuat penugasan POC..."}, "forms": {"employee": {"sections": {"personal": "Informasi Pribadi", "professional": "Informasi Profesional", "contact": "Informasi Kontak"}, "fields": {"fullName": "<PERSON><PERSON> *", "companyIdNumber": "Nomor <PERSON> *", "nationalIdNumber": "Nomor ID Nasional", "dateOfBirth": "<PERSON><PERSON>", "position": "Posisi/Jabatan *", "department": "Departemen *", "jobLevel": "Level Jabatan", "salary": "Gaji ({{currency}})", "hireDate": "Tanggal <PERSON> *", "employmentStatus": "Status Karyawan", "phone": "Nomor Telepon", "email": "<PERSON><PERSON><PERSON>", "note": "Catatan <PERSON>"}, "placeholders": {"fullName": "<PERSON><PERSON><PERSON><PERSON> nama lengkap", "companyIdNumber": "mis. EMP001", "nationalIdNumber": "mis. 1234567890123456", "position": "mis. <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "department": "mis. Operasional, Manajemen", "jobLevel": "mis. Junior, Senior, <PERSON><PERSON><PERSON>", "salary": "mis. 5000000", "phone": "mis. +62812345678", "email": "mis. <EMAIL>", "note": "Informasi tambahan tentang karyawan..."}, "pickDate": "<PERSON><PERSON><PERSON> tanggal", "buttons": {"cancel": "<PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "saving": "Menyimpan..."}}, "poc": {"title": "Tetapkan Point of Contact", "fields": {"employee": "<PERSON><PERSON><PERSON> *", "branch": "Cabang *", "assignedDate": "<PERSON><PERSON> *", "note": "Catatan"}, "placeholders": {"searchEmployees": "<PERSON><PERSON> ka<PERSON>wan...", "searchBranches": "Cari cabang...", "notes": "Catatan tambahan tentang penugasan ini..."}, "selectEmployee": "<PERSON><PERSON><PERSON>", "selectBranch": "<PERSON><PERSON><PERSON> cabang", "pickDate": "<PERSON><PERSON><PERSON> tanggal", "noEmployee": "<PERSON><PERSON><PERSON> tidak di<PERSON>n.", "noBranch": "Cabang tidak di<PERSON>n.", "loading": "<PERSON><PERSON><PERSON> karyawan dan cabang...", "buttons": {"assign": "Tetapkan POC", "assigning": "Menetapkan...", "cancel": "<PERSON><PERSON>"}}}}, "analytics": {"page": {"title": "<PERSON><PERSON><PERSON>", "description": "Proyeksi pendapatan dan analisis profit berbasis menu yang diorganisir per struktur menu"}, "controls": {"daysPerMonth": "<PERSON>"}, "loading": "Memuat analitik...", "table": {"title": "Proyeksi & Profit Berdasarkan Menu", "info": "{{menus}} menu • {{products}} produk • Target individual", "loadingTargets": "Memuat target...", "refresh": "<PERSON><PERSON>", "noDataTitle": "Tidak ada data menu", "noDataDescription": "Pastikan Anda memiliki:\n• Menu aktif beserta produk\n• <PERSON>duk dengan bahan dan data HPP\n• Harga produk ditentukan di menu", "headers": {"menuProduct": "Menu / Produk & Target", "cogs": "HPP/Unit", "price": "<PERSON><PERSON>", "dailyRevenue": "<PERSON><PERSON><PERSON><PERSON>", "weeklyRevenue": "Pendapatan Mingguan", "monthlyRevenue": "Pendapatan Bulanan", "dailyProfit": "<PERSON><PERSON>", "weeklyProfit": "Profit <PERSON>", "monthlyProfit": "Profit <PERSON>"}, "targetPerDay": "Target/hari:", "units": "unit"}, "explanation": {"defaultTitle": "<PERSON><PERSON><PERSON>", "clickPrompt": "<PERSON>lik baris produk untuk melihat perhitungan dan analisis detail.", "understandingTitle": "Memahami Analitik Berbasis Menu", "understanding": {"menuStructure": "Struktur Menu: <PERSON><PERSON><PERSON> diorganisir per menu dengan produk di bawahnya", "menuPrice": "Harga Menu: <PERSON>rga yang ditetapkan untuk produk ini pada menu tersebut", "cogs": "HPP/Unit: <PERSON><PERSON>ya bahan per unit", "revenue": "Pendapatan: Total pemasukan penjualan (Harga Menu × <PERSON><PERSON><PERSON>)", "profit": "Profit: Pendapatan dikurangi HPP (tidak termasuk biaya tetap)"}, "benefitsTitle": "Manfaat Berbasis Menu", "benefits": {"seePerformance": "• Lihat performa produk yang sama di menu berbeda", "comparePricing": "• Bandingkan strategi harga di tiap menu", "identifyProfitable": "• Identifikasi menu yang paling menguntungkan", "optimizePlacement": "• Optimalkan penempatan dan harga produk"}, "menuContext": "Konteks Menu", "unitEconomics": "<PERSON><PERSON><PERSON>", "dailyProjections": "<PERSON><PERSON><PERSON><PERSON>", "monthlyProjections": "Proyeks<PERSON>", "calculationFormula": "<PERSON><PERSON><PERSON>", "formula": {"revenue": "Pendapatan = <PERSON><PERSON> × <PERSON><PERSON><PERSON> × <PERSON>", "profit": "Profit = (<PERSON><PERSON> Menu - HPP) × <PERSON><PERSON><PERSON> × <PERSON>", "margin": "Margin = (Profit ÷ Pendapatan) × 100%"}, "performanceInsights": "Insight Performa", "insights": {"excellent": "✓ Margin profit sangat bagus pada menu ini", "good": "✓ Margin profit bagus pada menu ini", "moderate": "⚠ Margin profit sedang pada menu ini", "low": "⚠ Margin profit rendah pada menu ini", "negative": "⚠ Margin negatif - tinjau kembali harga pada menu ini"}, "compareHint": "💡 Bandingkan performa produk ini di berbagai menu untuk optimasi harga", "inMenu": "di {{menuName}}", "daysPerMonth": "Hari per <PERSON><PERSON>:", "totalQuantity": "Total Kuantitas:", "targetQuantity": "Target Kuantitas:", "revenueLabel": "Pendapatan:", "profitLabel": "Profit:", "profitMargin": "Margin Profit:"}}, "warehouse": {"sheet": {"triggerAria": "Tambah item ke gudang", "title": "Tambah ke Gudang", "description": "Tambah<PERSON> bahan ke stok gudang dengan memilih produk dan bahannya"}, "form": {"header": "Tambah Item ke Gudang", "productQuantity": "Produk & Jumlah", "fields": {"product": "Produk", "numberOfCups": "<PERSON><PERSON><PERSON> ya<PERSON>", "smartStock": "Perhitungan Stok Cerdas", "batchNote": "<PERSON><PERSON><PERSON> (Opsional)"}, "placeholders": {"product": "<PERSON><PERSON><PERSON> produk untuk menghitung bahan...", "numberOfCups": "<PERSON><PERSON><PERSON><PERSON> jumlah cangkir...", "batchNote": "Tambahkan catatan untuk batch gudang ini..."}, "autoCalcHint": "<PERSON><PERSON><PERSON> bahan akan dihitung otomatis berdasarkan resep produk", "smartStockHint": "<PERSON>ya menambah kekurangan agar mencapai jumlah target berdasarkan stok saat ini", "calculated": {"titleSmart": "Perhitungan Stok Cerdas", "titleDefault": "<PERSON><PERSON> ya<PERSON>", "forCups": "untuk {{cups}} cangkir {{product}}", "explanationSmart": "<PERSON><PERSON> jumlah kekurangan yang akan ditambahkan untuk mencapai target.", "explanation": "<PERSON><PERSON><PERSON> dan biaya dihitung otomatis berdasarkan resep produk.", "roundedInfo": "Jumlah aktual dibulatkan ke satuan pembelian minimum se<PERSON><PERSON> kemasan bahan."}, "table": {"ingredient": "<PERSON><PERSON>", "currentStock": "Stok Saat Ini", "usagePerCup": "<PERSON><PERSON><PERSON><PERSON> per <PERSON>", "requiredQuantity": "<PERSON><PERSON><PERSON>", "theoreticalNeed": "<PERSON><PERSON><PERSON><PERSON>", "actualQuantityToAdd": "Jumlah Aktual Ditambahkan", "purchaseInfo": "<PERSON><PERSON>", "costPerUnit": "Biaya per Sa<PERSON>", "totalCost": "Total Biaya", "sufficient": "<PERSON><PERSON><PERSON>", "noPurchaseNeeded": "Tidak perlu membeli", "roundedUpFrom": "Dibulatkan dari {{quantity}} {{unit}}"}, "totalCost": {"label": "Total Biaya Batch:", "costForDeficit": "Biaya kekurangan untuk mencapai {{cups}} cangkir {{product}}", "costFor": "Biaya untuk {{cups}} cangkir {{product}}", "sufficient": "Stok saat ini cukup untuk jumlah yang dipilih"}, "buttons": {"submitAdding": "Menambahkan ke Gudang...", "submitNoItems": "Tidak Ada Item yang <PERSON>lu Ditambahkan", "submitAdd": "Tambah {{count}} <PERSON><PERSON> ke <PERSON>ng"}, "messages": {"failedLoadProducts": "Gagal memuat produk", "failedLoadIngredients": "<PERSON><PERSON> memuat bahan produk", "noIngredients": "Tidak ada bahan untuk produk yang dipilih", "stockSufficient": "Stok saat ini cukup untuk jumlah yang dipilih. Tidak ada item yang perlu ditambahkan.", "successAdd": "<PERSON><PERSON><PERSON><PERSON> men<PERSON>bahkan {{count}} bahan ke batch gudang #{{batch}} untuk {{cups}} cangkir {{product}}", "failedAdd": "Gagal menambahkan item ke gudang"}}, "stock": {"loading": "Memuat level stok...", "error": "Gagal memuat level stok", "title": "Level Stok Saat Ini", "description": "Level persediaan real-time dan batas stok rendah", "noData": {"title": "Tidak Ada Data Stok", "description": "Level stok akan muncul setelah Anda menambahkan item ke gudang."}, "alerts": "Peringatan Stok Rendah ({{count}})", "alert": {"remaining": "{{count}} {{unit}} tersisa", "threshold": "Batas: {{threshold}} {{unit}}"}, "reservation": {"negative": "<PERSON><PERSON><PERSON> reservasi tidak boleh negatif", "updated": "Reservasi berhasil diperbarui. Stok tersedia: {{stock}}", "failed": "<PERSON><PERSON> me<PERSON><PERSON> reservasi", "unexpected": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat memperbarui reservasi", "reserved": "<PERSON><PERSON><PERSON><PERSON> mereservasi {{quantity}} {{unit}}. Stok tersedia: {{stock}}", "reserveFailed": "<PERSON><PERSON>i stok", "reserveUnexpected": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat mereservasi stok", "unreserved": "Berhasil melepas reservasi {{quantity}} {{unit}}. Stok tersedia: {{stock}}", "unreserveFailed": "Gagal melepas reservasi stok", "unreserveUnexpected": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat melepas reservasi stok"}, "table": {"ingredient": "<PERSON><PERSON>", "currentStock": "Stok Saat Ini", "reserved": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "available": "Tersedia", "status": "Status", "lowStockAlert": "Peringatan Stok Rendah", "actions": "<PERSON><PERSON><PERSON>", "low": "Stok Rendah", "good": "Baik", "moderate": "Sedang", "editReservation": "Ubah reservasi", "editThreshold": "Ubah batas stok rendah", "releaseReservations": "Lepas<PERSON> semua reservasi", "quickReserve": "Reservasi cepat {{quantity}} {{unit}}"}, "summary": {"totalIngredients": "Total Bahan", "lowStockItems": "Item Stok Rendah", "wellStocked": "Stok Aman"}}, "batchList": {"noBatches": {"title": "Belum Ada Batch G<PERSON>ng", "description": "Anda belum menambahkan item apapun ke gudang.", "hint": "<PERSON><PERSON><PERSON> Kalkulator HPP untuk membuat batch gudang pertama."}, "title": "<PERSON><PERSON> ({{count}})", "searchPlaceholder": "Cari batch, bahan, atau catatan...", "batchItems": "<PERSON><PERSON>", "table": {"ingredient": "<PERSON><PERSON>", "quantity": "<PERSON><PERSON><PERSON>", "unitCost": "<PERSON><PERSON><PERSON>", "totalCost": "Total Biaya"}, "items": "{{count}} item", "ingredients": "{{count}} bahan", "noResults": {"title": "Tidak Ada Hasil", "description": "Tidak ada batch yang cocok dengan kata pencarian \"{{term}}\"", "clear": "<PERSON><PERSON><PERSON><PERSON>"}}, "calendar": {"title": "<PERSON><PERSON><PERSON>", "batchesFor": "<PERSON>ch untuk {{date}}", "noActivity": {"title": "Tidak Ada Aktivitas Gudang", "description": "Belum ada batch gudang dibuat. <PERSON><PERSON><PERSON> Kalkulator HPP untuk menambah batch pertama."}, "days": {"sun": "Min", "mon": "<PERSON>", "tue": "<PERSON>l", "wed": "<PERSON><PERSON>", "thu": "<PERSON><PERSON>", "fri": "<PERSON><PERSON>", "sat": "<PERSON>b"}, "batchCount": "{{count}} batch{{plural}}"}, "management": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Ke<PERSON>la inventori dan pantau penambahan stok", "tabs": {"overview": "<PERSON><PERSON><PERSON><PERSON>", "stock": "Level Stok", "production": "Alokasi Produksi Cepat", "batches": "<PERSON><PERSON>", "calendar": "<PERSON><PERSON><PERSON>"}, "overview": {"recentBatches": "<PERSON><PERSON>", "noBatches": "Belum ada batch gudang", "addFirstBatch": "<PERSON><PERSON><PERSON> HPP untuk menambah batch pertama", "inventorySummary": "<PERSON><PERSON><PERSON>", "noInventory": "Tidak ada data inventori", "mostStockedIngredients": "Bahan Paling Banyak Stok", "viewAllBatches": "<PERSON><PERSON> ({{count}})"}}, "stats": {"totalBatches": "Total Batch", "totalItems": "Total Item", "totalValue": "Total Nilai", "latestBatch": "<PERSON><PERSON>", "none": "Tidak ada"}}, "production": {"allocation": {"loading": "<PERSON><PERSON><PERSON> alokasi produksi...", "title": "Alokasi Produksi Cepat", "description": "Alokasikan bahan untuk batch produksi dan cadangkan stok", "fields": {"product": "<PERSON><PERSON><PERSON> ya<PERSON>", "cups": "<PERSON><PERSON><PERSON> ya<PERSON>", "note": "Catatan Produ<PERSON>i (Opsional)"}, "placeholders": {"selectProduct": "<PERSON><PERSON><PERSON> produk", "cups": "<PERSON><PERSON><PERSON><PERSON> jumlah cangkir yang akan diproduksi", "note": "Tambahkan catatan tentang batch produksi ini..."}, "legacy": "Item HPP Lama", "productSuffix": " dari {{name}}", "selectedProductInfo": "{{count}} b<PERSON> di<PERSON>i", "hints": {"maxPossible": "<PERSON><PERSON><PERSON><PERSON> dengan stok saat ini: {{count}} cangkir", "noProduction": "Produksi tidak mungkin - stok semua bahan tidak mencukupi"}, "ingredientsTitle": "<PERSON><PERSON> yang akan dial<PERSON>an:", "stockSummary": {"insufficient": "Stok tidak cukup untuk {{cups}} cangkir", "useMaximum": "<PERSON><PERSON><PERSON>", "addStock": "Tambahkan stok ke gudang sebelum membuat batch produksi."}, "buttons": {"creating": "Membuat Batch Produksi...", "insufficient": "Stok Tidak Cukup untuk Produksi", "allocate": "Aloka<PERSON><PERSON> untuk {{count}} Cangkir{{product}}"}, "messages": {"invalidCups": "Ma<PERSON>kka<PERSON> jumlah cangkir yang valid untuk dialokasikan", "noValidIngredients": "Tidak ada bahan valid untuk alokasi produksi", "insufficientStock": "Stok tidak mencukupi untuk produksi: {{details}}{{maxCupsMessage}}", "success": "<PERSON>r<PERSON>il membuat Batch Produksi #{{batch}} untuk {{cups}} cangkir {{product}}. <PERSON><PERSON> dialoka<PERSON>an dari stok.", "failed": "Gagal membuat batch produksi: {{error}}", "unexpected": "<PERSON><PERSON><PERSON><PERSON> kesalahan tak terduga saat membuat batch produksi", "noteDefault": "<PERSON><PERSON><PERSON> produksi untuk {{cups}} cangkir {{product}}"}, "warningNoIngredients": "Tidak ada bahan valid. Tambahkan data penggunaan per cangkir di Kalkulator HPP.", "info": {"howTitle": "Cara kerja:", "step1": "<PERSON><PERSON><PERSON><PERSON> jumlah cangkir yang akan diproduksi", "step2": "Sistem memeriksa ketersediaan stok semua bahan", "step3": "Menghitung kebutuhan bahan berdasarkan data HPP", "step4": "Membuat batch produksi dan mencadangkan stok (jika cukup)", "step5": "Stok ditandai \"Tercadangkan\" sampai produksi selesai", "step6": "Kelola batch produksi di halaman Produksi", "stockTitle": "Validasi stok:", "stockStep1": "<PERSON><PERSON>an merah menandakan stok kurang", "stockStep2": "<PERSON>stem menyarankan jumlah produksi maksimum", "stockStep3": "Tombol dinonaktifkan jika stok tidak mencukupi"}}, "batchStatus": {"loading": "Memuat batch produksi...", "error": "Gagal memuat batch produksi: {{error}}", "title": "Status Batch Produksi", "description": "Kelola batch produksi aktif dan perbarui <PERSON>nya", "noActive": "Tidak ada batch produksi aktif. Buat dengan Alokasi Produksi Cepat.", "complete": "Se<PERSON><PERSON>", "completeTitle": "Selesaikan Batch Produksi", "completeConfirm": "Yakin menandai Batch #{{batch}} selesai? Ini akan mengurangi stok yang dialokasikan secara permanen.", "completeBatch": "<PERSON><PERSON><PERSON><PERSON>", "table": {"batch": "<PERSON><PERSON>", "status": "Status", "items": "<PERSON><PERSON>", "created": "Dibuat", "actions": "<PERSON><PERSON><PERSON>"}, "showing": {"partial": "Menampilkan {{shown}} dari {{total}} batch aktif.", "all": "Menampilkan {{shown}} batch aktif."}, "viewAll": "<PERSON><PERSON> semua {{count}} batch di halaman <PERSON>."}, "management": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Kelola batch produksi dan pantau alokasi bahan", "tabs": {"overview": "<PERSON><PERSON><PERSON><PERSON>", "batches": "<PERSON><PERSON>", "reservations": "Reservasi Stok"}, "quickActions": {"title": "Tindakan Cepat", "description": "<PERSON>gas umum manajemen produksi", "viewAllBatches": "<PERSON><PERSON>", "manageReservations": "Ke<PERSON>la Reservasi Stok"}, "stats": {"totalBatches": "Total Batch", "totalBatchesDesc": "Batch produksi dibuat", "pending": "<PERSON><PERSON><PERSON>", "pendingDesc": "<PERSON><PERSON><PERSON> produksi dimulai", "inProgress": "Sedang Be<PERSON>lan", "inProgressDesc": "Sedang diproduksi", "completed": "Se<PERSON><PERSON>", "completedDesc": "<PERSON><PERSON><PERSON><PERSON>", "latestBatch": "Batch Produksi Terbaru", "batchNumber": "<PERSON><PERSON>:", "status": "Status:", "created": "Dibuat:", "note": "Catatan:"}}, "batchList": {"filter": "Filter berdasarkan Status:", "allStatuses": "Semua Status", "showing": "Menampilkan {{shown}} dari {{total}} batch", "noBatches": {"all": "Belum ada batch produksi. Buat batch pertama dengan Alokasi Produksi Cepat.", "status": "Tidak ada batch {{status}}."}, "table": {"batch": "Batch #", "status": "Status", "created": "Dibuat", "items": "<PERSON><PERSON>", "note": "Catatan", "actions": "<PERSON><PERSON><PERSON>"}, "viewDetails": "<PERSON><PERSON> dan kelola detail batch produksi", "batchNote": "Catatan Batch", "updateNote": "Perbarui catatan batch produksi", "save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "deleteTitle": "Hapus Batch Produksi", "deleteConfirm": "Yakin ingin menghapus Batch Produksi #{{batch}}? {{releaseStock}} Tindakan ini tidak dapat dibatalkan.", "releaseStock": "Ini akan melepas semua stok yang dicadang<PERSON>.", "delete": "<PERSON><PERSON>", "noNote": "Tidak ada catatan", "allocated": "<PERSON><PERSON>:"}, "reservations": {"stats": {"total": "Total Reservasi", "manual": "Reservasi Manual", "production": "Reservasi Produksi"}, "title": "Reservasi Stok", "description": "<PERSON><PERSON><PERSON> semua reservasi stok dan tujuannya", "create": "Buat Reservasi", "createTitle": "Buat Reservasi Manual", "createDescription": "Cadangkan stok untuk keperluan manual atau pesanan mendatang", "selectStock": "<PERSON><PERSON><PERSON> dari <PERSON>", "chooseStock": "<PERSON><PERSON><PERSON> bahan dari stok yang tersedia", "noStockItems": "Tidak ada item stok untuk reservasi", "selected": "Dipi<PERSON>h: {{ingredient}}", "available": "Tersedia untuk reservasi:", "unit": "Unit", "quantity": "<PERSON><PERSON><PERSON> ya<PERSON>", "reason": "<PERSON><PERSON><PERSON> (Opsional)", "reasonPlaceholder": "Alasan reservasi ini...", "creating": "Membuat...", "selectFirst": "<PERSON><PERSON><PERSON>", "invalidQuantity": "<PERSON><PERSON><PERSON>", "invalidQuantityButton": "<PERSON><PERSON><PERSON>", "validQuantity": "✓ <PERSON><PERSON><PERSON> reservasi valid", "createAction": "Buat Reservasi", "noStockWarning": "Tidak ada stok untuk reservasi. Tambah item ke gudang terlebih dahulu.", "filter": "Fi<PERSON> be<PERSON><PERSON><PERSON>:", "allPurposes": "<PERSON><PERSON><PERSON>", "manual": "Reservasi Manual", "productionPurpose": "<PERSON><PERSON>", "showing": "Menampilkan {{shown}} dari {{total}} reservasi", "noReservations": {"all": "Tidak ada reservasi stok.", "purpose": "Tidak ada reservasi {{purpose}}."}, "table": {"ingredient": "<PERSON><PERSON>", "quantity": "<PERSON><PERSON><PERSON>", "purpose": "<PERSON><PERSON><PERSON>", "date": "Tanggal Reservasi", "actions": "<PERSON><PERSON><PERSON>"}, "releaseTitle": "Lepaskan Reservasi", "releaseConfirm": "Yakin ingin melepas reservasi {{quantity}} {{unit}} {{ingredient}}? Ini akan membuat stok tersedia kembali.", "release": "Lepaskan Reservasi"}}, "fixedAssets": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> aset tetap dan perhitungan depresiasi otomatis.", "assetInventory": "Inventaris Aset", "sheet": {"addTitle": "Tambah Aset Tetap", "editTitle": "Ubah Aset Tetap", "addDescription": "Tambahkan aset tetap baru dengan pelacakan depresiasi otomatis.", "editDescription": "Perbarui informasi aset dan pengaturan depresiasi."}, "summary": {"totalAssets": "Total Aset", "assetsDesc": "<PERSON><PERSON> tetap dalam inventaris", "totalPurchaseCost": "Total Biaya Pembelian", "purchaseCostDesc": "<PERSON><PERSON> awal", "currentValue": "<PERSON><PERSON>", "currentValueDesc": "<PERSON><PERSON><PERSON> de<PERSON>", "totalDepreciation": "Total Depresiasi", "totalDepreciationDesc": "{{rate}}% dari nilai awal", "explanationTitle": "Memahami Aset Tetap & Depresiasi", "explanation": {"p1": "<strong><PERSON><PERSON></strong> adalah barang jangka panjang milik usaha Anda se<PERSON>i peralatan, furnitur, atau teknologi.", "p2": "<strong><PERSON><PERSON><PERSON><PERSON><PERSON></strong> membagi biaya aset selama masa manfaatnya. Contoh: mesin kopi Rp60.000.000 dipakai 5 tahun berarti Rp12.000.000 per tahun (Rp1.000.000 per bulan).", "p3": "<strong><PERSON><PERSON></strong> menu<PERSON><PERSON><PERSON>n berapa nilai aset setelah memperhitungkan usia dan pemakaian.", "p4": "Sistem ini otomatis membuat entri depresiasi bulanan di Biaya Tetap agar laporan keuangan akurat."}}, "table": {"emptyTitle": "Belum ada aset tetap.", "emptyDescription": "Klik tombol + untuk menambahkan aset pertama <PERSON>a.", "columns": {"name": "<PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "purchaseDate": "Tanggal Bel<PERSON>", "purchaseCost": "<PERSON><PERSON><PERSON>", "currentValue": "<PERSON><PERSON>", "depreciation": "Periode Depresiasi", "status": "Status", "actions": "<PERSON><PERSON><PERSON>"}, "status": {"fully": "<PERSON><PERSON>", "mostly": "<PERSON><PERSON><PERSON>", "half": "Setengah Jalan", "recent": "<PERSON><PERSON>", "percent": "{{percent}}% terdepresiasi"}, "menu": "Buka menu", "edit": "Ubah", "delete": "Hapus", "deleteTitle": "<PERSON><PERSON>", "deleteConfirm": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus \"{{name}}\"? Ini juga akan menghapus entri depresiasi terkait dari Biaya Tetap. Tindakan ini tidak dapat dibatalkan.", "deleting": "Mengh<PERSON>us..."}, "form": {"fields": {"name": "<PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "purchaseDate": "Tanggal Bel<PERSON>", "purchaseCost": "<PERSON><PERSON>ya <PERSON>ian ({{currency}})", "depreciation": "Periode Depresiasi", "customDuration": "<PERSON><PERSON><PERSON> (Bulan)", "note": "Catatan (Opsional)"}, "placeholders": {"name": "<PERSON><PERSON>, <PERSON><PERSON>, Sistem POS", "selectCategory": "<PERSON><PERSON><PERSON> atau buat kategori...", "pickDate": "<PERSON><PERSON><PERSON> tanggal", "purchaseCost": "0", "customDuration": "36", "note": "Informasi tambahan tentang aset ini..."}, "preview": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentValue": "<PERSON><PERSON>", "totalDepreciation": "Total Depresiasi", "monthlyDepreciation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "remainingMonths": "<PERSON><PERSON>"}, "buttons": {"create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "saving": "Menyimpan...", "cancel": "<PERSON><PERSON>"}}, "categoryCombobox": {"searchPlaceholder": "<PERSON>i kategori...", "emptyText": "Tidak ada kategori.", "manage": "<PERSON><PERSON><PERSON>", "deleteTitle": "<PERSON><PERSON>", "alertDescription": "<PERSON><PERSON><PERSON> kategori yang ingin dihapus. <PERSON><PERSON> kategori yang tidak digunakan yang dapat dihapus.", "confirmTitle": "<PERSON><PERSON> \"{{name}}\"", "inUseMessage": "Tidak dapat menghapus \"{{name}}\". Saat ini digunakan oleh {{count}} aset.", "confirmMessage": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus \"{{name}}\"? Tindakan ini tidak dapat dibatalkan.", "deleting": "Mengh<PERSON>us...", "creating": "Me<PERSON><PERSON>t kategori..."}}, "learn": {"page": {"title": "<PERSON><PERSON><PERSON>", "description": "Pelajari konsep keuangan dan manajemen bisnis untuk kedai kopi Anda"}, "searchPlaceholder": "<PERSON>i topik pembel<PERSON>ran...", "modules": {"accounting": {"title": "Akuntansi & Manajemen <PERSON>", "description": "Pelajari konsep akunta<PERSON>i penting untuk bisnis kedai kopi Anda"}, "operations": {"title": "Manajemen Operasional & Penjualan", "description": "Memahami target penjualan, pen<PERSON><PERSON>n, dan metrik <PERSON>ional"}, "warehouse": {"title": "Manajemen Inventori & Gudang", "description": "<PERSON><PERSON><PERSON><PERSON> stok, pela<PERSON><PERSON> inventori, dan <PERSON>i gudang"}, "analytics": {"title": "Analitik Bisnis & Pelaporan", "description": "<PERSON><PERSON><PERSON> k<PERSON>, metrik, dan business intelligence"}}, "available": "Tersedia", "comingSoon": "<PERSON><PERSON><PERSON>", "startLearning": "<PERSON><PERSON>", "viewSection": "<PERSON><PERSON>ian {{section}} →", "quickLinks": {"title": "Siap menerapkan yang sudah dipelajari?", "goToAccounting": "<PERSON><PERSON>", "cogsCalculator": "Kalkulator HPP", "viewAnalytics": "<PERSON><PERSON>", "financialDashboard": "<PERSON><PERSON>"}, "comingSoonMessage": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON>mi sedang menyiapkan materi pembelajaran lengkap untuk modul ini."}, "tabs": {"basics": "<PERSON><PERSON>", "transactions": "Transaksi", "financialHealth": "<PERSON><PERSON><PERSON><PERSON>", "costManagement": "<PERSON><PERSON><PERSON><PERSON>"}, "section": {"definition": "Definisi", "example": "<PERSON><PERSON><PERSON>", "context": "Cara kerjanya di aplikasi"}, "accounting": {"title": "Pembelajaran <PERSON> & <PERSON><PERSON><PERSON><PERSON>", "description": "Konsep keuangan penting yang harus dipahami pemilik kedai kopi"}, "accountingTopics": {"basicConcepts": {"title": "<PERSON><PERSON><PERSON>", "description": "Prinsi<PERSON> a<PERSON>nta<PERSON>i dasar untuk pemilik kedai kopi", "transactions": {"term": "Transaksi", "definition": "Setiap aktivitas bisnis yang melibatkan uang masuk atau keluar dari kedai kopi Anda.", "example": "Menjual latte seharga Rp5.000 adalah transaksi penjualan. Me<PERSON>li biji kopi seharga Rp50.000 adalah transaksi pengeluaran.", "context": "<PERSON>, semua aktivitas bisnis dicatat sebagai transaksi pada bagian Akuntansi."}, "revenue": {"term": "Pendapatan", "definition": "<PERSON><PERSON><PERSON> uang yang diperoleh kedai kopi dari penjualan produk dan layanan.", "example": "<PERSON><PERSON> Anda menjual 100 latte seharga Rp5.000, pendapatan Anda Rp500.000 untuk periode tersebut.", "context": "Pantau pendapatan harian di bagian Operasi dan lihat totalnya di Akuntansi."}, "expenses": {"term": "Biaya", "definition": "<PERSON><PERSON><PERSON> uang yang dikeluarkan kedai kopi untuk operasional bisnis.", "example": "<PERSON><PERSON> (Rp2.000.000/bulan), bi<PERSON> kop<PERSON> (Rp500.000/bulan), gaji ka<PERSON> (Rp3.000.000/bulan) merupakan biaya.", "context": "<PERSON><PERSON><PERSON> pengeluaran rutin dan pantau semua biaya bisnis di bagian Akuntansi."}, "netIncome": {"term": "Lab<PERSON> Be<PERSON>", "definition": "<PERSON>sa pendapatan setelah dikurangi semua biaya. Positif = laba, negatif = rugi.", "example": "Pendapatan Rp10.000.000 - Biaya Rp8.000.000 = Laba Bersih Rp2.000.000", "context": "<PERSON>hat laba bersih pada kartu <PERSON> di <PERSON>."}}, "transactionTypes": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> kategori transaksi di kedai kopi", "salesIncome": {"term": "Pendapatan Penjualan", "definition": "<PERSON><PERSON> yang diperoleh dari penjualan kopi, kue, dan produk la<PERSON>ya kepada pelanggan.", "example": "Penju<PERSON> harian Rp500.000 dari latte, cappuccino, dan muffin.", "context": "Tercatat otomatis saat Anda mencatat penjualan di bagian Operasi."}, "operatingExpenses": {"term": "Biaya Operasional", "definition": "<PERSON><PERSON><PERSON> sehari-hari untuk menja<PERSON>an kedai kopi.", "example": "<PERSON><PERSON><PERSON>, util<PERSON>, alat k<PERSON>, biaya pema<PERSON>.", "context": "Tambahkan di bagian <PERSON> atau atur sebagai biaya rutin bulanan."}, "fixedCosts": {"term": "Biaya <PERSON>", "definition": "<PERSON><PERSON>ya yang tetap setiap bulan terlepas dari jumlah pen<PERSON>.", "example": "<PERSON><PERSON> (Rp2.000.000/bulan), <PERSON><PERSON><PERSON><PERSON> (Rp200.000/bulan), c<PERSON><PERSON> pinja<PERSON> (Rp500.000/bulan).", "context": "Atur pada lembar <PERSON> atau sebagai biaya rutin."}, "variableCosts": {"term": "<PERSON><PERSON><PERSON> (HPP)", "definition": "<PERSON><PERSON>ya yang berubah tergantung seberapa banyak Anda memproduksi atau menjual.", "example": "<PERSON><PERSON> kop<PERSON>, susu, gula - semakin banyak minuman dibuat, semakin besar biayanya.", "context": "Hitung menggunakan Kalkulator HPP dan catat pada lembar HPP Variabel."}, "capitalInvestment": {"term": "Investasi Modal", "definition": "<PERSON><PERSON> yang diinvestasikan untuk memulai atau mengembangkan bisnis, termasuk pembelian peralatan.", "example": "<PERSON><PERSON>li mesin espresso (Rp5.000.000), furnitur (Rp2.000.000), inventori awal (Rp1.000.000).", "context": "Catat pada lembar <PERSON> atau bagian Aset Tetap."}}, "financialHealth": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> penting untuk memantau performa keuangan kedai kopi", "grossProfit": {"term": "<PERSON><PERSON>", "definition": "Pendapatan dikurangi biaya langsung pembuatan produk (HPP).", "example": "Jika Anda menjual kopi senilai Rp1.000.000 dan biaya bahan Rp300.000, laba kotor Rp700.000.", "context": "<PERSON>hat pada <PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON> efisiensi produksi."}, "profitMargin": {"term": "<PERSON><PERSON>", "definition": "Persentase keuntungan dari setiap penjualan setelah semua biaya.", "example": "Jika Anda mendapat laba Rp2.000 dari latte seharga Rp5.000, margin laba 40%.", "context": "<PERSON>gin lebih tinggi berarti bisnis lebih menguntungkan per penjualan."}, "breakEven": {"term": "Titik Impas", "definition": "<PERSON><PERSON><PERSON> pen<PERSON>alan yang dibutuhkan untuk menutupi semua biaya (tidak untung, tidak rugi).", "example": "Jika biaya bulanan Rp8.000.000 dan laba rata-rata per penjualan Rp2.000, dibutuhkan 4.000 penjualan untuk impas.", "context": "<PERSON><PERSON><PERSON> untuk melihat apakah Anda di atas atau di bawah titik impas."}, "cashFlow": {"term": "<PERSON><PERSON>", "definition": "<PERSON><PERSON>tu uang masuk dibandingkan dengan uang keluar.", "example": "Bisnis bisa untung tapi bermasalah arus kas jika pelanggan membayar terlambat sementara Anda harus bayar pemasok segera.", "context": "<PERSON><PERSON>u pada indikator <PERSON>."}}, "costManagement": {"title": "Manajemen & Pengendalian Biaya", "description": "Mengel<PERSON> dan mengo<PERSON><PERSON>lkan biaya kedai kopi", "cogs": {"term": "Harga Pokok Penjualan (HPP)", "definition": "B<PERSON>ya langsung bahan dan material untuk membuat produk.", "example": "Untuk latte: biji kopi (Rp500), susu (Rp300), gelas (Rp100) = HPP Rp900", "context": "Hitung HPP secara tepat dengan Kalkulator HPP untuk tiap menu."}, "foodCostPct": {"term": "Persentase Biaya Bahan", "definition": "Persentase penjualan yang digunakan untuk biaya bahan baku.", "example": "Jika penjualan Rp1.000.000 dan biaya bahan Rp300.000, persentasenya 30%.", "context": "Standar industri kedai kopi bias<PERSON>a 25-35%."}, "laborCostPct": {"term": "Persentase Biaya Tenaga Kerja", "definition": "Persentase penjualan yang digunakan untuk gaji dan tunjangan karyawan.", "example": "Jika penjualan Rp1.000.000 dan gaji Rp250.000, persentasenya 25%.", "context": "Pantau dengan mencatat biaya staf di Biaya Operasional."}, "overheadCosts": {"term": "Biaya Overhead", "definition": "Biaya tidak langsung untuk menjalankan bisnis namun tidak terkait langsung dengan produksi.", "example": "<PERSON><PERSON>, util<PERSON>, <PERSON><PERSON><PERSON><PERSON>, jasa a<PERSON>, p<PERSON><PERSON><PERSON>.", "context": "Biasanya dicatat sebagai Biaya Tetap atau Biaya Operasional."}}}}}