@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:where(.dark, .dark *));

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }

  /* Global dark mode styles */
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Ensure all elements inherit the theme colors by default */
  * {
    border-color: hsl(var(--border));
  }

  /* Global component theming */
  .dark {
    color-scheme: dark;
  }

  /* Ensure cards and other components get proper theming */
  [data-radix-collection-item] {
    background-color: hsl(var(--card));
    color: hsl(var(--card-foreground));
  }

  /* Ensure proper background colors for shadcn/ui components */
  .bg-background {
    background-color: hsl(var(--background)) !important;
  }

  .bg-card {
    background-color: hsl(var(--card)) !important;
  }

  .bg-popover {
    background-color: hsl(var(--popover)) !important;
  }

  .text-foreground {
    color: hsl(var(--foreground)) !important;
  }

  .text-card-foreground {
    color: hsl(var(--card-foreground)) !important;
  }

  .text-popover-foreground {
    color: hsl(var(--popover-foreground)) !important;
  }

  /* Enhanced Sheet component styling with solid, theme-aware backgrounds */
  /* Sheet overlay with proper theme-aware opacity */
[data-radix-dialog-overlay] {
    background-color: hsl(var(--background) / 0.2) !important;
    backdrop-filter: blur(4px) !important;
    z-index: 100 !important;
  }

  .dark [data-radix-dialog-overlay] {
    background-color: hsl(var(--background) / 0.2) !important;
    backdrop-filter: blur(4px) !important;
  }

  /* Main Sheet content with solid, theme-aware background */
  [data-radix-dialog-content] {
    background-color: hsl(var(--background)) !important;
    color: hsl(var(--foreground)) !important;
    border: 1px solid hsl(var(--border)) !important;
    z-index: 101 !important;
    opacity: 1 !important;
    box-shadow: 0 25px 50px -12px hsl(var(--foreground) / 0.25) !important;
  }

  /* Force solid backgrounds for all sheet content with theme awareness */
  .sheet-content {
    background-color: hsl(var(--background)) !important;
    color: hsl(var(--foreground)) !important;
    opacity: 1 !important;
    z-index: 101 !important;
  }

  /* Enhanced Sheet component state handling */
  [data-state="open"][data-radix-dialog-content] {
    background-color: hsl(var(--background)) !important;
    color: hsl(var(--foreground)) !important;
    opacity: 1 !important;
    z-index: 101 !important;
  }

  [data-state="closed"][data-radix-dialog-content] {
    background-color: hsl(var(--background)) !important;
    color: hsl(var(--foreground)) !important;
  }

  /* Ensure all cards inside sheets have solid, theme-aware backgrounds */
  [data-radix-dialog-content] .bg-card,
  [data-radix-dialog-content] [class*="card"] {
    background-color: hsl(var(--card)) !important;
    color: hsl(var(--card-foreground)) !important;
    opacity: 1 !important;
  }

  /* Ensure all sheet content maintains theme consistency */
  [data-radix-dialog-content] * {
    opacity: 1 !important;
  }

  /* Theme-aware background for sheet headers and footers */
  [data-radix-dialog-content] [data-sheet-header],
  [data-radix-dialog-content] [data-sheet-footer] {
    background-color: hsl(var(--background)) !important;
    color: hsl(var(--foreground)) !important;
  }

  /* Enhanced table styling inside sheets with theme awareness */
  [data-radix-dialog-content] table {
    background-color: hsl(var(--background)) !important;
    color: hsl(var(--foreground)) !important;
  }

  [data-radix-dialog-content] thead,
  [data-radix-dialog-content] tbody,
  [data-radix-dialog-content] tfoot {
    background-color: hsl(var(--background)) !important;
    color: hsl(var(--foreground)) !important;
  }

  [data-radix-dialog-content] tr {
    background-color: hsl(var(--background)) !important;
    color: hsl(var(--foreground)) !important;
  }

  [data-radix-dialog-content] th,
  [data-radix-dialog-content] td {
    background-color: hsl(var(--background)) !important;
    color: hsl(var(--foreground)) !important;
    border-color: hsl(var(--border)) !important;
  }

  /* Enhanced background utility classes with theme awareness */
  .bg-background {
    background-color: hsl(var(--background)) !important;
    color: hsl(var(--foreground)) !important;
    opacity: 1 !important;
  }

  /* Input and form elements inside sheets */
  [data-radix-dialog-content] input,
  [data-radix-dialog-content] textarea,
  [data-radix-dialog-content] select {
    background-color: hsl(var(--background)) !important;
    color: hsl(var(--foreground)) !important;
    border-color: hsl(var(--border)) !important;
  }

  /* Alert Dialog specific styling for theme awareness */
  [data-radix-alert-dialog-overlay] {
    background-color: hsl(var(--background) / 0.8) !important;
    backdrop-filter: blur(4px) !important;
    z-index: 50 !important;
  }

  .dark [data-radix-alert-dialog-overlay] {
    background-color: hsl(var(--background) / 0.8) !important;
    backdrop-filter: blur(4px) !important;
  }

  /* Alert Dialog content with solid, theme-aware background */
  [data-radix-alert-dialog-content] {
    background-color: hsl(var(--background)) !important;
    color: hsl(var(--foreground)) !important;
    border: 1px solid hsl(var(--border)) !important;
    z-index: 50 !important;
    opacity: 1 !important;
    box-shadow: 0 25px 50px -12px hsl(var(--foreground) / 0.25) !important;
  }

  /* Ensure all alert dialog content maintains theme consistency */
  [data-radix-alert-dialog-content] * {
    opacity: 1 !important;
  }

  /* Alert dialog buttons with proper theme colors */
  [data-radix-alert-dialog-content] button {
    background-color: hsl(var(--background)) !important;
    color: hsl(var(--foreground)) !important;
    border-color: hsl(var(--border)) !important;
  }

  /* Alert dialog title and description styling */
  [data-radix-alert-dialog-content] [data-radix-alert-dialog-title] {
    color: hsl(var(--foreground)) !important;
  }

  [data-radix-alert-dialog-content] [data-radix-alert-dialog-description] {
    color: hsl(var(--muted-foreground)) !important;
  }

  /* Enhanced Select dropdown styling for better readability */
  [data-radix-select-content] {
    background-color: hsl(var(--popover)) !important;
    color: hsl(var(--popover-foreground)) !important;
    border-color: hsl(var(--border)) !important;
    isolation: isolate !important;
    opacity: 1 !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
  }

  /* Target the actual listbox element that gets rendered */
  [role="listbox"][data-state="open"] {
    background-color: hsl(var(--popover)) !important;
    color: hsl(var(--popover-foreground)) !important;
    opacity: 1 !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
  }

  /* Target by the specific classes applied */
  .bg-popover {
    background-color: hsl(var(--popover)) !important;
    opacity: 1 !important;
  }

  /* Target the exact element structure from the HTML */
  div[role="listbox"][data-state="open"].bg-popover {
    background-color: hsl(var(--popover)) !important;
    color: hsl(var(--popover-foreground)) !important;
    opacity: 1 !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
  }

  /* Ensure solid background for light theme */
  :root [data-radix-select-content],
  :root [role="listbox"][data-state="open"],
  :root .bg-popover,
  :root div[role="listbox"][data-state="open"].bg-popover,
  :root div[role="listbox"].bg-popover {
    background-color: hsl(0 0% 100%) !important;
    color: hsl(222.2 84% 4.9%) !important;
    border: 1px solid hsl(214.3 31.8% 91.4%) !important;
    box-shadow: 0 10px 15px -3px hsl(0 0% 0% / 0.1), 0 4px 6px -2px hsl(0 0% 0% / 0.05) !important;
  }

  /* Ensure solid background for dark theme */
  .dark [data-radix-select-content],
  .dark [role="listbox"][data-state="open"],
  .dark .bg-popover,
  .dark div[role="listbox"][data-state="open"].bg-popover,
  .dark div[role="listbox"].bg-popover {
    background-color: hsl(222.2 84% 4.9%) !important;
    color: hsl(210 40% 98%) !important;
    border: 1px solid hsl(217.2 32.6% 17.5%) !important;
    box-shadow: 0 10px 15px -3px hsl(0 0% 0% / 0.3), 0 4px 6px -2px hsl(0 0% 0% / 0.2) !important;
  }

  [data-radix-select-item],
  [role="option"] {
    color: hsl(var(--popover-foreground)) !important;
    display: flex !important;
    align-items: center !important;
    min-height: 2rem !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    background-color: transparent !important;
    opacity: 1 !important;
  }

  /* Ensure solid item text colors for light theme */
  :root [data-radix-select-item],
  :root [role="option"] {
    color: hsl(222.2 84% 4.9%) !important;
  }

  /* Ensure solid item text colors for dark theme */
  .dark [data-radix-select-item],
  .dark [role="option"] {
    color: hsl(210 40% 98%) !important;
  }

  [data-radix-select-item][data-highlighted] {
    background-color: hsl(var(--accent)) !important;
    color: hsl(var(--accent-foreground)) !important;
    opacity: 1 !important;
  }

  [data-radix-select-item][data-state="checked"] {
    background-color: hsl(var(--accent)) !important;
    color: hsl(var(--accent-foreground)) !important;
    opacity: 1 !important;
  }

  /* Ensure solid hover/selected backgrounds for light theme */
  :root [data-radix-select-item][data-highlighted],
  :root [data-radix-select-item][data-state="checked"] {
    background-color: hsl(210 40% 96%) !important;
    color: hsl(222.2 47.4% 11.2%) !important;
  }

  /* Ensure solid hover/selected backgrounds for dark theme */
  .dark [data-radix-select-item][data-highlighted],
  .dark [data-radix-select-item][data-state="checked"] {
    background-color: hsl(217.2 32.6% 17.5%) !important;
    color: hsl(210 40% 98%) !important;
  }

  /* Prevent multiple select dropdowns from interfering */
  [data-radix-select-content] {
    contain: layout style paint !important;
  }

  /* Button styling inside sheets */
  [data-radix-dialog-content] button:not([class*="bg-"]) {
    background-color: hsl(var(--secondary)) !important;
    color: hsl(var(--secondary-foreground)) !important;
    border-color: hsl(var(--border)) !important;
  }

  /* Specific fix for sidebar sheet conflicts */
  [data-sidebar="sidebar"] {
    z-index: 10 !important;
  }

  /* Ensure sidebar mobile sheet doesn't conflict with other sheets */
  [data-mobile="true"][data-sidebar="sidebar"] {
    z-index: 50 !important;
  }

  /* Ensure SidebarTrigger is visible and properly styled */
  [data-sidebar="trigger"] {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    opacity: 1 !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 1px solid hsl(var(--border)) !important;
    color: hsl(var(--foreground)) !important;
    transition: all 0.2s ease !important;
  }

  [data-sidebar="trigger"]:hover {
    background-color: hsl(var(--accent)) !important;
    color: hsl(var(--accent-foreground)) !important;
  }

  /* Ensure the icon inside the trigger is visible */
  [data-sidebar="trigger"] svg {
    opacity: 1 !important;
    visibility: visible !important;
    width: 1rem !important;
    height: 1rem !important;
  }

  /* Debug: Ensure CSS variables are working */
  :root {
    /* Fallback colors in case variables fail */
    --background-fallback: #ffffff;
    --foreground-fallback: #000000;
  }

  .dark {
    --background-fallback: #000000;
    --foreground-fallback: #ffffff;
  }

  /* Fallback for Sheet content if CSS variables fail */
  [data-radix-dialog-content]:not([style*="background"]) {
    background-color: var(--background-fallback) !important;
    color: var(--foreground-fallback) !important;
  }

  /* Enhanced Sheet component parts styling */
  [data-radix-dialog-content] [class*="sheet-header"],
  [data-radix-dialog-content] [class*="sheet-footer"],
  [data-radix-dialog-content] [class*="sheet-title"],
  [data-radix-dialog-content] [class*="sheet-description"] {
    background-color: transparent !important;
    color: hsl(var(--foreground)) !important;
  }

  /* Ensure proper styling for close button */
  [data-radix-dialog-content] [data-radix-dialog-close] {
    background-color: hsl(var(--secondary)) !important;
    color: hsl(var(--secondary-foreground)) !important;
    border-color: hsl(var(--border)) !important;
  }

  [data-radix-dialog-content] [data-radix-dialog-close]:hover {
    background-color: hsl(var(--secondary) / 0.8) !important;
  }

  /* Ensure scrollable content maintains theme */
  [data-radix-dialog-content] [class*="scroll"],
  [data-radix-dialog-content] .overflow-auto,
  [data-radix-dialog-content] .overflow-y-auto {
    background-color: hsl(var(--background)) !important;
    color: hsl(var(--foreground)) !important;
  }

  /* Ensure Sheet components have proper minimum width on mobile */
  [data-radix-dialog-content] {
    min-width: 320px; /* Minimum width for mobile readability */
  }

  /* Responsive adjustments for very small screens */
  @media (max-width: 640px) {
    [data-radix-dialog-content] {
      min-width: 280px;
      max-width: calc(100vw - 16px); /* Leave some margin on very small screens */
    }
  }

  /* Wide sheet specific adjustments */
  [data-radix-dialog-content][data-side="right-wide"] {
    max-width: calc(100vw - 32px); /* Ensure some margin on all screen sizes */
  }

  /* Mobile adjustments for wide sheets - prioritize usability */
  @media (max-width: 640px) {
    [data-radix-dialog-content][data-side="right-wide"] {
      width: calc(100vw - 16px) !important;
      max-width: calc(100vw - 16px);
      right: 8px !important;
      left: 8px !important;
      transform: none !important;
    }
  }

  /* Small tablet adjustments for wide sheets */
  @media (min-width: 641px) and (max-width: 768px) {
    [data-radix-dialog-content][data-side="right-wide"] {
      width: 92% !important;
      max-width: 92%;
    }
  }

  /* Large tablet adjustments for wide sheets */
  @media (min-width: 769px) and (max-width: 1024px) {
    [data-radix-dialog-content][data-side="right-wide"] {
      width: 88% !important;
      max-width: 88%;
    }
  }

  /* Desktop adjustments for wide sheets */
  @media (min-width: 1025px) {
    [data-radix-dialog-content][data-side="right-wide"] {
      width: 80% !important;
      max-width: 80%;
    }
  }

  /* Large desktop adjustments for wide sheets */
  @media (min-width: 1440px) {
    [data-radix-dialog-content][data-side="right-wide"] {
      width: 75% !important;
      max-width: 75%;
    }
  }

  /* Table improvements for wide sheets */
  [data-radix-dialog-content][data-side="right-wide"] .table-fixed {
    table-layout: fixed;
  }

  [data-radix-dialog-content][data-side="right-wide"] .table-fixed td,
  [data-radix-dialog-content][data-side="right-wide"] .table-fixed th {
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Responsive table adjustments for mobile in wide sheets */
  @media (max-width: 640px) {
    [data-radix-dialog-content][data-side="right-wide"] .table-fixed th,
    [data-radix-dialog-content][data-side="right-wide"] .table-fixed td {
      padding: 8px 4px;
      font-size: 0.875rem;
    }
  }

  /* Sticky table headers fix */
  .table-sticky-header {
    position: sticky !important;
    top: 0 !important;
    z-index: 20 !important;
    background-color: hsl(var(--background)) !important;
    border-bottom: 1px solid hsl(var(--border)) !important;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05) !important;
    /* Add a subtle background to make it more visible when sticky */
    backdrop-filter: blur(8px);
  }

  /* Ensure table container supports sticky positioning */
  .table-container {
    position: relative;
    overflow: auto;
  }

  /* Additional sticky header support for table elements */
  .table-container table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
  }

  /* Ensure sticky headers work in all browsers */
  .table-container thead th.table-sticky-header {
    position: sticky !important;
    top: 0 !important;
    z-index: 21 !important;
  }

  /* Fix for webkit browsers */
  .table-container thead.table-sticky-header {
    position: sticky !important;
    top: 0 !important;
    z-index: 20 !important;
  }

  /* Sheet animations */
  @keyframes slide-in-from-right {
    from {
      transform: translateX(100%);
    }
    to {
      transform: translateX(0);
    }
  }

  @keyframes slide-out-to-right {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(100%);
    }
  }

  @keyframes slide-in-from-left {
    from {
      transform: translateX(-100%);
    }
    to {
      transform: translateX(0);
    }
  }

  @keyframes slide-out-to-left {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(-100%);
    }
  }

  @keyframes slide-in-from-top {
    from {
      transform: translateY(-100%);
    }
    to {
      transform: translateY(0);
    }
  }

  @keyframes slide-out-to-top {
    from {
      transform: translateY(0);
    }
    to {
      transform: translateY(-100%);
    }
  }

  @keyframes slide-in-from-bottom {
    from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
  }

  @keyframes slide-out-to-bottom {
    from {
      transform: translateY(0);
    }
    to {
      transform: translateY(100%);
    }
  }

  @keyframes fade-in {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes fade-out {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }

  .animate-in {
    animation-fill-mode: forwards;
  }

  .animate-out {
    animation-fill-mode: forwards;
  }

  .slide-in-from-right {
    animation: slide-in-from-right 0.5s ease-out;
  }

  .slide-out-to-right {
    animation: slide-out-to-right 0.3s ease-in;
  }

  .slide-in-from-left {
    animation: slide-in-from-left 0.5s ease-out;
  }

  .slide-out-to-left {
    animation: slide-out-to-left 0.3s ease-in;
  }

  .slide-in-from-top {
    animation: slide-in-from-top 0.5s ease-out;
  }

  .slide-out-to-top {
    animation: slide-out-to-top 0.3s ease-in;
  }

  .slide-in-from-bottom {
    animation: slide-in-from-bottom 0.5s ease-out;
  }

  .slide-out-to-bottom {
    animation: slide-out-to-bottom 0.3s ease-in;
  }

  .fade-in {
    animation: fade-in 0.5s ease-out;
  }

  .fade-out {
    animation: fade-out 0.3s ease-in;
  }
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.129 0.042 264.695);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.129 0.042 264.695);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.129 0.042 264.695);
  --primary: oklch(0.208 0.042 265.755);
  --primary-foreground: oklch(0.984 0.003 247.858);
  --secondary: oklch(0.968 0.007 247.896);
  --secondary-foreground: oklch(0.208 0.042 265.755);
  --muted: oklch(0.968 0.007 247.896);
  --muted-foreground: oklch(0.554 0.046 257.417);
  --accent: oklch(0.968 0.007 247.896);
  --accent-foreground: oklch(0.208 0.042 265.755);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.929 0.013 255.508);
  --input: oklch(0.929 0.013 255.508);
  --ring: oklch(0.704 0.04 256.788);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.984 0.003 247.858);
  --sidebar-foreground: oklch(0.129 0.042 264.695);
  --sidebar-primary: oklch(0.208 0.042 265.755);
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
  --sidebar-accent: oklch(0.968 0.007 247.896);
  --sidebar-accent-foreground: oklch(0.208 0.042 265.755);
  --sidebar-border: oklch(0.929 0.013 255.508);
  --sidebar-ring: oklch(0.704 0.04 256.788);
}

.dark {
  --background: oklch(0.129 0.042 264.695);
  --foreground: oklch(0.984 0.003 247.858);
  --card: oklch(0.208 0.042 265.755);
  --card-foreground: oklch(0.984 0.003 247.858);
  --popover: oklch(0.208 0.042 265.755);
  --popover-foreground: oklch(0.984 0.003 247.858);
  --primary: oklch(0.929 0.013 255.508);
  --primary-foreground: oklch(0.208 0.042 265.755);
  --secondary: oklch(0.279 0.041 260.031);
  --secondary-foreground: oklch(0.984 0.003 247.858);
  --muted: oklch(0.279 0.041 260.031);
  --muted-foreground: oklch(0.704 0.04 256.788);
  --accent: oklch(0.279 0.041 260.031);
  --accent-foreground: oklch(0.984 0.003 247.858);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.551 0.027 264.364);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.208 0.042 265.755);
  --sidebar-foreground: oklch(0.984 0.003 247.858);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
  --sidebar-accent: oklch(0.279 0.041 260.031);
  --sidebar-accent-foreground: oklch(0.984 0.003 247.858);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.551 0.027 264.364);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
