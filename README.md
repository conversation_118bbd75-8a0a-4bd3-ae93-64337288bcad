# 📚 KWACI Documentation Index

Complete documentation for the KWACI coffee shop management application, focusing on development, testing, and authentication.

## 🚀 Quick Start

**New to the project? Start here:**

### **[🎯 Quick Start Login Guide](./QUICK-START-LOGIN-GUIDE.md)**
*5-minute guide to login with seeded users and explore the application*

- 🔑 Test credentials table
- 📊 What you'll see after login
- 🧪 Features to test immediately
- 🚨 Quick troubleshooting

---

## 🔐 Authentication & User Management

### **[🛠️ Development Authentication Setup](./development-authentication-setup.md)**
*Complete setup guide for development authentication*

- 🎯 Overview of authentication architecture
- 🚀 Quick start commands
- 🔑 Default test credentials
- 🛠️ Development tools and helpers
- 📊 What gets seeded
- 🔧 Customization options

### **[🚨 Authentication Troubleshooting](./authentication-troubleshooting.md)**
*Solutions for common authentication issues*

- 🔍 Problem diagnosis
- 🛠️ Manual resolution steps
- 🛡️ Prevention strategies
- 📋 Monitoring and debugging
- 🔄 Recovery procedures

### **[🚦 Rate Limiting Configuration](./rate-limiting-configuration.md)**
*Complete guide to Better Auth rate limiting for development and production*

- 🔍 Understanding 429 errors
- ⚙️ Environment-aware configuration
- 🛠️ Development tools and helpers
- 🚨 Troubleshooting rate limits
- 🛡️ Security best practices

---

## 🧪 Testing & Development

### **[🧪 Testing with Seeded Data](./testing-with-seeded-data.md)**
*Comprehensive guide for testing with realistic data*

- 🔑 Complete test credentials
- 📊 Detailed data structure
- 🧪 Testing scenarios for each feature
- 🔄 Reset and workflow procedures
- 📈 Data validation methods
- 🎯 Testing best practices

### **[🌱 Database Seeding](./database-seeding.md)**
*Technical guide to the database seeding system*

- 🏗️ Architecture and relationships
- 📊 Generated data overview
- ⚙️ Implementation details
- 🔧 Configuration options
- 🚀 Usage instructions
- 🔮 Future enhancements

---

## 🛠️ Development Tools

### **Available Scripts**

#### **Database Management**
```bash
bun run db:reset     # Complete reset (migrations + seeding)
bun run db:seed      # Just reseed data
bun run db:generate  # Generate new migration
bun run db:migrate   # Apply migrations
bun run db:studio    # Open database studio
```

#### **User Management**
```bash
bun run dev:users              # List all test users
bun run dev:auth               # User management helper
bun run dev:clear-rate-limits  # Clear rate limits (development)
bun run dev:rate-limit-status  # Check rate limit status
```

#### **Development**
```bash
bun run dev          # Start development server
bun run build        # Build for production
bun run start        # Start production server
```

### **Development Helper Commands**
```bash
# User management
bun run dev:auth list                    # List all users
bun run dev:auth create <email> [name]   # Create test user
bun run dev:auth reset <email> [pass]    # Reset password
bun run dev:auth quick                   # Create common test users
bun run dev:auth delete <email>          # Delete user
```

---

## 📊 Data Structure Overview

### **Test Users & Businesses**
| Email | Password | Business | Use Case |
|-------|----------|----------|----------|
| `<EMAIL>` | `password123` | KWACI Coffee House | Premium coffee business |
| `<EMAIL>` | `password123` | Brew & Bean Cafe | Cozy cafe business |
| `<EMAIL>` | `password123` | Morning Glory Coffee | Modern coffee shop |

### **Seeded Data Per Business**
- **16 Categories** (8 ingredient + 8 product)
- **16 Ingredients** with realistic pricing
- **10 Products** with descriptions
- **Complete relationships** between all entities

### **Category Types**

#### **Ingredient Categories**
- ☕ Coffee Beans
- 🥛 Dairy
- 🍯 Sweeteners
- 🌿 Spices & Flavorings
- 🌱 Alternative Milks
- 🍦 Toppings
- 🍵 Tea
- 🧁 Baking Ingredients

#### **Product Categories**
- ☕ Hot Beverages
- 🧊 Cold Beverages
- 🥐 Pastries
- 🍿 Snacks
- ✨ Specialty Drinks
- 🍰 Desserts
- 🍳 Breakfast Items
- 🥪 Lunch Items

---

## 🔄 Common Workflows

### **Daily Development**
1. `bun run dev` - Start development server
2. Login with test credentials
3. Test features with realistic data
4. Reset data when needed: `bun run db:seed`

### **Feature Testing**
1. `bun run db:reset` - Fresh start
2. Login with test credentials
3. Test new features
4. Verify data relationships

### **Authentication Testing**
1. Test login/logout flows
2. Verify session persistence
3. Test user isolation
4. Verify business data scoping

### **Database Changes**
1. Modify schema
2. `bun run db:generate` - Create migration
3. `bun run db:migrate` - Apply migration
4. `bun run db:seed` - Reseed data

---

## 🚨 Important Notes

### **Security**
- ⚠️ **Development only**: Test credentials are for development
- ⚠️ **Never in production**: Use proper registration in production
- ⚠️ **Environment isolation**: Keep test data separate

### **Data Management**
- 🔄 **Seeding resets data**: Running seed replaces all data
- 💾 **Schema preserved**: Migrations are kept during seeding
- 🗑️ **Clean slate**: Use `db:reset` for complete fresh start

### **Performance**
- 📊 **Realistic volumes**: Test with proper data relationships
- ⚡ **Query optimization**: Monitor performance with seeded data
- 🔍 **Database studio**: Use for data inspection

---

## 🎯 Success Checklist

After setup, you should be able to:

- ✅ **Login** with test credentials
- ✅ **See business data** scoped to logged-in user
- ✅ **Browse inventory** with categories, ingredients, products
- ✅ **Use detail sheets** for ingredient/product information
- ✅ **Create/edit items** with proper relationships
- ✅ **Test authentication** flows and session management
- ✅ **Switch users** to test different business contexts
- ✅ **Reset data** when needed for clean testing

---

## 🆘 Need Help?

### **Quick Fixes**
```bash
# Can't login?
bun run dev:users

# No data?
bun run db:reset

# Authentication issues?
# Clear browser cookies and try again
```

### **Documentation**
- Start with **[Quick Start Login Guide](./QUICK-START-LOGIN-GUIDE.md)**
- Check **[Authentication Troubleshooting](./authentication-troubleshooting.md)**
- Review **[Testing Guide](./testing-with-seeded-data.md)** for comprehensive scenarios

### **Development Tools**
- Use `bun run dev:auth` for user management
- Use `bun run db:studio` for database inspection
- Check console logs for detailed error information

---

**Happy Development! 🎉**

*This documentation provides everything you need to develop and test the KWACI coffee shop management application with realistic data and proper authentication.*
