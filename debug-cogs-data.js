import { db } from './app/lib/db/connection.ts';
import { products, ingredients, productIngredients, businesses } from './app/lib/db/schema.ts';
import { eq } from 'drizzle-orm';
import process from 'process';

console.log('=== Debug COGS Data ===');

// Find the first business (KWACI Coffee House)
const firstBusiness = await db
  .select()
  .from(businesses)
  .limit(1);

if (firstBusiness.length === 0) {
  console.log('❌ No businesses found!');
  process.exit(1);
}

const businessId = firstBusiness[0].id;
console.log('🏢 Using business:', {
  id: businessId,
  name: firstBusiness[0].name
});

// Get all products for this business
const businessProducts = await db
  .select()
  .from(products)
  .where(eq(products.businessId, businessId))
  .limit(5); // Just check first 5 products

console.log(`📦 Found ${businessProducts.length} products for this business`);

for (const product of businessProducts) {
  console.log(`\n🔍 Checking product: ${product.name}`);
  console.log(`   - ID: ${product.id}`);
  console.log(`   - COGS per cup: ${product.cogsPerCup}`);
  
  // Get ingredients for this product
  const productIngredientsData = await db
    .select({
      ingredient: ingredients,
      quantity: productIngredients.quantityNeeded
    })
    .from(productIngredients)
    .innerJoin(ingredients, eq(productIngredients.ingredientId, ingredients.id))
    .where(eq(productIngredients.productId, product.id));
  
  console.log(`   - Ingredients: ${productIngredientsData.length}`);
  
  if (productIngredientsData.length === 0) {
    console.log('   ❌ No ingredients found for this product!');
  } else {
    console.log('   📋 Ingredient details:');
    for (const item of productIngredientsData) {
      console.log(`     - ${item.ingredient.name}: ${item.quantity} ${item.ingredient.unit}`);
      console.log(`       Cost: ${item.ingredient.baseUnitCost} per ${item.ingredient.baseUnitQuantity} ${item.ingredient.unit}`);
    }
  }
}

// Check total counts
const totalProducts = await db.select().from(products).where(eq(products.businessId, businessId));
const totalIngredients = await db.select().from(ingredients).where(eq(ingredients.businessId, businessId));
const totalProductIngredients = await db
  .select()
  .from(productIngredients)
  .innerJoin(products, eq(productIngredients.productId, products.id))
  .where(eq(products.businessId, businessId));

console.log('\n📊 Summary:');
console.log(`   - Total products: ${totalProducts.length}`);
console.log(`   - Total ingredients: ${totalIngredients.length}`);
console.log(`   - Total product-ingredient relationships: ${totalProductIngredients.length}`);

console.log('=== Debug Complete ===');
process.exit(0);