# Better Auth Redis Configuration Example
# Copy this file to .env and fill in your actual values

# ===== REDIS CONFIGURATION =====
# Upstash Redis URL (get from Upstash console)
UPSTASH_REDIS_URL=https://your-redis-instance.upstash.io

# Upstash Redis Token (get from Upstash console)
UPSTASH_REDIS_TOKEN=your-redis-token-here

# ===== BETTER AUTH CONFIGURATION =====
# Better Auth secret key (generate a secure random string)
BETTER_AUTH_SECRET=your-super-secure-secret-key-change-this-in-production

# Better Auth base URL (your application URL)
BETTER_AUTH_URL=https://your-domain.com

# ===== DATABASE CONFIGURATION =====
# PostgreSQL database URL (primary storage)
DATABASE_URL=postgresql://username:password@host:port/database

# ===== RATE LIMITING CONFIGURATION =====
# Enable/disable rate limiting (default: true)
RATE_LIMIT_ENABLED=true

# Enable rate limiting analytics (default: true)
RATE_LIMIT_ANALYTICS=true

# ===== OPTIONAL CONFIGURATION =====
# Node environment
NODE_ENV=development

# CORS allowed origins (comma-separated)
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5174,https://your-domain.com

# Session configuration (optimized for better user experience)
SESSION_EXPIRES_IN=2592000  # 30 days in seconds
SESSION_UPDATE_AGE=21600    # 6 hours in seconds

# ===== SOCIAL PROVIDERS (OPTIONAL) =====
# GitHub OAuth
# GITHUB_CLIENT_ID=your-github-client-id
# GITHUB_CLIENT_SECRET=your-github-client-secret

# Google OAuth
# GOOGLE_CLIENT_ID=your-google-client-id
# GOOGLE_CLIENT_SECRET=your-google-client-secret

# ===== EMAIL CONFIGURATION (OPTIONAL) =====
# Email service configuration for verification emails
# EMAIL_FROM=<EMAIL>
# EMAIL_SMTP_HOST=smtp.your-provider.com
# EMAIL_SMTP_PORT=587
# EMAIL_SMTP_USER=your-smtp-username
# EMAIL_SMTP_PASS=your-smtp-password

# ===== MONITORING AND LOGGING =====
# Enable detailed logging (development only)
DEBUG_AUTH=false

# Enable request logging
LOG_REQUESTS=false

# ===== SECURITY CONFIGURATION =====
# Enable secure cookies in production
SECURE_COOKIES=true

# Enable HTTPS redirect
FORCE_HTTPS=true

# ===== RATE LIMITING CUSTOMIZATION =====
# Custom rate limits (optional - defaults are configured in code)
# Format: RATE_LIMIT_<ENDPOINT>_WINDOW=seconds
# Format: RATE_LIMIT_<ENDPOINT>_MAX=requests

# Login rate limiting
RATE_LIMIT_SIGNIN_WINDOW=900    # 15 minutes
RATE_LIMIT_SIGNIN_MAX=5         # 5 attempts

# Registration rate limiting
RATE_LIMIT_SIGNUP_WINDOW=3600   # 1 hour
RATE_LIMIT_SIGNUP_MAX=3         # 3 attempts

# Password reset rate limiting
RATE_LIMIT_PASSWORD_RESET_WINDOW=3600  # 1 hour
RATE_LIMIT_PASSWORD_RESET_MAX=3        # 3 attempts

# Session check rate limiting
RATE_LIMIT_SESSION_WINDOW=60    # 1 minute
RATE_LIMIT_SESSION_MAX=100      # 100 requests

# General API rate limiting
RATE_LIMIT_GENERAL_WINDOW=60    # 1 minute
RATE_LIMIT_GENERAL_MAX=60       # 60 requests
