// Simple test to check if the route is accessible with proper authentication
const testRouteAccess = async () => {
  console.log('🔍 Testing Route Access with Session...');
  
  try {
    // Test the RBAC route with the session cookies
    const response = await fetch('http://localhost:5175/business/26b99509-b432-4e5d-8930-c8bc28c8ed0e/settings/rbac', {
      method: 'GET',
      headers: {
        'Cookie': 'better-auth.session_data=%7B%22user%22%3A%7B%22id%22%3A%223f92d9b7-0758-43b6-9c5e-a5e8d976400c%22%2C%22email%22%3A%22test%40rbac.com%22%2C%22emailVerified%22%3Afalse%2C%22name%22%3A%22Test%20User%22%2C%22createdAt%22%3A%222024-12-19T14%3A59%3A59.000Z%22%2C%22updatedAt%22%3A%222024-12-19T14%3A59%3A59.000Z%22%2C%22firstName%22%3Anull%2C%22lastName%22%3Anull%7D%2C%22session%22%3A%7B%22id%22%3A%22GOFhdFtje3NrZTES4sQmXXSMC8mY7LPS%22%2C%22userId%22%3A%223f92d9b7-0758-43b6-9c5e-a5e8d976400c%22%2C%22expiresAt%22%3A%222025-01-18T15%3A00%3A00.000Z%22%2C%22token%22%3A%22GOFhdFtje3NrZTES4sQmXXSMC8mY7LPS.caaM%2Bt89DST0VLvZLDvagSIC5NtHBSoM7DdoNlinEEo%3D%22%2C%22ipAddress%22%3A%22127.0.0.1%22%2C%22userAgent%22%3A%22curl%2F8.7.1%22%2C%22createdAt%22%3A%222024-12-19T15%3A00%3A00.000Z%22%2C%22updatedAt%22%3A%222024-12-19T15%3A00%3A00.000Z%22%7D%7D; better-auth.session_token=GOFhdFtje3NrZTES4sQmXXSMC8mY7LPS.caaM%2Bt89DST0VLvZLDvagSIC5NtHBSoM7DdoNlinEEo%3D',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
      },
      redirect: 'manual' // Don't follow redirects
    });
    
    console.log('📊 Response Status:', response.status);
    console.log('📍 Response Headers:');
    for (const [key, value] of response.headers.entries()) {
      console.log(`   ${key}: ${value}`);
    }
    
    if (response.status === 302) {
      console.log('❌ Still getting redirected to login');
      console.log('🔍 This suggests the session is not being recognized properly');
    } else if (response.status === 200) {
      console.log('✅ Route is accessible!');
    } else {
      console.log(`⚠️ Unexpected status: ${response.status}`);
    }
    
  } catch (error) {
    console.error('❌ Error testing route access:', error);
  }
};

testRouteAccess();