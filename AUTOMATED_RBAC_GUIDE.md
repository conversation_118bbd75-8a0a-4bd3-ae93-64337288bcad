# Automated RBAC Protection Guide

This guide explains how to use the new automated RBAC (Role-Based Access Control) system for protecting routes with minimal code changes.

## Overview

The automated RBAC system provides:
- **Automatic permission checking** based on HTTP methods (GET → read, POST → create, PUT → update, DELETE → delete)
- **Business ID extraction** from URL parameters or query strings
- **Pre-configured settings** for common resources
- **Minimal code changes** to existing routes

## Quick Start

### 1. Import the Required Functions

```typescript
import { createProtectedRoute, RBAC_CONFIGS } from "~/lib/middleware/autoRBAC.server";
```

### 2. Convert Your Route Functions

Change your exported functions to regular functions:

```typescript
// Before
export async function loader({ request }: LoaderFunctionArgs) {
  // Your existing code
}

export async function action({ request }: ActionFunctionArgs) {
  // Your existing code
}

// After
async function myLoader({ request }: LoaderFunctionArgs) {
  // Your existing code (remove manual RBAC checks)
}

async function myAction({ request }: ActionFunctionArgs) {
  // Your existing code (remove manual RBAC checks)
}
```

### 3. Create Protected Route

```typescript
// Create protected route with automatic RBAC
const { loader, action } = createProtectedRoute(
  RBAC_CONFIGS.products, // Use pre-configured settings
  {
    loader: myLoader,
    action: myAction,
  }
);

export { loader, action };
```

## Available Pre-configured Resources

The following resources have pre-configured RBAC settings:

- `RBAC_CONFIGS.products`
- `RBAC_CONFIGS.ingredients`
- `RBAC_CONFIGS.categories`
- `RBAC_CONFIGS.inventory`
- `RBAC_CONFIGS.cogs`
- `RBAC_CONFIGS.users`
- `RBAC_CONFIGS.roles`

## Permission Mapping

The system automatically maps HTTP methods to permissions:

| HTTP Method | Permission Action | Example Permission |
|-------------|-------------------|--------------------|
| GET         | read              | `products.read`    |
| POST        | create            | `products.create`  |
| PUT         | update            | `products.update`  |
| PATCH       | update            | `products.update`  |
| DELETE      | delete            | `products.delete`  |

## Business ID Extraction

The system automatically extracts `businessId` from:

1. **URL Parameters**: `/business/:businessId/products`
2. **Query Parameters**: `/api/products?businessId=123`
3. **Request Body**: `{ businessId: "123", ... }`

## Custom Configuration

For routes that need custom settings:

```typescript
const customConfig: AutoRBACConfig = {
  resource: 'custom_resource',
  getBusinessId: async (args) => {
    // Custom business ID extraction logic
    const body = await args.request.json();
    return body.customBusinessId;
  },
  additionalChecks: async (args, businessId) => {
    // Custom validation logic
    if (someCondition) {
      throw new Response('Custom error', { status: 403 });
    }
  },
};

const { loader, action } = createProtectedRoute(customConfig, {
  loader: myLoader,
  action: myAction,
});
```

## What Gets Removed from Your Code

When using automated RBAC, you can remove:

```typescript
// ❌ Remove these manual checks
const session = await getSession(request);
if (!session?.user) {
  return json({ error: "Unauthorized" }, { status: 401 });
}

// ❌ Remove business ID validation
if (!businessId) {
  return json({ error: "Business ID is required" }, { status: 400 });
}

// ❌ Remove manual permission checks
await requirePermissions(request, {
  permissions: ['products.read'],
  businessId,
});
```

## Error Handling

The automated system returns standardized errors:

- **401 Unauthorized**: User not authenticated
- **400 Bad Request**: Business ID missing or invalid
- **403 Forbidden**: Insufficient permissions
- **500 Internal Server Error**: System errors

## Migration Examples

### Before (Manual RBAC)

```typescript
export async function loader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const url = new URL(request.url);
  const businessId = url.searchParams.get("businessId");
  
  if (!businessId) {
    return json({ error: "Business ID is required" }, { status: 400 });
  }

  await requirePermissions(request, {
    permissions: ['products.read'],
    businessId,
  });

  // Actual business logic
  const products = await ProductService.getAll(businessId);
  return json({ products });
}
```

### After (Automated RBAC)

```typescript
import { createProtectedRoute, RBAC_CONFIGS } from "~/lib/middleware/autoRBAC.server";

async function productsLoader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request); // Still needed for user ID
  const url = new URL(request.url);
  const businessId = url.searchParams.get("businessId")!; // businessId guaranteed to exist

  // Only business logic remains
  const products = await ProductService.getAll(businessId);
  return json({ products });
}

const { loader } = createProtectedRoute(
  RBAC_CONFIGS.products,
  { loader: productsLoader }
);

export { loader };
```

## Benefits

1. **Consistency**: All routes use the same permission checking logic
2. **Maintainability**: RBAC logic is centralized
3. **Security**: Reduces chance of missing permission checks
4. **Developer Experience**: Less boilerplate code
5. **Type Safety**: TypeScript support for configurations

## Best Practices

1. **Use pre-configured resources** when possible
2. **Keep business logic separate** from RBAC concerns
3. **Test permission scenarios** thoroughly
4. **Document custom configurations** for team members
5. **Follow naming conventions** for resources and permissions

## Troubleshooting

### Common Issues

1. **Business ID not found**: Check URL structure and query parameters
2. **Permission denied**: Verify user has correct role assignments
3. **Type errors**: Ensure proper imports and function signatures

### Debug Mode

Enable debug logging by setting environment variable:
```bash
DEBUG_RBAC=true
```

## Future Enhancements

- Support for route-level permission overrides
- Integration with audit logging
- Performance optimizations
- Additional pre-configured resources