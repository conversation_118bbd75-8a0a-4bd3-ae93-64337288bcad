# Authentication Setup Guide

This guide will help you set up the comprehensive authentication system for KWACI Grow using Better Auth with PostgreSQL/Supabase.

## Overview

The authentication system includes:
- ✅ Better Auth integration with PostgreSQL adapter
- ✅ Supabase-compatible database setup
- ✅ Email/password authentication
- ✅ Protected routes
- ✅ User session management
- ✅ Login/Register UI components
- ✅ Integration with existing business management system

## Prerequisites

- Node.js 20+ installed
- Bun package manager
- Supabase account (or PostgreSQL database)

## Step 1: Database Setup

### Option A: Using Supabase (Recommended)

1. **Create a Supabase Project**
   - Go to [supabase.com](https://supabase.com)
   - Create a new project
   - Wait for the project to be ready

2. **Get Database Connection String**
   - Go to Project Settings → Database
   - Copy the connection string under "Connection string"
   - It should look like: `postgresql://postgres:[YOUR-PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres`

3. **Run Database Schema**
   - Go to SQL Editor in Supabase dashboard
   - Copy and paste the contents of `database/schema.sql`
   - Click "Run" to create the tables

### Option B: Using Local PostgreSQL

1. **Install PostgreSQL**
   ```bash
   # macOS
   brew install postgresql
   
   # Ubuntu
   sudo apt-get install postgresql
   ```

2. **Create Database**
   ```bash
   createdb kwaci_grow
   ```

3. **Run Schema**
   ```bash
   psql kwaci_grow < database/schema.sql
   ```

## Step 2: Environment Configuration

1. **Copy Environment Template**
   ```bash
   cp .env.example .env
   ```

2. **Update Environment Variables**
   Edit `.env` file with your actual values:
   ```env
   # Database Configuration
   DATABASE_URL="your-actual-database-connection-string"
   
   # Better Auth Configuration
   BETTER_AUTH_SECRET="your-random-secret-key"
   BETTER_AUTH_URL="http://localhost:3000"
   ```

3. **Generate Secret Key**
   ```bash
   # Generate a secure secret key
   openssl rand -base64 32
   ```

## Step 3: Install Dependencies

Dependencies are already installed, but if you need to reinstall:

```bash
bun install
```

## Step 4: Start Development Server

```bash
bun run dev
```

## Step 5: Test Authentication

1. **Visit the Application**
   - Open http://localhost:3000
   - You should see the authentication prompt

2. **Create an Account**
   - Click "Sign Up"
   - Fill in the registration form
   - Submit to create your account

3. **Sign In**
   - Use your credentials to sign in
   - You should be redirected to the dashboard

## Features

### Authentication Components

- **LoginForm**: Email/password login form
- **RegisterForm**: User registration form
- **AuthModal**: Modal wrapper for auth forms
- **UserMenu**: Dropdown menu for authenticated users
- **ProtectedRoute**: Component to protect routes

### Integration Points

- **Business Store Integration**: Authentication is integrated with the existing business management system
- **Route Protection**: The root route is now protected and requires authentication
- **User Context**: User information is available throughout the app via `useAuth()` hook

### API Routes

- **`/api/auth/*`**: All Better Auth endpoints are handled by this route

## Environment Variables Reference

| Variable | Description | Required | Example |
|----------|-------------|----------|---------|
| `DATABASE_URL` | PostgreSQL connection string | Yes | `********************************/db` |
| `BETTER_AUTH_SECRET` | Secret key for session encryption | Yes | `your-secret-key` |
| `BETTER_AUTH_URL` | Application base URL | Yes | `http://localhost:3000` |
| `GITHUB_CLIENT_ID` | GitHub OAuth client ID | No | `your-github-client-id` |
| `GITHUB_CLIENT_SECRET` | GitHub OAuth client secret | No | `your-github-client-secret` |

## Optional: Social Authentication

To enable social authentication (GitHub, Google, etc.):

1. **Update Auth Configuration**
   Edit `app/lib/auth.server.ts` and uncomment the social providers section

2. **Add Environment Variables**
   Add the required OAuth credentials to your `.env` file

3. **Configure OAuth Apps**
   - GitHub: https://github.com/settings/applications/new
   - Google: https://console.cloud.google.com/

## Database Schema

The authentication system creates the following tables:

- **`user`**: User accounts and profile information
- **`session`**: Active user sessions
- **`account`**: OAuth accounts and password hashes
- **`verification`**: Email verification and password reset tokens

## Session Configuration

The authentication system is configured with optimized session settings to provide a balance of security and user experience:

- **Session Duration**: 30 days - Extended for better user experience
- **Session Refresh**: Every 6 hours - Keeps sessions fresh for active users
- **Cookie Cache**: 24 hours - Reduces database calls and improves performance
- **Auto Refresh**: Enabled - Sessions automatically extend for active users

This configuration eliminates short session timeouts while maintaining security through regular session updates.

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Verify your `DATABASE_URL` is correct
   - Check if your database is accessible
   - Ensure the database exists

2. **Authentication Not Working**
   - Check if the database schema is properly created
   - Verify `BETTER_AUTH_SECRET` is set
   - Check browser console for errors

3. **Session Issues**
   - Clear browser cookies and localStorage
   - Restart the development server
   - Check if session table exists in database

### Getting Help

If you encounter issues:
1. Check the browser console for errors
2. Check the server logs
3. Verify all environment variables are set correctly
4. Ensure database tables are created properly

## Next Steps

### Possible Enhancements

1. **Email Verification**
   - Configure SMTP settings
   - Enable email verification in auth config
   - Add email verification UI

2. **Password Reset**
   - Implement password reset flow
   - Add forgot password UI
   - Configure email templates

3. **Two-Factor Authentication**
   - Add 2FA plugin to Better Auth
   - Implement TOTP setup UI
   - Add backup codes

4. **Social Login**
   - Configure OAuth providers
   - Add social login buttons
   - Handle OAuth callbacks

5. **User Profile Management**
   - Add profile editing UI
   - Implement avatar upload
   - Add account settings page

6. **Role-Based Access Control**
   - Define user roles
   - Implement permission system
   - Add admin interface

7. **Audit Logging**
   - Track user actions
   - Log authentication events
   - Add security monitoring

## Security Considerations

- Always use HTTPS in production
- Keep `BETTER_AUTH_SECRET` secure and unique
- Regularly update dependencies
- Implement rate limiting for auth endpoints
- Use strong password requirements
- Consider implementing 2FA for sensitive accounts
