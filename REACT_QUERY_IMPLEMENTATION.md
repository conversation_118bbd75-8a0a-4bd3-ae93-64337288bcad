# TanStack Query (React Query) v5 Implementation

This document describes the implementation of TanStack Query v5 in the kwaci-grow project for performant data fetching with automatic caching, background updates, and optimistic updates.

## Overview

TanStack Query has been integrated to replace direct API calls with a more robust data fetching solution that provides:

- **Automatic caching** with intelligent cache invalidation
- **Background updates** to keep data fresh
- **Optimistic updates** for immediate UI feedback
- **Built-in loading and error states**
- **Automatic retries** with configurable strategies
- **DevTools** for debugging and monitoring

## Architecture

### File Structure

```
app/lib/query/
├── QueryProvider.tsx          # Main provider component
├── queryClient.ts            # QueryClient configuration
├── queryKeys.ts              # Centralized query key definitions
├── hooks/
│   ├── index.ts              # Export all hooks
│   ├── useBusinesses.ts      # Fetch all businesses for a user
│   ├── useBusiness.ts        # Fetch single business
│   ├── useCreateBusiness.ts  # Create business mutation
│   ├── useUpdateBusiness.ts  # Update business mutation
│   └── useDeleteBusiness.ts  # Delete business mutation
├── utils/
│   └── cacheUtils.ts         # Cache management utilities
└── index.ts                  # Main exports
```

### Query Keys Structure

Query keys follow a hierarchical structure for efficient cache management:

```typescript
// All businesses for a user
['businesses', userId]

// Specific business
['businesses', userId, businessId]
```

This structure allows for:
- Invalidating all businesses for a user
- Targeting specific business data
- Proper cache management and cleanup

## Configuration

### QueryClient Settings

- **staleTime**: 5 minutes (data doesn't change frequently)
- **gcTime**: 10 minutes (keep data in cache longer)
- **refetchOnWindowFocus**: true (refresh when user returns to tab)
- **refetchOnReconnect**: true (refresh when internet reconnects)
- **retry**: 3 attempts with smart error handling

### Error Handling

- Global error handlers for queries and mutations
- Integration with sonner toast notifications
- Smart retry logic that avoids retrying authentication errors

## Usage

### Setup

The QueryProvider is already configured in the root component:

```tsx
import { QueryProvider } from '~/lib/query'

function RootComponent() {
  return (
    <QueryProvider>
      {/* Your app components */}
    </QueryProvider>
  )
}
```

### Hooks

#### Fetching Data

```tsx
import { useBusinesses, useBusiness } from '~/lib/query/hooks'

// Fetch all businesses for a user
const { data: businesses, isLoading, error } = useBusinesses(userId)

// Fetch a specific business
const { data: business, isLoading, error } = useBusiness(businessId, userId)
```

#### Mutations

```tsx
import { 
  useCreateBusiness, 
  useUpdateBusiness, 
  useDeleteBusiness 
} from '~/lib/query/hooks'

// Create a business
const createMutation = useCreateBusiness(userId)
await createMutation.mutateAsync(businessData)

// Update a business
const updateMutation = useUpdateBusiness(businessId, userId)
await updateMutation.mutateAsync(updateData)

// Delete a business
const deleteMutation = useDeleteBusiness(userId)
await deleteMutation.mutateAsync(businessId)
```

## Integration with Existing Code

### Zustand Store Integration

The implementation maintains compatibility with the existing Zustand store:

- React Query handles data fetching and caching
- Zustand store manages UI state (current business selection)
- Data is synced between both systems

### Component Updates

Components like `BusinessManagementSheet` have been updated to use React Query mutations while maintaining the same interface.

## Benefits

### Performance Improvements

1. **Automatic Caching**: Reduces unnecessary API calls
2. **Background Updates**: Keeps data fresh without user intervention
3. **Optimistic Updates**: Immediate UI feedback for better UX
4. **Intelligent Refetching**: Only fetches when necessary

### Developer Experience

1. **DevTools**: Visual debugging and monitoring
2. **Built-in Loading States**: No need to manage loading manually
3. **Error Handling**: Centralized error management
4. **Type Safety**: Full TypeScript support

### Cache Management

1. **Automatic Invalidation**: Cache updates automatically after mutations
2. **Selective Invalidation**: Target specific queries for updates
3. **Cache Cleanup**: Automatic cleanup on logout
4. **Stale-While-Revalidate**: Show cached data while fetching fresh data

## DevTools

React Query DevTools are available in development mode:
- Access via the floating button in the bottom-right corner
- Monitor query states, cache contents, and network requests
- Debug performance issues and cache behavior

## Migration Notes

### Backward Compatibility

The implementation maintains backward compatibility with existing components:
- Same hook interfaces from Zustand store
- Existing components work without changes
- Gradual migration path available

### Future Improvements

1. **Server-Side Rendering**: Add SSR support with hydration
2. **Infinite Queries**: For paginated business lists
3. **Real-time Updates**: WebSocket integration for live data
4. **Offline Support**: Cache persistence for offline functionality

## Best Practices

1. **Use Query Keys Consistently**: Always use the centralized query keys
2. **Handle Loading States**: Provide proper loading indicators
3. **Error Boundaries**: Implement error boundaries for better error handling
4. **Cache Invalidation**: Use proper invalidation strategies
5. **Optimistic Updates**: Implement optimistic updates for better UX

## Troubleshooting

### Common Issues

1. **Cache Not Updating**: Check query key consistency
2. **Infinite Loops**: Verify dependency arrays in useEffect
3. **Memory Leaks**: Ensure proper cleanup on unmount
4. **Stale Data**: Check staleTime and refetch settings

### Debug Tools

1. **React Query DevTools**: Monitor query states and cache
2. **Network Tab**: Check actual API calls
3. **Console Logs**: Built-in logging for debugging
4. **Error Boundaries**: Catch and display errors properly
