# Better Auth Redis Upgrade Guide

## Overview

This guide covers the upgrade of your Better Auth implementation to use Redis as secondary storage for improved performance, scalability, and comprehensive rate limiting.

## Features Implemented

✅ **Redis Secondary Storage**: Session data stored in Redis for better performance  
✅ **Comprehensive Rate Limiting**: Different limits for different authentication endpoints  
✅ **CORS Support**: Enhanced CORS handling for external applications  
✅ **Error Handling**: Internationalized error messages for rate limiting  
✅ **Health Monitoring**: Redis health checks and fallback mechanisms  
✅ **Analytics**: Rate limiting analytics through Upstash dashboard  

## Prerequisites

### 1. Required Dependencies

Add these dependencies to your `package.json`:

```bash
bun add @upstash/redis @upstash/ratelimit
```

### 2. Environment Variables

Add the following environment variables to your `.env` file:

```env
# Redis Configuration (Upstash)
UPSTASH_REDIS_URL=https://your-redis-url.upstash.io
UPSTASH_REDIS_TOKEN=your-redis-token

# Better Auth Configuration (existing)
BETTER_AUTH_SECRET=your-secret-key
BETTER_AUTH_URL=https://your-domain.com
DATABASE_URL=postgresql://user:password@host:port/database

# Optional: Rate Limiting Configuration
RATE_LIMIT_ENABLED=true
RATE_LIMIT_ANALYTICS=true
```

### 3. Upstash Redis Setup

1. **Create Upstash Account**: Go to [console.upstash.com](https://console.upstash.com)
2. **Create Redis Database**: 
   - Choose your preferred region
   - Select appropriate plan (free tier available)
   - Note down the Redis URL and token
3. **Configure Environment**: Add the credentials to your `.env` file

## Implementation Details

### 1. Redis Configuration (`app/lib/redis.server.ts`)

- **Secondary Storage**: Implements Better Auth secondary storage interface
- **Rate Limiting**: Configures different rate limiters for different endpoints
- **Connection Pooling**: Automatic retry and connection management
- **Health Checks**: Redis availability monitoring

### 2. Enhanced Auth Server (`app/lib/auth.server.ts`)

- **Secondary Storage Integration**: Sessions stored in Redis
- **Rate Limiting Configuration**: Built-in rate limiting rules
- **Performance Optimization**: Cookie caching and session management

### 3. Rate Limiting Middleware (`app/lib/rateLimit.server.ts`)

- **Endpoint-Specific Limits**: Different limits for different operations
- **User-Based Limiting**: Support for authenticated user rate limiting
- **API Key Limiting**: Support for API key-based rate limiting
- **Comprehensive Logging**: Rate limit event logging and monitoring

### 4. Enhanced Auth Route (`app/routes/api.auth.$.enhanced.ts`)

- **Integrated Rate Limiting**: Automatic rate limit checking
- **CORS Support**: Cross-origin request handling
- **Bearer Token Support**: API client authentication
- **Error Handling**: Comprehensive error responses

## Rate Limiting Configuration

### Default Rate Limits

| Endpoint | Window | Max Requests | Description |
|----------|--------|--------------|-------------|
| `/sign-in/email` | 15 minutes | 5 | Login attempts |
| `/sign-up/email` | 1 hour | 3 | Registration attempts |
| `/forget-password` | 1 hour | 3 | Password reset requests |
| `/reset-password` | 1 hour | 5 | Password reset attempts |
| `/get-session` | 1 minute | 100 | Session checks |
| `/sign-out` | 1 minute | 10 | Logout attempts |

### Customizing Rate Limits

Edit the `RATE_LIMIT_CONFIGS` in `app/lib/rateLimit.server.ts`:

```typescript
export const RATE_LIMIT_CONFIGS = {
  '/sign-in/email': {
    window: 15 * 60, // 15 minutes
    max: 5, // 5 attempts per 15 minutes
    description: 'Login attempts',
  },
  // Add or modify other endpoints
} as const;
```

## Migration Steps

### 1. Install Dependencies

```bash
bun add @upstash/redis @upstash/ratelimit
```

### 2. Set Up Environment Variables

```bash
# Copy the example environment variables
cp .env.example .env

# Add your Upstash Redis credentials
echo "UPSTASH_REDIS_URL=your-redis-url" >> .env
echo "UPSTASH_REDIS_TOKEN=your-redis-token" >> .env
```

### 3. Replace Auth Route

```bash
# Backup existing auth route
mv app/routes/api.auth.$.ts app/routes/api.auth.$.backup.ts

# Use the enhanced auth route
mv app/routes/api.auth.$.enhanced.ts app/routes/api.auth.$.ts
```

### 4. Test the Implementation

```bash
# Start the development server
bun run dev

# Test authentication endpoints
curl -X POST http://localhost:5174/api/auth/sign-in/email \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

## Testing Rate Limiting

### 1. Test Login Rate Limiting

```bash
# Make multiple login attempts to trigger rate limiting
for i in {1..6}; do
  curl -X POST http://localhost:5174/api/auth/sign-in/email \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"wrongpassword"}' \
    -w "Status: %{http_code}\n"
done
```

### 2. Check Rate Limit Headers

```bash
curl -X POST http://localhost:5174/api/auth/sign-in/email \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}' \
  -I
```

Expected headers:
- `X-RateLimit-Limit`: Maximum requests allowed
- `X-RateLimit-Remaining`: Remaining requests in window
- `X-RateLimit-Reset`: Reset timestamp
- `Retry-After`: Seconds to wait (if rate limited)

### 3. Monitor Redis

```bash
# Check Redis connection
curl http://localhost:5174/api/health/redis

# View rate limiting data in Upstash console
# Go to console.upstash.com → Your Database → Rate Limit Analytics
```

## Performance Benefits

### Before (PostgreSQL Only)
- Session data stored in PostgreSQL
- No rate limiting protection
- Higher database load for session operations
- No automatic session expiration

### After (Redis + PostgreSQL)
- Session data stored in Redis (faster access)
- Comprehensive rate limiting protection
- Reduced PostgreSQL load
- Automatic session expiration via Redis TTL
- Real-time rate limiting analytics

## Security Enhancements

### 1. Rate Limiting Protection
- **Brute Force Protection**: Login attempt limiting
- **DDoS Mitigation**: Request rate limiting
- **Resource Protection**: API endpoint protection

### 2. Session Security
- **Automatic Expiration**: Redis TTL for sessions
- **Secure Storage**: Encrypted session data
- **Performance**: Faster session validation

### 3. Monitoring and Analytics
- **Rate Limit Analytics**: Upstash dashboard
- **Request Logging**: Comprehensive request tracking
- **Health Monitoring**: Redis availability checks

## Troubleshooting

### Common Issues

1. **Redis Connection Failed**
   ```bash
   # Check environment variables
   echo $UPSTASH_REDIS_URL
   echo $UPSTASH_REDIS_TOKEN
   
   # Test Redis connection
   curl -X POST $UPSTASH_REDIS_URL/ping \
     -H "Authorization: Bearer $UPSTASH_REDIS_TOKEN"
   ```

2. **Rate Limiting Not Working**
   ```bash
   # Check Redis health endpoint
   curl http://localhost:5174/api/health/redis
   
   # Check application logs
   bun run dev
   ```

3. **Sessions Not Persisting**
   ```bash
   # Check Redis data
   # Go to Upstash console → Data Browser
   # Look for keys starting with "session:"
   ```

### Fallback Behavior

The implementation includes fallback mechanisms:
- **Redis Unavailable**: Rate limiting disabled, authentication continues
- **Rate Limit Errors**: Requests allowed (fail-open policy)
- **Session Storage**: Falls back to PostgreSQL if Redis fails

## Monitoring and Maintenance

### 1. Upstash Dashboard
- Monitor rate limiting analytics
- View request patterns and blocked requests
- Track Redis performance metrics

### 2. Application Logs
- Rate limiting events
- Redis connection status
- Authentication request patterns

### 3. Health Checks
```bash
# Redis health check
curl http://localhost:5174/api/health/redis

# Rate limiting status
curl http://localhost:5174/api/health/ratelimit
```

## Next Steps

1. **Monitor Performance**: Track Redis usage and rate limiting effectiveness
2. **Adjust Limits**: Fine-tune rate limits based on usage patterns
3. **Add Monitoring**: Implement alerting for Redis availability
4. **Scale Redis**: Consider Redis clustering for high-traffic applications
5. **Analytics**: Use rate limiting analytics to understand usage patterns

## Support

- **Better Auth Documentation**: [better-auth.com/docs](https://www.better-auth.com/docs)
- **Upstash Documentation**: [upstash.com/docs](https://upstash.com/docs)
- **Rate Limiting Guide**: [upstash.com/docs/redis/sdks/ratelimit-ts](https://upstash.com/docs/redis/sdks/ratelimit-ts)
