import { RBACService } from './app/lib/services/rbacService.server.js';

const userId = '1d5fafa1-1342-4d8a-9d17-5e1b46f6a5f4'; // <PERSON>'s ID
const businessId = '87b243bd-cf17-4af0-9af1-04bd5e5c99d2'; // KWACI Coffee House ID

console.log('Testing <PERSON>\'s permissions...');

// Test getUserPermissions
const permissions = await RBACService.getUserPermissions(userId, businessId);
console.log('User permissions:', JSON.stringify(permissions, null, 2));

// Test hasRole specifically
const hasBusinessOwnerRole = await RBACService.hasRole(userId, 'business_owner', businessId);
console.log('Has business_owner role:', hasBusinessOwnerRole);

process.exit(0);