import { db } from './app/lib/db/connection.ts';
import { users, userRoles, roles } from './app/lib/db/schema.ts';
import { eq } from 'drizzle-orm';

console.log('=== Debug Session User ===');

// Check who the session user is
const sessionUserId = '1d5fafa1-1342-4d8a-9d17-5e1b46f6a5f4';
const sessionUser = await db
  .select()
  .from(users)
  .where(eq(users.id, sessionUserId))
  .limit(1);

if (sessionUser.length === 0) {
  console.log('❌ Session user not found!');
} else {
  console.log('✅ Session user found:', {
    id: sessionUser[0].id,
    name: sessionUser[0].name,
    email: sessionUser[0].email
  });
}

// Check <PERSON>'s user ID
const johnUser = await db
  .select()
  .from(users)
  .where(eq(users.email, '<EMAIL>'))
  .limit(1);

if (johnUser.length > 0) {
  console.log('✅ John user found:', {
    id: john<PERSON><PERSON>[0].id,
    name: john<PERSON><PERSON>[0].name,
    email: john<PERSON><PERSON>[0].email
  });
}

// Check if session user has any roles for the business
const businessId = 'c2dc351a-f97f-406b-84c6-fd4d3c1fb1c9';
const sessionUserRoles = await db
  .select({
    roleName: roles.name,
    businessId: userRoles.businessId,
    isActive: userRoles.isActive
  })
  .from(userRoles)
  .innerJoin(roles, eq(userRoles.roleId, roles.id))
  .where(
    eq(userRoles.userId, sessionUserId) &&
    eq(userRoles.businessId, businessId)
  );

console.log('📋 Session user roles for business:', sessionUserRoles);

console.log('=== Debug Complete ===');