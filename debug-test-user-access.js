import { PrismaClient } from '@prisma/client';
import { RBACService } from './app/lib/services/rbacService.server.js';

const prisma = new PrismaClient();

async function debugUserAccess() {
  const testUser = await prisma.user.findMany({
    where: { email: '<EMAIL>' }
  });
  
  if (testUser.length === 0) {
    console.log('❌ User <EMAIL> not found');
    return;
  }
  
  const user = testUser[0];
  console.log('👤 User Details:');
  console.log(`- ID: ${user.id}`);
  console.log(`- Email: ${user.email}`);
  console.log(`- Name: ${user.name}`);
  
  // Check owned businesses
  const ownedBusinesses = await prisma.business.findMany({
    where: { userId: user.id }
  });
  
  console.log(`\n🏢 Owned Businesses: ${ownedBusinesses.length}`);
  
  // Check specific business
  const targetBusinessId = '26b99509-b432-4e5d-8930-c8bc28c8ed0e';
  const targetBusiness = await prisma.business.findUnique({
    where: { id: targetBusinessId }
  });
  
  console.log(`\n🔍 Checking access to business: ${targetBusinessId}`);
  if (targetBusiness) {
    console.log(`✅ Target business exists: ${targetBusiness.name}`);
    console.log(`- Owner ID: ${targetBusiness.userId}`);
    console.log(`- Is user the owner? ${targetBusiness.userId === user.id}`);
  } else {
    console.log('❌ Target business not found');
  }
  
  // Check user roles for this business
  const userRoles = await prisma.userRole.findMany({
    where: {
      userId: user.id,
      businessId: targetBusinessId
    },
    include: {
      role: true
    }
  });
  
  console.log(`\n🎭 User Roles for target business: ${userRoles.length}`);
  userRoles.forEach(userRole => {
    console.log(`- Role: ${userRole.role.name} | Active: ${userRole.isActive}`);
  });
  
  // Test RBAC Service functions
  console.log('\n🔐 RBAC Service Results:');
  try {
    const hasBusinessOwnerRole = await RBACService.hasRole(user.id, 'business_owner', targetBusinessId);
    console.log(`- Has business_owner role: ${hasBusinessOwnerRole}`);
    
    const canAccess = await RBACService.canAccessBusiness(user.id, targetBusinessId);
    console.log(`- Can access business: ${canAccess}`);
    
    const permissions = await RBACService.getUserPermissions(user.id, targetBusinessId);
    console.log(`- Permissions count: ${permissions.length}`);
    
    const roles = await RBACService.getUserRoles(user.id, targetBusinessId);
    console.log(`- Roles: ${JSON.stringify(roles)}`);
  } catch (error) {
    console.error('❌ RBAC Service error:', error);
  }
  
  console.log('\n✅ Debug script completed successfully');
}

debugUserAccess().catch(console.error).finally(() => prisma.$disconnect());
