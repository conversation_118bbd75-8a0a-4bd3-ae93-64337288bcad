#!/usr/bin/env bun

import { RBACService } from '../app/lib/services/rbacService.server';
import { db } from '../app/lib/db/connection';
import { users, businesses, roles, userRoles, rolePermissions, permissions } from '../app/lib/db/schema';
import { eq, and, like } from 'drizzle-orm';

// Available roles for testing
const AVAILABLE_ROLES = [
  'super_admin',
  'business_owner', 
  'business_manager',
  'inventory_manager',
  'staff',
  'viewer'
] as const;

type Role = typeof AVAILABLE_ROLES[number];

// Function to assign specific permissions to test user
async function assignTestUserPermissions(permissionNames: string[], businessName?: string) {
  try {
    console.log('🔍 Finding test user...');
    const testUser = await db.select().from(users).where(eq(users.email, '<EMAIL>')).limit(1);
    
    if (!testUser[0]) {
      console.error('❌ Test user (<EMAIL>) not found. Please run database seeding first.');
      process.exit(1);
    }

    console.log(`✅ Found test user: ${testUser[0].name} (${testUser[0].email})`);

    // Find the business
    const targetBusinessName = businessName || 'KWACI Coffee House';
    console.log(`🏢 Finding business: ${targetBusinessName}...`);
    
    const business = await db.select().from(businesses)
      .where(eq(businesses.name, targetBusinessName))
      .limit(1);
    
    if (!business[0]) {
      console.error(`❌ Business '${targetBusinessName}' not found.`);
      console.log('Available businesses:');
      const allBusinesses = await db.select().from(businesses);
      allBusinesses.forEach(b => console.log(`  - ${b.name}`));
      process.exit(1);
    }

    console.log(`✅ Found business: ${business[0].name}`);
    
    // Validate permissions exist
    console.log('🔍 Validating permissions...');
    const allPermissions = await RBACService.getAllPermissions();
    const validPermissions = permissionNames.filter(permName => 
      allPermissions.some(p => p.name === permName)
    );
    const invalidPermissions = permissionNames.filter(permName => 
      !allPermissions.some(p => p.name === permName)
    );
    
    if (invalidPermissions.length > 0) {
      console.error(`❌ Invalid permissions found: ${invalidPermissions.join(', ')}`);
      console.log('\nAvailable permissions:');
      allPermissions.forEach(p => console.log(`  - ${p.name}: ${p.description || 'No description'}`));
      process.exit(1);
    }
    
    if (validPermissions.length === 0) {
      console.error('❌ No valid permissions provided.');
      process.exit(1);
    }
    
    console.log(`✅ Valid permissions: ${validPermissions.join(', ')}`);
    
    // Create a custom role for these permissions
    const customRoleName = `test_custom_${Date.now()}`;
    const customRoleDisplayName = `Test Custom Role - ${validPermissions.join(', ')}`;
    
    console.log(`🔐 Creating custom role: ${customRoleDisplayName}...`);
    const customRole = await RBACService.createRole(
      customRoleName,
      customRoleDisplayName,
      `Custom role for testing permissions: ${validPermissions.join(', ')}`
    );
    
    // Assign permissions to the custom role
    console.log('🔗 Assigning permissions to custom role...');
    await RBACService.assignPermissionsToRole(customRole.id, validPermissions);
    
    // Assign the custom role to the test user
    console.log(`👤 Assigning custom role to test user for ${business[0].name}...`);
    await RBACService.assignRole(testUser[0].id, customRoleName, testUser[0].id, business[0].id);
    
    console.log(`✅ Successfully assigned permissions to test user for ${business[0].name}`);
    console.log('\n🧪 Test user is now ready for RBAC testing!');
    console.log(`   📧 Login: <EMAIL>`);
    console.log(`   🔒 Password: password123`);
    console.log(`   🎯 Permissions: ${validPermissions.join(', ')}`);
    console.log(`   🏢 Business: ${business[0].name}`);
    
  } catch (error) {
    console.error('❌ Error assigning permissions:', error);
    process.exit(1);
  }
}

async function assignTestUserRole(roleName: Role, businessName?: string) {
  try {
    console.log('🔍 Finding test user...');
    const testUser = await db.select().from(users).where(eq(users.email, '<EMAIL>')).limit(1);
    
    if (!testUser[0]) {
      console.error('❌ Test user (<EMAIL>) not found. Please run database seeding first.');
      process.exit(1);
    }

    console.log(`✅ Found test user: ${testUser[0].name} (${testUser[0].email})`);

    // For super_admin, no business context needed
    if (roleName === 'super_admin') {
      console.log('🔐 Assigning super_admin role (system-wide access)...');
      // Super admin doesn't need business context
      await RBACService.assignRole(testUser[0].id, roleName, testUser[0].id, undefined);
      console.log('✅ Successfully assigned super_admin role to test user');
      return;
    }

    // For other roles, find the business
    const targetBusinessName = businessName || 'KWACI Coffee House';
    console.log(`🏢 Finding business: ${targetBusinessName}...`);
    
    const business = await db.select().from(businesses)
      .where(eq(businesses.name, targetBusinessName))
      .limit(1);
    
    if (!business[0]) {
      console.error(`❌ Business '${targetBusinessName}' not found.`);
      console.log('Available businesses:');
      const allBusinesses = await db.select().from(businesses);
      allBusinesses.forEach(b => console.log(`  - ${b.name}`));
      process.exit(1);
    }

    console.log(`✅ Found business: ${business[0].name}`);
    console.log(`🔐 Assigning ${roleName} role to test user for ${business[0].name}...`);
    
    await RBACService.assignRole(testUser[0].id, roleName, testUser[0].id, business[0].id);
    
    console.log(`✅ Successfully assigned ${roleName} role to test user for ${business[0].name}`);
    console.log('\n🧪 Test user is now ready for RBAC testing!');
    console.log(`   📧 Login: <EMAIL>`);
    console.log(`   🔒 Password: password123`);
    console.log(`   👤 Role: ${roleName}`);
    console.log(`   🏢 Business: ${business[0].name}`);
    
  } catch (error) {
    console.error('❌ Error assigning role:', error);
    process.exit(1);
  }
}

async function removeTestUserRoles() {
  try {
    console.log('🔍 Finding test user...');
    const testUser = await db.select().from(users).where(eq(users.email, '<EMAIL>')).limit(1);
    
    if (!testUser[0]) {
      console.error('❌ Test user (<EMAIL>) not found.');
      process.exit(1);
    }

    console.log('🧹 Removing all roles from test user...');
    
    // Remove system-wide roles (like super_admin)
    const systemPermissions = await RBACService.getUserPermissions(testUser[0].id);
    for (const roleName of systemPermissions.roles) {
      console.log(`  🗑️  Removing system role: ${roleName}`);
      await RBACService.removeRole(testUser[0].id, roleName, undefined);
    }
    
    // Remove business-specific roles for all businesses
    const allBusinesses = await db.select().from(businesses);
    for (const business of allBusinesses) {
      const businessPermissions = await RBACService.getUserPermissions(testUser[0].id, business.id);
      for (const roleName of businessPermissions.roles) {
        console.log(`  🗑️  Removing business role: ${roleName} for ${business.name}`);
        await RBACService.removeRole(testUser[0].id, roleName, business.id);
      }
    }
    
    console.log('✅ All roles removed from test user');
    
  } catch (error) {
    console.error('❌ Error removing roles:', error);
    process.exit(1);
  }
}

async function listTestUserPermissionAssignments() {
  try {
    console.log('🔍 Finding test user...');
    const testUser = await db.select().from(users).where(eq(users.email, '<EMAIL>')).limit(1);
    
    if (!testUser[0]) {
      console.error('❌ Test user (<EMAIL>) not found.');
      process.exit(1);
    }

    console.log(`✅ Found test user: ${testUser[0].name} (${testUser[0].email})`);
    console.log('');
    
    // Get all roles for the user (only active assignments)
    const allRoles = await db.select({
      roleName: roles.name,
      businessId: userRoles.businessId,
      businessName: businesses.name
    })
    .from(userRoles)
    .innerJoin(roles, eq(userRoles.roleId, roles.id))
    .leftJoin(businesses, eq(userRoles.businessId, businesses.id))
    .where(
      and(
        eq(userRoles.userId, testUser[0].id),
        eq(userRoles.isActive, true)
      )
    );
    
    // Separate regular roles from temporary permission roles
    const regularRoles = allRoles.filter(role => !role.roleName.startsWith('temp_permissions_'));
    const permissionRoles = allRoles.filter(role => role.roleName.startsWith('temp_permissions_'));
    
    console.log('📋 Current Role Assignments:');
    console.log('============================');
    
    if (regularRoles.length === 0) {
      console.log('  No regular roles assigned');
    } else {
      for (const role of regularRoles) {
        const rolePerms = await getRolePermissions(role.roleName);
        if (role.businessId) {
          console.log(`  🏢 ${role.roleName} (Business: ${role.businessName})`);
        } else {
          console.log(`  🌐 ${role.roleName} (System-wide)`);
        }
        if (rolePerms.length > 0) {
          console.log(`    🔑 Permissions: ${rolePerms.join(', ')}`);
        } else {
          console.log(`    🔑 Permissions: None`);
        }
      }
    }
    
    console.log('');
    console.log('🔑 Current Permission Assignments:');
    console.log('==================================');
    
    if (permissionRoles.length === 0) {
      console.log('  No permission-based assignments');
    } else {
      for (const role of permissionRoles) {
        // Get permissions for this temporary role
        const rolePermissionData = await db.select({
          permissionName: permissions.name
        })
        .from(rolePermissions)
        .innerJoin(permissions, eq(rolePermissions.permissionId, permissions.id))
        .innerJoin(roles, eq(rolePermissions.roleId, roles.id))
        .where(eq(roles.name, role.roleName));
        
        const permissionNames = rolePermissionData.map((p: { permissionName: string }) => p.permissionName).join(', ');
        
        if (role.businessId) {
          console.log(`  🏢 Permissions: ${permissionNames} (Business: ${role.businessName})`);
        } else {
          console.log(`  🌐 Permissions: ${permissionNames} (System-wide)`);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Error listing assignments:', error);
    process.exit(1);
  }
}

async function removeTestUserPermissionAssignments(businessName?: string) {
  try {
    console.log('🔍 Finding test user...');
    const testUser = await db.select().from(users).where(eq(users.email, '<EMAIL>')).limit(1);
    
    if (!testUser[0]) {
      console.error('❌ Test user (<EMAIL>) not found.');
      process.exit(1);
    }

    console.log('🧹 Removing permission-based assignments from test user...');
    
    let businessId: string | undefined;
    
    if (businessName) {
      console.log(`🏢 Finding business: ${businessName}...`);
      const business = await db.select().from(businesses)
        .where(eq(businesses.name, businessName))
        .limit(1);
      
      if (!business[0]) {
        console.error(`❌ Business '${businessName}' not found.`);
        console.log('Available businesses:');
        const allBusinesses = await db.select().from(businesses);
        allBusinesses.forEach(b => console.log(`  - ${b.name}`));
        process.exit(1);
      }
      
      businessId = business[0].id;
      console.log(`✅ Found business: ${business[0].name}`);
    }
    
    // Get all temporary permission roles for the user
    const tempRoles = await db.select({
      roleName: roles.name,
      roleId: roles.id,
      businessId: userRoles.businessId
    })
    .from(userRoles)
    .innerJoin(roles, eq(userRoles.roleId, roles.id))
    .where(
      and(
        eq(userRoles.userId, testUser[0].id),
        like(roles.name, 'temp_permissions_%'),
        businessName ? eq(userRoles.businessId, businessId!) : undefined
      )
    );
    
    if (tempRoles.length === 0) {
      const scope = businessName ? `for business '${businessName}'` : 'system-wide';
      console.log(`ℹ️  No permission-based assignments found ${scope}`);
      return;
    }
    
    for (const tempRole of tempRoles) {
      console.log(`  🗑️  Removing permission assignment: ${tempRole.roleName}`);
      
      // Remove the role assignment from user
      await RBACService.removeRole(testUser[0].id, tempRole.roleName, tempRole.businessId || undefined);
      
      // Delete the temporary role itself
      await db.delete(rolePermissions).where(eq(rolePermissions.roleId, tempRole.roleId));
      await db.delete(roles).where(eq(roles.id, tempRole.roleId));
    }
    
    const scope = businessName ? `for business '${businessName}'` : 'system-wide';
    console.log(`✅ Successfully removed ${tempRoles.length} permission-based assignment(s) ${scope}`);
    
  } catch (error) {
    console.error('❌ Error removing permission assignments:', error);
    process.exit(1);
  }
}

// Helper function to get permissions for a role
async function getRolePermissions(roleName: string): Promise<string[]> {
  const rolePermissionData = await db.select({
    permissionName: permissions.name
  })
  .from(rolePermissions)
  .innerJoin(permissions, eq(rolePermissions.permissionId, permissions.id))
  .innerJoin(roles, eq(rolePermissions.roleId, roles.id))
  .where(eq(roles.name, roleName));
  
  return rolePermissionData.map(p => p.permissionName);
}

async function listTestUserRoles() {
  try {
    console.log('🔍 Finding test user...');
    const testUser = await db.select().from(users).where(eq(users.email, '<EMAIL>')).limit(1);
    
    if (!testUser[0]) {
      console.error('❌ Test user (<EMAIL>) not found.');
      process.exit(1);
    }

    console.log(`✅ Found test user: ${testUser[0].name} (${testUser[0].email})`);
    console.log('');
    
    // Get system-wide permissions
    const systemPermissions = await RBACService.getUserPermissions(testUser[0].id);
    
    console.log('📋 Current Role Assignments:');
    console.log('============================');
    
    if (systemPermissions.roles.length > 0) {
      console.log('🌐 System-wide roles:');
      for (const role of systemPermissions.roles) {
        const rolePerms = await getRolePermissions(role);
        console.log(`  - ${role}`);
        if (rolePerms.length > 0) {
          console.log(`    🔑 Permissions: ${rolePerms.join(', ')}`);
        } else {
          console.log(`    🔑 Permissions: None`);
        }
      }
    }
    
    // Get business-specific roles
    const allBusinesses = await db.select().from(businesses);
    let hasBusinessRoles = false;
    
    for (const business of allBusinesses) {
      const businessPermissions = await RBACService.getUserPermissions(testUser[0].id, business.id);
      if (businessPermissions.roles.length > 0) {
        if (!hasBusinessRoles) {
          console.log('');
          console.log('🏢 Business-specific roles:');
          hasBusinessRoles = true;
        }
        console.log(`  ${business.name}:`);
        for (const role of businessPermissions.roles) {
          const rolePerms = await getRolePermissions(role);
          console.log(`    - ${role}`);
          if (rolePerms.length > 0) {
            console.log(`      🔑 Permissions: ${rolePerms.join(', ')}`);
          } else {
            console.log(`      🔑 Permissions: None`);
          }
        }
      }
    }
    
    if (systemPermissions.roles.length === 0 && !hasBusinessRoles) {
      console.log('  No roles assigned');
    }
    
  } catch (error) {
    console.error('❌ Error listing roles:', error);
    process.exit(1);
  }
}

async function removeSpecificRole(roleName: Role, businessName?: string) {
  try {
    console.log('🔍 Finding test user...');
    const testUser = await db.select().from(users).where(eq(users.email, '<EMAIL>')).limit(1);
    
    if (!testUser[0]) {
      console.error('❌ Test user (<EMAIL>) not found.');
      process.exit(1);
    }

    console.log(`✅ Found test user: ${testUser[0].name} (${testUser[0].email})`);

    // For super_admin, no business context needed
    if (roleName === 'super_admin') {
      console.log('🗑️  Removing super_admin role (system-wide access)...');
      await RBACService.removeRole(testUser[0].id, roleName, undefined);
      console.log('✅ Successfully removed super_admin role from test user');
      return;
    }

    // For other roles, find the business
    const targetBusinessName = businessName || 'KWACI Coffee House';
    console.log(`🏢 Finding business: ${targetBusinessName}...`);
    
    const business = await db.select().from(businesses)
      .where(eq(businesses.name, targetBusinessName))
      .limit(1);
    
    if (!business[0]) {
      console.error(`❌ Business '${targetBusinessName}' not found.`);
      console.log('Available businesses:');
      const allBusinesses = await db.select().from(businesses);
      allBusinesses.forEach(b => console.log(`  - ${b.name}`));
      process.exit(1);
    }

    console.log(`✅ Found business: ${business[0].name}`);
    console.log(`🗑️  Removing ${roleName} role from test user for ${business[0].name}...`);
    
    await RBACService.removeRole(testUser[0].id, roleName, business[0].id);
    
    console.log(`✅ Successfully removed ${roleName} role from test user for ${business[0].name}`);
    
  } catch (error) {
    console.error('❌ Error removing role:', error);
    process.exit(1);
  }
}

async function removeCustomRole(roleName: string, businessName?: string) {
  try {
    console.log('🔍 Finding test user...');
    const testUser = await db.select().from(users).where(eq(users.email, '<EMAIL>')).limit(1);
    
    if (!testUser[0]) {
      console.error('❌ Test user (<EMAIL>) not found.');
      process.exit(1);
    }

    console.log(`✅ Found test user: ${testUser[0].name} (${testUser[0].email})`);

    // Check if the custom role exists
    const customRole = await db.select().from(roles).where(eq(roles.name, roleName)).limit(1);
    
    if (!customRole[0]) {
      console.error(`❌ Custom role '${roleName}' not found.`);
      
      // Show available custom roles for this user
      const userCustomRoles = await db.select({
        roleName: roles.name,
        businessId: userRoles.businessId,
        businessName: businesses.name
      })
      .from(userRoles)
      .innerJoin(roles, eq(userRoles.roleId, roles.id))
      .leftJoin(businesses, eq(userRoles.businessId, businesses.id))
      .where(
        and(
          eq(userRoles.userId, testUser[0].id),
          like(roles.name, 'test_custom_%')
        )
      );
      
      if (userCustomRoles.length > 0) {
        console.log('Available custom roles for this user:');
        userCustomRoles.forEach(role => {
          if (role.businessId) {
            console.log(`  - ${role.roleName} (Business: ${role.businessName})`);
          } else {
            console.log(`  - ${role.roleName} (System-wide)`);
          }
        });
      } else {
        console.log('No custom roles found for this user.');
      }
      
      process.exit(1);
    }

    let businessId: string | undefined;
    
    if (businessName) {
      console.log(`🏢 Finding business: ${businessName}...`);
      const business = await db.select().from(businesses)
        .where(eq(businesses.name, businessName))
        .limit(1);
      
      if (!business[0]) {
        console.error(`❌ Business '${businessName}' not found.`);
        console.log('Available businesses:');
        const allBusinesses = await db.select().from(businesses);
        allBusinesses.forEach(b => console.log(`  - ${b.name}`));
        process.exit(1);
      }
      
      businessId = business[0].id;
      console.log(`✅ Found business: ${business[0].name}`);
    } else {
      // If no business name provided, find the businessId from existing role assignment
      const existingAssignment = await db.select()
        .from(userRoles)
        .where(
          and(
            eq(userRoles.userId, testUser[0].id),
            eq(userRoles.roleId, customRole[0].id),
            eq(userRoles.isActive, true)
          )
        )
        .limit(1);
      
      if (existingAssignment[0]) {
         businessId = existingAssignment[0].businessId || undefined;
         if (businessId) {
           const business = await db.select().from(businesses)
             .where(eq(businesses.id, businessId))
             .limit(1);
           console.log(`🏢 Found role assignment for business: ${business[0]?.name || businessId}`);
         }
       }
    }

    console.log(`🗑️  Removing custom role '${roleName}' from test user...`);
    
    // Remove the role assignment from user
    await RBACService.removeRole(testUser[0].id, roleName, businessId);
    
    // Check if this role is assigned to any other users (only active assignments)
    const otherUserRoles = await db.select().from(userRoles)
      .where(
        and(
          eq(userRoles.roleId, customRole[0].id),
          eq(userRoles.isActive, true)
        )
      );
    
    if (otherUserRoles.length === 0) {
      console.log(`🧹 Cleaning up unused custom role '${roleName}'...`);
      
      // Delete role permissions
      await db.delete(rolePermissions).where(eq(rolePermissions.roleId, customRole[0].id));
      
      // Delete the role itself
      await db.delete(roles).where(eq(roles.id, customRole[0].id));
      
      console.log(`✅ Successfully removed custom role '${roleName}' and cleaned up database`);
    } else {
      console.log(`✅ Successfully removed custom role '${roleName}' from test user (role kept for other users)`);
    }
    
  } catch (error) {
    console.error('❌ Error removing custom role:', error);
    process.exit(1);
  }
}

async function showUsage() {
  console.log('🧪 RBAC Test User Role Assignment Script');
  console.log('=====================================');
  console.log('');
  console.log('This script assigns roles or specific permissions to the test user (<EMAIL>) for testing RBAC functionality.');
  console.log('');
  console.log('Usage:');
  console.log('  bun run scripts/assign-test-user-role.ts <role_name> [business_name]');
  console.log('  bun run scripts/assign-test-user-role.ts permissions <permission1,permission2,...> [business_name]');
  console.log('  bun run scripts/assign-test-user-role.ts remove [role_name] [business_name]');
  console.log('  bun run scripts/assign-test-user-role.ts remove-permissions [business_name]');
  console.log('  bun run scripts/assign-test-user-role.ts list');
  console.log('  bun run scripts/assign-test-user-role.ts list-detailed');
  console.log('');
  console.log('Available roles:');
  AVAILABLE_ROLES.forEach(role => {
    console.log(`  - ${role}`);
  });
  console.log('');
  console.log('Examples:');
  console.log('  # Assign roles');
  console.log('  bun run scripts/assign-test-user-role.ts business_owner');
  console.log('  bun run scripts/assign-test-user-role.ts business_manager "My Business"');
  console.log('  bun run scripts/assign-test-user-role.ts super_admin');
  console.log('');
  console.log('  # Assign specific permissions (comma-separated)');
  console.log('  bun run scripts/assign-test-user-role.ts permissions inventory.read,inventory.create');
  console.log('  bun run scripts/assign-test-user-role.ts permissions "inventory.read,categories.update" "My Business"');
  console.log('  bun run scripts/assign-test-user-role.ts permissions business.read,business.update');
  console.log('');
  console.log('  # Remove roles');
  console.log('  bun run scripts/assign-test-user-role.ts remove                    # Remove all roles');
  console.log('  bun run scripts/assign-test-user-role.ts remove business_owner     # Remove specific role');
  console.log('  bun run scripts/assign-test-user-role.ts remove business_manager "My Business"');
  console.log('  bun run scripts/assign-test-user-role.ts remove super_admin');
  console.log('  bun run scripts/assign-test-user-role.ts remove test_custom_1234567890  # Remove custom role');
  console.log('');
  console.log('  # Remove permission-based assignments');
  console.log('  bun run scripts/assign-test-user-role.ts remove-permissions        # Remove all permission assignments');
  console.log('  bun run scripts/assign-test-user-role.ts remove-permissions "My Business"  # Remove for specific business');
  console.log('');
  console.log('  # List assignments');
  console.log('  bun run scripts/assign-test-user-role.ts list                     # List current roles only');
  console.log('  bun run scripts/assign-test-user-role.ts list-detailed            # List roles and permissions separately');
  console.log('');
  console.log('Notes:');
  console.log('- If no business name is provided, "KWACI Coffee House" will be used as default');
  console.log('- super_admin role is system-wide and doesn\'t require a business context');
  console.log('- When using permissions command, separate multiple permissions with commas');
  console.log('- Permission assignments create a temporary custom role with the specified permissions');
  console.log('- Use remove-permissions to clean up permission-based assignments');
  console.log('- The test user must exist in the database (<EMAIL>)');
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args[0] === 'help' || args[0] === '--help') {
    await showUsage();
    process.exit(0);
  }
  
  const command = args[0];
  
  if (command === 'permissions') {
    // Handle permissions assignment: permissions <permission1,permission2,...> [business_name]
    if (args.length < 2) {
      console.error('❌ Permissions command requires permission names.');
      console.log('Usage: bun run scripts/assign-test-user-role.ts permissions <permission1,permission2,...> [business_name]');
      process.exit(1);
    }
    
    const permissionsString = args[1];
    const businessName = args[2]; // Optional business name
    
    // Parse comma-separated permissions
    const permissionNames = permissionsString.split(',').map(p => p.trim()).filter(p => p.length > 0);
    
    if (permissionNames.length === 0) {
      console.error('❌ No valid permissions provided.');
      console.log('Example: inventory.read,inventory.create,categories.update');
      process.exit(1);
    }
    
    await assignTestUserPermissions(permissionNames, businessName);
  } else if (command === 'remove') {
    if (args.length === 1) {
      // Remove all roles
      await removeTestUserRoles();
    } else {
      // Remove specific role: remove <role_name> [business_name]
      const roleName = args[1];
      const businessName = args[2]; // Optional business name
      
      // Check if it's a predefined role or a custom role
      if (AVAILABLE_ROLES.includes(roleName as Role)) {
        await removeSpecificRole(roleName as Role, businessName);
      } else if (roleName.startsWith('test_custom_') || roleName.startsWith('temp_permissions_')) {
        // Handle custom role removal
        await removeCustomRole(roleName, businessName);
      } else {
        console.error(`❌ Invalid role: ${roleName}`);
        console.log('Available roles:', AVAILABLE_ROLES.join(', '));
        console.log('Or use a custom role name (test_custom_* or temp_permissions_*)');
        process.exit(1);
      }
    }
  } else if (command === 'remove-permissions') {
    // Handle permission removal: remove-permissions [business_name]
    const businessName = args[1]; // Optional business name
    await removeTestUserPermissionAssignments(businessName);
  } else if (command === 'list') {
    // Handle basic listing (existing functionality)
    await listTestUserRoles();
  } else if (command === 'list-detailed') {
    // Handle detailed listing with separate roles and permissions
    await listTestUserPermissionAssignments();
  } else {
    // Assume it's a role assignment
    const roleName = command as Role;
    const businessName = args[1]; // Optional business name
    
    if (!AVAILABLE_ROLES.includes(roleName)) {
      console.error(`❌ Invalid role: ${roleName}`);
      console.log('Available roles:', AVAILABLE_ROLES.join(', '));
      process.exit(1);
    }
    
    await assignTestUserRole(roleName, businessName);
  }
  
  process.exit(0);
}

// Main execution
await main();