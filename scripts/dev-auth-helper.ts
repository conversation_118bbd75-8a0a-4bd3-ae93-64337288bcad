import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import { eq } from 'drizzle-orm';
import { randomUUID } from 'crypto';
import { auth } from '../app/lib/auth.server';
import { users, accounts, businesses } from '../app/lib/db/schema';

// Database connection
const connectionString = process.env.DATABASE_URL!;
const pool = new Pool({ connectionString });
const db = drizzle(pool, { schema: { users, accounts, businesses } });

interface CreateTestUserOptions {
  email: string;
  password?: string;
  name?: string;
  businessName?: string;
  businessDescription?: string;
}

class DevAuthHelper {
  /**
   * Create a test user with authentication credentials
   */
  static async createTestUser(options: CreateTestUserOptions) {
    try {
      const {
        email,
        password = 'password123',
        name = 'Test User',
        businessName = 'Test Business',
        businessDescription = 'A test business for development'
      } = options;

      console.log(`🔧 Creating test user: ${email}`);

      // Check if user already exists
      const [existingUser] = await db.select()
        .from(users)
        .where(eq(users.email, email))
        .limit(1);

      if (existingUser) {
        console.log(`❌ User ${email} already exists`);
        return existingUser;
      }

      // Get Better Auth context for password hashing
      const authContext = await auth.$context;
      const hashedPassword = await authContext.password.hash(password);

      // Create user
      const userId = randomUUID();
      const [user] = await db.insert(users).values({
        id: userId,
        name,
        email,
        emailVerified: true,
        image: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      }).returning();

      // Create authentication account
      await db.insert(accounts).values({
        id: randomUUID(),
        accountId: email,
        providerId: 'credential',
        userId: user.id,
        password: hashedPassword,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Create business for the user
      const [business] = await db.insert(businesses).values({
        name: businessName,
        description: businessDescription,
        userId: user.id,
      }).returning();

      console.log(`✅ Created user: ${email} / ${password}`);
      console.log(`✅ Created business: ${business.name}`);
      
      return { user, business };
    } catch (error) {
      console.error('❌ Error creating test user:', error);
      throw error;
    }
  }

  /**
   * List all test users with their credentials
   */
  static async listTestUsers() {
    try {
      console.log('📋 Listing all test users...\n');

      const allUsers = await db.select({
        id: users.id,
        name: users.name,
        email: users.email,
        emailVerified: users.emailVerified,
        createdAt: users.createdAt,
      }).from(users);

      if (allUsers.length === 0) {
        console.log('❌ No users found. Run seeding first: bun run db:seed');
        return;
      }

      console.log('🔑 Available test users:');
      allUsers.forEach((user, index) => {
        console.log(`${index + 1}. 📧 ${user.email}`);
        console.log(`   👤 ${user.name}`);
        console.log(`   🔒 password123 (default test password)`);
        console.log(`   ✅ Verified: ${user.emailVerified}`);
        console.log(`   📅 Created: ${user.createdAt.toLocaleDateString()}`);
        console.log('');
      });

      console.log('💡 Use any of these credentials to log into the application.');
    } catch (error) {
      console.error('❌ Error listing users:', error);
    }
  }

  /**
   * Reset a user's password
   */
  static async resetUserPassword(email: string, newPassword: string = 'password123') {
    try {
      console.log(`🔧 Resetting password for: ${email}`);

      // Find user
      const [user] = await db.select()
        .from(users)
        .where(eq(users.email, email))
        .limit(1);

      if (!user) {
        console.log(`❌ User ${email} not found`);
        return;
      }

      // Hash new password
      const authContext = await auth.$context;
      const hashedPassword = await authContext.password.hash(newPassword);

      // Update account password
      await db.update(accounts)
        .set({ 
          password: hashedPassword,
          updatedAt: new Date()
        })
        .where(eq(accounts.userId, user.id));

      console.log(`✅ Password reset for ${email}`);
      console.log(`🔒 New password: ${newPassword}`);
    } catch (error) {
      console.error('❌ Error resetting password:', error);
    }
  }

  /**
   * Delete a test user and their data
   */
  static async deleteTestUser(email: string) {
    try {
      console.log(`🗑️ Deleting test user: ${email}`);

      // Find user
      const [user] = await db.select()
        .from(users)
        .where(eq(users.email, email))
        .limit(1);

      if (!user) {
        console.log(`❌ User ${email} not found`);
        return;
      }

      // Delete user (cascade will handle accounts and businesses)
      await db.delete(users).where(eq(users.id, user.id));

      console.log(`✅ Deleted user: ${email}`);
    } catch (error) {
      console.error('❌ Error deleting user:', error);
    }
  }

  /**
   * Create quick test users for common scenarios
   */
  static async createQuickTestUsers() {
    console.log('🚀 Creating quick test users for development...\n');

    const testUsers = [
      {
        email: '<EMAIL>',
        name: 'Admin User',
        businessName: 'Admin Coffee Co',
        businessDescription: 'Administrative test business'
      },
      {
        email: '<EMAIL>',
        name: 'Manager User',
        businessName: 'Manager Cafe',
        businessDescription: 'Manager test business'
      },
      {
        email: '<EMAIL>',
        name: 'Regular User',
        businessName: 'User Coffee Shop',
        businessDescription: 'Regular user test business'
      }
    ];

    for (const userData of testUsers) {
      await this.createTestUser(userData);
    }

    console.log('\n🎉 Quick test users created!');
    console.log('You can now log in with:');
    testUsers.forEach(user => {
      console.log(`   📧 ${user.email} / 🔒 password123`);
    });
  }
}

// CLI interface
async function main() {
  const command = process.argv[2];
  const arg1 = process.argv[3];
  const arg2 = process.argv[4];

  try {
    switch (command) {
      case 'list':
        await DevAuthHelper.listTestUsers();
        break;

      case 'create':
        if (!arg1) {
          console.log('❌ Usage: bun run scripts/dev-auth-helper.ts create <email> [name]');
          break;
        }
        await DevAuthHelper.createTestUser({
          email: arg1,
          name: arg2 || 'Test User',
          businessName: `${arg2 || 'Test User'}'s Business`,
        });
        break;

      case 'reset':
        if (!arg1) {
          console.log('❌ Usage: bun run scripts/dev-auth-helper.ts reset <email> [password]');
          break;
        }
        await DevAuthHelper.resetUserPassword(arg1, arg2);
        break;

      case 'delete':
        if (!arg1) {
          console.log('❌ Usage: bun run scripts/dev-auth-helper.ts delete <email>');
          break;
        }
        await DevAuthHelper.deleteTestUser(arg1);
        break;

      case 'quick':
        await DevAuthHelper.createQuickTestUsers();
        break;

      default:
        console.log('🛠️ Development Authentication Helper');
        console.log('');
        console.log('Available commands:');
        console.log('  list                           - List all test users');
        console.log('  create <email> [name]          - Create a test user');
        console.log('  reset <email> [password]       - Reset user password');
        console.log('  delete <email>                 - Delete a test user');
        console.log('  quick                          - Create common test users');
        console.log('');
        console.log('Examples:');
        console.log('  bun run scripts/dev-auth-helper.ts list');
        console.log('  bun run scripts/dev-auth-helper.<NAME_EMAIL> "Test User"');
        console.log('  bun run scripts/dev-auth-helper.<NAME_EMAIL> newpassword');
        console.log('  bun run scripts/dev-auth-helper.ts quick');
        break;
    }
  } catch (error) {
    console.error('💥 Command failed:', error);
  } finally {
    await pool.end();
  }
}

// Run if called directly
if (require.main === module) {
  main();
}
