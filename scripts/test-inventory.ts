#!/usr/bin/env bun

/**
 * Simple test script to verify inventory system functionality
 * This script tests the database operations and API endpoints
 */

import { db } from '../app/lib/db/connection';
import { businesses, ingredients, products, productIngredients } from '../app/lib/db/schema';
import { IngredientServiceServer } from '../app/lib/services/ingredientService.server';
import { ProductServiceServer } from '../app/lib/services/productService.server';
import { eq } from 'drizzle-orm';

async function testInventorySystem() {
  console.log('🧪 Testing Inventory Management System...\n');

  try {
    // Test 1: Check if tables exist
    console.log('1. Testing database tables...');
    
    const ingredientCount = await db.select().from(ingredients).limit(1);
    const productCount = await db.select().from(products).limit(1);
    const productIngredientCount = await db.select().from(productIngredients).limit(1);
    
    console.log('✅ All inventory tables are accessible');

    // Test 2: Find a test business (or create one)
    console.log('\n2. Finding test business...');
    
    const [testBusiness] = await db.select().from(businesses).limit(1);
    
    if (!testBusiness) {
      console.log('❌ No business found. Please create a business first.');
      return;
    }
    
    console.log(`✅ Using business: ${testBusiness.name} (${testBusiness.id})`);

    // Test 3: Test ingredient creation
    console.log('\n3. Testing ingredient creation...');
    
    const testIngredientData = {
      name: 'Test Coffee Beans',
      description: 'High quality arabica beans for testing',
      unitOfMeasurement: 'kg',
      costPerUnit: '50000',
      supplierInfo: 'Test Supplier Co.',
    };

    try {
      const newIngredient = await IngredientServiceServer.create(
        testBusiness.id,
        testBusiness.userId,
        testIngredientData
      );
      console.log(`✅ Ingredient created: ${newIngredient.name} (${newIngredient.id})`);

      // Test 4: Test ingredient retrieval
      console.log('\n4. Testing ingredient retrieval...');
      
      const ingredients = await IngredientServiceServer.getAllByBusiness(
        testBusiness.id,
        testBusiness.userId
      );
      console.log(`✅ Retrieved ${ingredients.length} ingredients`);

      // Test 5: Test product creation with ingredients
      console.log('\n5. Testing product creation...');
      
      const testProductData = {
        name: 'Test Cappuccino',
        description: 'Delicious cappuccino for testing',
        sellingPrice: '25000',
        category: 'beverage',
        status: 'active' as const,
        ingredients: [
          {
            ingredientId: newIngredient.id,
            quantityNeeded: '0.02', // 20g per cup
          }
        ],
      };

      const newProduct = await ProductServiceServer.create(
        testBusiness.id,
        testBusiness.userId,
        testProductData
      );
      console.log(`✅ Product created: ${newProduct.name} (${newProduct.id})`);

      // Test 6: Test product with ingredients retrieval
      console.log('\n6. Testing product with ingredients retrieval...');
      
      const productWithIngredients = await ProductServiceServer.getByIdWithIngredients(
        newProduct.id,
        testBusiness.id,
        testBusiness.userId
      );
      
      if (productWithIngredients) {
        console.log(`✅ Product retrieved with ${productWithIngredients.productIngredients.length} ingredients`);
        console.log(`   - ${productWithIngredients.productIngredients[0].ingredient.name}: ${productWithIngredients.productIngredients[0].quantityNeeded} ${productWithIngredients.productIngredients[0].ingredient.unitOfMeasurement}`);
      }

      // Test 7: Test ingredient name uniqueness validation
      console.log('\n7. Testing ingredient name uniqueness...');
      
      const nameExists = await IngredientServiceServer.nameExistsInBusiness(
        'Test Coffee Beans',
        testBusiness.id,
        testBusiness.userId
      );
      console.log(`✅ Name uniqueness check: ${nameExists ? 'exists' : 'available'}`);

      // Test 8: Clean up test data
      console.log('\n8. Cleaning up test data...');
      
      await ProductServiceServer.delete(newProduct.id, testBusiness.id, testBusiness.userId);
      console.log('✅ Test product deleted');
      
      await IngredientServiceServer.delete(newIngredient.id, testBusiness.id, testBusiness.userId);
      console.log('✅ Test ingredient deleted');

      console.log('\n🎉 All tests passed! Inventory system is working correctly.');

    } catch (error) {
      console.error('❌ Test failed:', error);
      throw error;
    }

  } catch (error) {
    console.error('💥 Test suite failed:', error);
    process.exit(1);
  }
}

// Run the test
testInventorySystem()
  .then(() => {
    console.log('\n✨ Test completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Test failed:', error);
    process.exit(1);
  });
