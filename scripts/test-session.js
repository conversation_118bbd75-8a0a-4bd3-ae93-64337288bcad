#!/usr/bin/env node

/**
 * Session Management Test Script
 * 
 * This script runs comprehensive tests for the session management system.
 * 
 * Usage:
 *   bun run test:session
 *   npm run test:session
 *   node scripts/test-session.js
 */

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function logHeader(message) {
  console.log('\n' + colorize('='.repeat(60), 'cyan'));
  console.log(colorize(`  ${message}`, 'bright'));
  console.log(colorize('='.repeat(60), 'cyan'));
}

function logSuccess(message) {
  console.log(colorize(`✅ ${message}`, 'green'));
}

function logError(message) {
  console.log(colorize(`❌ ${message}`, 'red'));
}

function logInfo(message) {
  console.log(colorize(`ℹ️  ${message}`, 'cyan'));
}

// Simple test runner
class TestRunner {
  constructor() {
    this.tests = [];
    this.results = { total: 0, passed: 0, failed: 0 };
  }

  test(name, fn) {
    this.tests.push({ name, fn });
  }

  async run() {
    logHeader('Session Management Test Suite');
    
    for (const test of this.tests) {
      this.results.total++;
      
      try {
        const startTime = Date.now();
        await test.fn();
        const duration = Date.now() - startTime;
        
        this.results.passed++;
        logSuccess(`${test.name} (${duration}ms)`);
      } catch (error) {
        this.results.failed++;
        logError(`${test.name}: ${error.message}`);
      }
    }

    this.printSummary();
    return this.results.failed === 0;
  }

  printSummary() {
    const { total, passed, failed } = this.results;
    const passRate = total > 0 ? Math.round((passed / total) * 100) : 0;
    
    console.log('\n' + colorize('📊 Test Results:', 'blue'));
    console.log(`Total: ${total}, Passed: ${passed}, Failed: ${failed}`);
    console.log(`Pass Rate: ${passRate}%`);
    
    if (passRate === 100) {
      logSuccess('🎉 All tests passed!');
    } else {
      logError('💥 Some tests failed.');
    }
  }
}

// Test implementations
function createTests(runner) {
  runner.test('Session Data Validation', async () => {
    const validSession = {
      sessionId: 'test-session-123',
      userId: 'test-user-456',
      expiresAt: Date.now() + 60000,
      createdAt: Date.now() - 60000,
      lastActivity: Date.now(),
    };
    
    const requiredFields = ['sessionId', 'userId', 'expiresAt'];
    for (const field of requiredFields) {
      if (!validSession[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }
    
    if (validSession.expiresAt <= Date.now()) {
      throw new Error('Session expiration must be in the future');
    }
  });

  runner.test('Session Conversion Logic', async () => {
    // Test different session formats
    const testCases = [
      {
        name: 'Standard format',
        input: {
          session: { id: 'sess-123', expiresAt: new Date(Date.now() + 60000).toISOString() },
          user: { id: 'user-456' }
        },
        shouldPass: true
      },
      {
        name: 'Missing session.id',
        input: {
          session: { expiresAt: new Date(Date.now() + 60000).toISOString() },
          user: { id: 'user-456' }
        },
        shouldPass: false
      },
      {
        name: 'Missing user.id',
        input: {
          session: { id: 'sess-123', expiresAt: new Date(Date.now() + 60000).toISOString() },
          user: {}
        },
        shouldPass: false
      }
    ];

    for (const testCase of testCases) {
      const hasValidStructure = !!(
        testCase.input.session?.id &&
        testCase.input.user?.id &&
        testCase.input.session?.expiresAt
      );

      if (hasValidStructure !== testCase.shouldPass) {
        throw new Error(`${testCase.name}: Expected ${testCase.shouldPass ? 'valid' : 'invalid'}, got ${hasValidStructure ? 'valid' : 'invalid'}`);
      }
    }
  });

  runner.test('Time Calculations', async () => {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    const expiresAt = now + oneHour;
    
    const timeRemaining = expiresAt - now;
    const minutesRemaining = Math.floor(timeRemaining / (1000 * 60));
    
    if (minutesRemaining !== 60) {
      throw new Error(`Expected 60 minutes, got ${minutesRemaining}`);
    }
    
    const expiredAt = now - oneHour;
    const expiredTimeRemaining = Math.max(0, expiredAt - now);
    
    if (expiredTimeRemaining !== 0) {
      throw new Error(`Expected 0 for expired session, got ${expiredTimeRemaining}`);
    }
  });

  runner.test('Configuration Validation', async () => {
    const validConfig = {
      warningThresholds: [10, 5, 1],
      checkInterval: 30000,
      enabled: true,
    };
    
    if (!Array.isArray(validConfig.warningThresholds) || validConfig.warningThresholds.length === 0) {
      throw new Error('Warning thresholds must be a non-empty array');
    }
    
    if (validConfig.checkInterval <= 0) {
      throw new Error('Check interval must be positive');
    }
    
    if (typeof validConfig.enabled !== 'boolean') {
      throw new Error('Enabled must be a boolean');
    }
  });
}

// Main execution
async function main() {
  try {
    logInfo('Initializing test runner...');
    const runner = new TestRunner();
    
    logInfo('Creating test suite...');
    createTests(runner);
    
    logInfo('Running tests...');
    const success = await runner.run();
    
    if (success) {
      console.log('\n' + colorize('🚀 Next Steps:', 'blue'));
      logInfo('1. Add SessionDiagnostic component to your app');
      logInfo('2. Check console logs for session conversion details');
      logInfo('3. Verify no more continuous start/stop cycles');
      logInfo('4. Test session warning modal functionality');
      
      process.exit(0);
    } else {
      logError('\n💥 Tests failed! Please fix the issues before proceeding.');
      process.exit(1);
    }
  } catch (error) {
    logError(`\nTest runner failed: ${error.message}`);
    process.exit(1);
  }
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  logHeader('Session Management Test Script');
  console.log('\nUsage:');
  console.log('  bun run test:session           Run all tests');
  console.log('  bun run test:session --help    Show this help message');
  console.log('\nDescription:');
  console.log('  This script validates the session management system configuration.');
  console.log('  For full integration testing, use the SessionDiagnostic component.');
  process.exit(0);
}

// Run the tests
main().catch((error) => {
  logError(`Unexpected error: ${error.message}`);
  process.exit(1);
});
