#!/usr/bin/env bun

import { createBusinessTable, createInventoryTables, migrateIngredientSchema } from '../app/lib/db/migrate';

async function main() {
  try {
    console.log('Running database migration...');
    await createBusinessTable();
    await createInventoryTables();
    await migrateIngredientSchema();
    console.log('Migration completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

main();
