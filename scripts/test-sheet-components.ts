#!/usr/bin/env bun

/**
 * Test script to verify the sheet components are properly integrated
 * This script tests the component imports and basic functionality
 */

import { db } from '../app/lib/db/connection';
import { businesses } from '../app/lib/db/schema';

async function testSheetComponents() {
  console.log('🧪 Testing Sheet Components Integration...\n');

  try {
    // Test 1: Check if we can import the sheet components
    console.log('1. Testing component imports...');
    
    try {
      // These imports will fail if there are TypeScript errors
      const { IngredientManagementSheet } = await import('../app/components/inventory/IngredientManagementSheet');
      const { ProductManagementSheet } = await import('../app/components/inventory/ProductManagementSheet');
      
      console.log('✅ IngredientManagementSheet imported successfully');
      console.log('✅ ProductManagementSheet imported successfully');
    } catch (error) {
      console.error('❌ Component import failed:', error);
      throw error;
    }

    // Test 2: Check if business exists for testing
    console.log('\n2. Checking business availability...');
    
    const [testBusiness] = await db.select().from(businesses).limit(1);
    
    if (!testBusiness) {
      console.log('⚠️  No business found. Sheet components require a business context.');
      console.log('   Please create a business first to test the sheet functionality.');
    } else {
      console.log(`✅ Business available: ${testBusiness.name} (${testBusiness.id})`);
    }

    // Test 3: Check if routes are accessible
    console.log('\n3. Testing route accessibility...');
    
    try {
      // Test if the inventory routes can be imported
      const ingredientsRoute = await import('../app/routes/inventory.ingredients');
      const productsRoute = await import('../app/routes/inventory.products');
      
      console.log('✅ Ingredients route imported successfully');
      console.log('✅ Products route imported successfully');
    } catch (error) {
      console.error('❌ Route import failed:', error);
      throw error;
    }

    // Test 4: Verify translation keys exist
    console.log('\n4. Testing translation files...');
    
    try {
      const inventoryEn = await import('../app/locales/en/inventory.json');
      const inventoryId = await import('../app/locales/id/inventory.json');
      
      // Check for key translation keys
      const requiredKeys = [
        'ingredients.title',
        'products.title',
        'ingredients.form.title.create',
        'products.form.title.create',
        'common.edit',
        'common.delete'
      ];
      
      let missingKeys = [];
      
      for (const key of requiredKeys) {
        const keyPath = key.split('.');
        let enValue = inventoryEn.default;
        let idValue = inventoryId.default;
        
        for (const part of keyPath) {
          enValue = enValue?.[part];
          idValue = idValue?.[part];
        }
        
        if (!enValue || !idValue) {
          missingKeys.push(key);
        }
      }
      
      if (missingKeys.length > 0) {
        console.log(`⚠️  Missing translation keys: ${missingKeys.join(', ')}`);
      } else {
        console.log('✅ All required translation keys are present');
      }
    } catch (error) {
      console.error('❌ Translation file test failed:', error);
      throw error;
    }

    console.log('\n🎉 Sheet Components Integration Test Completed!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Component imports working');
    console.log('   ✅ Route imports working');
    console.log('   ✅ Translation files accessible');
    console.log('   ✅ Database connection working');
    
    if (testBusiness) {
      console.log('\n🚀 Ready to test in browser:');
      console.log('   1. Navigate to http://localhost:5175/inventory/ingredients');
      console.log('   2. Click "Add Ingredient" to test the sheet component');
      console.log('   3. Navigate to http://localhost:5175/inventory/products');
      console.log('   4. Click "Add Product" to test the product sheet component');
    } else {
      console.log('\n⚠️  To fully test the sheet components:');
      console.log('   1. Create a business first in the application');
      console.log('   2. Then test the inventory management features');
    }

  } catch (error) {
    console.error('💥 Sheet Components Test Failed:', error);
    process.exit(1);
  }
}

// Run the test
testSheetComponents()
  .then(() => {
    console.log('\n✨ Test completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Test failed:', error);
    process.exit(1);
  });
