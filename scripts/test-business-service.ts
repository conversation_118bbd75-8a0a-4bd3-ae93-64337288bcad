#!/usr/bin/env bun

import { BusinessService } from '../app/lib/services/businessService';

async function testBusinessService() {
  console.log('Testing Business Service...');
  
  // Test user ID (using real user from database)
  const testUserId = 'ef36a6e9-9018-4c20-abcb-132f732b33ae';
  
  try {
    // Test 1: Create a business
    console.log('\n1. Creating a test business...');
    const newBusiness = await BusinessService.create(testUserId, {
      name: 'Test Coffee Shop',
      description: 'A test coffee shop for integration testing',
      note: 'This is a test business',
      currency: 'IDR',
    });
    console.log('✅ Business created:', newBusiness);

    // Test 2: Get all businesses for user
    console.log('\n2. Getting all businesses for user...');
    const businesses = await BusinessService.getAllByUser(testUserId);
    console.log('✅ Businesses found:', businesses.length);

    // Test 3: Get business by ID
    console.log('\n3. Getting business by ID...');
    const foundBusiness = await BusinessService.getByIdAndUser(newBusiness.id, testUserId);
    console.log('✅ Business found by ID:', foundBusiness?.name);

    // Test 4: Update business
    console.log('\n4. Updating business...');
    await BusinessService.update(newBusiness.id, testUserId, {
      name: 'Updated Coffee Shop',
      description: 'Updated description',
    });
    console.log('✅ Business updated');

    // Test 5: Verify update
    const updatedBusiness = await BusinessService.getByIdAndUser(newBusiness.id, testUserId);
    console.log('✅ Updated business name:', updatedBusiness?.name);

    // Test 6: Count businesses
    console.log('\n5. Counting businesses...');
    const count = await BusinessService.countByUser(testUserId);
    console.log('✅ Business count:', count);

    // Test 7: Search businesses
    console.log('\n6. Searching businesses...');
    const searchResults = await BusinessService.searchByNameAndUser('Updated', testUserId);
    console.log('✅ Search results:', searchResults.length);

    // Test 8: Delete business
    console.log('\n7. Deleting business...');
    await BusinessService.delete(newBusiness.id, testUserId);
    console.log('✅ Business deleted');

    // Test 9: Verify deletion
    const deletedBusiness = await BusinessService.getByIdAndUser(newBusiness.id, testUserId);
    console.log('✅ Business after deletion:', deletedBusiness ? 'Still exists' : 'Successfully deleted');

    console.log('\n🎉 All tests passed!');
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

testBusinessService();
