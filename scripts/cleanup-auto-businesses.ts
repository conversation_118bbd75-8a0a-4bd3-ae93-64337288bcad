#!/usr/bin/env bun

import { db } from '../app/lib/db/connection';
import { businesses } from '../app/lib/db/schema';
import { and, eq, or } from 'drizzle-orm';

/**
 * One-time cleanup script to remove automatically created businesses
 * This removes businesses that were created by the auto-initialization feature
 */
async function cleanupAutoBusinesses() {
  console.log('🧹 Starting cleanup of automatically created businesses...');
  
  try {
    // Find businesses that were automatically created
    // These typically have specific names and descriptions
    const autoBusinesses = await db.select()
      .from(businesses)
      .where(
        or(
          and(
            eq(businesses.name, 'My Coffee Business'),
            eq(businesses.description, 'Default business created during setup')
          ),
          and(
            eq(businesses.name, 'My Coffee Business'),
            eq(businesses.note, 'This is the default business. You can rename or modify this business as needed.')
          )
        )
      );

    console.log(`Found ${autoBusinesses.length} automatically created businesses`);

    if (autoBusinesses.length === 0) {
      console.log('✅ No automatically created businesses found. Nothing to clean up.');
      return;
    }

    // Show details of businesses to be deleted
    console.log('\n📋 Businesses to be deleted:');
    autoBusinesses.forEach((business, index) => {
      console.log(`${index + 1}. ID: ${business.id}`);
      console.log(`   Name: ${business.name}`);
      console.log(`   Description: ${business.description}`);
      console.log(`   User ID: ${business.userId}`);
      console.log(`   Created: ${business.createdAt}`);
      console.log('');
    });

    // Delete the automatically created businesses
    const deleteResult = await db.delete(businesses)
      .where(
        or(
          and(
            eq(businesses.name, 'My Coffee Business'),
            eq(businesses.description, 'Default business created during setup')
          ),
          and(
            eq(businesses.name, 'My Coffee Business'),
            eq(businesses.note, 'This is the default business. You can rename or modify this business as needed.')
          )
        )
      );

    console.log(`✅ Successfully deleted ${autoBusinesses.length} automatically created businesses`);
    console.log('');
    console.log('🎉 Cleanup completed! Users will now see proper empty states when they have no businesses.');
    console.log('');
    console.log('📝 Note: Users can now manually create their first business through the UI.');

  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    process.exit(1);
  }
}

// Run the cleanup
cleanupAutoBusinesses();
