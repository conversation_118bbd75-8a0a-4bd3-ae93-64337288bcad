import { db } from '../app/lib/db/connection';
import { businesses, userRoles, users, roles } from '../app/lib/db/schema';
import { eq } from 'drizzle-orm';

const businessId = '87b243bd-cf17-4af0-9af1-04bd5e5c99d2';

console.log(`Checking business ID: ${businessId}`);

// Check if business exists
const business = await db.select().from(businesses).where(eq(businesses.id, businessId));
console.log('Business found:', business.length > 0);

if (business.length > 0) {
  console.log('Business details:', {
    id: business[0].id,
    name: business[0].name,
    ownerId: business[0].userId
  });
  
  // Check user roles for this business with role names
  const userRoleData = await db.select({
    userId: userRoles.userId,
    roleId: userRoles.roleId,
    roleName: roles.name,
    businessId: userRoles.businessId,
    isActive: userRoles.isActive
  })
  .from(userRoles)
  .innerJoin(roles, eq(userRoles.roleId, roles.id))
  .where(eq(userRoles.businessId, businessId));
  
  console.log('User roles for this business:', userRoleData);
  
  // Get user details for each role
  for (const roleData of userRoleData) {
    const user = await db.select({
      id: users.id,
      name: users.name,
      email: users.email
    }).from(users).where(eq(users.id, roleData.userId));
    
    if (user.length > 0) {
      console.log(`User ${user[0].email} (${user[0].name}) has role: ${roleData.roleName} (active: ${roleData.isActive})`);
    }
  }
} else {
  console.log('Business not found in database');
}

process.exit(0);