#!/usr/bin/env bun

import { RBACService } from '../app/lib/services/rbacService.server';
import { db } from '../app/lib/db/connection';
import { users, businesses, roles, userRoles, rolePermissions, permissions } from '../app/lib/db/schema';
import { eq, and } from 'drizzle-orm';

async function debugTestUserAccess() {
  try {
    console.log('🔍 Debug: Test User Access Analysis');
    console.log('=' .repeat(50));
    
    // Find test user
    const testUser = await db.select().from(users).where(eq(users.email, '<EMAIL>')).limit(1);
    
    if (!testUser[0]) {
      console.error('❌ Test user (<EMAIL>) not found.');
      process.exit(1);
    }

    console.log(`✅ Found test user: ${testUser[0].name} (${testUser[0].email})`);
    console.log(`   User ID: ${testUser[0].id}`);
    console.log();

    // Get all businesses
    const allBusinesses = await db.select().from(businesses);
    console.log('🏢 All Businesses:');
    allBusinesses.forEach(b => {
      console.log(`   - ${b.name} (ID: ${b.id}, Owner: ${b.userId})`);
    });
    console.log();

    // Check if user owns any businesses
    const ownedBusinesses = allBusinesses.filter(b => b.userId === testUser[0].id);
    console.log('👑 Businesses Owned by Test User:');
    if (ownedBusinesses.length === 0) {
      console.log('   None');
    } else {
      ownedBusinesses.forEach(b => console.log(`   - ${b.name}`));
    }
    console.log();

    // Get user's direct role assignments
    console.log('🎭 Direct Role Assignments:');
    const directRoles = await db
      .select({
        role: roles,
        userRole: userRoles,
        business: businesses
      })
      .from(userRoles)
      .innerJoin(roles, eq(userRoles.roleId, roles.id))
      .leftJoin(businesses, eq(userRoles.businessId, businesses.id))
      .where(and(
        eq(userRoles.userId, testUser[0].id),
        eq(userRoles.isActive, true)
      ));

    if (directRoles.length === 0) {
      console.log('   No direct role assignments found');
    } else {
      directRoles.forEach(r => {
        const context = r.business ? `for ${r.business.name}` : 'globally';
        console.log(`   - ${r.role.name} ${context}`);
        console.log(`     Role ID: ${r.role.id}, Assignment ID: ${r.userRole.id}`);
      });
    }
    console.log();

    // Check permissions for each business
    console.log('🔐 Permissions Analysis by Business:');
    for (const business of allBusinesses) {
      console.log(`\n📋 Business: ${business.name}`);
      
      // Get user permissions for this business
      const userPermissions = await RBACService.getUserPermissions(testUser[0].id, business.id);
      console.log(`   Roles: ${userPermissions.roles.length > 0 ? userPermissions.roles.join(', ') : 'None'}`);
      console.log(`   Permissions: ${userPermissions.permissions.length > 0 ? userPermissions.permissions.join(', ') : 'None'}`);
      
      // Check specific permissions
      const hasBusinessRead = await RBACService.hasPermission(testUser[0].id, 'business.read', business.id);
      const hasProductsRead = await RBACService.hasPermission(testUser[0].id, 'products.read', business.id);
      const canAccessBusiness = await RBACService.canAccessBusiness(testUser[0].id, business.id);
      
      console.log(`   Has business.read: ${hasBusinessRead}`);
      console.log(`   Has products.read: ${hasProductsRead}`);
      console.log(`   Can access business: ${canAccessBusiness}`);
    }
    console.log();

    // Check getUserBusinesses result
    console.log('🌐 getUserBusinesses() Result:');
    const accessibleBusinesses = await RBACService.getUserBusinesses(testUser[0].id);
    if (accessibleBusinesses.length === 0) {
      console.log('   No accessible businesses');
    } else {
      accessibleBusinesses.forEach(b => {
        console.log(`   - ${b.name} (ID: ${b.id})`);
      });
    }
    console.log();

    // Check global permissions
    console.log('🌍 Global Permissions:');
    const globalPermissions = await RBACService.getUserPermissions(testUser[0].id);
    console.log(`   Global Roles: ${globalPermissions.roles.length > 0 ? globalPermissions.roles.join(', ') : 'None'}`);
    console.log(`   Global Permissions: ${globalPermissions.permissions.length > 0 ? globalPermissions.permissions.join(', ') : 'None'}`);
    console.log();

    // Check all custom roles (roles created by assign-test-user-role.ts)
    console.log('🛠️  Custom Roles Analysis:');
    const customRoles = await db
      .select()
      .from(roles)
      .where(eq(roles.isActive, true));
    
    const testCustomRoles = customRoles.filter(r => r.name.startsWith('test_custom_'));
    if (testCustomRoles.length === 0) {
      console.log('   No custom test roles found');
    } else {
      for (const customRole of testCustomRoles) {
        console.log(`   - ${customRole.name}: ${customRole.displayName}`);
        
        // Get permissions for this custom role
        const rolePerms = await db
          .select({ permission: permissions })
          .from(rolePermissions)
          .innerJoin(permissions, eq(rolePermissions.permissionId, permissions.id))
          .where(eq(rolePermissions.roleId, customRole.id));
        
        console.log(`     Permissions: ${rolePerms.map(p => p.permission.name).join(', ')}`);
        
        // Check if test user has this role
        const userHasRole = await db
          .select()
          .from(userRoles)
          .where(and(
            eq(userRoles.userId, testUser[0].id),
            eq(userRoles.roleId, customRole.id),
            eq(userRoles.isActive, true)
          ));
        
        if (userHasRole.length > 0) {
          const assignment = userHasRole[0];
          const businessContext = assignment.businessId ? 
            allBusinesses.find(b => b.id === assignment.businessId)?.name || 'Unknown Business' : 
            'Global';
          console.log(`     ✅ Assigned to test user (Context: ${businessContext})`);
        } else {
          console.log(`     ❌ Not assigned to test user`);
        }
      }
    }
    console.log();

    console.log('🎯 Summary:');
    console.log('=' .repeat(50));
    console.log('The issue analysis:');
    console.log('1. getUserBusinesses() includes businesses where user has ANY role');
    console.log('2. canAccessBusiness() specifically requires "business.read" permission');
    console.log('3. When you assign only "products.read", the user gets access to the business');
    console.log('   via getUserBusinesses(), but canAccessBusiness() returns false');
    console.log('4. This creates a mismatch where the business appears in the list but');
    console.log('   access checks fail unless "business.read" is also granted.');
    console.log();
    console.log('💡 Recommendation:');
    console.log('- Either modify canAccessBusiness() to check for ANY permission on the business');
    console.log('- Or ensure "business.read" is automatically included with other permissions');
    console.log('- Or update the business selection logic to use canAccessBusiness() filter');
    
  } catch (error) {
    console.error('❌ Error during debug:', error);
    process.exit(1);
  }
}

// Run the debug
await debugTestUserAccess();
process.exit(0);