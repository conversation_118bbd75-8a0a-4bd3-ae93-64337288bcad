import { redis } from '../app/lib/redis.server';

/**
 * Development helper to clear Better Auth rate limits
 * Use this when you hit rate limits during development testing
 */
async function clearRateLimits() {
  try {
    console.log('🧹 Clearing Better Auth rate limits...');

    // Get all rate limit keys (check both patterns for compatibility)
    const keys1 = await redis.keys('rate_limit:*');
    const keys2 = await redis.keys('ratelimit:*');
    const keys = [...keys1, ...keys2];
    
    if (keys.length === 0) {
      console.log('✅ No rate limit keys found - limits are already clear');
      return;
    }

    console.log(`🔍 Found ${keys.length} rate limit keys to clear`);

    // Delete all rate limit keys
    if (keys.length > 0) {
      await redis.del(...keys);
    }

    console.log('✅ Rate limits cleared successfully!');
    console.log('🚀 You can now login without rate limit restrictions');
    
    // Show some of the cleared keys for reference
    if (keys.length > 0) {
      console.log('\n📋 Cleared rate limit keys:');
      keys.slice(0, 5).forEach(key => {
        console.log(`   - ${key}`);
      });
      if (keys.length > 5) {
        console.log(`   ... and ${keys.length - 5} more`);
      }
    }

  } catch (error) {
    console.error('❌ Error clearing rate limits:', error);
    
    if (error instanceof Error && error.message.includes('ECONNREFUSED')) {
      console.log('\n💡 Redis connection failed. Make sure Redis is running:');
      console.log('   - Check if Redis is installed and running');
      console.log('   - Verify REDIS_URL in your .env file');
      console.log('   - Try: redis-server (if installed locally)');
    }
  } finally {
    // Note: Upstash Redis doesn't need explicit connection closing
    console.log('🔌 Redis operations completed');
  }
}

/**
 * Show current rate limit status
 */
async function showRateLimitStatus() {
  try {
    console.log('📊 Checking current rate limit status...\n');

    // Get all rate limit keys (check both patterns for compatibility)
    const keys1 = await redis.keys('rate_limit:*');
    const keys2 = await redis.keys('ratelimit:*');
    const keys = [...keys1, ...keys2];
    
    if (keys.length === 0) {
      console.log('✅ No active rate limits found');
      console.log('🚀 All endpoints are available for testing');
      return;
    }

    console.log(`🔍 Found ${keys.length} active rate limit entries:\n`);

    // Show details for each rate limit key
    for (const key of keys.slice(0, 10)) { // Limit to first 10 for readability
      try {
        const value = await redis.get(key);
        const ttl = await redis.ttl(key);
        
        // Parse the key to extract endpoint and identifier
        const keyParts = key.split(':');
        const endpoint = keyParts[2] || 'unknown';
        const identifier = keyParts[3] || 'unknown';
        
        console.log(`📍 Endpoint: ${endpoint}`);
        console.log(`   🆔 Identifier: ${identifier}`);
        console.log(`   📊 Current count: ${value}`);
        console.log(`   ⏰ Expires in: ${ttl > 0 ? `${ttl} seconds` : 'expired'}`);
        console.log('');
      } catch (err) {
        console.log(`❌ Error reading key ${key}:`, err);
      }
    }

    if (keys.length > 10) {
      console.log(`... and ${keys.length - 10} more rate limit entries`);
    }

    console.log('\n💡 To clear all rate limits: bun run dev:clear-rate-limits');

  } catch (error) {
    console.error('❌ Error checking rate limit status:', error);
  } finally {
    // Note: Upstash Redis doesn't need explicit connection closing
    console.log('🔌 Redis operations completed');
  }
}

// CLI interface
async function main() {
  const command = process.argv[2];

  // Check if we're in development
  if (process.env.NODE_ENV === 'production') {
    console.log('⚠️  This script is for development only!');
    console.log('❌ Cannot clear rate limits in production environment');
    process.exit(1);
  }

  try {
    switch (command) {
      case 'clear':
      case undefined: // Default action
        await clearRateLimits();
        break;

      case 'status':
        await showRateLimitStatus();
        break;

      default:
        console.log('🛠️ Better Auth Rate Limit Helper');
        console.log('');
        console.log('Available commands:');
        console.log('  clear (default)  - Clear all rate limits');
        console.log('  status          - Show current rate limit status');
        console.log('');
        console.log('Examples:');
        console.log('  bun run scripts/clear-rate-limits.ts');
        console.log('  bun run scripts/clear-rate-limits.ts clear');
        console.log('  bun run scripts/clear-rate-limits.ts status');
        break;
    }
  } catch (error) {
    console.error('💥 Command failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}
