#!/usr/bin/env bun

import { db } from '../app/lib/db/connection';
import { sql } from 'drizzle-orm';

async function checkUsers() {
  try {
    console.log('Checking existing users...');
    
    const result = await db.execute(sql`
      SELECT id, name, email, "createdAt" 
      FROM "user" 
      ORDER BY "createdAt" DESC 
      LIMIT 5
    `);
    
    console.log('Found users:', result.rows.length);
    result.rows.forEach((user, index) => {
      console.log(`${index + 1}. ID: ${user.id}, Name: ${user.name}, Email: ${user.email}`);
    });
    
    if (result.rows.length === 0) {
      console.log('\nNo users found. You need to register a user first through the app.');
    } else {
      console.log('\nYou can use any of these user IDs for testing business operations.');
    }
    
  } catch (error) {
    console.error('Error checking users:', error);
  }
}

checkUsers();
