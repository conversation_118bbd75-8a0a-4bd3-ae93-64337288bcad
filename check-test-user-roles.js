import { RBACService } from './app/lib/services/rbacService.server.js';
import { db } from './app/lib/db/connection.js';
import { users } from './app/lib/db/schema.js';
import { eq } from 'drizzle-orm';

async function checkTestUserRoles() {
  try {
    // Find test user
    const testUser = await db.select().from(users).where(eq(users.email, '<EMAIL>')).limit(1);
    
    if (!testUser[0]) {
      console.log('❌ Test user not found');
      return;
    }

    console.log(`✅ Found test user: ${testUser[0].name} (${testUser[0].email})`);
    console.log(`   User ID: ${testUser[0].id}`);
    
    // Check permissions without business context (system-wide)
    const systemPermissions = await RBACService.getUserPermissions(testUser[0].id);
    console.log('\n🌐 System-wide permissions:');
    console.log('   Roles:', systemPermissions.roles);
    console.log('   Permissions:', systemPermissions.permissions.slice(0, 5), systemPermissions.permissions.length > 5 ? `... and ${systemPermissions.permissions.length - 5} more` : '');
    
    // Check permissions for KWACI Coffee House business
    const businessPermissions = await RBACService.getUserPermissions(testUser[0].id, '26b99509-b432-4e5d-8930-c8bc28c8ed0e');
    console.log('\n🏢 KWACI Coffee House permissions:');
    console.log('   Roles:', businessPermissions.roles);
    console.log('   Permissions:', businessPermissions.permissions.slice(0, 5), businessPermissions.permissions.length > 5 ? `... and ${businessPermissions.permissions.length - 5} more` : '');
    
  } catch (error) {
    console.error('❌ Error checking roles:', error);
  }
}

await checkTestUserRoles();