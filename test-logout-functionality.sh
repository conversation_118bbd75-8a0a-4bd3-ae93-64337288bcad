#!/bin/bash

# Comprehensive Logout Functionality Testing Script
# This script tests the enhanced logout system with Redis integration

set -e

# Configuration
BASE_URL="http://localhost:5174"
API_BASE="$BASE_URL/api/auth"
HEALTH_URL="$BASE_URL/api/health/redis"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test Redis health for logout functionality
test_redis_health_for_logout() {
    log_info "Testing Redis health for logout functionality..."
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$HEALTH_URL?detailed=true")
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')
    
    if [ "$http_code" -eq 200 ]; then
        log_success "Redis is healthy for logout operations"
        echo "$body" | jq '.redis.operations' 2>/dev/null || echo "$body"
    else
        log_warning "Redis health check failed - logout will work in fallback mode"
        echo "$body"
    fi
}

# Test logout endpoint availability
test_logout_endpoint() {
    log_info "Testing logout endpoint availability..."
    
    # Test OPTIONS request (CORS preflight)
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        -X OPTIONS "$API_BASE/sign-out" \
        -H "Origin: http://localhost:3000" \
        -H "Access-Control-Request-Method: POST")
    
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    
    if [ "$http_code" -eq 200 ] || [ "$http_code" -eq 204 ]; then
        log_success "Logout endpoint is available and CORS-enabled"
    else
        log_warning "Logout endpoint CORS check failed (HTTP $http_code)"
    fi
}

# Test logout rate limiting
test_logout_rate_limiting() {
    log_info "Testing logout rate limiting..."
    
    local attempts=0
    local rate_limited=false
    
    # Make multiple logout attempts to test rate limiting
    for i in {1..12}; do
        attempts=$((attempts + 1))
        log_info "Logout attempt $attempts..."
        
        response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
            -X POST "$API_BASE/sign-out" \
            -H "Content-Type: application/json" \
            -H "Origin: http://localhost:3000")
        
        http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
        body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')
        
        if [ "$http_code" -eq 429 ]; then
            log_success "Logout rate limiting triggered after $attempts attempts"
            echo "$body" | jq '.' 2>/dev/null || echo "$body"
            rate_limited=true
            break
        elif [ "$http_code" -eq 200 ] || [ "$http_code" -eq 401 ]; then
            log_info "Logout attempt $attempts completed (status: $http_code)"
        else
            log_warning "Unexpected response code: $http_code"
            echo "$body"
        fi
        
        # Small delay between attempts
        sleep 0.5
    done
    
    if [ "$rate_limited" = false ]; then
        log_warning "Logout rate limiting was not triggered after $attempts attempts"
        log_info "This might be expected if rate limits are high for logout endpoint"
    fi
}

# Test logout with invalid session
test_logout_invalid_session() {
    log_info "Testing logout with invalid session..."
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        -X POST "$API_BASE/sign-out" \
        -H "Content-Type: application/json" \
        -H "Cookie: better-auth.session_token=invalid-token-12345" \
        -H "Origin: http://localhost:3000")
    
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')
    
    if [ "$http_code" -eq 200 ] || [ "$http_code" -eq 401 ]; then
        log_success "Logout with invalid session handled correctly (HTTP $http_code)"
    else
        log_warning "Unexpected response for invalid session logout: $http_code"
        echo "$body"
    fi
}

# Test logout headers and response format
test_logout_response_format() {
    log_info "Testing logout response format and headers..."
    
    response=$(curl -s -I \
        -X POST "$API_BASE/sign-out" \
        -H "Content-Type: application/json" \
        -H "Origin: http://localhost:3000")
    
    if echo "$response" | grep -q "Access-Control-Allow-Origin"; then
        log_success "CORS headers present in logout response"
    else
        log_warning "CORS headers missing in logout response"
    fi
    
    if echo "$response" | grep -q "X-RateLimit"; then
        log_success "Rate limit headers present in logout response"
        echo "$response" | grep "X-RateLimit"
    else
        log_info "Rate limit headers not found (may be expected)"
    fi
}

# Test session revocation endpoints
test_session_revocation() {
    log_info "Testing session revocation endpoints..."
    
    # Test revoke sessions endpoint
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        -X POST "$API_BASE/revoke-sessions" \
        -H "Content-Type: application/json" \
        -H "Origin: http://localhost:3000")
    
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    
    if [ "$http_code" -eq 200 ] || [ "$http_code" -eq 401 ]; then
        log_success "Session revocation endpoint available (HTTP $http_code)"
    else
        log_info "Session revocation endpoint response: $http_code"
    fi
}

# Test logout with network simulation
test_logout_network_conditions() {
    log_info "Testing logout under various network conditions..."
    
    # Test with timeout (simulating slow network)
    response=$(timeout 2s curl -s -w "HTTPSTATUS:%{http_code}" \
        -X POST "$API_BASE/sign-out" \
        -H "Content-Type: application/json" \
        --max-time 1 2>/dev/null || echo "TIMEOUT")
    
    if [[ "$response" == *"TIMEOUT"* ]]; then
        log_info "Logout timeout test completed (expected behavior)"
    else
        log_info "Logout completed within timeout period"
    fi
}

# Test Redis session cleanup verification
test_redis_session_cleanup() {
    log_info "Testing Redis session cleanup verification..."
    
    # Check if we can verify session cleanup through health endpoint
    response=$(curl -s "$HEALTH_URL?detailed=true")
    
    if echo "$response" | grep -q "operations"; then
        log_success "Redis operations monitoring available for session cleanup verification"
    else
        log_info "Redis operations monitoring not available"
    fi
}

# Main test execution
main() {
    log_info "Starting Comprehensive Logout Functionality Tests"
    echo "=================================================="
    
    # Check if server is running
    if ! curl -s "$BASE_URL" > /dev/null; then
        log_error "Server is not running at $BASE_URL"
        log_info "Please start the server with: bun run dev"
        exit 1
    fi
    
    log_success "Server is running at $BASE_URL"
    echo ""
    
    # Run tests
    test_redis_health_for_logout
    echo ""
    
    test_logout_endpoint
    echo ""
    
    test_logout_response_format
    echo ""
    
    test_logout_invalid_session
    echo ""
    
    test_session_revocation
    echo ""
    
    test_logout_network_conditions
    echo ""
    
    test_redis_session_cleanup
    echo ""
    
    test_logout_rate_limiting
    echo ""
    
    log_success "All logout functionality tests completed!"
    echo ""
    log_info "Manual Testing Checklist:"
    echo "1. ✓ Test logout button in header UserMenu"
    echo "2. ✓ Test logout button in sidebar NavUser"
    echo "3. ✓ Test logout confirmation dialog"
    echo "4. ✓ Test 'logout from all devices' option"
    echo "5. ✓ Test toast notifications for success/error"
    echo "6. ✓ Test logout with network disconnected"
    echo "7. ✓ Test logout redirection"
    echo "8. ✓ Test session cleanup in Redis (Upstash console)"
    echo "9. ✓ Test logout in different browsers/devices"
    echo "10. ✓ Test logout rate limiting in browser"
}

# Check dependencies
check_dependencies() {
    if ! command -v curl &> /dev/null; then
        log_error "curl is required but not installed"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_warning "jq is not installed - JSON output will not be formatted"
    fi
    
    if ! command -v timeout &> /dev/null; then
        log_warning "timeout command not available - network tests may be limited"
    fi
}

# Script execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    check_dependencies
    main "$@"
fi
