# RBAC Troubleshooting Guide

This document provides comprehensive troubleshooting steps for Role-Based Access Control (RBAC) issues in the Kwaci Grow application, particularly focusing on database seeding and permission assignment problems.

## Common Issues and Solutions

### 1. Business Owner Role Has No Permissions

**Symptoms:**
- User has `business_owner` role but gets "Access Denied" errors
- RBAC settings page returns 403/unauthorized
- Debug scripts show role exists but no permissions assigned

**Root Cause:**
The `business_owner` role exists in the database but has no permissions assigned to it due to:
- RBAC seeding script not running properly
- Role-permission assignments failing silently
- Database constraint issues during seeding

**Solution Steps:**

1. **Verify Role Exists:**
   ```bash
   # Create debug script to check role
   echo "import { db } from './app/lib/db/connection';
   import { roles, rolePermissions, permissions } from './app/lib/db/schema';
   import { eq } from 'drizzle-orm';
   
   const role = await db.select().from(roles).where(eq(roles.name, 'business_owner'));
   console.log('Business Owner Role:', role);
   
   if (role.length > 0) {
     const perms = await db
       .select({ permission: permissions.name })
       .from(rolePermissions)
       .innerJoin(permissions, eq(rolePermissions.permissionId, permissions.id))
       .where(eq(rolePermissions.roleId, role[0].id));
     console.log('Assigned Permissions:', perms);
   }
   
   process.exit(0);" > debug-business-owner.js
   
   bun run debug-business-owner.js
   ```

2. **Re-run RBAC Seeding:**
   ```bash
   bun run app/lib/db/rbac-seed.ts
   ```

3. **Manual Permission Assignment (if seeding fails):**
   ```bash
   # Create manual fix script
   echo "import { db } from './app/lib/db/connection';
   import { roles, rolePermissions, permissions } from './app/lib/db/schema';
   import { eq } from 'drizzle-orm';
   
   const businessOwnerPerms = [
     'business.read', 'business.update', 'business.manage_users',
     'inventory.read', 'inventory.create', 'inventory.update', 'inventory.delete',
     'categories.read', 'categories.create', 'categories.update', 'categories.delete',
     'cogs.read', 'cogs.calculate', 'cogs.update',
     'users.read', 'users.assign_roles',
     'roles.read'
   ];
   
   const role = await db.select().from(roles).where(eq(roles.name, 'business_owner')).limit(1);
   if (role.length === 0) {
     console.error('Business owner role not found!');
     process.exit(1);
   }
   
   for (const permName of businessOwnerPerms) {
     const perm = await db.select().from(permissions).where(eq(permissions.name, permName)).limit(1);
     if (perm.length === 0) {
       console.warn('Permission not found:', permName);
       continue;
     }
     
     const existing = await db.select().from(rolePermissions)
       .where(eq(rolePermissions.roleId, role[0].id) && eq(rolePermissions.permissionId, perm[0].id))
       .limit(1);
     
     if (existing.length === 0) {
       await db.insert(rolePermissions).values({
         roleId: role[0].id,
         permissionId: perm[0].id
       });
       console.log('Assigned permission:', permName);
     }
   }
   
   console.log('Business owner permissions fixed!');
   process.exit(0);" > fix-business-owner-permissions.js
   
   bun run fix-business-owner-permissions.js
   ```

### 2. User Has Role But No Business Association

**Symptoms:**
- User has correct role but permissions don't work for specific business
- `getUserPermissions` returns empty results
- Business-scoped operations fail

**Root Cause:**
User role assignment is missing `businessId` or has incorrect business association.

**Solution:**
```bash
# Check user role assignments
echo "import { db } from './app/lib/db/connection';
import { userRoles, users, businesses } from './app/lib/db/schema';
import { eq } from 'drizzle-orm';

const userId = 'USER_ID_HERE';
const businessId = 'BUSINESS_ID_HERE';

const userRoleAssignments = await db
  .select({
    userId: userRoles.userId,
    roleId: userRoles.roleId,
    businessId: userRoles.businessId
  })
  .from(userRoles)
  .where(eq(userRoles.userId, userId));

console.log('User Role Assignments:', userRoleAssignments);
process.exit(0);" > check-user-roles.js

bun run check-user-roles.js
```

### 3. Development Server Access Issues

**Symptoms:**
- Connection refused errors when accessing RBAC pages
- Server stops unexpectedly during testing

**Solution:**
1. **Check if server is running:**
   ```bash
   lsof -i :5173
   ```

2. **Kill existing processes and restart:**
   ```bash
   pkill -f "bun run dev"
   bun run dev
   ```

3. **Check for port conflicts:**
   ```bash
   netstat -tulpn | grep :5173
   ```

### 4. Database Reset and Seeding Issues

**Symptoms:**
- Seeding fails with constraint violations
- Incomplete data after seeding
- RBAC data missing after reset

**Prevention Steps:**

1. **Always use the complete reset and seed process:**
   ```bash
   bun run db:reset
   ```

2. **Verify seeding completion:**
   ```bash
   # Check if all RBAC data was created
   echo "import { db } from './app/lib/db/connection';
   import { roles, permissions, rolePermissions, userRoles } from './app/lib/db/schema';
   
   const roleCount = await db.select().from(roles);
   const permCount = await db.select().from(permissions);
   const rolePermCount = await db.select().from(rolePermissions);
   const userRoleCount = await db.select().from(userRoles);
   
   console.log('RBAC Data Summary:');
   console.log('- Roles:', roleCount.length);
   console.log('- Permissions:', permCount.length);
   console.log('- Role-Permission Assignments:', rolePermCount.length);
   console.log('- User-Role Assignments:', userRoleCount.length);
   
   process.exit(0);" > verify-rbac-data.js
   
   bun run verify-rbac-data.js
   ```

## Diagnostic Scripts

### Complete RBAC Health Check

Create this script to diagnose all RBAC issues:

```javascript
// rbac-health-check.js
import { db } from './app/lib/db/connection';
import { roles, permissions, rolePermissions, userRoles, users, businesses } from './app/lib/db/schema';
import { eq } from 'drizzle-orm';
import { RBACService } from './app/lib/services/rbacService.server';

async function healthCheck() {
  console.log('🔍 RBAC Health Check Starting...');
  
  // 1. Check basic RBAC data
  const allRoles = await db.select().from(roles);
  const allPermissions = await db.select().from(permissions);
  const allRolePermissions = await db.select().from(rolePermissions);
  const allUserRoles = await db.select().from(userRoles);
  
  console.log('\n📊 Basic RBAC Data:');
  console.log(`- Roles: ${allRoles.length}`);
  console.log(`- Permissions: ${allPermissions.length}`);
  console.log(`- Role-Permission Assignments: ${allRolePermissions.length}`);
  console.log(`- User-Role Assignments: ${allUserRoles.length}`);
  
  // 2. Check business_owner role specifically
  const businessOwnerRole = allRoles.find(r => r.name === 'business_owner');
  if (businessOwnerRole) {
    const businessOwnerPerms = await db
      .select({ permission: permissions.name })
      .from(rolePermissions)
      .innerJoin(permissions, eq(rolePermissions.permissionId, permissions.id))
      .where(eq(rolePermissions.roleId, businessOwnerRole.id));
    
    console.log('\n👑 Business Owner Role:');
    console.log(`- Role ID: ${businessOwnerRole.id}`);
    console.log(`- Permissions: ${businessOwnerPerms.length}`);
    if (businessOwnerPerms.length === 0) {
      console.log('❌ WARNING: Business owner has no permissions!');
    } else {
      console.log('✅ Business owner has permissions assigned');
    }
  } else {
    console.log('❌ ERROR: Business owner role not found!');
  }
  
  // 3. Check test users
  const testUsers = await db.select().from(users).where(eq(users.email, '<EMAIL>'));
  if (testUsers.length > 0) {
    const testUser = testUsers[0];
    const userBusinesses = await db.select().from(businesses).where(eq(businesses.userId, testUser.id));
    
    console.log('\n👤 Test User (John Doe):');
    console.log(`- User ID: ${testUser.id}`);
    console.log(`- Businesses: ${userBusinesses.length}`);
    
    if (userBusinesses.length > 0) {
      const business = userBusinesses[0];
      console.log(`- Business ID: ${business.id}`);
      
      // Check permissions using RBACService
      try {
        const hasBusinessOwnerRole = await RBACService.hasRole(testUser.id, 'business_owner', business.id);
        const userPermissions = await RBACService.getUserPermissions(testUser.id, business.id);
        
        console.log(`- Has business_owner role: ${hasBusinessOwnerRole}`);
        console.log(`- Total permissions: ${userPermissions.permissions.length}`);
        
        if (!hasBusinessOwnerRole || userPermissions.permissions.length === 0) {
          console.log('❌ WARNING: User permissions issue detected!');
        } else {
          console.log('✅ User permissions look good');
        }
      } catch (error) {
        console.log('❌ ERROR checking user permissions:', error.message);
      }
    }
  }
  
  console.log('\n🔍 Health check completed!');
}

healthCheck().then(() => process.exit(0)).catch(console.error);
```

## Prevention Best Practices

### 1. Always Use Complete Database Reset

When resetting the database, always use the complete process:

```bash
# Complete reset with proper seeding
bun run db:reset

# Or manual steps:
bun run db:migrate
bun run db:seed
```

### 2. Verify Seeding Success

After seeding, always verify the data was created correctly:

```bash
# Run health check
bun run rbac-health-check.js

# Test login with seeded credentials
# <EMAIL> / password123
```

### 3. Monitor RBAC Service Logs

Enable debug logging in development to catch permission issues early:

```typescript
// In rbacService.server.ts, add logging
console.log('Checking permission:', { userId, permission, businessId });
console.log('User permissions:', userPermissions);
```

### 4. Use Proper Error Handling

Always handle RBAC errors gracefully:

```typescript
try {
  await requireBusinessOwner(request, businessId);
} catch (error) {
  console.error('RBAC check failed:', error);
  throw redirect('/unauthorized');
}
```

## Emergency Recovery

If RBAC is completely broken:

1. **Full database reset:**
   ```bash
   bun run db:reset
   ```

2. **Manual RBAC fix:**
   ```bash
   bun run fix-business-owner-permissions.js
   ```

3. **Verify fix:**
   ```bash
   bun run rbac-health-check.js
   ```

4. **Test access:**
   - Navigate to RBAC settings page
   - Verify no "Access Denied" errors

## Related Documentation

- [RBAC Implementation Guide](./rbac-implementation.md)
- [Database Seeding Guide](./database-seeding.md)
- [Authentication Troubleshooting](./authentication-troubleshooting.md)

## Support

If issues persist after following this guide:

1. Check the application logs for detailed error messages
2. Verify database connectivity and schema integrity
3. Ensure all migrations have been applied
4. Review the RBAC service implementation for recent changes