# Role-Based Access Control (RBAC) Implementation

This document describes the RBAC system implementation for the Kwaci Grow application.

## Overview

The RBAC system provides fine-grained access control for users within the application, supporting both system-wide and business-specific permissions. It's built on top of the existing Better Auth authentication system and integrates seamlessly with Remix routes and React components.

## Architecture

### Database Schema

The RBAC system uses four main tables:

- **`role`**: Defines available roles (e.g., super_admin, business_owner, staff)
- **`permission`**: Defines granular permissions (e.g., inventory.read, business.create)
- **`role_permission`**: Junction table linking roles to their permissions
- **`user_role`**: Assigns roles to users, optionally scoped to specific businesses

### Key Components

1. **RBAC Service** (`app/lib/services/rbacService.server.ts`)
   - Core business logic for permission checking
   - User role management
   - Database operations

2. **RBAC Middleware** (`app/lib/middleware/rbac.server.ts`)
   - Route protection functions
   - Permission validation for server-side routes

3. **RBAC Hook** (`app/lib/hooks/useRBAC.ts`)
   - React hook for client-side permission checking
   - Protected component wrapper

4. **RBAC Loader Utilities** (`app/lib/utils/rbacLoader.server.ts`)
   - Helper functions for including RBAC data in route loaders

## Default Roles and Permissions

### System Roles

- **`super_admin`**: Full system access, can manage all businesses and users
- **`business_owner`**: Full access to their business, can manage business users
- **`business_manager`**: Broad business access, limited user management
- **`inventory_manager`**: Full inventory and category management
- **`staff`**: Basic operational access
- **`viewer`**: Read-only access

### Permission Categories

- **Business**: `business.create`, `business.read`, `business.update`, `business.delete`, `business.manage_users`
- **Inventory**: `inventory.create`, `inventory.read`, `inventory.update`, `inventory.delete`
- **Categories**: `categories.create`, `categories.read`, `categories.update`, `categories.delete`
- **COGS**: `cogs.read`, `cogs.calculate`, `cogs.update`
- **Users**: `users.create`, `users.read`, `users.update`, `users.delete`, `users.assign_roles`
- **Roles**: `roles.create`, `roles.read`, `roles.update`, `roles.delete`

## Usage Examples

### 1. Protecting Routes with Middleware

```typescript
// app/routes/business.$businessId.inventory.tsx
import { requireInventoryAccess } from '~/lib/middleware/rbac.server';

export async function loader({ request, params }: LoaderFunctionArgs) {
  const businessId = params.businessId!;
  
  // Require inventory read access
  await requireInventoryAccess(request, businessId, 'read');
  
  // Your loader logic here...
}
```

### 2. Including RBAC Data in Loaders

```typescript
// Method 1: Manual inclusion
import { withRBAC } from '~/lib/utils/rbacLoader.server';

export async function loader({ request, params }: LoaderFunctionArgs) {
  const rbac = await withRBAC(request, params.businessId);
  const data = await getYourData();
  
  return json({ data, rbac });
}

// Method 2: Using the helper function
import { createRBACLoader, getBusinessIdFromParams } from '~/lib/utils/rbacLoader.server';

export const loader = createRBACLoader(
  async ({ request, params, rbac }) => {
    const data = await getYourData();
    return { data };
  },
  { 
    businessId: getBusinessIdFromParams,
    requireAuth: true 
  }
);
```

### 3. Using RBAC in React Components

```tsx
import { useRBAC, Protected } from '~/lib/hooks/useRBAC';

export default function MyComponent() {
  const rbac = useRBAC();
  
  return (
    <div>
      {/* Conditional rendering based on permissions */}
      <Protected permissions={['inventory.create']}>
        <button>Add New Item</button>
      </Protected>
      
      {/* Multiple permissions (ANY) */}
      <Protected permissions={['inventory.update', 'inventory.delete']}>
        <div>Edit Actions</div>
      </Protected>
      
      {/* Multiple permissions (ALL required) */}
      <Protected 
        permissions={['inventory.update', 'business.manage_users']} 
        requireAll={true}
      >
        <div>Advanced Actions</div>
      </Protected>
      
      {/* Role-based access */}
      <Protected roles={['business_owner', 'business_manager']}>
        <div>Management Panel</div>
      </Protected>
      
      {/* Using hook methods directly */}
      {rbac.inventory.canCreate && (
        <button>Create Item</button>
      )}
      
      {rbac.isBusinessOwner && (
        <div>Owner Dashboard</div>
      )}
    </div>
  );
}
```

### 4. Server-Side Permission Checking

```typescript
import { RBACService } from '~/lib/services/rbacService.server';

// Check specific permission
const canEdit = await RBACService.hasPermission(
  userId, 
  'inventory.update', 
  businessId
);

// Check multiple permissions (ANY)
const canManage = await RBACService.hasAnyPermission(
  userId, 
  ['inventory.update', 'inventory.delete'], 
  businessId
);

// Check role
const isOwner = await RBACService.hasRole(
  userId, 
  'business_owner', 
  businessId
);

// Get all user permissions
const userPerms = await RBACService.getUserPermissions(userId, businessId);
```

## Common Middleware Functions

```typescript
// Require specific business access
await requireBusinessAccess(request, businessId);

// Require business owner role
await requireBusinessOwner(request, businessId);

// Require super admin
await requireSuperAdmin(request);

// Require inventory permissions
await requireInventoryAccess(request, businessId, 'create');

// Require category permissions
await requireCategoryAccess(request, businessId, 'update');

// Require user management permissions
await requireUserManagement(request, 'assign_roles', businessId);
```

## Role Management

### Assigning Roles

```typescript
// Assign business owner role
await RBACService.assignBusinessOwnerRole(userId, businessId);

// Assign any role
await RBACService.assignRole(userId, 'staff', businessId);

// Remove role
await RBACService.removeRole(userId, 'staff', businessId);
```

### Creating Custom Roles

```typescript
// Create new role
const roleId = await RBACService.createRole(
  'custom_role',
  'Custom Role Description'
);

// Assign permissions to role
await RBACService.assignPermissionsToRole(roleId, [
  'inventory.read',
  'categories.read'
]);
```

## Security Considerations

1. **Server-Side Validation**: Always validate permissions on the server side. Client-side checks are for UX only.

2. **Business Scoping**: Most permissions are scoped to specific businesses. Ensure business ID validation.

3. **Super Admin Access**: Super admins have system-wide access. Use sparingly.

4. **Permission Granularity**: Use the most specific permission required for each action.

5. **Role Hierarchy**: Consider role inheritance for complex permission structures.

## Testing

### Testing Protected Routes

```typescript
// Test with proper permissions
const response = await request(app)
  .get('/business/123/inventory')
  .set('Cookie', sessionCookieWithPermissions)
  .expect(200);

// Test without permissions
const response = await request(app)
  .get('/business/123/inventory')
  .set('Cookie', sessionCookieWithoutPermissions)
  .expect(302); // Redirect to unauthorized
```

### Testing Components

```tsx
// Mock RBAC data in tests
const mockRBACData = {
  permissions: ['inventory.read', 'inventory.create'],
  roles: ['staff'],
  userId: 'user123',
  businessId: 'business123'
};

render(
  <RemixStub initialEntries={["/"]}>
    <Route path="/" element={<YourComponent />} loader={() => ({ rbac: mockRBACData })} />
  </RemixStub>
);
```

## Migration and Setup

1. **Database Migration**: Run `bun run db:migrate` to apply RBAC schema
2. **Seed Data**: Run `bun app/lib/db/rbac-seed.ts` to populate default roles and permissions
3. **Assign Initial Roles**: Assign business owners and super admins as needed

## Troubleshooting

### Common Issues

1. **Permission Denied**: Check if user has the required role/permission for the business
2. **Business Scoping**: Ensure business ID is correctly passed to permission checks
3. **Session Issues**: Verify user session is valid and contains user ID
4. **Database Sync**: Ensure migrations are applied and seed data is loaded

### Debug Mode

The example inventory route includes debug information in development mode to help troubleshoot permission issues.

## Future Enhancements

1. **Permission Caching**: Implement Redis caching for frequently checked permissions
2. **Audit Logging**: Track permission changes and access attempts
3. **Dynamic Permissions**: Support for runtime permission creation
4. **Role Templates**: Predefined role templates for common business types
5. **Permission Inheritance**: Hierarchical permission structures