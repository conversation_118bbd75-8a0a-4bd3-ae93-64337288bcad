# Route Creation Checklist

This checklist ensures consistent RBAC implementation and security practices when creating new routes in the Kwaci Grow application.

## 📋 Server Endpoint Route Checklist

### Required Steps

- [ ] **Protect with `createProtectedRoute`**
  ```typescript
  // app/routes/api.resource.protected.tsx
  import { createProtectedRoute } from '~/lib/middleware/autoRBAC.server';
  import { RBAC_CONFIGS } from '~/lib/middleware/autoRBAC.server';
  
  // Your business logic functions
  export const resourceLoader = async ({ request, params }: LoaderFunctionArgs) => {
    // Implementation here
  };
  
  export const resourceAction = async ({ request, params }: ActionFunctionArgs) => {
    // Implementation here
  };
  
  // Export protected versions
  export const { loader, action } = createProtectedRoute({
    loader: resourceLoader,
    action: resourceAction,
    config: RBAC_CONFIGS.resource, // Use appropriate config
  });
  ```

- [ ] **Verify RBAC Config exists**
  - Check if `RBAC_CONFIGS.{resource}` exists in `app/lib/middleware/autoRBAC.server.ts`
  - If not, create new config following existing patterns

- [ ] **Create helper functions if needed**
  - Add resource-specific helper functions in `app/lib/middleware/rbac.server.ts`
  - Follow naming convention: `require{Resource}Access`
  
  ```typescript
  // app/lib/middleware/rbac.server.ts
  export async function requireResourceAccess(
    request: Request,
    businessId: string,
    action: 'read' | 'create' | 'update' | 'delete' = 'read',
    redirectTo?: string
  ): Promise<{ userId: string; businessId: string }> {
    const result = await requirePermissions(request, {
      permissions: [`resource.${action}`],
      businessId,
      redirectTo,
    });
    
    return { ...result, businessId };
  }
  ```

### Verification Steps

- [ ] **Test automatic permission mapping**
  - GET requests → `{resource}.read` permission
  - POST requests → `{resource}.create` permission
  - PUT/PATCH requests → `{resource}.update` permission
  - DELETE requests → `{resource}.delete` permission

- [ ] **Test unauthorized access**
  - Verify users without permissions are redirected
  - Check error handling and response codes

- [ ] **Validate business context**
  - Ensure business ID is properly handled when required
  - Test cross-business access restrictions

## 🖥️ UI Route Checklist

### Required Steps

- [ ] **Protect with `createProtectedLoader`**
  ```typescript
  // app/routes/resource.tsx
  import { createProtectedLoader } from '~/lib/middleware/autoRBAC.server';
  import { withRBAC } from '~/lib/utils/rbacLoader.server';
  import { RBAC_CONFIGS } from '~/lib/middleware/autoRBAC.server';
  
  const resourceLoader = async ({ request, params }: LoaderFunctionArgs) => {
    // Your data fetching logic
    const data = await getResourceData();
    
    // REQUIRED: Include RBAC data for client-side use
    const rbacData = await withRBAC(request, params.businessId);
    
    return json({
      data,
      ...rbacData, // Includes permissions, roles, userId, businessId
    });
  };
  
  // Export protected loader
  export const loader = createProtectedLoader({
    loader: resourceLoader,
    config: RBAC_CONFIGS.resource,
  });
  ```

- [ ] **Pass RBAC data using `withRBAC`**
  - Always include `withRBAC` call in loader function
  - Spread RBAC data into the JSON response
  - Ensure business ID is passed when available

- [ ] **Add client-side route protection**
  ```typescript
  // Component implementation
  import { useRouteProtection } from '~/lib/hooks/useRouteProtection';
  import { useRBAC, Protected } from '~/lib/hooks/useRBAC';
  
  export default function ResourcePage() {
    // REQUIRED: Protect against client-side navigation
    useRouteProtection({
      permission: 'resource.read', // Use appropriate permission
      preserveUrl: true, // Preserve URL for post-auth redirect
      redirectTo: '/unauthorized' // Optional: custom redirect path
    });
    
    const { data } = useLoaderData<typeof loader>();
    const rbac = useRBAC();
    
    return (
      <div>
        {/* Conditional rendering based on permissions */}
        {rbac.categories.canCreate && (
          <button>Create New Category</button>
        )}
        
        {rbac.categories.canUpdate && (
          <EditButton />
        )}
        
        {rbac.categories.canDelete && (
          <DeleteButton />
        )}
        
        {/* Alternative: Using Protected component for cleaner JSX */}
        <Protected permissions={['categories.create']}>
          <button>Create New Category</button>
        </Protected>
        
        <Protected permissions={['categories.update']}>
          <EditButton />
        </Protected>
        
        <Protected permissions={['categories.delete']}>
          <DeleteButton />
        </Protected>
      </div>
    );
  }
  
  // Note: Choose between conditional rendering approaches:
      // 1. Direct checks: rbac.categories.canCreate && <Component />
      // 2. Protected component: <Protected permissions={['categories.create']}><Component /></Protected>
  // Protected component is cleaner for complex permission logic
  ```

- [ ] **Implement client-side RBAC checks**
  - Use conditional rendering based on permissions from `useRBAC`
  - Choose between direct checks or `Protected` component
  - Hide/show UI elements based on user capabilities

- [ ] **Add new resource to useRBAC hook (if creating new resource type)**
  - Create resource object in `app/lib/hooks/useRBAC.ts`
  - Follow existing pattern with `canRead`, `canCreate`, `canUpdate`, `canDelete`
  - Add any resource-specific permissions (e.g., `canCancel`, `canRefund`)
  - Export the resource object in the return statement
  - Update RBAC_CONFIGS if needed
  - Update documentation with new resource object

### Verification Steps

- [ ] **Test server-side protection**
  - Verify unauthorized users cannot access the route
  - Check proper redirects to login/unauthorized pages

- [ ] **Test client-side route protection**
  - Verify `useRouteProtection` redirects unauthorized users
  - Test direct URL navigation, browser back/forward, and programmatic navigation
  - Confirm redirects work without requiring browser refresh
  - Verify original URL is preserved when `preserveUrl: true`

- [ ] **Test client-side RBAC data**
  - Confirm RBAC data is available in `useRBAC()` hook
  - Verify permission-based UI rendering works correctly

- [ ] **Test conditional UI elements**
  - Create/Edit/Delete buttons show/hide based on permissions
  - Navigation elements respect user permissions
  - Form fields are disabled when user lacks update permissions

- [ ] **Add Permissions Information Sheet (for complex modules)**
  - Create or update permission configuration in `app/lib/utils/permissionConfigs.tsx`
  - Add permissions button to route header (protected by `system.view_permissions`)
  - Implement permissions sheet using reusable `PermissionsSheet` component
  - Test permissions sheet displays correct information for the module
  
  **Example Implementation (from products route):**
  ```typescript
  // Import required components
  import { Shield } from 'lucide-react';
  import { PermissionsSheet, getPermissionConfig } from '~/components/rbac/PermissionsSheet';
  
  // Add state for sheet visibility
  const [showPermissionsSheet, setShowPermissionsSheet] = useState(false);
  const { hasPermission } = useRBAC();
  
  // Add permissions button to header
  {hasPermission('system.view_permissions') && (
    <Button
      variant="outline"
      size="sm"
      onClick={() => setShowPermissionsSheet(true)}
      className="flex items-center gap-2"
    >
      <Shield className="h-4 w-4" />
      {t('permissions')}
    </Button>
  )}
  
  // Add PermissionsSheet component
  <PermissionsSheet
    open={showPermissionsSheet}
    onOpenChange={setShowPermissionsSheet}
    routeName="products"
    title={t('productsPermissions')}
    description={t('productsPermissionsDescription')}
    permissions={getPermissionConfig('products')?.permissions || []}
  />
  ```

## 🔧 Common Patterns & Best Practices

### File Naming Conventions

- [ ] **API Routes**: `api.{resource}.protected.tsx`
- [ ] **UI Routes**: `{resource}.tsx` or `{context}.{resource}.tsx`
- [ ] **Nested Routes**: `{parent}.$id.{resource}.tsx`

### RBAC Configuration

- [ ] **Use existing RBAC_CONFIGS when possible**
  ```typescript
  // Available configs in autoRBAC.server.ts
  RBAC_CONFIGS.categories
  RBAC_CONFIGS.inventory
  RBAC_CONFIGS.business
  RBAC_CONFIGS.cogs
  RBAC_CONFIGS.users
  RBAC_CONFIGS.roles
  ```

- [ ] **Create new config if needed**
  ```typescript
  // Add to RBAC_CONFIGS in autoRBAC.server.ts
  export const RBAC_CONFIGS = {
    // ... existing configs
    newResource: {
      resource: 'new_resource',
      // Optional: custom business ID extraction
      getBusinessId: (args) => args.params.businessId,
      // Optional: custom permission mapping
      customPermissions: {
        GET: ['new_resource.read'],
        POST: ['new_resource.create'],
      },
    } satisfies AutoRBACConfig,
  };
  ```

### Error Handling

- [ ] **Implement proper error boundaries**
- [ ] **Handle permission errors gracefully**
- [ ] **Provide meaningful error messages**
- [ ] **Log security-related errors appropriately**

### Performance Considerations

- [ ] **Minimize RBAC data size**
  - Only include necessary permissions in response
  - Use resource-specific permission objects from `useRBAC`

- [ ] **Cache RBAC checks when appropriate**
  - Leverage memoization in `useRBAC` hook
  - Avoid redundant permission checks

## 🧪 Testing Checklist

### Manual Testing

- [ ] **Test with different user roles**
  - Business Owner
  - Business Manager
  - Inventory Manager
  - Staff
  - Viewer

- [ ] **Test cross-business scenarios**
  - User accessing different business contexts
  - Unauthorized business access attempts

- [ ] **Test permission edge cases**
  - Users with no permissions
  - Users with partial permissions
  - Super admin access

### Automated Testing

- [ ] **Unit tests for permission logic**
- [ ] **Integration tests for route protection**
- [ ] **E2E tests for user workflows**

## 🚨 Security Checklist

### Server-Side Security

- [ ] **Never rely solely on client-side permission checks**
- [ ] **Always validate permissions on the server**
- [ ] **Sanitize and validate all inputs**
- [ ] **Use parameterized queries for database operations**

### Client-Side Security

- [ ] **Hide sensitive UI elements based on permissions**
- [ ] **Validate form submissions on both client and server**
- [ ] **Handle authentication state changes gracefully**

### Data Protection

- [ ] **Filter data based on user permissions**
- [ ] **Never expose sensitive data in API responses**
- [ ] **Log access attempts for audit purposes**

## 📚 Quick Reference

### Import Statements

```typescript
// Server-side RBAC
import { createProtectedRoute, createProtectedLoader } from '~/lib/middleware/autoRBAC.server';
import { RBAC_CONFIGS } from '~/lib/middleware/autoRBAC.server';
import { withRBAC } from '~/lib/utils/rbacLoader.server';
import { requirePermissions } from '~/lib/middleware/rbac.server';

// Client-side RBAC
import { useRBAC, Protected } from '~/lib/hooks/useRBAC';
```

### Available Resource Objects from useRBAC

The `useRBAC` hook returns the following resource-specific objects, each with `canRead`, `canCreate`, `canUpdate`, and `canDelete` properties:

- `categories` - Category management permissions
- `products` - Product management permissions  
- `ingredients` - Ingredient management permissions
- `inventory` - Inventory management permissions
- `business` - Business management permissions (includes `canManageUsers`)
- `users` - User management permissions (includes `canAssignRoles`)
- `rolesManagement` - Role management permissions
- `cogs` - COGS calculation permissions (includes `canCalculate`)

```typescript
// Example usage of resource objects
const rbac = useRBAC();

// Check specific resource permissions
if (rbac.categories.canCreate) {
  // Show create category button
}

if (rbac.business.canManageUsers) {
  // Show user management interface
}

if (rbac.cogs.canCalculate) {
  // Show COGS calculation features
}
```

### Common Permission Patterns

```typescript
// Resource-specific permissions
rbac.categories.canRead
rbac.categories.canCreate
rbac.categories.canUpdate
rbac.categories.canDelete

// Role-based checks
rbac.isBusinessOwner
rbac.isSuperAdmin
rbac.isBusinessManager

// Custom permission checks
rbac.hasPermission('custom.permission')
rbac.hasAnyPermission(['perm1', 'perm2'])
rbac.hasAllPermissions(['perm1', 'perm2'])
```

### Declarative Permission Components

```typescript
<Protected permissions={['resource.create']}>
  <CreateButton />
</Protected>

<Protected roles={['business_owner']} fallback={<AccessDenied />}>
  <AdminPanel />
</Protected>
```

## Adding New Resources to RBAC System:

When adding a new resource type to your application, you should update the `useRBAC` hook to include a resource-specific object:

1. **Add the resource object to useRBAC**: In `app/lib/hooks/useRBAC.ts`, create a new resource object following the existing pattern:

```typescript
// Example: Adding a new 'orders' resource
const orders = useMemo(
  () => ({
    canRead: canRead('orders'),
    canCreate: canCreate('orders'),
    canUpdate: canUpdate('orders'),
    canDelete: canDelete('orders'),
    // Add any resource-specific permissions
    canCancel: hasPermission('orders.cancel'),
    canRefund: hasPermission('orders.refund'),
  }),
  [canRead, canCreate, canUpdate, canDelete, hasPermission]
);
```

2. **Export the resource object**: Add it to the return statement of `useRBAC`:

```typescript
return {
  // ... existing exports
  orders, // Add your new resource
};
```

3. **Update RBAC_CONFIGS**: Ensure your new resource is defined in the RBAC configuration files.

4. **Update documentation**: Add the new resource to the "Available Resource Objects" section above.

## Common Mistakes to Avoid:

- **Forgetting `createProtectedRoute`**: Exposing API endpoints or sensitive UI routes without server-side protection.
- **Not using `withRBAC` for UI routes**: Client-side components will lack RBAC data, leading to incorrect UI rendering or functionality.
- **Misunderstanding `createRBACLoader`**: Using it when `createProtectedLoader` is required for security, or vice-versa.
- **Hardcoding permissions**: Always use the defined `RBAC_CONFIGS` or dynamically determine permissions where appropriate.
- **Not adding resource objects to useRBAC**: When adding new resources, forgetting to create the corresponding resource object in the `useRBAC` hook for convenient client-side access.

---

**Remember**: Security is paramount. When in doubt, err on the side of caution and implement stricter permission checks rather than more permissive ones.