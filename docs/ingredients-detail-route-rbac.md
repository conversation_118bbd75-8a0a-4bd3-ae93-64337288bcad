# Ingredients Detail Route RBAC Documentation

## Route Overview

**Route:** `/inventory/ingredients?detail={ingredientId}`

**Purpose:** Display detailed information about a specific ingredient, including its properties, usage data, and management actions.

## Required RBAC Permissions

### Primary Access Permission

- **`ingredients.read`** - Required for accessing the ingredients route and viewing ingredient details
  - This permission is enforced by the `createProtectedLoader(RBAC_CONFIGS.ingredients, ...)` configuration
  - Without this permission, users will receive a 403 Forbidden response

### Secondary Permissions (Optional with Graceful Degradation)

- **`categories.read`** - Required for displaying category information in the ingredient detail
  - If missing, category name and color will show as "-" instead of the actual values
  - Implemented through client-side `useRBAC()` hook with conditional rendering

- **`products.read`** - Required for displaying the "Product Usage" section
  - If missing, the entire Product Usage section will be hidden with a message "You don&apos;t have permission to view product usage data"
  - Implemented through client-side `useRBAC()` hook with conditional rendering

- **`cogs.read`** - Required for displaying the "Cost Analysis" section
  - If missing, the entire Cost Analysis section will be hidden with a message "You don&apos;t have permission to view cost analysis data"
  - Implemented through client-side `useRBAC()` hook with conditional rendering

### Action-Based Permissions

- **`ingredients.update`** - Required for editing ingredient details
  - Controls visibility and functionality of the "Edit" button in the detail sheet
  - Enforced through client-side `useRBAC()` hook

- **`ingredients.delete`** - Required for deleting ingredients
  - Controls visibility and functionality of the "Delete" button in the detail sheet
  - Enforced through client-side `useRBAC()` hook

- **`ingredients.create`** - Required for creating new ingredients
  - Controls visibility of the "Add Ingredient" button on the main page
  - Not directly related to detail view but part of the overall ingredients management

## RBAC Configuration

### Auto-RBAC Setup

The route uses the `RBAC_CONFIGS.ingredients` configuration:

```typescript
ingredients: {
  resource: 'ingredients',
  getBusinessId: getBusinessIdFromQuery,
  customPermissions: {
    read: ['ingredients.read'],
    create: ['ingredients.create'],
    update: ['ingredients.update'],
    delete: ['ingredients.delete'],
  },
}
```

### Permission Mapping

| Action | HTTP Method | Required Permission | Enforcement Level |
|--------|-------------|--------------------|-----------------|
| View Details | GET | `ingredients.read` | Server-side (Auto-RBAC) |
| View Categories | GET | `categories.read` | Client-side (Graceful degradation) |
| View Product Usage | GET | `products.read` | Client-side (Graceful degradation) |
| View Cost Analysis | GET | `cogs.read` | Client-side (Graceful degradation) |
| Edit Ingredient | POST/PUT | `ingredients.update` | Client-side + Server-side |
| Delete Ingredient | DELETE | `ingredients.delete` | Client-side + Server-side |
| Create Ingredient | POST | `ingredients.create` | Client-side + Server-side |

## API Endpoints Involved

### 1. Main Route Loader
- **Endpoint:** `/inventory/ingredients`
- **Method:** GET
- **Required Permission:** `ingredients.read`
- **Purpose:** Load ingredients list and categories data

### 2. Ingredient Usage Data
- **Endpoint:** `/api/ingredients/{id}/usage`
- **Method:** GET
- **Required Permission:** `ingredients.read` (via `RBAC_CONFIGS.ingredients`)
- **Purpose:** Fetch usage statistics for the selected ingredient

### 3. Ingredient CRUD Operations
- **Endpoint:** `/api/ingredients`
- **Methods:** POST, PUT, DELETE
- **Required Permissions:** 
  - POST: `ingredients.create`
  - PUT: `ingredients.update`
  - DELETE: `ingredients.delete`
- **Purpose:** Handle ingredient creation, updates, and deletion

## User Experience by Permission Level

### Full Access (`ingredients.read` + `categories.read` + `products.read` + `cogs.read` + `ingredients.update` + `ingredients.delete`)
- Can view all ingredient details including category information
- Can view product usage data
- Can view cost analysis data
- Can edit ingredient properties
- Can delete ingredients
- Can view ingredient usage statistics
- Full functionality available

### Read-Only Access (`ingredients.read` only)
- Can view ingredient details but category shows as "-"
- Product Usage section is hidden (requires `products.read`)
- Cost Analysis section is hidden (requires `cogs.read`)
- Cannot edit or delete ingredients
- Can view ingredient usage statistics
- Edit and Delete buttons are hidden

### Read with Categories (`ingredients.read` + `categories.read`)
- Can view all ingredient details including category information
- Product Usage section is hidden (requires `products.read`)
- Cost Analysis section is hidden (requires `cogs.read`)
- Cannot edit or delete ingredients
- Can view ingredient usage statistics
- Edit and Delete buttons are hidden

### Partial Access Examples

#### With Products but no COGS (`ingredients.read` + `categories.read` + `products.read`)
- Can view ingredient details with category information
- Can view product usage data
- Cost Analysis section is hidden (requires `cogs.read`)
- Cannot edit or delete ingredients

#### With COGS but no Products (`ingredients.read` + `categories.read` + `cogs.read`)
- Can view ingredient details with category information
- Product Usage section is hidden (requires `products.read`)
- Can view cost analysis data
- Cannot edit or delete ingredients

### No Access (missing `ingredients.read`)
- Cannot access the route at all
- Receives 403 Forbidden response
- Redirected to unauthorized page

## Implementation Details

### Server-Side Protection
- Uses `createProtectedLoader()` with `RBAC_CONFIGS.ingredients`
- Automatically checks `ingredients.read` permission
- Extracts business ID from query parameters
- Returns 403 error for unauthorized access

### Client-Side Permission Checks
- Uses `useRBAC()` hook for granular permission checking
- Implements conditional rendering for category information
- Controls visibility of action buttons (Edit/Delete)
- Provides graceful degradation for missing permissions

### URL State Management
- Detail view is controlled by `detail` query parameter
- Ingredient ID is passed as the parameter value
- State is managed through URL for bookmarkable links
- Sheet component handles opening/closing based on parameter presence

## Security Considerations

1. **Defense in Depth**: Both server-side and client-side permission checks
2. **Graceful Degradation**: Missing optional permissions don't break functionality
3. **Business Context**: All permissions are scoped to the specific business
4. **API Protection**: All related API endpoints are also protected with matching permissions
5. **URL Security**: Direct access to detail URLs still requires proper permissions

## Error Handling

- **401 Unauthorized**: User not authenticated
- **403 Forbidden**: User lacks required `ingredients.read` permission
- **404 Not Found**: Ingredient ID not found or not accessible
- **Graceful Degradation**: Missing optional permissions show placeholder content instead of errors

## Conclusion

The ingredients detail route implements a comprehensive RBAC system with:
- **Mandatory** `ingredients.read` permission for basic access
- **Optional** `categories.read` permission with graceful degradation for category display
- **Optional** `products.read` permission with graceful degradation for product usage section
- **Optional** `cogs.read` permission with graceful degradation for cost analysis section
- **Action-specific** permissions for edit/delete operations
- **Multi-layer** protection at both server and client levels
- **User-friendly** experience that adapts to available permissions

This approach ensures security while maintaining usability across different permission levels.