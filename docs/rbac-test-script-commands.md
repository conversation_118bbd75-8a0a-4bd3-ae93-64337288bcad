# RBAC Test User Role Assignment Script Commands

This document lists all possible commands for the RBAC test user role assignment script located at `scripts/assign-test-user-role.ts`.

## Overview

The script manages roles for the test user (`<EMAIL>`) to facilitate RBAC (Role-Based Access Control) testing. It supports assigning roles, removing roles (individually or all), and listing current roles.

## Available Roles

- `super_admin` - System-wide administrative access
- `business_owner` - Full control over a specific business
- `business_manager` - Management access to a specific business
- `inventory_manager` - Inventory management access for a specific business
- `viewer` - Read-only access to a specific business

## Available Permissions

Permissions follow the format `<module>.<action>`. Common permissions include:

### Inventory Permissions
- `inventory.read` - View inventory items
- `inventory.create` - Create new inventory items
- `inventory.update` - Update existing inventory items
- `inventory.delete` - Delete inventory items

### Category Permissions
- `categories.read` - View categories
- `categories.create` - Create new categories
- `categories.update` - Update existing categories
- `categories.delete` - Delete categories

### Business Permissions
- `business.read` - View business information
- `business.update` - Update business settings
- `business.delete` - Delete business

**Note**: The exact list of available permissions depends on your application's RBAC configuration. Use the permissions that are defined in your system's permission tables.

## Command Categories

### 1. Permission Assignment Commands

Assign specific permissions directly to the test user. This creates a temporary custom role with the specified permissions.

```bash
# Assign single permission (uses default business "KWACI Coffee House")
bun run scripts/assign-test-user-role.ts permissions inventory.read

# Assign multiple permissions (comma-separated, uses default business)
bun run scripts/assign-test-user-role.ts permissions inventory.read,inventory.create,inventory.update

# Assign permissions to a specific business
bun run scripts/assign-test-user-role.ts permissions inventory.read,categories.update "My Custom Business"

# Assign category-related permissions
bun run scripts/assign-test-user-role.ts permissions categories.read,categories.create,categories.update,categories.delete

# Assign mixed permissions across different modules
bun run scripts/assign-test-user-role.ts permissions inventory.read,categories.read,business.read
```

### 2. Role Assignment Commands

Assign roles to the test user for specific businesses or system-wide access.

```bash
# Assign business_owner role (uses default business "KWACI Coffee House")
bun run scripts/assign-test-user-role.ts business_owner

# Assign business_owner role to a specific business
bun run scripts/assign-test-user-role.ts business_owner "My Custom Business"

# Assign business_manager role (uses default business)
bun run scripts/assign-test-user-role.ts business_manager

# Assign business_manager role to a specific business
bun run scripts/assign-test-user-role.ts business_manager "Another Business"

# Assign inventory_manager role (uses default business)
bun run scripts/assign-test-user-role.ts inventory_manager

# Assign inventory_manager role to a specific business
bun run scripts/assign-test-user-role.ts inventory_manager "Inventory Business"

# Assign viewer role (uses default business)
bun run scripts/assign-test-user-role.ts viewer

# Assign viewer role to a specific business
bun run scripts/assign-test-user-role.ts viewer "View Only Business"

# Assign super_admin role (system-wide, no business context needed)
bun run scripts/assign-test-user-role.ts super_admin
```

### 3. Role Removal Commands

Remove roles from the test user, either individually or all at once.

```bash
# Remove ALL roles from the test user
bun run scripts/assign-test-user-role.ts remove

# Remove specific business_owner role (from default business)
bun run scripts/assign-test-user-role.ts remove business_owner

# Remove specific business_owner role from a specific business
bun run scripts/assign-test-user-role.ts remove business_owner "My Custom Business"

# Remove specific business_manager role (from default business)
bun run scripts/assign-test-user-role.ts remove business_manager

# Remove specific business_manager role from a specific business
bun run scripts/assign-test-user-role.ts remove business_manager "Another Business"

# Remove specific inventory_manager role (from default business)
bun run scripts/assign-test-user-role.ts remove inventory_manager

# Remove specific inventory_manager role from a specific business
bun run scripts/assign-test-user-role.ts remove inventory_manager "Inventory Business"

# Remove specific viewer role (from default business)
bun run scripts/assign-test-user-role.ts remove viewer

# Remove specific viewer role from a specific business
bun run scripts/assign-test-user-role.ts remove viewer "View Only Business"

# Remove super_admin role (system-wide)
bun run scripts/assign-test-user-role.ts remove super_admin

# Remove custom roles created by permission assignments
# The script automatically detects the business context, no need to specify business name
bun run scripts/assign-test-user-role.ts remove test_custom_1234567890
bun run scripts/assign-test-user-role.ts remove temp_permissions_1234567890
```

### 4. Permission Removal Commands

Remove permission-based assignments (temporary custom roles created for permissions).

```bash
# Remove all permission-based assignments system-wide
bun run scripts/assign-test-user-role.ts remove-permissions

# Remove permission-based assignments for a specific business
bun run scripts/assign-test-user-role.ts remove-permissions "My Custom Business"

# Remove permission-based assignments for default business
bun run scripts/assign-test-user-role.ts remove-permissions "KWACI Coffee House"
```

### 5. Information Commands

List current roles and get help information.

```bash
# List all current roles assigned to the test user (basic view)
bun run scripts/assign-test-user-role.ts list

# List roles and permissions separately with detailed breakdown
bun run scripts/assign-test-user-role.ts list-detailed

# Show help and usage information
bun run scripts/assign-test-user-role.ts help
bun run scripts/assign-test-user-role.ts --help

# Show usage when no arguments provided
bun run scripts/assign-test-user-role.ts
```

## Command Syntax Patterns

### General Syntax
```bash
bun run scripts/assign-test-user-role.ts <command> [role_name] [business_name]
```

### Permission Assignment Pattern
```bash
bun run scripts/assign-test-user-role.ts permissions <permission1,permission2,...> [business_name]
```

### Role Assignment Pattern
```bash
bun run scripts/assign-test-user-role.ts <role_name> [business_name]
```

### Removal Pattern
```bash
bun run scripts/assign-test-user-role.ts remove [role_name] [business_name]
```

### Permission Removal Pattern
```bash
bun run scripts/assign-test-user-role.ts remove-permissions [business_name]
```

### Information Pattern
```bash
bun run scripts/assign-test-user-role.ts <list|list-detailed|help|--help>
```

## Important Notes

1. **Default Business**: If no business name is provided, "KWACI Coffee House" is used as the default business.

2. **Super Admin Role**: The `super_admin` role is system-wide and doesn't require a business context. Business names are ignored for this role.

3. **Test User**: The script operates on the test user with email `<EMAIL>`. This user must exist in the database.

4. **Business Names**: Business names with spaces should be enclosed in quotes.

5. **Case Sensitivity**: Role names are case-sensitive and must match exactly.

6. **Permission Assignment**: When using the `permissions` command, the script creates a temporary custom role with the specified permissions and assigns it to the test user. This allows testing specific permission combinations without predefined roles.

7. **Custom Role Naming**: Custom roles created for permission testing are named with the format `temp_permissions_<timestamp>` to avoid conflicts.

8. **Custom Role Removal**: You can remove individual custom roles using the `remove` command with the exact role name (e.g., `test_custom_1234567890` or `temp_permissions_1234567890`). The script automatically detects the business context from existing role assignments, so you don't need to specify the business name when removing custom roles. Use `list-detailed` to see the exact names of custom roles assigned to the test user.

9. **Permission Removal**: The `remove-permissions` command specifically targets and removes temporary custom roles created for permission testing. It also cleans up the role definitions and permission mappings.

10. **Detailed Listing**: The `list-detailed` command provides a comprehensive view showing both regular role assignments and temporary permission-based assignments, distinguishing between system-wide and business-specific assignments.

11. **Permission Validation**: The script validates that specified permissions exist in the system before creating custom roles. Invalid permissions will cause the script to exit with an error.

12. **Error Handling**: The script will exit with an error if:
    - The test user doesn't exist
    - An invalid role name is provided (for predefined roles)
    - A custom role doesn't exist (for custom role removal)
    - A specified business doesn't exist
    - No permissions are provided for the permissions command
    - Invalid permissions are specified that don't exist in the system

## Complete Command Reference

| Command Type | Syntax | Description |
|--------------|--------|-------------|
| Assign Permissions | `permissions <perm1,perm2,...>` | Assign permissions to default business |
| Assign Permissions | `permissions <perm1,perm2,...> "<business_name>"` | Assign permissions to specific business |
| Assign Role | `<role_name>` | Assign role to default business |
| Assign Role | `<role_name> "<business_name>"` | Assign role to specific business |
| Remove All | `remove` | Remove all roles from test user |
| Remove Specific | `remove <role_name>` | Remove role from default business |
| Remove Specific | `remove <role_name> "<business_name>"` | Remove role from specific business |
| List Roles | `list` | Show all current roles |
| Help | `help` or `--help` | Show usage information |

## Examples by Use Case

### Testing Specific Permissions
```bash
# Test inventory read access only
bun run scripts/assign-test-user-role.ts permissions inventory.read

# Test full inventory management
bun run scripts/assign-test-user-role.ts permissions inventory.read,inventory.create,inventory.update,inventory.delete

# Test category management with inventory read
bun run scripts/assign-test-user-role.ts permissions categories.read,categories.create,categories.update,categories.delete,inventory.read

# Clean up (removes all roles including custom permission roles)
bun run scripts/assign-test-user-role.ts remove
```

### Testing Business Owner Access
```bash
# Grant full business access
bun run scripts/assign-test-user-role.ts business_owner

# Test and then remove
bun run scripts/assign-test-user-role.ts remove business_owner
```

### Testing Multi-Business Scenarios
```bash
# Assign roles to different businesses
bun run scripts/assign-test-user-role.ts business_manager "Coffee Shop A"
bun run scripts/assign-test-user-role.ts viewer "Coffee Shop B"

# List to verify
bun run scripts/assign-test-user-role.ts list

# Clean up specific business
bun run scripts/assign-test-user-role.ts remove business_manager "Coffee Shop A"
```

### Testing System Admin Access
```bash
# Grant system-wide access
bun run scripts/assign-test-user-role.ts super_admin

# Remove system access
bun run scripts/assign-test-user-role.ts remove super_admin
```

### Complete Reset
```bash
# Remove all roles and start fresh
bun run scripts/assign-test-user-role.ts remove
```