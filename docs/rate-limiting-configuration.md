# 🚦 Rate Limiting Configuration

Complete guide to Better Auth rate limiting configuration for development and production environments.

## 🔍 Understanding the 429 Error

### **What is Rate Limiting?**
Rate limiting protects your authentication endpoints from:
- **Brute force attacks** (password guessing)
- **Account enumeration** (checking if emails exist)
- **Denial of service** (overwhelming the server)
- **Automated abuse** (bot attacks)

### **The 429 Error Explained**
```
POST http://localhost:5173/api/auth/sign-in/email 429 (Too Many Requests)
```

This means:
- ❌ **Rate limit exceeded** for the sign-in endpoint
- 🕐 **Too many attempts** in the configured time window
- 🔒 **Better Auth protection** is working as designed
- ⏰ **Wait period required** before next attempt

## ⚙️ Current Configuration

### **Environment-Aware Setup**
Our Better Auth configuration automatically adjusts based on environment:

#### **🔒 Production (Strict Security)**
```typescript
// Production rate limits (NODE_ENV=production)
rateLimit: {
  enabled: true,
  storage: "secondary-storage", // Redis
  window: 60, // 1 minute
  max: 100,   // 100 requests per minute (default)
  
  customRules: {
    "/sign-in/email": {
      window: 15 * 60, // 15 minutes
      max: 5,          // 5 attempts per 15 minutes
    },
    "/sign-up/email": {
      window: 60 * 60, // 1 hour
      max: 3,          // 3 registrations per hour
    },
    // ... other endpoints
  }
}
```

#### **🔧 Development (Relaxed for Testing)**
```typescript
// Development rate limits (NODE_ENV=development)
rateLimit: {
  enabled: true,
  storage: "secondary-storage", // Redis
  window: 60,  // 1 minute
  max: 1000,   // 1000 requests per minute (very generous)
  
  customRules: {
    "/sign-in/email": {
      window: 60, // 1 minute
      max: 100,   // 100 attempts per minute
    },
    "/sign-up/email": {
      window: 60, // 1 minute
      max: 50,    // 50 registrations per minute
    },
    // ... other endpoints
  }
}
```

## 🛠️ Development Tools

### **Clear Rate Limits**
```bash
# Clear all rate limits (development only)
bun run dev:clear-rate-limits

# Check current rate limit status
bun run dev:rate-limit-status
```

### **Environment Detection**
The system automatically detects your environment:
- **Development**: `NODE_ENV !== 'production'`
- **Production**: `NODE_ENV === 'production'`

### **Startup Logging**
When you start the server, you'll see:

**Development:**
```
🔧 Better Auth: Development mode - Relaxed rate limiting enabled
   📊 Sign-in: 100 attempts per minute
   🚀 Use "bun run dev:clear-rate-limits" to reset limits if needed
```

**Production:**
```
🔒 Better Auth: Production mode - Strict rate limiting enabled
   📊 Sign-in: 5 attempts per 15 minutes
```

## 🚨 Troubleshooting Rate Limits

### **1. Quick Fix - Clear Rate Limits**
```bash
# Immediate solution for development
bun run dev:clear-rate-limits
```

### **2. Check Current Status**
```bash
# See what rate limits are active
bun run dev:rate-limit-status
```

### **3. Restart Development Server**
```bash
# Kill and restart to reset environment
Ctrl+C
bun run dev
```

### **4. Verify Environment**
```bash
# Check your NODE_ENV
echo $NODE_ENV

# Should be empty or 'development' for dev mode
# Should be 'production' for production mode
```

## 🔧 Manual Configuration

### **Temporarily Disable Rate Limiting**
If you need to completely disable rate limiting for testing:

```typescript
// In app/lib/auth.server.ts (DEVELOPMENT ONLY!)
rateLimit: {
  enabled: false, // Completely disable rate limiting
},
```

⚠️ **Warning**: Never disable rate limiting in production!

### **Custom Rate Limits for Testing**
```typescript
// Custom limits for specific testing scenarios
rateLimit: {
  enabled: true,
  storage: "secondary-storage",
  window: 10, // 10 seconds
  max: 1000,  // Very high limit
  
  customRules: {
    "/sign-in/email": {
      window: 10, // 10 seconds
      max: 1000,  // Unlimited for testing
    },
  }
}
```

## 📊 Rate Limit Monitoring

### **Redis Keys**
Rate limits are stored in Redis with keys like:
```
rate_limit:sign-in/email:*************
rate_limit:sign-up/email:<EMAIL>
```

### **Manual Redis Inspection**
```bash
# Connect to Redis CLI
redis-cli

# List all rate limit keys
KEYS rate_limit:*

# Check specific rate limit
GET rate_limit:sign-in/email:*************

# Check TTL (time to live)
TTL rate_limit:sign-in/email:*************

# Delete specific rate limit
DEL rate_limit:sign-in/email:*************
```

## 🔄 Common Workflows

### **Daily Development**
1. Start development: `bun run dev`
2. Test login with seeded users
3. If you hit rate limits: `bun run dev:clear-rate-limits`
4. Continue testing

### **Intensive Testing**
1. Clear rate limits: `bun run dev:clear-rate-limits`
2. Run your test suite
3. Monitor status: `bun run dev:rate-limit-status`
4. Clear again if needed

### **Production Deployment**
1. Set `NODE_ENV=production`
2. Verify strict rate limits are active
3. Monitor authentication attempts
4. Never clear rate limits in production

## 🛡️ Security Best Practices

### **Development**
- ✅ **Use relaxed limits** for testing
- ✅ **Clear limits when needed** for development
- ✅ **Monitor rate limit status** during testing
- ❌ **Never disable completely** (keep some protection)

### **Production**
- ✅ **Use strict limits** for security
- ✅ **Monitor failed attempts** for attacks
- ✅ **Log rate limit violations** for analysis
- ❌ **Never clear rate limits** in production
- ❌ **Never disable rate limiting** in production

## 🔍 Debugging Rate Limit Issues

### **Check Environment**
```bash
# Verify your environment setting
node -e "console.log('NODE_ENV:', process.env.NODE_ENV)"
```

### **Check Redis Connection**
```bash
# Test Redis connectivity
redis-cli ping
# Should return: PONG
```

### **Check Rate Limit Configuration**
Look for startup logs when running `bun run dev`:
- Should show development mode with relaxed limits
- Should show the correct rate limit values

### **Manual Rate Limit Reset**
```bash
# Clear specific endpoint rate limits
redis-cli DEL rate_limit:sign-in/email:*

# Clear all rate limits
redis-cli EVAL "return redis.call('del', unpack(redis.call('keys', 'rate_limit:*')))" 0
```

## 📈 Performance Impact

### **Development**
- **Minimal impact** with relaxed limits
- **Redis overhead** is negligible for testing
- **Fast reset** with clear commands

### **Production**
- **Essential security** protection
- **Minimal performance** impact with Redis
- **Prevents abuse** and attacks

## 🎯 Testing Scenarios

### **Authentication Flow Testing**
1. Clear rate limits: `bun run dev:clear-rate-limits`
2. Test multiple rapid logins
3. Test with different users
4. Test invalid credentials
5. Verify no rate limit blocks

### **Rate Limit Testing**
1. Set very low limits temporarily
2. Test rate limit triggers
3. Verify 429 responses
4. Test rate limit reset
5. Restore normal limits

---

## 📚 Related Documentation

- [Quick Start Login Guide](./QUICK-START-LOGIN-GUIDE.md) - Testing with seeded users
- [Authentication Troubleshooting](./authentication-troubleshooting.md) - Common auth issues
- [Development Setup](./development-authentication-setup.md) - Complete setup guide

---

**Rate limiting is now optimized for development testing while maintaining production security! 🚀**
