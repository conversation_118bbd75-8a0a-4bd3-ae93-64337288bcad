# RBAC System & User Invitation Guide

## Table of Contents
- [Overview](#overview)
- [Quick Start Guide](#quick-start-guide)
- [Test User Accounts](#test-user-accounts)
- [Role Assignment Workflows](#role-assignment-workflows)
- [User Invitation System](#user-invitation-system)
- [Cross-Business Management](#cross-business-management)
- [Permission Matrix](#permission-matrix)
- [Notification System](#notification-system)
- [Troubleshooting](#troubleshooting)

## Overview

The KWACI Grow application features a comprehensive Role-Based Access Control (RBAC) system that enables:

- **Multi-business user management** across different coffee shop locations
- **Cross-business role assignment** for administrators managing multiple businesses
- **User invitation system** with email-based invitations and acceptance workflow
- **Real-time notifications** for pending invitations
- **Granular permissions** controlling access to specific features and data

### Key Features
- ✅ **Cross-business role assignment** - Assign users to multiple businesses
- ✅ **User invitation workflow** - Invite new users via email with role pre-assignment
- ✅ **Notification system** - Centralized invitation management at `/notifications`
- ✅ **Permission-based access** - Granular control over user capabilities
- ✅ **Real-time updates** - Notification badges and live permission checking

## Quick Start Guide

### 1. <PERSON><PERSON> as Administrator
Use the test administrator account to start managing users:

```
📧 Email: <EMAIL>
🔒 Password: password123
👤 Role: Business Manager (all businesses) + Business Owner (KWACI Coffee House)
```

### 2. Access RBAC Management
Navigate to any business RBAC settings:
```
🌐 URL: /business/{businessId}/settings/rbac
📍 Example: /business/85cd9e07-7084-496e-880d-************/settings/rbac
```

### 3. Start Assigning Roles
1. **For existing users**: Use the "Assign Roles" section
2. **For new users**: Use the "Invite New User" section

## Test User Accounts

### Primary Test Account
| Email | Password | Access Level | Businesses | Capabilities |
|-------|----------|--------------|------------|--------------|
| `<EMAIL>` | `password123` | **Multi-Business Admin** | All 3 businesses | ✅ Can invite users<br/>✅ Can assign roles<br/>✅ Cross-business management |

**Current Business Access:**
- **KWACI Coffee House** - Business Owner + Business Manager
- **Brew & Bean Cafe** - Business Manager  
- **Morning Glory Coffee** - Business Manager

### Business Owner Accounts
| Email | Password | Business | Role | Capabilities |
|-------|----------|----------|------|--------------|
| `<EMAIL>` | `password123` | KWACI Coffee House | Business Owner | ✅ Full business control<br/>✅ Can invite users |
| `<EMAIL>` | `password123` | Brew & Bean Cafe | Business Owner | ✅ Full business control<br/>✅ Can invite users |
| `<EMAIL>` | `password123` | Morning Glory Coffee | Business Owner | ✅ Full business control<br/>✅ Can invite users |

### Staff Accounts
| Email | Password | Business | Role | Capabilities |
|-------|----------|----------|------|--------------|
| `<EMAIL>` | `password123` | KWACI Coffee House | Staff Member | ❌ Cannot invite users<br/>✅ Basic business access |
| `<EMAIL>` | `password123` | Brew & Bean Cafe | Staff Member | ❌ Cannot invite users<br/>✅ Basic business access |

## Role Assignment Workflows

### Assigning Roles to Existing Users

#### Single Business Assignment
1. **Navigate** to RBAC settings for target business
2. **Select user** from the "Select User" dropdown
3. **Choose role** from available roles
4. **Click "Assign Role"** to complete assignment

#### Cross-Business Assignment
1. **Login** as user with multi-business access (e.g., `<EMAIL>`)
2. **Navigate** to any business RBAC settings
3. **Select target business** from "Target Business" dropdown
4. **Select user** from filtered user list for that business
5. **Choose role** and **assign**

### Business Selector Visibility

The "Target Business" dropdown appears when:
- ✅ User has access to **2 or more businesses**
- ✅ User has role management permissions in multiple businesses

**If dropdown is missing:**
- User only has access to 1 business
- Assign user to additional businesses to enable cross-business management

## User Invitation System

### Creating Invitations

#### Step-by-Step Process
1. **Access invitation form** in RBAC settings
2. **Select target business** (if managing multiple businesses)
3. **Enter email address** of user to invite
4. **Select initial role** for the invited user
5. **Click "Send Invitation"** to create invitation

#### Invitation Details
- **Expiration**: 7 days from creation
- **Token-based**: Unique secure invitation links
- **Role pre-assignment**: User gets specified role upon acceptance
- **Cross-business**: Can invite to any business you manage

### Invitation Acceptance Workflow

#### Method 1: Direct Invitation Link
```
🔗 Format: /invite/{unique-token}
📧 Sent via email (future enhancement)
✅ One-click acceptance process
```

#### Method 2: Notifications Page
```
🌐 URL: /notifications
📱 Accessible via notification badge in header
📋 Centralized invitation management
```

### Invitation States
- **Pending** - Awaiting user acceptance
- **Accepted** - User accepted and role assigned
- **Expired** - Invitation expired (7 days)
- **Cancelled** - Manually cancelled by inviter

## Cross-Business Management

### Prerequisites for Cross-Business Access
Users need **one of the following** in each target business:
- ✅ `business_owner` role
- ✅ `business.manage_users` permission
- ✅ `users.assign_roles` permission

### Cross-Business Capabilities
- **Role assignment** across multiple businesses
- **User invitation** to any accessible business
- **Permission management** with business context
- **Unified interface** for multi-business administration

### Business Access Levels
1. **Single Business** - Standard business-specific access
2. **Multi-Business Manager** - Can manage users across multiple businesses
3. **System Administrator** - Full access to all businesses (business owners + managers)

## Permission Matrix

### Role Hierarchy
```
Business Owner > Business Manager > Staff Member
```

### Detailed Permissions

#### Business Owner
- ✅ **Full business control** - All permissions in owned business
- ✅ **User invitation** - Can invite users with any role
- ✅ **Role assignment** - Can assign/remove any role
- ✅ **Cross-business** - If owner of multiple businesses
- ✅ **RBAC management** - Full access to role management interface

#### Business Manager
- ✅ **User invitation** - Can invite users (staff, manager roles)
- ✅ **Role assignment** - Can assign roles they have permission for
- ✅ **Business operations** - Inventory, products, categories, COGS
- ✅ **User management** - Read user information
- ❌ **Business ownership** - Cannot transfer ownership or delete business

#### Staff Member
- ✅ **Basic business access** - Read-only access to business data
- ✅ **Inventory operations** - Can view and manage inventory
- ❌ **User invitation** - Cannot invite new users
- ❌ **Role assignment** - Cannot assign roles to others
- ❌ **RBAC management** - Cannot access role management interface

### Permission Requirements for Key Actions

#### User Invitation
**Required:** ONE of the following in target business
- `business_owner` role
- `business.manage_users` permission  
- `users.assign_roles` permission

#### Role Assignment
**Required:** ONE of the following in target business
- `business_owner` role
- `users.assign_roles` permission

#### RBAC Interface Access
**Required:** ONE of the following in target business
- `business_owner` role
- `business.manage_users` permission
- `users.assign_roles` permission

## Notification System

### Accessing Notifications
- **Header Badge** - Shows count of pending invitations
- **Sidebar Link** - "Notifications" under Settings section
- **Direct URL** - `/notifications`

### Notification Features
- ✅ **Real-time count** - Badge updates automatically
- ✅ **Invitation details** - Business name, role, inviter, expiration
- ✅ **Urgency indicators** - Visual warnings for expiring invitations
- ✅ **Bulk actions** - Accept/decline multiple invitations
- ✅ **Empty state** - Friendly message when no pending invitations

### Notification Badge Behavior
- **Hidden** - When user has no pending invitations
- **Number** - Shows count of pending invitations (1-99)
- **99+** - For users with more than 99 pending invitations
- **Auto-refresh** - Updates every 5 minutes

### Invitation Card Information
Each invitation displays:
- 🏢 **Business name** - Which business you're invited to
- 👤 **Role offered** - What role you'll receive
- 👥 **Invited by** - Who sent the invitation
- ⏰ **Expiration date** - When invitation expires
- 🚨 **Urgency indicator** - Color-coded expiration warnings

## Troubleshooting

### Common Permission Errors

#### "You do not have permission to invite users to the selected business"

**Cause:** User lacks invitation permissions in target business

**Solutions:**
1. **Check user roles** in target business
2. **Assign business_manager role** to user for that business
3. **Verify business_manager role** has required permissions:
   - `business.manage_users`
   - `users.assign_roles`

**Quick Fix:**
```bash
bun run scripts/assign-test-user-role.ts business_manager "Business Name"
```

#### "Business selector dropdown not visible"

**Cause:** User only has access to 1 business

**Solution:** Assign user to additional businesses
```bash
bun run scripts/assign-test-user-role.ts business_manager "Second Business"
bun run scripts/assign-test-user-role.ts business_manager "Third Business"
```

#### "Invitation acceptance button stuck on 'Accepting...'"

**Cause:** Frontend state management issue (fixed in latest version)

**Solution:** 
1. **Refresh page** to reset state
2. **Check browser console** for JavaScript errors
3. **Verify invitation is valid** and not expired

### Permission Debugging

#### Check User Permissions
```bash
# Run diagnostic script
bun run diagnose-invitation-permissions.ts
```

#### Verify Role Assignments
```bash
# Check accessible businesses
bun run check-accessible-businesses.ts
```

#### Fix Missing Permissions
```bash
# Re-seed RBAC permissions
bun run app/lib/db/rbac-seed.ts
```

### Database Issues

#### Reset Test User Permissions
```bash
# Assign comprehensive permissions
bun run scripts/assign-test-user-role.ts business_owner "KWACI Coffee House"
bun run scripts/assign-test-user-role.ts business_manager "Brew & Bean Cafe"
bun run scripts/assign-test-user-role.ts business_manager "Morning Glory Coffee"
```

#### Clean Up Expired Invitations
```typescript
// Run cleanup (future enhancement)
await InvitationService.cleanupExpiredInvitations();
```

---

## Quick Reference

### Key URLs
- **RBAC Management**: `/business/{businessId}/settings/rbac`
- **Notifications**: `/notifications`
- **Direct Invitation**: `/invite/{token}`

### Test Commands
```bash
# Assign roles
bun run scripts/assign-test-user-role.ts [role] [business]

# Debug permissions  
bun run diagnose-invitation-permissions.ts

# Check business access
bun run check-accessible-businesses.ts

# Re-seed RBAC
bun run app/lib/db/rbac-seed.ts
```

### Support
For additional help or to report issues with the RBAC system, refer to the development team or check the application logs for detailed error information.
