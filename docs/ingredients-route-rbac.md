# Ingredients Route RBAC Documentation

## Route Overview
**Route:** `/inventory/ingredients`  
**URL:** `http://localhost:5173/inventory/ingredients`  
**File:** `app/routes/inventory.ingredients.tsx`

## RBAC Implementation

### 1. Server-Side Protection (Loader)

The ingredients route uses **Protected Loader** via the `createProtectedLoader` function with automatic RBAC permission enforcement:

```typescript
// From app/routes/inventory.ingredients.tsx
export const loader = createProtectedLoader(
  RBAC_CONFIGS.ingredients,
  async ({ request }) => {
    // Loader implementation with automatic permission checks
    // Requires ingredients.read permission for access
  }
);
```

### 2. RBAC Configuration

The route uses the pre-configured `RBAC_CONFIGS.ingredients` from `autoRBAC.server.ts`:

```typescript
ingredients: {
  resource: 'ingredients',
  getBusinessId: getBusinessIdFromQuery,
  customPermissions: {
    read: ['ingredients.read'],
    create: ['ingredients.create'],
    update: ['ingredients.update'],
    delete: ['ingredients.delete'],
  },
}
```

### 3. Required Permissions

#### For Route Access (GET requests):
- **Required Permission:** `ingredients.read` (enforced by `createProtectedLoader` with `RBAC_CONFIGS.ingredients`)
- **Authentication:** User must be logged in (automatically enforced by protected loader)
- **Optional Permission:** `categories.read` (for loading ingredient categories and displaying category column)
- **Business Context:** Automatically determined from user's default business
- **Graceful Degradation:** Category features implement graceful degradation when `categories.read` permission is missing
- **Category Loading:** User needs `categories.read` permission; without it, route continues with empty categories array
- **Category Column Display:** The ingredients table's category column requires `categories.read` permission to display category names and colors properly

#### For CRUD Operations:
- **Create Ingredients:** `ingredients.create`
- **Read Ingredients:** `ingredients.read`
- **Update Ingredients:** `ingredients.update`
- **Delete Ingredients:** `ingredients.delete`

### 4. Permission Mapping

The route maps HTTP methods and form actions to permissions:

| HTTP Method | Form Action | Required Permission |
|-------------|-------------|--------------------|
| GET | - | `ingredients.read` |
| POST | `create` | `ingredients.create` |
| POST | `update` | `ingredients.update` |
| POST | `delete` | `ingredients.delete` |
| PUT/PATCH | - | `ingredients.update` |
| DELETE | - | `ingredients.delete` |

### 5. Client-Side Permission Checks

The component uses the `useRBAC` hook for client-side permission validation:

```typescript
const { ingredients: ingredientsPermissions } = useRBAC();

// Available permission checks:
// ingredientsPermissions.canRead
// ingredientsPermissions.canCreate
// ingredientsPermissions.canUpdate
// ingredientsPermissions.canDelete
```

### 6. Business Context

The route determines business context automatically:
- **Method:** `BusinessServiceServer.getDefaultByUser()`
- **Source:** User's default business from database
- **Fallback:** Throws 404 error if no business found

### 7. Access Control Flow

1. **Permission Check:** User must have `ingredients.read` permission (enforced by `createProtectedLoader`)
2. **Authentication Check:** User must be logged in (automatically enforced by protected loader)
3. **Business Context:** Get user's default business automatically
4. **Data Loading:** Load ingredients for the business
5. **Permission-Based Features:** Load categories only if user has `categories.read` permission
6. **Error Handling:** Redirect to `/unauthorized` on permission errors, graceful degradation for feature-specific permissions

### 8. Error Handling



#### Enhanced Error Handling Features:

- **Graceful Category Loading:** If user lacks `categories.read` permission, the route continues without categories instead of failing
- **Proper Error UI:** Permission errors redirect to `/unauthorized` page instead of showing blank "500" error
- **Defensive Programming:** Client-side components handle undefined data gracefully with default values
- **Layered Permission Checks:** Both automated RBAC and service-level permission checks are handled appropriately
- **Category Column Handling:** The ingredients table gracefully handles missing category data when `categories.read` permission is denied

### 8.1. Category Column RBAC Implementation

The ingredients table displays category information in a dedicated column that requires `categories.read` permission:

#### Category Column Features:
- **Category Name Display:** Shows the ingredient's category name as a badge
- **Category Color Indicator:** Displays a colored dot representing the category color
- **Fallback Display:** Shows "-" when category information is unavailable

#### Permission Requirements:
- **Required Permission:** `categories.read`
- **Fallback Behavior:** When permission is denied, `ingredient.categoryName` and `ingredient.categoryColor` will be null/undefined
- **UI Handling:** The table gracefully displays "-" for missing category information

#### Implementation Notes:
- Category data is loaded via `CategoryServiceServer.getHierarchicalCategories()` in the loader
- If `categories.read` permission is denied, the loader continues with empty categories array
- **Current Implementation Gap:** Individual ingredient records still contain category information (`categoryName`, `categoryColor`) from database LEFT JOIN, regardless of `categories.read` permission
- **Recommended Enhancement:** The `IngredientServiceServer.getAllByBusinessWithCategories()` method should respect `categories.read` permission and exclude category data when permission is denied
- The category filter functionality will be limited when categories are not loaded due to permission restrictions

#### Current Implementation Status:
✅ **Full RBAC Compliance:** The ingredients table now properly checks for `categories.read` permission before displaying category information. Users without this permission will see "-" in the category column instead of category names and colors.

#### Suggested Implementation Enhancement:
```typescript
// In IngredientServiceServer.getAllByBusinessWithCategories()
// Check if user has categories.read permission before including category data
const hasCategoryPermission = await RBACService.hasPermission(
  userId,
  'categories.read',
  businessId
);

const selectFields = {
  // ... ingredient fields
  categoryName: hasCategoryPermission ? categories.name : sql`NULL`,
  categoryColor: hasCategoryPermission ? categories.color : sql`NULL`,
};
```

### 9. Related API Routes

The ingredients route works with these protected API endpoints:

#### `/api/ingredients`
- **File:** `app/routes/api.ingredients.tsx`
- **Protection:** Uses `createProtectedRoute` with `RBAC_CONFIGS.ingredients`
- **Permissions:** Same as main route (`ingredients.*`)

#### `/api/ingredients/check-name`
- **File:** `app/routes/api.ingredients.check-name.tsx`
- **Protection:** Manual authentication check (no automated RBAC)
- **Requirements:** User must be logged in

### 10. Permission Hierarchy

Users can access ingredients through these roles:

#### System Roles:
- **`super_admin`:** Global access to all businesses
- **`business_owner`:** Full access to owned businesses

#### Business-Specific Roles:
- **`business_manager`:** Typically has all inventory permissions
- **`inventory_manager`:** Specialized role for inventory management
- **`staff`:** Limited permissions based on role configuration
- **`viewer`:** Read-only access

### 11. Permission Sources

Permissions are granted through:
1. **Direct Role Assignment:** User assigned to role with inventory permissions
2. **Business Ownership:** Business owners inherit all permissions
3. **Custom Role Permissions:** Custom roles with specific inventory permissions

### 12. Security Features

- **Automatic Protection:** No manual permission checks needed in route handlers
- **Business Isolation:** Users can only access ingredients from businesses they have access to
- **Method-Specific Permissions:** Different permissions for read vs. write operations
- **Consistent API Protection:** Both UI routes and API endpoints use same RBAC logic
- **Graceful Degradation:** Route continues with limited functionality when optional permissions are missing
- **Enhanced Error Handling:** Proper error boundaries and user-friendly error pages
- **Defensive Client Code:** Components handle undefined/null data safely with default values

### 13. Usage Examples

#### Accessing the Route:
```
GET /inventory/ingredients
```
**Required:** User must be authenticated (business context determined automatically)

#### Creating an Ingredient:
```
POST /api/ingredients
Form Data: { _action: "create", businessId: "123", ... }
```
**Required:** User must have `ingredients.create` permission for business `123`

### 14. Configuration Notes

- **Resource Name:** `ingredients`
- **Permission Prefix:** `ingredients`
- **Business Context:** Always required
- **Skip Methods:** None (all methods are protected)
- **Custom Permissions:** Maps to ingredients permissions

### 15. Implementation Details

#### Enhanced Error Handling Implementation:

```typescript
// Graceful category loading with permission error handling
try {
  categories = await CategoryServiceServer.getHierarchicalCategories(
    business.id,
    session.user.id,
    'ingredient'
  );
} catch (categoryError: unknown) {
  // Check if it's a permission error
  if (categoryError instanceof Error && 
      (categoryError.message?.includes('Access denied') || 
       categoryError.message?.includes('insufficient permissions'))) {
    console.warn('User does not have permission to read categories, continuing without categories');
    categories = [];
  } else {
    // Re-throw other errors
    throw categoryError;
  }
}
```

#### Client-Side Safety Checks:

```typescript
// Defensive data handling
const loaderData = useLoaderData<LoaderData>();
const { 
  ingredients = [], 
  businessId = '', 
  businessName = '', 
  categories: allCategories = [] 
} = loaderData || {};

// Safe array operations
const selectedIngredient = useMemo(() =>
  ingredients?.find(ing => ing.id === detailIngredientId) || null,
  [ingredients, detailIngredientId]
);

// Safe filtering with array validation
const filteredIngredients = useMemo(() => {
  if (!ingredients || !Array.isArray(ingredients)) return [];
  return ingredients.filter(ingredient => {
    // filtering logic...
  });
}, [ingredients, searchTerm, selectedCategoryId]);
```

## Summary

The `/inventory/ingredients` route implements authentication-based protection with permission-based feature access:

1. **Permission Protection**: `createProtectedLoader` with `RBAC_CONFIGS.ingredients` enforces `ingredients.read` permission
2. **Authentication Protection**: Automatic authentication enforcement through protected loader
3. **Category Access**: Separate `categories.read` permission check with graceful degradation
4. **Client-side Validation**: `useRBAC` hook for UI permission checks
5. **Business Context**: Automatic business determination from user's default business
6. **Enhanced Error Handling**: Graceful degradation for category permission issues
7. **Defensive Programming**: Client-side safety checks for undefined data
8. **✅ Category Column Protection**: Category column properly respects `categories.read` permission

### Permission Requirements
- **Mandatory**: `ingredients.read` - Required for route access
- **Mandatory**: User authentication - Automatically enforced by protected loader
- **Optional**: `categories.read` - Required for category features (graceful degradation if missing)

### User Experience
- **With ingredients.read + categories.read**: Full functionality including category display and filtering
- **With ingredients.read only**: Basic ingredient management with limited category features (category column shows "-" instead of category data)
- **Without ingredients.read**: Denied access, redirected to `/unauthorized` page
- **Unauthenticated**: Redirected to login page

### Implementation Status
- ✅ **Permission-based Access**: Fully implemented with `createProtectedLoader` and `RBAC_CONFIGS.ingredients`
- ✅ **Ingredients.read Enforcement**: Route properly enforces `ingredients.read` permission for access
- ✅ **Category loading**: Respects `categories.read` permission with fallback
- ✅ **Category column**: Now properly respects `categories.read` permission in UI layer

This ensures that only users with proper `ingredients.read` permission can access ingredient data while providing a robust user experience with graceful degradation for optional category features. The route enforces strict permission requirements for core functionality while maintaining graceful degradation for supplementary features like category information.