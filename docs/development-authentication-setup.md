# Development Authentication Setup

Complete guide for testing the KWACI application with seeded data and proper authentication.

## 🎯 Overview

This setup provides a complete testing environment with:
- **Realistic seeded data** for coffee shop businesses
- **Proper authentication credentials** that work with Better Auth
- **Multiple test users** representing different business scenarios
- **Development tools** for easy user management

## 🚀 Quick Start

### 1. **Setup Database with Authentication**
```bash
# Complete setup with seeded data and authentication
bun run db:reset
```

### 2. **View Available Test Users**
```bash
# List all test users with credentials
bun run dev:users
```

### 3. **Start Development Server**
```bash
bun run dev
```

### 4. **Log In and Test**
Use any of the test credentials to log in and explore the seeded data.

## 🔑 Default Test Credentials

| Email | Password | Business | Use Case |
|-------|----------|----------|----------|
| `<EMAIL>` | `password123` | KWACI Coffee House | Premium coffee business testing |
| `<EMAIL>` | `password123` | Brew & Bean Cafe | Cozy cafe business testing |
| `<EMAIL>` | `password123` | Morning Glory Coffee | Modern coffee shop testing |

## 🛠️ Development Tools

### **User Management Helper**
```bash
# Show all available commands
bun run dev:auth

# List all test users
bun run dev:users

# Create a new test user
bun run dev:<NAME_EMAIL> "Test User"

# Reset a user's password
bun run dev:<NAME_EMAIL> newpassword

# Create quick test users for common scenarios
bun run dev:auth quick

# Delete a test user
bun run dev:<NAME_EMAIL>
```

### **Database Management**
```bash
# Complete reset (migrations + seeding)
bun run db:reset

# Just reseed data (keep schema)
bun run db:seed

# Open database studio
bun run db:studio

# Generate new migration
bun run db:generate

# Apply migrations
bun run db:migrate
```

## 📊 What Gets Seeded

### **Users & Authentication**
- ✅ **3 Users** with proper Better Auth credentials
- ✅ **Email verification** set to true for easy testing
- ✅ **Hashed passwords** using Better Auth's password utilities
- ✅ **Authentication accounts** linked to users

### **Business Data**
- ✅ **3 Businesses** (one per user)
- ✅ **Complete business profiles** with descriptions
- ✅ **Proper user-business relationships**

### **Categories**
- ✅ **48 Categories** total (16 types × 3 businesses)
- ✅ **8 Ingredient categories** per business
- ✅ **8 Product categories** per business
- ✅ **Color-coded** for UI consistency

### **Inventory Data**
- ✅ **48 Ingredients** with realistic pricing
- ✅ **30 Products** with descriptions and pricing
- ✅ **Proper category relationships**
- ✅ **Business isolation** (users only see their data)

## 🔐 Authentication Architecture

### **Better Auth Integration**
- Uses Better Auth's official password hashing
- Creates proper `user` and `account` records
- Supports email/password authentication flow
- Maintains security best practices

### **Database Schema**
```sql
-- Users table (Better Auth)
user: id, name, email, emailVerified, image, createdAt, updatedAt

-- Accounts table (Better Auth)
account: id, accountId, providerId, userId, password, createdAt, updatedAt

-- Business relationship
business: id, name, description, userId, createdAt, updatedAt
```

### **Security Features**
- ✅ **Password hashing** using Better Auth utilities
- ✅ **Email verification** support
- ✅ **Session management** via Better Auth
- ✅ **User isolation** (business-scoped data)

## 🧪 Testing Workflows

### **Feature Development**
1. Start with fresh data: `bun run db:reset`
2. Log in with test credentials
3. Develop and test features with realistic data
4. Reset when needed for clean testing

### **Authentication Testing**
1. Test login/logout flows
2. Verify session persistence
3. Test password reset (if implemented)
4. Verify user isolation

### **Business Logic Testing**
1. Test with different business contexts
2. Verify category and inventory relationships
3. Test CRUD operations
4. Verify data validation

### **UI/UX Testing**
1. Test with realistic data volumes
2. Verify responsive design
3. Test dark/light mode (if implemented)
4. Test accessibility features

## 🔧 Customization

### **Add More Test Users**
```bash
# Create specific test scenarios
bun run dev:<NAME_EMAIL> "Admin User"
bun run dev:<NAME_EMAIL> "Manager User"
bun run dev:<NAME_EMAIL> "Staff User"
```

### **Modify Seeded Data**
Edit `app/lib/db/seed.ts` to:
- Add more categories
- Include more ingredients/products
- Adjust pricing and descriptions
- Add custom business scenarios

### **Environment-Specific Setup**
```bash
# Development
NODE_ENV=development bun run db:reset

# Testing
NODE_ENV=test bun run db:reset

# Staging (be careful!)
NODE_ENV=staging bun run db:reset
```

## 🚨 Important Notes

### **Security Considerations**
- ⚠️ **Development only**: These credentials are for development/testing
- ⚠️ **Never use in production**: Always use proper user registration in production
- ⚠️ **Environment isolation**: Ensure test data doesn't reach production

### **Data Persistence**
- 🔄 **Seeding resets data**: Running `db:seed` will replace all data
- 💾 **Schema preservation**: Migrations are preserved during seeding
- 🗑️ **Clean slate**: Use `db:reset` for complete fresh start

### **Performance**
- 📊 **Realistic volumes**: Seeded data provides realistic testing volumes
- ⚡ **Query optimization**: Test queries with proper data relationships
- 🔍 **Monitoring**: Use `db:studio` to inspect data and relationships

## 📚 Related Documentation

- [Testing with Seeded Data](./testing-with-seeded-data.md) - Comprehensive testing guide
- [Database Seeding](./database-seeding.md) - Technical seeding details
- [Authentication Troubleshooting](./authentication-troubleshooting.md) - Common issues and solutions

## 🎉 Success Checklist

After setup, you should be able to:

- ✅ Log in with any test credential
- ✅ See business-specific data only
- ✅ Browse categories, ingredients, and products
- ✅ Create, edit, and delete inventory items
- ✅ Test all authentication flows
- ✅ Switch between different business contexts
- ✅ Reset data when needed for testing

---

**You're all set for development! 🚀**

The authentication system is now fully integrated with realistic seeded data, providing a complete testing environment for the KWACI coffee shop management application.
