# Authentication & User Management Troubleshooting

This document explains how to resolve common authentication and user management issues in the KWACI application.

## Common Issue: Foreign Key Constraint Violation

### Problem
```
Business action failed: DrizzleQueryError: Failed query: insert into "business" 
violates foreign key constraint "business_userId_user_id_fk"
```

### Root Cause
This error occurs when:
1. A user session exists but the corresponding user record is missing from the database
2. Database was reset/seeded but active sessions still reference old user IDs
3. Better <PERSON>th created a session but failed to create the user record

### Solution Implemented

#### 1. **Automatic User Creation**
The `BusinessServiceServer.create()` method now automatically ensures the user exists before creating a business:

```typescript
// In businessService.server.ts
static async create(userId: string, data: BusinessFormData): Promise<Business> {
  // Ensure user exists before creating business
  await UserServiceServer.ensureUserExists(userId);
  
  // ... rest of business creation logic
}
```

#### 2. **User Service for Robust User Management**
Created `UserServiceServer` with methods to:
- Check if user exists
- Create missing users automatically
- Handle Better Auth compatibility

```typescript
// In userService.server.ts
static async ensureUserExists(userId: string, userData?: {
  name?: string;
  email?: string;
  emailVerified?: boolean;
  image?: string | null;
}): Promise<void>
```

#### 3. **Improved Database Seeding**
Updated seeding script to use proper UUIDs instead of simple strings:

```typescript
// Before: id: 'user-1'
// After: id: randomUUID()
```

## Manual Resolution Steps

If you encounter this error, here are the resolution steps:

### Option 1: Clear Session (Recommended)
1. Clear your browser cookies/session
2. Log out and log back in
3. Better Auth will create a proper user record

### Option 2: Check Database State
```bash
# Check current users in database
bun run scripts/check-users.ts

# Check if specific user exists
# (Replace USER_ID with the actual ID from the error)
```

### Option 3: Manual User Creation
If needed, you can manually create the missing user:

```sql
INSERT INTO "user" (id, name, email, "emailVerified", image, "createdAt", "updatedAt")
VALUES ('your-user-id', 'User Name', '<EMAIL>', false, null, NOW(), NOW());
```

## Prevention Strategies

### 1. **Robust Error Handling**
All business operations now include automatic user creation to prevent this issue.

### 2. **Better Auth Configuration**
Ensure Better Auth is properly configured to create user records:

```typescript
// In auth.server.ts
export const auth = betterAuth({
  database: pool, // Ensure database connection is working
  emailAndPassword: {
    enabled: true,
    // ... other settings
  },
});
```

### 3. **Development Workflow**
When working with seeded data:
1. Always clear sessions after running `bun run db:seed`
2. Use `bun run db:reset` for complete reset
3. Log in fresh after database changes

## Testing the Fix

### 1. **Test Business Creation**
Try creating a business through the UI. It should now work without foreign key errors.

### 2. **Test with New Users**
1. Create a new user account
2. Try creating a business immediately
3. Should work seamlessly

### 3. **Test with Seeded Data**
1. Run `bun run db:reset`
2. Clear browser session
3. Log in with a new account
4. Create businesses successfully

## Monitoring & Debugging

### 1. **Check Logs**
The system now logs when users are automatically created:
```
Creating missing user with ID: abc-123-def
✅ Created user: abc-123-def
```

### 2. **Database Queries**
Monitor user creation in your database:
```sql
SELECT id, name, email, "createdAt" FROM "user" ORDER BY "createdAt" DESC LIMIT 10;
```

### 3. **Session Validation**
Verify session data matches database records:
```typescript
const session = await getSession(request);
const userExists = await UserServiceServer.exists(session.user.id);
```

## Future Improvements

### 1. **Enhanced User Sync**
- Implement middleware to sync Better Auth users with database
- Add user profile management features
- Handle user updates and deletions

### 2. **Better Error Messages**
- User-friendly error messages for authentication issues
- Automatic retry mechanisms
- Graceful degradation for auth failures

### 3. **Monitoring & Alerts**
- Track authentication success/failure rates
- Monitor user creation patterns
- Alert on foreign key constraint violations

## Related Files

- `app/lib/services/userService.server.ts` - User management service
- `app/lib/services/businessService.server.ts` - Business service with user validation
- `app/lib/auth.server.ts` - Better Auth configuration
- `app/lib/auth.session.server.ts` - Session management
- `app/lib/db/seed.ts` - Database seeding with proper UUIDs

## Support

If you continue to experience authentication issues:
1. Check the console logs for detailed error messages
2. Verify your DATABASE_URL environment variable
3. Ensure Better Auth is properly configured
4. Clear all browser data and try again
5. Check that the database schema is up to date with migrations
