# 🚀 Quick Start: Login with Seeded Users

**Complete guide to login and explore seeded data in 5 minutes**

## 🔑 Step 1: Get Your Test Credentials

After running `bun run db:reset`, you have these test accounts:

| 📧 Email | 🔒 Password | 🏢 Business | 📊 Data Available |
|----------|-------------|-------------|-------------------|
| `<EMAIL>` | `password123` | KWACI Coffee House | ✅ 16 categories, 16 ingredients, 10 products |
| `<EMAIL>` | `password123` | Brew & Bean Cafe | ✅ 16 categories, 16 ingredients, 10 products |
| `<EMAIL>` | `password123` | Morning Glory Coffee | ✅ 16 categories, 16 ingredients, 10 products |

## 🎯 Step 2: Login Process

### **Start the Application**
```bash
# Make sure you have seeded data
bun run db:reset

# Start development server
bun run dev
```

### **Navigate to Login**
1. Open your browser to `http://localhost:3000` (or your dev URL)
2. Go to the login page
3. Enter any test credential from the table above
4. Click "Sign In"

### **Expected Result**
✅ You should be logged in and redirected to the dashboard/main application

## 📊 Step 3: What You'll See After Login

### **Business Context**
- 🏢 **Your Business**: You'll see data for the specific business tied to your login
- 👤 **User Isolation**: You can only see data for your business (not other test users' data)
- 🔒 **Secure Access**: Full authentication with session management

### **Available Data Structure**

#### **📂 Categories (16 total per business)**

**Ingredient Categories (8):**
- ☕ Coffee Beans
- 🥛 Dairy  
- 🍯 Sweeteners
- 🌿 Spices & Flavorings
- 🌱 Alternative Milks
- 🍦 Toppings
- 🍵 Tea
- 🧁 Baking Ingredients

**Product Categories (8):**
- ☕ Hot Beverages
- 🧊 Cold Beverages
- 🥐 Pastries
- 🍿 Snacks
- ✨ Specialty Drinks
- 🍰 Desserts
- 🍳 Breakfast Items
- 🥪 Lunch Items

#### **🥄 Ingredients (16 total per business)**

**Sample Ingredients You'll Find:**
- **Coffee**: Arabica Coffee Beans, Robusta Coffee Beans, Espresso Blend
- **Dairy**: Whole Milk, Skim Milk, Heavy Cream
- **Plant-Based**: Almond Milk, Oat Milk, Soy Milk
- **Sweeteners**: White Sugar, Brown Sugar, Vanilla Syrup, Caramel Syrup
- **Spices**: Cinnamon Powder, Vanilla Extract, Cocoa Powder

*Each ingredient includes: pricing, unit measurements, supplier info, and category assignment*

#### **☕ Products (10 total per business)**

**Sample Products You'll Find:**
- **Hot Drinks**: Espresso (₹15,000), Americano (₹18,000), Cappuccino (₹25,000), Latte (₹28,000)
- **Cold Drinks**: Iced Coffee (₹20,000), Cold Brew (₹25,000), Iced Latte (₹30,000)
- **Food**: Croissant (₹15,000), Blueberry Muffin (₹18,000), Chocolate Croissant (₹20,000)

*Each product includes: descriptions, pricing, category assignment, and status*

## 🧪 Step 4: What to Test

### **Navigation & UI**
- ✅ **Dashboard**: Overview of your business
- ✅ **Inventory**: Browse ingredients with detail sheets
- ✅ **Products**: Browse product catalog
- ✅ **Categories**: Manage ingredient and product categories
- ✅ **Business Settings**: Edit business information

### **Core Features to Test**

#### **Ingredient Management**
1. Go to `/inventory/ingredients`
2. Click on any ingredient row
3. **Expected**: Ingredient detail sheet opens with:
   - Ingredient name and details
   - Category assignment
   - Pricing and unit information
   - Usage statistics
   - Cost analysis

#### **Product Management**
1. Go to `/inventory/products` (or products section)
2. Browse product catalog
3. **Expected**: Products organized by categories with:
   - Product descriptions
   - Pricing information
   - Category assignments
   - Status indicators

#### **Category Management**
1. Navigate to categories section
2. **Expected**: See both ingredient and product categories
3. Test filtering by type (ingredient vs product)
4. **Expected**: Color-coded categories with proper organization

#### **Business Context**
1. Check business information
2. **Expected**: See the specific business for your logged-in user
3. Try creating new items
4. **Expected**: New items are scoped to your business only

## 🔍 Step 5: Verify Everything Works

### **Data Isolation Test**
1. Log in as `<EMAIL>`
2. Note the business name: "KWACI Coffee House"
3. Log out and log in as `<EMAIL>`
4. **Expected**: Different business name: "Brew & Bean Cafe"
5. **Expected**: Same data structure but different business context

### **Feature Functionality Test**
- ✅ **Create**: Try adding a new ingredient or product
- ✅ **Read**: Browse existing data in detail sheets
- ✅ **Update**: Edit existing items
- ✅ **Delete**: Remove test items (be careful with seeded data!)

### **Authentication Test**
- ✅ **Session**: Refresh page, should stay logged in
- ✅ **Logout**: Should redirect to login page
- ✅ **Re-login**: Should work with same credentials

## 🚨 Troubleshooting

### **Can't Login?**
```bash
# Check if users exist
bun run dev:users

# Reset if needed
bun run db:reset
```

### **No Data Visible?**
1. Verify you're logged in (check user context)
2. Check browser console for errors
3. Verify seeding completed successfully
4. Try different test user

### **Wrong Business Data?**
- Each user sees only their business data
- Make sure you're logged in as the expected user
- Check business name in the UI matches your login

## 🔄 Reset When Needed

```bash
# Complete fresh start
bun run db:reset

# Just check users
bun run dev:users

# Create additional test users
bun run dev:<NAME_EMAIL> "Test User"
```

## 📱 Expected User Experience

### **Successful Login Flow:**
1. **Login Page** → Enter credentials → **Dashboard**
2. **Business Context** → See your specific business data
3. **Navigation** → Access inventory, products, categories
4. **Detail Views** → Click items to see detailed information
5. **CRUD Operations** → Create, edit, delete items
6. **Session Persistence** → Stay logged in across page refreshes

### **Data Relationships:**
- **Categories** organize ingredients and products
- **Ingredients** have pricing, units, and supplier info
- **Products** have descriptions, pricing, and category assignments
- **Business Scope** ensures data isolation between users

## 🎉 Success Indicators

You'll know everything is working when:

- ✅ **Login succeeds** with test credentials
- ✅ **Business context** shows correct business name
- ✅ **Data loads** with categories, ingredients, and products
- ✅ **Detail sheets** open with complete information
- ✅ **Navigation works** between different sections
- ✅ **User isolation** prevents seeing other businesses' data
- ✅ **CRUD operations** work for creating/editing items

---

## 📚 **Need More Details?**

### **Authentication & Basic Setup**
- **[Complete Testing Guide](./testing-with-seeded-data.md)** - Comprehensive testing scenarios
- **[Development Setup](./development-authentication-setup.md)** - Technical details and tools
- **[Troubleshooting](./authentication-troubleshooting.md)** - Common issues and solutions

### **🔐 RBAC System & User Management**
- **[RBAC Quick Setup](./RBAC-QUICK-SETUP.md)** - Get started with role management in 5 minutes
- **[RBAC System Guide](./RBAC-SYSTEM-GUIDE.md)** - Complete user and role management documentation
- **[RBAC Developer Reference](./RBAC-DEVELOPER-REFERENCE.md)** - Technical implementation details

**Key RBAC Features:**
- ✅ **Cross-business role assignment** - Manage users across multiple businesses
- ✅ **User invitation system** - Invite new users via email with role pre-assignment
- ✅ **Real-time notifications** - Centralized invitation management at `/notifications`
- ✅ **Granular permissions** - Fine-grained access control for all features

**Quick RBAC Access:**
- **Role Management**: Settings → Role Management
- **Test Admin Account**: `<EMAIL>` / `password123` (Multi-business admin)

**Happy Testing! 🎉**

*You now have a complete coffee shop business management system with realistic data and proper authentication ready for testing and development.*
