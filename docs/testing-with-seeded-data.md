# Testing with Seeded Data

This guide explains how to test the KWACI application using the seeded data with proper authentication credentials.

## 🔑 Test Credentials

After running the seeding script, you can log in with any of these test accounts:

| Email | Password | Business | Description |
|-------|----------|----------|-------------|
| `<EMAIL>` | `password123` | KWACI Coffee House | Premium coffee experience with locally sourced beans |
| `<EMAIL>` | `password123` | Brew & Bean Cafe | Cozy neighborhood cafe with artisanal drinks |
| `<EMAIL>` | `password123` | Morning Glory Coffee | Modern coffee shop with specialty beverages |
| `<EMAIL>` | `password123` | KWACI Coffee House (Viewer) | Read-only access to KWACI Coffee House |
| `<EMAIL>` | `password123` | None (initially) | **RBAC Test User** - For testing role assignments and permissions |

## 🚀 Quick Start Testing
### 1. **Reset and Seed Database**

```bash
# Complete reset with fresh seeded data
bun run db:reset

# Or just seed (if database is already migrated)
bun run db:seed
```

### 2. **RBAC Testing with Test User**

The `<EMAIL>` user is specifically designed for testing role-based access control:

**Available Roles for Testing:**
- `super_admin` - Full system access
- `business_owner` - Full business management
- `business_manager` - Business operations management
- `inventory_manager` - Inventory and product management
- `staff` - Basic inventory operations
- `viewer` - Read-only access

**Manual Role Assignment for Testing:**
```bash
# Example: Assign inventory_manager role to test user for KWACI Coffee House
echo "import { RBACService } from './app/lib/services/rbacService.server';
import { db } from './app/lib/db/connection';
import { users, businesses } from './app/lib/db/schema';
import { eq } from 'drizzle-orm';

const testUser = await db.select().from(users).where(eq(users.email, '<EMAIL>')).limit(1);
const business = await db.select().from(businesses).where(eq(businesses.name, 'KWACI Coffee House')).limit(1);

if (testUser[0] && business[0]) {
  await RBACService.assignRole(testUser[0].id, 'super_admin', business[0].userId, business[0].id);
  console.log('✅ Assigned inventory_manager role to test user');
}

process.exit(0);" > assign-test-role.js

bun run assign-test-role.js
```

**Testing Different Permission Levels:**
1. Login as `<EMAIL>`
2. Try accessing different endpoints/features
3. Verify access is granted/denied based on assigned role
4. Change roles and repeat testing

### 3. **Start Development Server**
```bash
bun run dev
```

### 3. **Log In**
1. Navigate to your application
2. Go to the login page
3. Use any of the test credentials above
4. You'll be logged in with access to a complete business setup

### 4. **Explore Seeded Data**
Each test user has access to:
- **1 Business** with complete profile
- **16 Categories** (8 ingredient + 8 product categories)
- **16 Ingredients** with realistic pricing and details
- **10 Products** with descriptions and pricing

## 📊 Seeded Data Structure

### **Categories**

#### Ingredient Categories
- Coffee Beans
- Dairy
- Sweeteners
- Spices & Flavorings
- Alternative Milks
- Toppings
- Tea
- Baking Ingredients

#### Product Categories
- Hot Beverages
- Cold Beverages
- Pastries
- Snacks
- Specialty Drinks
- Desserts
- Breakfast Items
- Lunch Items

### **Sample Ingredients**
- **Coffee**: Arabica Coffee Beans, Robusta Coffee Beans, Espresso Blend
- **Dairy**: Whole Milk, Skim Milk, Heavy Cream
- **Alternative Milks**: Almond Milk, Oat Milk, Soy Milk
- **Sweeteners**: White Sugar, Brown Sugar, Vanilla Syrup, Caramel Syrup
- **Spices**: Cinnamon Powder, Vanilla Extract, Cocoa Powder

### **Sample Products**
- **Hot Beverages**: Espresso, Americano, Cappuccino, Latte
- **Cold Beverages**: Iced Coffee, Cold Brew, Iced Latte
- **Pastries**: Croissant, Blueberry Muffin, Chocolate Croissant

## 🧪 Testing Scenarios

### **1. Business Management**
- ✅ View business dashboard
- ✅ Edit business information
- ✅ Manage business settings

### **2. Category Management**
- ✅ View ingredient and product categories
- ✅ Create new categories
- ✅ Edit existing categories
- ✅ Filter by category type

### **3. Ingredient Management**
- ✅ Browse ingredient inventory
- ✅ View ingredient details in drawer
- ✅ Create new ingredients
- ✅ Edit ingredient information
- ✅ Assign ingredients to categories
- ✅ Track ingredient costs and usage

### **4. Product Management**
- ✅ Browse product catalog
- ✅ View product details
- ✅ Create new products
- ✅ Edit product information
- ✅ Assign products to categories
- ✅ Manage product pricing

### **5. User Experience**
- ✅ Authentication flow
- ✅ Session management
- ✅ Navigation between features
- ✅ Responsive design
- ✅ Dark/light mode (if implemented)

## 🔄 Resetting Test Data

### **Complete Reset**
```bash
# Reset database and reseed with fresh data
bun run db:reset
```

### **Partial Reset**
```bash
# Just reseed (keeps schema, replaces data)
bun run db:seed
```

### **Clear Session**
If you encounter authentication issues:
1. Clear browser cookies/localStorage
2. Log out and log back in
3. Or use incognito/private browsing mode

## 🛠️ Development Workflow

### **Daily Development**
1. Start with `bun run dev`
2. Use existing test credentials
3. Test new features with realistic data
4. Reset data when needed with `bun run db:seed`

### **Feature Testing**
1. Reset to clean state: `bun run db:reset`
2. Log in with test credentials
3. Test feature with fresh data
4. Verify data persistence and relationships

### **Integration Testing**
1. Test with different user accounts
2. Verify business isolation (users can't see other businesses' data)
3. Test category and inventory relationships
4. Verify authentication and authorization

## 🔍 Debugging Tips

### **Authentication Issues**
- Verify test credentials are correct
- Check if seeding completed successfully
- Clear browser session and try again
- Check console for authentication errors

### **Data Issues**
- Verify foreign key relationships
- Check if categories are properly assigned
- Ensure business isolation is working
- Verify data types and constraints

### **Performance Testing**
- Test with realistic data volumes
- Verify query performance with seeded data
- Test pagination and filtering
- Monitor database query efficiency

## 📈 Data Validation

### **Verify Seeded Data**
```bash
# Check users and authentication
bun run scripts/check-users.ts

# Or manually verify in database
bun run db:studio
```

### **Expected Counts**
- **Users**: 3 (with authentication)
- **Businesses**: 3 (one per user)
- **Categories**: 48 (16 types × 3 businesses)
- **Ingredients**: 48 (16 types × 3 businesses)
- **Products**: 30 (10 types × 3 businesses)

## 🚨 Troubleshooting

### **Login Fails**
1. Verify credentials: `<EMAIL>` / `password123`
2. Check if seeding completed successfully
3. Clear browser session
4. Check authentication configuration

### **No Data Visible**
1. Verify you're logged in with correct user
2. Check business association
3. Verify database seeding completed
4. Check for JavaScript errors in console

### **Foreign Key Errors**
1. Run `bun run db:reset` for complete reset
2. Verify migration status
3. Check schema consistency

## 🎯 Next Steps

After successful testing with seeded data:

1. **Implement additional features** using the realistic test data
2. **Add more test scenarios** based on business requirements
3. **Optimize performance** with realistic data volumes
4. **Enhance user experience** based on testing feedback
5. **Prepare for production** with proper data migration strategies

## 📚 Related Documentation

- [Database Seeding Guide](./database-seeding.md)
- [Authentication Troubleshooting](./authentication-troubleshooting.md)
- [Development Setup](../README.md)

---

**Happy Testing! 🎉**

You now have a complete testing environment with realistic data and proper authentication. Each test user represents a different coffee shop business with full inventory management capabilities.
