# API Endpoint Best Practices Guide

This guide explains the best practices for creating new API endpoints in the Kwaci Grow application, including proper RBAC (Role-Based Access Control) implementation patterns.

## Table of Contents

1. [Server Logic Routes (API Endpoints)](#server-logic-routes-api-endpoints)
2. [UI Routes](#ui-routes)
3. [Understanding createRBACLoader](#understanding-createrbacloader)
4. [The useRBAC Hook](#the-userbac-hook)
5. [The rbac.server.ts File](#the-rbacserverts-file)
6. [Quick Reference](#quick-reference)

## Server Logic Routes (API Endpoints)

### Pattern: `api.*` files

For server-side API endpoints that handle data fetching, database operations, and business logic, use the `createProtectedRoute` function.

### Example Structure

```typescript
// app/routes/api.categories.protected.tsx
import { createProtectedRoute } from '~/lib/middleware/autoRBAC.server';
import { RBAC_CONFIGS } from '~/lib/middleware/autoRBAC.server';

// Your original loader and action functions
export const categoriesLoader = async ({ request, params }: LoaderFunctionArgs) => {
  // Database operations, business logic
  return json({ categories: await CategoryService.getAll() });
};

export const categoriesAction = async ({ request, params }: ActionFunctionArgs) => {
  // Handle POST, PUT, DELETE operations
  return json({ success: true });
};

// Export protected versions
export const { loader, action } = createProtectedRoute({
  loader: categoriesLoader,
  action: categoriesAction,
  config: RBAC_CONFIGS.categories, // Automatically maps HTTP methods to permissions
});
```

### How it Works

- **Automatic Permission Mapping**: `createProtectedRoute` uses `DEFAULT_PERMISSION_MAPPING` to automatically enforce permissions:
  - `GET` requests → `{resource}.read` permission
  - `POST` requests → `{resource}.create` permission
  - `PUT/PATCH` requests → `{resource}.update` permission
  - `DELETE` requests → `{resource}.delete` permission

- **Server-Side Only**: API routes typically don't need client-side RBAC data, so `createProtectedRoute` only handles server-side permission enforcement.

### Best Practices

1. **Use RBAC_CONFIGS**: Always use predefined RBAC configurations for consistency
2. **Keep Logic Separate**: Write your business logic in separate functions, then wrap them with `createProtectedRoute`
3. **Handle Errors Gracefully**: The middleware automatically redirects unauthorized users
4. **Business Context**: Ensure your RBAC config includes proper business ID handling when needed

## UI Routes

### Pattern: Regular route files that render UI components

For routes that render user interfaces and need both server-side protection and client-side RBAC data, use `createProtectedRoute` combined with `withRBAC`.

### Example Structure

```typescript
// app/routes/categories.tsx
import { createProtectedLoader } from '~/lib/middleware/autoRBAC.server';
import { withRBAC } from '~/lib/utils/rbacLoader.server';
import { useRBAC } from '~/lib/hooks/useRBAC';

// Original loader function
const categoriesLoader = async ({ request, params }: LoaderFunctionArgs) => {
  const categories = await CategoryService.getAll();
  
  // Include RBAC data for client-side use
  const rbacData = await withRBAC(request, params.businessId);
  
  return json({
    categories,
    ...rbacData, // Includes permissions, roles, userId, businessId
  });
};

// Export protected loader
export const loader = createProtectedLoader({
  loader: categoriesLoader,
  config: RBAC_CONFIGS.categories,
});

// Component with client-side RBAC
export default function CategoriesPage() {
  const { categories } = useLoaderData<typeof loader>();
  const rbac = useRBAC();
  
  return (
    <div>
      <h1>Categories</h1>
      {rbac.categories.canCreate && (
        <button>Add New Category</button>
      )}
      {/* Rest of your UI */}
    </div>
  );
}
```

### Why Both Server and Client Protection?

1. **Server-Side**: Prevents unauthorized access to the route entirely
2. **Client-Side**: Enables conditional UI rendering based on user permissions

### Best Practices

1. **Always Include withRBAC**: UI routes should include RBAC data for client components
2. **Use useRBAC Hook**: Leverage the hook's convenience methods for permission checks
3. **Conditional Rendering**: Show/hide UI elements based on user permissions
4. **Consistent Patterns**: Follow the same structure across all UI routes

## Understanding createRBACLoader

### Purpose

`createRBACLoader` is an alternative to using `createProtectedLoader + withRBAC` that automatically includes RBAC data without enforcing specific permissions.

### When to Use

```typescript
// app/lib/utils/rbacLoader.server.ts
export const createRBACLoader = (loader: LoaderFunction, options?: {
  requireAuth?: boolean;
  getBusinessId?: (args: LoaderFunctionArgs) => string | undefined;
}) => {
  // Automatically injects RBAC data into loader response
};
```

### Use Cases

1. **Dashboard Routes**: Need RBAC data but don't require specific permissions
2. **Navigation Components**: Need to show/hide menu items based on permissions
3. **Flexible Permission Checking**: When you need granular control over permission logic
4. **Multi-Resource Pages**: Pages that check permissions for multiple resources

### Example

```typescript
// app/routes/dashboard.tsx
import { createRBACLoader } from '~/lib/utils/rbacLoader.server';

const dashboardLoader = async ({ request, params }: LoaderFunctionArgs) => {
  // No automatic permission enforcement
  const dashboardData = await getDashboardData();
  return json({ dashboardData });
};

// Automatically includes RBAC data
export const loader = createRBACLoader(dashboardLoader, {
  requireAuth: true,
  getBusinessId: (args) => args.params.businessId,
});

export default function Dashboard() {
  const { dashboardData } = useLoaderData<typeof loader>();
  const rbac = useRBAC();
  
  return (
    <div>
      {rbac.inventory.canRead && <InventoryWidget />}
      {rbac.categories.canRead && <CategoriesWidget />}
      {rbac.business.canManageUsers && <UserManagementWidget />}
    </div>
  );
}
```

### createRBACLoader vs createProtectedLoader + withRBAC

| Feature | createRBACLoader | createProtectedLoader + withRBAC |
|---------|------------------|----------------------------------|
| **Permission Enforcement** | ❌ Manual | ✅ Automatic |
| **RBAC Data Injection** | ✅ Automatic | ⚠️ Manual (withRBAC call) |
| **Use Case** | Flexible/Dashboard | Standard CRUD operations |
| **Setup Complexity** | 🟢 Simple | 🟡 Two-step process |
| **Best For** | Multi-resource pages | Single-resource operations |

## The useRBAC Hook

### Purpose

The `useRBAC` hook provides a comprehensive client-side interface for checking user permissions and roles.

### Key Features

```typescript
// app/lib/hooks/useRBAC.ts
export function useRBAC() {
  return {
    // Raw data
    permissions: string[],
    roles: string[],
    userId: string,
    businessId?: string,
    
    // Permission checks
    hasPermission: (permission: string) => boolean,
    hasAnyPermission: (permissions: string[]) => boolean,
    hasAllPermissions: (permissions: string[]) => boolean,
    
    // Role checks
    hasRole: (role: string) => boolean,
    hasAnyRole: (roles: string[]) => boolean,
    hasAllRoles: (roles: string[]) => boolean,
    
    // Convenience methods
    canRead: (resource: string) => boolean,
    canCreate: (resource: string) => boolean,
    canUpdate: (resource: string) => boolean,
    canDelete: (resource: string) => boolean,
    
    // Role shortcuts
    isBusinessOwner: boolean,
    isSuperAdmin: boolean,
    isBusinessManager: boolean,
    isInventoryManager: boolean,
    isStaff: boolean,
    isViewer: boolean,
    
    // Resource-specific permissions
    inventory: { canRead, canCreate, canUpdate, canDelete },
    categories: { canRead, canCreate, canUpdate, canDelete },
    business: { canRead, canCreate, canUpdate, canDelete, canManageUsers },
    cogs: { canRead, canCalculate, canUpdate },
    users: { canRead, canCreate, canUpdate, canDelete, canAssignRoles },
    rolesManagement: { canRead, canCreate, canUpdate, canDelete },
    ingredients: { canRead, canCreate, canUpdate, canDelete },
    products: { canRead, canCreate, canUpdate, canDelete }
  };
}
```

### Usage Patterns

```typescript
export default function MyComponent() {
  const rbac = useRBAC();
  
  // Direct permission checks
  if (rbac.hasPermission('categories.create')) {
    // Show create button
  }
  
  // Role-based checks
  if (rbac.isBusinessOwner) {
    // Show owner-only features
  }
  
  // Resource-specific shortcuts
  if (rbac.categories.canUpdate) {
    // Show edit functionality
  }
  
  // Multiple permission checks
  if (rbac.hasAnyPermission(['inventory.read', 'categories.read'])) {
    // Show if user can read either resource
  }
}
```

### Protected Component

The hook also provides a `Protected` component for declarative permission checking:

```typescript
import { Protected } from '~/lib/hooks/useRBAC';

<Protected permissions={['categories.create']}>
  <CreateCategoryButton />
</Protected>

<Protected roles={['business_owner', 'business_manager']} requireAll={false}>
  <AdminPanel />
</Protected>
```

## The rbac.server.ts File

### Purpose

The `rbac.server.ts` file provides server-side RBAC utilities and middleware functions for protecting routes and checking permissions.

### Key Functions

#### Core Middleware

```typescript
// Main permission checking function
export async function requirePermissions(
  request: Request,
  options: RBACOptions
): Promise<{ userId: string; businessId?: string }>
```

#### Helper Functions

```typescript
// Business access helpers
export async function requireBusinessAccess(request, businessId, redirectTo?)
export async function requireBusinessOwner(request, businessId, redirectTo?)

// Resource-specific helpers
export async function requireInventoryAccess(request, businessId, action, redirectTo?)
export async function requireCategoryAccess(request, businessId, action, redirectTo?)
export async function requireCOGSAccess(request, businessId, action, redirectTo?)
export async function requireUserManagement(request, action, businessId?, redirectTo?)
export async function requireRoleManagement(request, action, redirectTo?)

// Admin helpers
export async function requireSuperAdmin(request, redirectTo?)

// Utility functions
export async function canAccessBusiness(request, businessId): Promise<boolean>
export async function getUserBusinessPermissions(request, businessId?)
```

### Usage Examples

```typescript
// Manual permission checking in loaders
export async function loader({ request, params }: LoaderFunctionArgs) {
  // Require specific permission
  await requirePermissions(request, {
    permissions: ['inventory.read'],
    businessId: params.businessId,
  });
  
  // Or use helper functions
  await requireInventoryAccess(request, params.businessId, 'read');
  
  return json({ data: await getInventoryData() });
}

// Check access without throwing
export async function loader({ request, params }: LoaderFunctionArgs) {
  const canAccess = await canAccessBusiness(request, params.businessId);
  
  if (!canAccess) {
    return json({ error: 'Access denied' }, { status: 403 });
  }
  
  return json({ data: await getBusinessData() });
}
```

### When to Use Direct vs Automatic

| Scenario | Use | Reason |
|----------|-----|--------|
| **Standard CRUD** | `createProtectedRoute` | Automatic mapping, less code |
| **Custom Logic** | `requirePermissions` | Full control over permission logic |
| **Multiple Permissions** | `requirePermissions` | Can check multiple permissions/roles |
| **Conditional Access** | `canAccessBusiness` | Non-throwing checks |
| **Complex Business Rules** | Manual helpers | Specific business logic requirements |

## Quick Reference

### Decision Tree

```
Creating a new endpoint?
├── API Route (api.*)
│   └── Use: createProtectedRoute
│       └── Automatic permission mapping
│       └── Server-side only protection
└── UI Route
    ├── Standard CRUD operations?
    │   └── Use: createProtectedLoader + withRBAC
    │       └── Server protection + client RBAC data
    └── Dashboard/Multi-resource page?
        └── Use: createRBACLoader
            └── RBAC data without automatic enforcement
```

### File Patterns

```typescript
// API Route Pattern
export const { loader, action } = createProtectedRoute({
  loader: myLoader,
  action: myAction,
  config: RBAC_CONFIGS.resource,
});

// UI Route Pattern
export const loader = createProtectedLoader({
  loader: async (args) => {
    const data = await getData();
    const rbacData = await withRBAC(args.request, args.params.businessId);
    return json({ data, ...rbacData });
  },
  config: RBAC_CONFIGS.resource,
});

// Flexible Route Pattern
export const loader = createRBACLoader(
  async (args) => {
    const data = await getData();
    return json({ data });
  },
  { requireAuth: true, getBusinessId: (args) => args.params.businessId }
);
```

### Adding New Resources to RBAC System

When creating endpoints for new resource types, you must also update the `useRBAC` hook to include resource-specific permission objects:

```typescript
// app/lib/hooks/useRBAC.ts

// Add to the useMemo for resource-specific permissions
const orders = useMemo(() => ({
  canRead: hasPermission('orders.read'),
  canCreate: hasPermission('orders.create'),
  canUpdate: hasPermission('orders.update'),
  canDelete: hasPermission('orders.delete'),
  canCancel: hasPermission('orders.cancel'),
  canRefund: hasPermission('orders.refund'),
}), [hasPermission]);

// Add to the return statement
return {
  // ... existing properties
  orders,
};
```

### Common Mistakes to Avoid

1. **Forgetting withRBAC**: UI routes need RBAC data for client-side permission checks
2. **Wrong Tool Choice**: Using `createRBACLoader` for simple CRUD operations
3. **Missing Business Context**: Not providing business ID when required
4. **Inconsistent Patterns**: Mixing different approaches in similar routes
5. **Client-Only Protection**: Relying solely on client-side permission checks
6. **Not Adding Resource Objects**: When creating new resource types, forgetting to add them to `useRBAC`

## Client-Side Route Protection

For routes that need protection against client-side navigation (e.g., when users navigate via React Router without triggering server-side loaders), use the `useRouteProtection` hook:

```typescript
// app/routes/protected-page.tsx
import { useRouteProtection } from '~/lib/hooks/useRouteProtection';

export default function ProtectedPage() {
  // Protect the route with specific permission
  useRouteProtection({
    permission: 'resource.read', // Required permission
    preserveUrl: true, // Preserve original URL for post-auth redirect
    redirectTo: '/unauthorized' // Optional: custom redirect path
  });

  // Component content...
}
```

### When to Use Client-Side Protection

- **Required for all protected UI routes**: Even with server-side protection via `createProtectedLoader`, client-side navigation can bypass server checks
- **Navigation scenarios**: Direct URL access, browser back/forward, programmatic navigation
- **User experience**: Prevents the need for browser refresh to trigger redirects

### Available Protection Hooks

```typescript
// General route protection
useRouteProtection({ permission: 'resource.read' });

// Permission-specific protection
usePermissionProtection('resource.read');

// Role-specific protection
useRoleProtection('business_owner');
```

### Best Practices

- Always use `preserveUrl: true` for better UX
- Place the hook call early in your component
- Use specific permissions rather than broad role checks when possible
- Combine with server-side protection for complete security

### Performance Tips

1. **Cache RBAC Data**: The `useRBAC` hook uses memoization for performance
2. **Minimize Database Calls**: RBAC middleware efficiently batches permission checks
3. **Use Resource-Specific Helpers**: Leverage pre-built permission objects in `useRBAC`
4. **Conditional Loading**: Only load RBAC data when needed for the specific route

## Permissions Information Sheets

### Purpose

Permissions information sheets provide users with transparency about what permissions are required for different modules and their purposes. This improves user experience and helps administrators understand permission requirements.

### Implementation Pattern

For UI routes that need to display permissions information, implement a reusable permissions sheet component:

#### 1. Create Permission Configuration

Define permission configurations in a centralized location:

```typescript
// app/lib/utils/permissionConfigs.tsx
import { Eye, Plus, Edit, Trash2, Shield, DollarSign, Package, Tag } from 'lucide-react';
import type { PermissionInfo } from '~/components/inventory/PermissionsSheet';

export const PERMISSION_CONFIGS = {
  ingredients: {
    routeName: '/inventory/ingredients',
    permissions: [
      {
        name: "ingredients.read",
        purpose: "Required to access the ingredients route and view ingredient data",
        icon: <Eye className="h-4 w-4 text-blue-600" />,
        category: "primary" as const
      },
      {
        name: "ingredients.create",
        purpose: "Allows creating new ingredients via the 'Add New' button",
        icon: <Plus className="h-4 w-4 text-green-600" />,
        category: "action" as const
      },
      // ... more permissions
    ] as PermissionInfo[]
  },
  // ... other modules
};

export function getPermissionConfig(module: keyof typeof PERMISSION_CONFIGS) {
  return PERMISSION_CONFIGS[module];
}
```

#### 2. Create Reusable Permissions Sheet Component

```typescript
// app/components/inventory/PermissionsSheet.tsx
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetDescription } from "~/components/ui/sheet";
import { PermissionsInfoSection } from "~/components/rbac/PermissionsInfoSection";

export interface PermissionInfo {
  name: string;
  purpose: string;
  icon: React.ReactNode;
  category: "primary" | "action" | "related";
}

interface PermissionsSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  routeName: string;
  title: string;
  description: string;
  permissions: PermissionInfo[];
}

export function PermissionsSheet({
  open,
  onOpenChange,
  routeName,
  title,
  description,
  permissions
}: PermissionsSheetProps) {
  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="w-[600px] sm:max-w-[600px] overflow-y-auto">
        <SheetHeader>
          <SheetTitle>{title}</SheetTitle>
          <SheetDescription>{description}</SheetDescription>
        </SheetHeader>
        <div className="mt-6">
          <PermissionsInfoSection
            routeName={routeName}
            permissions={permissions}
          />
        </div>
      </SheetContent>
    </Sheet>
  );
}
```

#### 3. Add Permissions Button to Route

```typescript
// Example: app/routes/inventory.ingredients.tsx
import { Shield } from "lucide-react";
import { PermissionsSheet } from "~/components/inventory/PermissionsSheet";
import { getPermissionConfig } from "~/lib/utils/permissionConfigs";
import { useRBAC } from "~/lib/hooks/useRBAC";

export default function IngredientsPage() {
  const [showPermissionsSheet, setShowPermissionsSheet] = useState(false);
  const { hasPermission } = useRBAC();
  
  return (
    <div>
      {/* Header with permissions button */}
      <div className="flex items-center justify-between">
        <h1>Ingredients</h1>
        <div className="flex items-center gap-2">
          {/* Other buttons */}
          
          {/* Permissions button - protected by system.view_permissions */}
          {hasPermission('system.view_permissions') && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowPermissionsSheet(true)}
            >
              <Shield className="h-4 w-4 mr-2" />
              Permissions
            </Button>
          )}
        </div>
      </div>
      
      {/* Rest of your component */}
      
      {/* Permissions Sheet */}
      <PermissionsSheet
        open={showPermissionsSheet}
        onOpenChange={setShowPermissionsSheet}
        routeName={getPermissionConfig('ingredients').routeName}
        title="Ingredients Module Permissions"
        description="This section shows all permissions required for the ingredients module and their purposes."
        permissions={getPermissionConfig('ingredients').permissions}
      />
    </div>
  );
}
```

**✅ Successfully Implemented Routes:**
- `/inventory/ingredients` - Full permissions sheet implementation
- `/inventory/products` - Full permissions sheet implementation with conditional rendering based on `system.view_permissions`

### Best Practices

1. **Centralized Configuration**: Keep all permission configurations in a single file for maintainability
2. **Reusable Components**: Create generic components that can be used across different modules
3. **Permission Protection**: Always protect the permissions button with `system.view_permissions`
4. **Clear Categorization**: Group permissions by category (primary, action, related) for better organization
5. **Descriptive Icons**: Use appropriate icons that clearly represent the permission type
6. **Helpful Descriptions**: Provide clear, user-friendly descriptions of what each permission enables

### Permission Categories

- **Primary**: Core permissions required to access the module
- **Action**: Permissions for specific actions (create, update, delete)
- **Related**: Optional permissions for related functionality that gracefully degrades if missing

### When to Add Permissions Sheets

- **Complex Modules**: Routes with multiple permission requirements
- **Administrative Routes**: Where understanding permissions is crucial for role management
- **User Training**: To help users understand what permissions they need
- **Troubleshooting**: To help administrators diagnose access issues

This guide ensures consistent, secure, and maintainable RBAC implementation across your application.