# RBAC System Quick Setup Guide

## 🚀 Get Started in 5 Minutes

### 1. <PERSON><PERSON> as Test Administrator
```
📧 Email: <EMAIL>
🔒 Password: password123
```

### 2. Access RBAC Management
Navigate to: **Settings → Role Management** or directly to:
```
/business/{businessId}/settings/rbac
```

### 3. Start Managing Users
You'll see three main sections:
- **Assign Roles** - For existing users
- **Invite New User** - For new users  
- **Pending Invitations** - Track invitation status

## ✅ What You Can Do Immediately

### Cross-Business Management
The test user has access to **all 3 businesses**:
- ✅ KWACI Coffee House (Business Owner)
- ✅ Brew & Bean Cafe (Business Manager)
- ✅ Morning Glory Coffee (Business Manager)

### Available Actions
- ✅ **Invite users** to any business
- ✅ **Assign roles** across businesses
- ✅ **Manage permissions** for all users
- ✅ **View notifications** at `/notifications`

## 🎯 Common Tasks

### Invite a New User
1. Go to **"Invite New User"** section
2. **Select target business** from dropdown
3. **Enter email** and **select role**
4. **Click "Send Invitation"**

### Assign Role to Existing User
1. Go to **"Assign Roles"** section
2. **Select target business** (if managing multiple)
3. **Choose user** and **select role**
4. **Click "Assign Role"**

### Check Notifications
1. Look for **notification badge** in header
2. Click **"Notifications"** in sidebar
3. **Accept/decline** pending invitations

## 🔧 Troubleshooting

### Can't See Business Selector?
**Issue:** Only have access to 1 business
**Solution:** Test user already has access to all businesses

### Permission Denied Error?
**Issue:** Missing invitation permissions
**Solution:** Test user already has proper permissions

### Need More Test Users?
Use these existing accounts:
```
<EMAIL> / password123 (Business Owner)
<EMAIL> / password123 (Business Owner)
<EMAIL> / password123 (Business Owner)
<EMAIL> / password123 (Staff Member)
```

## 📚 Full Documentation

For complete details, see:
- **[RBAC System Guide](./RBAC-SYSTEM-GUIDE.md)** - Complete user guide
- **[Developer Reference](./RBAC-DEVELOPER-REFERENCE.md)** - Technical documentation
- **[Login Guide](./login-guide.md)** - Basic authentication setup

## 🎉 You're Ready!

The RBAC system is fully configured and ready for testing. The test user has comprehensive permissions across all businesses, so you can immediately start:

- ✅ Inviting new users
- ✅ Assigning roles across businesses  
- ✅ Managing permissions
- ✅ Testing the notification system

Happy testing! 🚀
