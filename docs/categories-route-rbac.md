# Categories Route RBAC Documentation

## Overview

The `/categories` route implements Role-Based Access Control (RBAC) to ensure only authorized users can access category management functionality. This route requires specific permissions and redirects unauthorized users to the `/unauthorized` page.

## Required RBAC Permissions

### Primary Access Permission

- **`categories.read`** - Required for accessing the categories route and viewing category data
  - This permission is enforced by the `requirePermissions()` function in the route loader
  - Without this permission, users will be redirected to `/unauthorized` page
  - The permission check is performed at the business level using the user's default business

### Action-Specific Permissions

- **`categories.create`** - Required for creating new categories
  - Controls visibility and functionality of "Create Category" buttons
  - Enforced through client-side `Protected` component using `useRBAC` hook
  - Users without this permission will not see create buttons

- **`categories.edit`** - Required for editing existing categories
  - Controls visibility and functionality of "Edit" buttons in category table and detail sheet
  - Enforced through client-side `Protected` component using `useRBAC` hook
  - Users without this permission will not see edit buttons

- **`categories.delete`** - Required for deleting categories
  - Controls visibility and functionality of "Delete" buttons in category detail sheet
  - Enforced through client-side `Protected` component using `useRBAC` hook
  - Users without this permission will not see delete buttons

## RBAC Implementation

### Server-Side Protection

The route loader implements RBAC protection using the `requirePermissions` middleware:

```typescript
// Check RBAC permissions - redirect to unauthorized if user lacks categories.read permission
await requirePermissions(request, {
  permissions: ['categories.read'],
  businessId: business.id,
  redirectTo: '/unauthorized'
});
```

### Client-Side Protection

The route component implements granular RBAC protection using the `useRBAC` hook and `Protected` component:

```typescript
import { Protected } from "~/lib/hooks/useRBAC";

// Create Category buttons protected by categories.create permission
<Protected permissions={["categories.create"]}>
  <Button onClick={() => setIsCreating(true)}>
    <Plus className="mr-2 h-4 w-4" />
    {t('categories.actions.create')}
  </Button>
</Protected>

// Edit buttons protected by categories.edit permission
<Protected permissions={["categories.edit"]}>
  <Button onClick={() => onEdit(category)}>
    <Edit className="mr-2 h-4 w-4" />
    {t('categories.actions.edit')}
  </Button>
</Protected>

// Delete buttons protected by categories.delete permission
<Protected permissions={["categories.delete"]}>
  <Button variant="destructive" onClick={() => setDeletingCategory(category)}>
    <Trash2 className="mr-2 h-4 w-4" />
    {t('categories.actions.delete')}
  </Button>
</Protected>
```

### Implementation Details

1. **Authentication Check**: First verifies user session exists
2. **Business Context**: Retrieves user's default business
3. **Permission Validation**: Checks for `categories.read` permission in business context
4. **Redirect on Failure**: Automatically redirects to `/unauthorized` if permission is missing
5. **Data Loading**: Only proceeds to load category data if permission check passes

### Error Handling

- **No Session**: Returns 401 Unauthorized response
- **No Business**: Returns 404 Not Found response
- **Missing Permission**: Redirects to `/unauthorized` page
- **Server Errors**: Returns 500 Internal Server Error response

## Route Functionality

### What the Route Does

The categories route provides:
- View all categories across different types (ingredient, product, supplier, customer)
- Hierarchical category display with parent-child relationships
- Category filtering and search functionality
- Category management (create, edit, delete) through modal sheets with RBAC protection
- Category detail viewing with permission-based action buttons

### Data Loading

After permission validation, the route loads:
- All category types: `['ingredient', 'product', 'supplier', 'customer']`
- Hierarchical category structure using `CategoryServiceServer.getHierarchicalCategories()`
- Business information for context

## User Experience by Permission Level

### Full Access (has all permissions: `categories.read`, `categories.create`, `categories.edit`, `categories.delete`)
- Can access the categories route
- View all categories in hierarchical structure
- Use search and filtering functionality
- Create new categories using "Create Category" buttons
- Edit existing categories using "Edit" buttons
- Delete categories using "Delete" buttons in detail sheet
- View category details with all action buttons visible

### Read-Only Access (has `categories.read` only)
- Can access the categories route
- View all categories in hierarchical structure
- Use search and filtering functionality
- View category details
- Cannot see create, edit, or delete buttons (hidden by RBAC)

### Partial Access (has `categories.read` + some action permissions)
- Can access the categories route
- View all categories and use search/filtering
- See only the action buttons for permissions they have:
  - `categories.create`: Can see "Create Category" buttons
  - `categories.edit`: Can see "Edit" buttons
  - `categories.delete`: Can see "Delete" buttons in detail sheet

### No Access (missing `categories.read`)
- Cannot access the route at all
- Automatically redirected to `/unauthorized` page
- Sees clear "Access Denied" message with explanation
- Provided with navigation options to return to dashboard or go back

## Security Features

### Business Context Validation
- Permission check is scoped to user's default business
- Ensures users can only access categories for businesses they have access to
- Business ownership and role-based permissions are respected

### Graceful Error Handling
- Clear error messages for different failure scenarios
- Proper HTTP status codes for different error types
- User-friendly unauthorized page with helpful guidance

## Related Components

### Unauthorized Page (`/unauthorized`)
- Displays clear access denied message
- Provides navigation options (Dashboard, Go Back)
- Consistent UI with proper error styling
- Includes contact information for permission requests

### Category Service
- `CategoryServiceServer.getHierarchicalCategories()` also performs permission checks
- Throws "Access denied: insufficient permissions" error if user lacks `categories.read`
- This provides defense-in-depth security

## Permission Hierarchy

### Business-Specific Roles
- **`business_owner`**: Typically has all category permissions
- **`category_manager`**: Specialized role for category management
- **`staff`**: Limited permissions based on role configuration
- **`viewer`**: Read-only access (includes `categories.read`)

### Permission Inheritance
Permissions are granted through:
1. **Direct Role Assignment**: User assigned to role with category permissions
2. **Business Ownership**: Business owners inherit all permissions
3. **Custom Role Permissions**: Custom roles with specific category permissions

## Usage Examples

### Accessing the Route
```
GET /categories
```
**Required**: User must have `categories.read` permission for their default business

### Expected Responses
- **200 OK**: User has permission, categories page loads
- **302 Redirect**: User lacks permission, redirected to `/unauthorized`
- **401 Unauthorized**: User not authenticated
- **404 Not Found**: User has no default business

## Implementation Notes

### Route Configuration
- **File**: `app/routes/categories.tsx`
- **Protection Method**: `requirePermissions` middleware
- **Permission**: `categories.read`
- **Business Context**: User's default business
- **Redirect Target**: `/unauthorized`

### Security Considerations
- Permission check occurs before any data loading
- Business context is properly validated
- Error responses don't leak sensitive information
- Consistent with other RBAC-protected routes

## Summary

The `/categories` route implements comprehensive RBAC protection through:

1. **Server-side Protection**: `requirePermissions` with `categories.read` permission requirement
2. **Business Context**: Automatic business validation and scoping
3. **Graceful Redirects**: Unauthorized users redirected to helpful error page
4. **Defense in Depth**: Both route-level and service-level permission checks
5. **Clear Error Handling**: Appropriate responses for different failure scenarios

### Permission Requirements
- **Mandatory**: `categories.read` - Required for route access
- **Optional**: `categories.create` - Required for creating new categories
- **Optional**: `categories.edit` - Required for editing existing categories
- **Optional**: `categories.delete` - Required for deleting categories

### User Experience
- **With all permissions**: Full category management functionality
- **With read-only**: Can view categories but no management actions
- **With partial permissions**: Can perform only allowed actions
- **Without read permission**: Redirected to clear unauthorized page with guidance

### Implementation Status
- ✅ **Route-level RBAC**: Fully implemented with proper redirects
- ✅ **Client-side RBAC**: Action buttons protected with `Protected` component
- ✅ **Granular permissions**: Create, edit, and delete permissions enforced
- ✅ **Error handling**: Comprehensive error responses
- ✅ **User experience**: Clear unauthorized page with navigation options

This ensures that only authorized users can access category data while providing a clear and helpful experience for unauthorized users.