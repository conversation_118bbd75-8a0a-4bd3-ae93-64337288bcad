# Permissions Reference Guide

This document provides a comprehensive reference for all system permissions, explaining their purpose, usage, and implementation details.

## Business Management Permissions

### business.read

- **Name**: `business.read`
- **Display Name**: "View Business"
- **Description**: "View business information"
- **Purpose**: This permission allows users to access and view basic business data and is required for most business-specific routes

#### Usage Details

- **Required For**: 
  - Accessing business-specific routes (`/business/:businessId`)
  - Viewing business settings (`/business/:businessId/settings`)
  - Core RBAC functions like `canAccessBusiness()` and `requireBusinessAccess()`

- **Implementation**: 
  - Enforced by `requireBusinessAccess()` helper function
  - Checked in UI route protection
  - Used in business switcher access validation

- **Role Assignments**:
  - `super_admin`: ✅ Has permission
  - `business_owner`: ✅ Has permission
  - `business_manager`: ✅ Has permission
  - `staff`: ✅ Has permission
  - `viewer`: ✅ Has permission

- **Notes**: 
  - This is a fundamental permission required for basic business access
  - Users without this permission cannot access business-specific functionality
  - There's a potential mismatch where `getUserBusinesses()` allows users with any role to see businesses in the switcher, but `canAccessBusiness()` explicitly requires this permission

---

## Additional Permissions

*This section will be expanded with more permission details as needed.*

### How to Add New Permissions to This Guide

1. Follow the template format shown above for `business.read`
2. Include all relevant usage details and role assignments
3. Add implementation notes and any known issues
4. Update the table of contents if adding new categories

### Related Documentation

- [RBAC System Guide](./RBAC-SYSTEM-GUIDE.md) - Overview of the role-based access control system
- [RBAC Developer Reference](./RBAC-DEVELOPER-REFERENCE.md) - Technical implementation details
- [API Endpoint Best Practices](./API-ENDPOINT-BEST-PRACTICES.md) - Permission implementation in API routes
- [Route Creation Checklist](./ROUTE-CREATION-CHECKLIST.md) - Adding permissions to new routes

### Permission Seed Data

For the complete technical definition of all permissions, see `rbac-seed.ts` in the project root.