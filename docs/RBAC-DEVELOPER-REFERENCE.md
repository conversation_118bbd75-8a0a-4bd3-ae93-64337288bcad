# RBAC System Developer Reference

## Architecture Overview

### Core Components
- **RBACService** - Main service for role and permission management
- **InvitationService** - Handles user invitation workflow
- **RBAC Routes** - UI components for role management
- **Notification System** - Real-time invitation notifications

### Database Schema
```sql
-- Core RBAC Tables
users              -- User accounts
businesses         -- Business entities
roles              -- Available roles (system + custom)
permissions        -- Available permissions
user_roles         -- User-role assignments per business
role_permissions   -- Role-permission mappings
user_invitations   -- Pending user invitations
```

## Service Layer APIs

### RBACService Methods

#### User Business Access
```typescript
// Get all businesses user has access to
await RBACService.getUserBusinesses(userId: string)

// Check if user can access specific business
await RBACService.canAccessBusiness(userId: string, businessId: string)
```

#### Role Management
```typescript
// Check if user has specific role in business
await RBACService.hasRole(userId: string, roleName: string, businessId: string)

// Assign role to user in business
await RBACService.assignRole(userId: string, roleName: string, assignedBy: string, businessId: string)

// Remove role from user in business
await RBACService.removeRole(userId: string, roleName: string, businessId?: string)
```

#### Permission Checking
```typescript
// Check if user has any of the specified permissions
await RBACService.hasAnyPermission(userId: string, permissions: string[], businessId: string)

// Get all user permissions in business
await RBACService.getUserPermissions(userId: string, businessId: string)
```

### InvitationService Methods

#### Creating Invitations
```typescript
// Create new user invitation
await InvitationService.createInvitation({
  email: string,
  businessId: string,
  roleName: string,
  invitedBy: string
})
```

#### Managing Invitations
```typescript
// Get pending invitations for user
await InvitationService.getUserPendingInvitations(email: string)

// Accept invitation by ID
await InvitationService.acceptInvitationById(invitationId: string, userId: string)

// Decline invitation
await InvitationService.declineInvitation(invitationId: string)

// Get invitation count for notification badge
await InvitationService.getUserInvitationCount(email: string)
```

## Frontend Components

### RBAC Management Interface
**File:** `app/routes/business.$businessId.settings.rbac.tsx`

**Key Features:**
- Cross-business role assignment
- User invitation form
- Pending invitations management
- Permission matrix display

**State Management:**
```typescript
const [targetBusinessId, setTargetBusinessId] = useState<string>(businessId);
const [inviteTargetBusinessId, setInviteTargetBusinessId] = useState<string>(businessId);
```

### Notification System
**File:** `app/routes/notifications.tsx`

**Key Features:**
- Pending invitation display
- Accept/decline actions
- Real-time status updates
- Responsive design

## Permission System

### Permission Categories
```typescript
// Business Management
'business.read', 'business.update', 'business.delete', 'business.manage_users'

// Inventory Management  
'inventory.read', 'inventory.create', 'inventory.update', 'inventory.delete'

// Product Management
'products.read', 'products.create', 'products.update', 'products.delete'

// User Management
'users.read', 'users.create', 'users.update', 'users.delete', 'users.assign_roles'

// Role Management
'roles.read', 'roles.create', 'roles.update', 'roles.delete'
```

### Role Definitions
```typescript
// System Roles (defined in rbac-seed.ts)
const rolePermissionAssignments = {
  business_owner: [
    'business.read', 'business.update', 'business.manage_users',
    'inventory.*', 'products.*', 'ingredients.*', 'categories.*',
    'users.read', 'users.assign_roles', 'roles.read'
  ],
  
  business_manager: [
    'business.read', 'business.manage_users',
    'inventory.*', 'products.*', 'ingredients.*', 'categories.*',
    'users.read', 'users.assign_roles'
  ],
  
  staff: [
    'business.read', 'inventory.read', 'inventory.create', 'inventory.update',
    'products.read', 'products.create', 'products.update',
    'ingredients.read', 'ingredients.create', 'ingredients.update'
  ]
}
```

## UI Logic Patterns

### Business Selector Visibility
```typescript
// Dropdown only shows when user has access to multiple businesses
{accessibleBusinesses.length > 1 && (
  <Select value={targetBusinessId} onValueChange={setTargetBusinessId}>
    {accessibleBusinesses.map(business => (
      <SelectItem key={business.id} value={business.id}>
        {business.name}
      </SelectItem>
    ))}
  </Select>
)}
```

### Permission-Based UI Rendering
```typescript
// Check permissions before showing UI elements
const canInviteUsers = await RBACService.hasAnyPermission(
  userId, 
  ['business.manage_users', 'users.assign_roles'], 
  businessId
) || await RBACService.hasRole(userId, 'business_owner', businessId);

{canInviteUsers && (
  <InviteUserForm />
)}
```

### Loading States
```typescript
// Use Remix navigation state for loading indicators
const navigation = useNavigation();
const isSubmitting = navigation.state === 'submitting';
const submittingInvitationId = navigation.formData?.get('invitationId') as string;

<Button disabled={isSubmitting && submittingInvitationId === invitation.id}>
  {isSubmitting ? 'Accepting...' : 'Accept'}
</Button>
```

## Database Queries

### Common RBAC Queries
```typescript
// Get user roles in business
const userRoles = await db
  .select({
    roleName: roles.name,
    roleDisplayName: roles.displayName,
  })
  .from(userRoles)
  .innerJoin(roles, eq(userRoles.roleId, roles.id))
  .where(
    and(
      eq(userRoles.userId, userId),
      eq(userRoles.businessId, businessId),
      eq(userRoles.isActive, true)
    )
  );

// Get role permissions
const rolePermissions = await db
  .select({
    permissionName: permissions.name,
  })
  .from(rolePermissions)
  .innerJoin(permissions, eq(rolePermissions.permissionId, permissions.id))
  .where(eq(rolePermissions.roleId, roleId));
```

### Invitation Queries
```typescript
// Get pending invitations for user
const pendingInvitations = await db
  .select({
    id: userInvitations.id,
    businessName: businesses.name,
    roleDisplayName: roles.displayName,
    status: userInvitations.status,
    expiresAt: userInvitations.expiresAt,
  })
  .from(userInvitations)
  .innerJoin(businesses, eq(userInvitations.businessId, businesses.id))
  .innerJoin(roles, eq(userInvitations.roleId, roles.id))
  .where(
    and(
      eq(userInvitations.email, email),
      eq(userInvitations.status, 'pending'),
      gt(userInvitations.expiresAt, new Date())
    )
  );
```

## Testing Utilities

### Role Assignment Scripts
```bash
# Assign specific role to test user
bun run scripts/assign-test-user-role.ts [roleName] [businessName]

# Examples:
bun run scripts/assign-test-user-role.ts business_owner "KWACI Coffee House"
bun run scripts/assign-test-user-role.ts business_manager "Brew & Bean Cafe"
bun run scripts/assign-test-user-role.ts staff "Morning Glory Coffee"
```

### Diagnostic Scripts
```bash
# Check user permissions across all businesses
bun run diagnose-invitation-permissions.ts

# Verify business access levels
bun run check-accessible-businesses.ts

# Validate RBAC seeding
bun run app/lib/db/rbac-seed.ts
```

### Test Data Creation
```typescript
// Create test invitation
const invitationToken = await InvitationService.createInvitation({
  email: '<EMAIL>',
  businessId: 'business-uuid',
  roleName: 'staff',
  invitedBy: 'admin-user-uuid',
});

// Create test user with roles
await RBACService.assignRole(userId, 'business_manager', adminId, businessId);
```

## Error Handling

### Common Error Patterns
```typescript
// Permission validation
try {
  const canManageRoles = await RBACService.hasAnyPermission(
    currentUserId,
    ['business.manage_users', 'users.assign_roles'],
    targetBusinessId
  );
  
  if (!canManageRoles) {
    return json({
      error: 'You do not have permission to invite users to the selected business'
    }, { status: 403 });
  }
} catch (error) {
  return json({
    error: 'You do not have access to the selected business'
  }, { status: 403 });
}
```

### Frontend Error Display
```typescript
// Action data error handling
{actionData && 'error' in actionData && (
  <Alert variant="destructive">
    <AlertTriangle className="h-4 w-4" />
    <AlertDescription>{actionData.error}</AlertDescription>
  </Alert>
)}

// Success feedback
{actionData && 'success' in actionData && (
  <Alert className="border-green-200 bg-green-50">
    <Check className="h-4 w-4 text-green-600" />
    <AlertDescription className="text-green-800">
      {actionData.success}
    </AlertDescription>
  </Alert>
)}
```

## Performance Considerations

### Caching Strategies
- **User permissions** - Cache per business context
- **Business access** - Cache user business list
- **Role definitions** - Cache system roles and permissions

### Query Optimization
- **Batch permission checks** - Check multiple permissions in single query
- **Eager loading** - Include related data in initial queries
- **Index usage** - Ensure proper indexing on user_roles and role_permissions

### Real-time Updates
- **Notification badges** - Update every 5 minutes
- **Permission changes** - Invalidate cache on role assignment
- **Business access** - Refresh on business role changes

## Security Considerations

### Permission Validation
- **Server-side validation** - Always validate permissions on server
- **Business context** - Ensure business ID matches user access
- **Role hierarchy** - Respect role hierarchy in assignments

### Invitation Security
- **Token expiration** - 7-day expiration for invitations
- **Unique tokens** - Cryptographically secure invitation tokens
- **Email validation** - Validate email format and domain

### Access Control
- **Business isolation** - Users can only access assigned businesses
- **Role restrictions** - Users can only assign roles they have permission for
- **Audit logging** - Log all role assignments and permission changes

## Deployment Notes

### Environment Variables
```env
# Database connection for RBAC
DATABASE_URL=postgresql://...

# Better Auth configuration
BETTER_AUTH_SECRET=...
BETTER_AUTH_URL=...
```

### Migration Requirements
```bash
# Run RBAC seeding after schema changes
bun run app/lib/db/rbac-seed.ts

# Migrate existing business owners
bun run app/lib/db/migrate-business-owners.ts
```

### Monitoring
- **Permission errors** - Monitor 403 responses
- **Invitation metrics** - Track invitation acceptance rates
- **Performance** - Monitor RBAC query performance
