# Database Seeding with Dr<PERSON>zle ORM

This document explains how to use the database seeding system implemented using Dr<PERSON>zle ORM's official seeding approach.

## Overview

The seeding system uses the official `drizzle-seed` package to populate the database with realistic test data for development and testing purposes. It follows <PERSON><PERSON><PERSON>'s recommended patterns and best practices.

## Architecture

### Schema Relationships

The seeding system properly handles foreign key relationships:

- **Users** → **Businesses** (one-to-many)
- **Businesses** → **Categories** (one-to-many)
- **Categories** → **Ingredients** (one-to-many, optional)
- **Categories** → **Products** (one-to-many, optional)

### Category System

The system creates two types of categories:

#### Ingredient Categories
- Coffee Beans
- Dairy
- Sweeteners
- Spices & Flavorings
- Alternative Milks
- Toppings
- Tea
- Baking Ingredients

#### Product Categories
- Hot Beverages
- Cold Beverages
- Pastries
- Snacks
- Specialty Drinks
- Desserts
- Breakfast Items
- Lunch Items

## Usage

### Available Scripts

```bash
# Seed the database with test data
bun run db:seed

# Reset database and run seeding (includes migrations)
bun run db:reset
```

### Manual Seeding

You can also run the seed file directly:

```bash
bun run app/lib/db/seed.ts
```

## Generated Data

The seeding process creates:

- **3 Users**: Sample business owners
- **3 Businesses**: Different coffee shop businesses
- **48 Categories**: 16 category types × 3 businesses
- **48 Ingredients**: 16 ingredient types × 3 businesses
- **30 Products**: 10 product types × 3 businesses

### Sample Data Examples

#### Businesses
- KWACI Coffee House
- Brew & Bean Cafe
- Morning Glory Coffee

#### Sample Ingredients
- Arabica Coffee Beans
- Robusta Coffee Beans
- Espresso Blend
- Whole Milk, Skim Milk, Heavy Cream
- Almond Milk, Oat Milk, Soy Milk
- White Sugar, Brown Sugar
- Vanilla Syrup, Caramel Syrup
- Cinnamon Powder, Vanilla Extract, Cocoa Powder

#### Sample Products
- Espresso, Americano, Cappuccino, Latte
- Iced Coffee, Cold Brew, Iced Latte
- Croissant, Blueberry Muffin, Chocolate Croissant

## Implementation Details

### Database Reset Strategy

The seeding system uses Drizzle's `reset` function which:

- **PostgreSQL**: Uses `TRUNCATE ... CASCADE`
- **MySQL**: Disables foreign key checks, truncates tables, re-enables checks
- **SQLite**: Disables foreign keys, deletes data, re-enables foreign keys

### Seeding Process

1. **Reset Database**: Clean slate using `reset(db, schema)`
2. **Create Users**: Manual insertion with predefined user data
3. **Create Businesses**: Manual insertion linked to users
4. **Seed RBAC Data**: Create roles and permissions with validation
5. **Assign Business Owner Roles**: Link users to businesses with proper permissions
6. **Create Categories**: Manual insertion for each business
7. **Create Ingredients**: Manual insertion with proper category references
8. **Create Products**: Manual insertion with proper category references

### RBAC Validation (New Feature)

The seeding process now includes comprehensive RBAC validation:

```typescript
async function validateRBACSeeding() {
  // 1. Verify business_owner role exists
  const businessOwnerRole = await db.select().from(roles)
    .where(eq(roles.name, 'business_owner')).limit(1);
  
  if (businessOwnerRole.length === 0) {
    throw new Error('business_owner role not found after seeding');
  }

  // 2. Verify role has permissions assigned
  const permissions = await db.select()
    .from(rolePermissions)
    .where(eq(rolePermissions.roleId, businessOwnerRole[0].id));
  
  if (permissions.length === 0) {
    throw new Error('business_owner role has no permissions assigned');
  }

  // 3. Verify essential permissions are present
  const essentialPermissions = ['business.read', 'business.update', 'business.manage_users'];
  // ... validation logic
}
```

**Benefits:**
- **Fail Fast**: Seeding stops immediately if RBAC issues are detected
- **Clear Error Messages**: Specific error messages help identify the exact problem
- **Prevents Silent Failures**: No more situations where roles exist but have no permissions
- **Validation at Each Step**: Role assignments are validated immediately after creation

### Data Generation

The system combines:
- **Manual data creation** for core entities (users, businesses, categories)
- **Realistic sample data** for ingredients and products
- **Proper foreign key relationships** throughout the data hierarchy

## Configuration

### Environment Variables

Ensure your `.env` file contains:

```env
DATABASE_URL="your-postgresql-connection-string"
```

### Customization

To customize the seed data:

1. **Edit categories**: Modify `ingredientCategories` and `productCategories` arrays
2. **Edit sample data**: Update `sampleIngredients` and `sampleProducts` arrays
3. **Adjust quantities**: Change the count values in the seeding loops

## Best Practices

1. **Always reset before seeding**: Use `reset()` to ensure clean state
2. **Handle foreign keys properly**: Create parent entities before children
3. **Use realistic data**: Provide meaningful sample data for testing
4. **Maintain relationships**: Ensure all foreign key references are valid
5. **Test thoroughly**: Verify seeding works across different environments

## Troubleshooting

### Common Issues

1. **Connection Errors**
   - Ensure PostgreSQL is running
   - Check database credentials in `.env`
   - Verify database exists

2. **Constraint Violations**
   - Usually caused by existing data
   - Run `bun run db:reset` to clean slate
   - Check for unique constraint conflicts

3. **Missing Dependencies**
   - Ensure all npm packages are installed
   - Check import paths in seed files
   - Verify database schema is up to date

4. **Performance Issues**
   - Large datasets may take time
   - Consider reducing sample data size
   - Monitor database connection limits

5. **RBAC Seeding Issues**
   - **Symptoms**: Users can't access protected routes, "Access Denied" errors
   - **Cause**: RBAC roles/permissions not properly seeded or assigned
   - **Solution**: 
     ```bash
     # Re-run complete seeding process
     bun run db:reset
     
     # Or manually fix RBAC issues
     bun run app/lib/db/rbac-seed.ts
     
     # Validate RBAC data
     bun run rbac-health-check.js
     ```
   - **Prevention**: The seed script now includes automatic RBAC validation
   - See [RBAC Troubleshooting Guide](./rbac-troubleshooting-guide.md) for detailed solutions

6. **Business Owner Role Issues**
   - **Symptoms**: Business owners can't access RBAC settings
   - **Cause**: Role exists but has no permissions assigned
   - **Solution**: Use the manual permission assignment script from troubleshooting guide
   - **Prevention**: Seeding now validates role assignments and fails fast if issues occur

### Debugging

Enable detailed logging by checking the console output during seeding. The process provides step-by-step feedback on what's being created.

## Integration with Development Workflow

### When to Seed

- After setting up a new development environment
- Before running integration tests
- When you need fresh test data
- After major schema changes

### CI/CD Integration

The seeding scripts can be integrated into your CI/CD pipeline for automated testing environments.

## Future Enhancements

Possible improvements to the seeding system:

1. **Configurable data volumes**: Environment-based data quantities
2. **Seed data variants**: Different data sets for different scenarios
3. **Incremental seeding**: Add data without full reset
4. **Data relationships**: Product-ingredient relationships (recipes)
5. **Performance optimization**: Batch insertions for large datasets
6. **Localization**: Multi-language seed data
7. **Business logic**: COGS calculations and inventory tracking
