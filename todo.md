- [x] Run this script in your Supabase SQL editor or PostgreSQL database: database/schema.sql
- [x] use react hook form for all form related
- [x] add [react query](https://tanstack.com/query/latest). implement performant data fetching with automatic caching, background updates, and more.
- [x] add kwaci grow logo
- [x] implement permission creation based on available endpoint
- [ ] fix Usage in ingredient List (not showing correct usage amount)
- [x] implement useRBAC for product route to show edit and add product based on permission
- [ ] implement add staff . need to create actual user using register endpoint and then can apply role to the staff. need to add rbac permission to the UI and server to
be able to use this.

NOTE
```
curl -c /tmp/cookies.txt -b /tmp/cookies.txt -X POST http://localhost:5173/api/auth/sign-in/email -H "Content-Type: application/json" -d '{"email":"<EMAIL>","password":"password123"}' && curl -b /tmp/cookies.txt http://localhost:5173/business/26b99509-b432-4e5d-8930-c8bc28c8ed0e/settings/rbac 


✅ FIXED: Business 404 Issue

Found and fixed the inconsistency in business access methods:
- BusinessServiceServer.getAllByUser() was using RBAC to return businesses user owns OR has role access to
- BusinessServiceServer.getByIdAndUser() was only checking direct ownership (businesses.userId = userId)

This caused 404 errors when users with role-based access (but not ownership) tried to access business routes directly.

Fix: Updated getByIdAndUser() to use the same RBAC logic as getAllByUser() for consistent access control.
```