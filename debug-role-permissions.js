import { db } from './app/lib/db/connection.js';
import { roles, permissions, rolePermissions } from './app/lib/db/schema.js';
import { eq } from 'drizzle-orm';

console.log('Checking business_owner role and its permissions...');

// Check if business_owner role exists
const businessOwnerRole = await db
  .select()
  .from(roles)
  .where(eq(roles.name, 'business_owner'));

console.log('Business owner role:', JSON.stringify(businessOwnerRole, null, 2));

if (businessOwnerRole.length > 0) {
  // Check permissions assigned to business_owner role
  const rolePerms = await db
    .select({
      roleName: roles.name,
      permissionName: permissions.name,
      permissionResource: permissions.resource,
      permissionAction: permissions.action
    })
    .from(rolePermissions)
    .innerJoin(roles, eq(rolePermissions.roleId, roles.id))
    .innerJoin(permissions, eq(rolePermissions.permissionId, permissions.id))
    .where(eq(roles.name, 'business_owner'));

  console.log('Business owner permissions:', JSON.stringify(rolePerms, null, 2));
}