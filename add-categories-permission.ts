import { db } from './app/lib/db/connection';
import { roles, permissions, rolePermissions } from './app/lib/db/schema';
import { eq, and } from 'drizzle-orm';

async function addCategoriesPermissionToTestRole() {
  console.log('🔧 Adding categories.read permission to TEST_READ_CREATE_INGREDIENT role...');
  
  // Find the TEST_READ_CREATE_INGREDIENT role
  const [role] = await db.select().from(roles).where(eq(roles.name, 'TEST_READ_CREATE_INGREDIENT')).limit(1);
  
  if (!role) {
    console.log('❌ TEST_READ_CREATE_INGREDIENT role not found!');
    return;
  }
  
  console.log('✅ Found role:', role.name, '(ID:', role.id + ')');
  
  // Find the categories.read permission
  const [permission] = await db.select().from(permissions).where(eq(permissions.name, 'categories.read')).limit(1);
  
  if (!permission) {
    console.log('❌ categories.read permission not found!');
    return;
  }
  
  console.log('✅ Found permission:', permission.name, '(ID:', permission.id + ')');
  
  // Check if the role already has this permission
  const [existing] = await db.select().from(rolePermissions)
    .where(and(
      eq(rolePermissions.roleId, role.id),
      eq(rolePermissions.permissionId, permission.id)
    ))
    .limit(1);
  
  if (existing) {
    console.log('ℹ️ Role already has categories.read permission');
    return;
  }
  
  // Add the permission to the role
  await db.insert(rolePermissions).values({
    roleId: role.id,
    permissionId: permission.id
  });
  
  console.log('✅ Successfully added categories.read permission to TEST_READ_CREATE_INGREDIENT role');
  
  // Verify the addition
  const [verification] = await db.select().from(rolePermissions)
    .where(and(
      eq(rolePermissions.roleId, role.id),
      eq(rolePermissions.permissionId, permission.id)
    ))
    .limit(1);
  
  if (verification) {
    console.log('✅ Verification successful - permission assignment confirmed');
  } else {
    console.log('❌ Verification failed - permission assignment not found');
  }
}

addCategoriesPermissionToTestRole().catch(console.error).finally(() => process.exit(0));