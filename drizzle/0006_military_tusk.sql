CREATE TABLE "user_invitation" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"email" text NOT NULL,
	"businessId" uuid NOT NULL,
	"roleId" uuid NOT NULL,
	"invitedBy" text NOT NULL,
	"invitationToken" text NOT NULL,
	"status" text DEFAULT 'pending' NOT NULL,
	"expiresAt" timestamp NOT NULL,
	"acceptedAt" timestamp,
	"acceptedBy" text,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "user_invitation_invitationToken_unique" UNIQUE("invitationToken")
);
--> statement-breakpoint
ALTER TABLE "user_invitation" ADD CONSTRAINT "user_invitation_businessId_business_id_fk" FOREIGN KEY ("businessId") REFERENCES "public"."business"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_invitation" ADD CONSTRAINT "user_invitation_roleId_role_id_fk" FOREIGN KEY ("roleId") REFERENCES "public"."role"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_invitation" ADD CONSTRAINT "user_invitation_invitedBy_user_id_fk" FOREIGN KEY ("invitedBy") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_invitation" ADD CONSTRAINT "user_invitation_acceptedBy_user_id_fk" FOREIGN KEY ("acceptedBy") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "idx_invitation_email" ON "user_invitation" USING btree ("email");--> statement-breakpoint
CREATE INDEX "idx_invitation_business" ON "user_invitation" USING btree ("businessId");--> statement-breakpoint
CREATE INDEX "idx_invitation_token" ON "user_invitation" USING btree ("invitationToken");--> statement-breakpoint
CREATE INDEX "idx_invitation_status" ON "user_invitation" USING btree ("status");--> statement-breakpoint
CREATE INDEX "idx_invitation_expires" ON "user_invitation" USING btree ("expiresAt");