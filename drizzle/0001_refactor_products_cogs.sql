-- Migration: Refactor products table to use COGS instead of selling price
-- Remove sellingPrice, add note, isActive, and cogsPerCup fields

-- Add new columns
ALTER TABLE "product" ADD COLUMN "note" text;
ALTER TABLE "product" ADD COLUMN "isActive" boolean DEFAULT true NOT NULL;
ALTER TABLE "product" ADD COLUMN "cogsPerCup" numeric(10, 2);

-- Remove the old sellingPrice column
ALTER TABLE "product" DROP COLUMN "sellingPrice";

-- Remove the old status column (replaced by isActive)
ALTER TABLE "product" DROP COLUMN "status";
