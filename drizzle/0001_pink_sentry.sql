CREATE TABLE "category" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"type" text NOT NULL,
	"parentId" uuid,
	"color" text,
	"sortOrder" numeric(5, 0) DEFAULT '0',
	"isActive" boolean DEFAULT true NOT NULL,
	"businessId" uuid NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "category_business_type_name_unique" UNIQUE("businessId","type","name")
);
--> statement-breakpoint
CREATE TABLE "product_cogs" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"productId" uuid NOT NULL,
	"businessId" uuid NOT NULL,
	"ingredientCosts" numeric(12, 4) DEFAULT '0' NOT NULL,
	"laborCosts" numeric(12, 4) DEFAULT '0' NOT NULL,
	"overheadCosts" numeric(12, 4) DEFAULT '0' NOT NULL,
	"totalCogs" numeric(12, 4) DEFAULT '0' NOT NULL,
	"calculationDate" timestamp DEFAULT now() NOT NULL,
	"calculationMethod" text DEFAULT 'automatic' NOT NULL,
	"isActive" boolean DEFAULT true NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "product_cogs_product_active_unique" UNIQUE("productId","isActive")
);
--> statement-breakpoint
CREATE TABLE "product_cogs_history" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"productCogsId" uuid NOT NULL,
	"productId" uuid NOT NULL,
	"businessId" uuid NOT NULL,
	"previousIngredientCosts" numeric(12, 4),
	"previousLaborCosts" numeric(12, 4),
	"previousOverheadCosts" numeric(12, 4),
	"previousTotalCogs" numeric(12, 4),
	"newIngredientCosts" numeric(12, 4) NOT NULL,
	"newLaborCosts" numeric(12, 4) NOT NULL,
	"newOverheadCosts" numeric(12, 4) NOT NULL,
	"newTotalCogs" numeric(12, 4) NOT NULL,
	"changeReason" text,
	"changeDescription" text,
	"triggeredBy" text,
	"createdAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "category" ADD CONSTRAINT "category_businessId_business_id_fk" FOREIGN KEY ("businessId") REFERENCES "public"."business"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "product_cogs" ADD CONSTRAINT "product_cogs_productId_product_id_fk" FOREIGN KEY ("productId") REFERENCES "public"."product"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "product_cogs" ADD CONSTRAINT "product_cogs_businessId_business_id_fk" FOREIGN KEY ("businessId") REFERENCES "public"."business"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "product_cogs_history" ADD CONSTRAINT "product_cogs_history_productCogsId_product_cogs_id_fk" FOREIGN KEY ("productCogsId") REFERENCES "public"."product_cogs"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "product_cogs_history" ADD CONSTRAINT "product_cogs_history_productId_product_id_fk" FOREIGN KEY ("productId") REFERENCES "public"."product"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "product_cogs_history" ADD CONSTRAINT "product_cogs_history_businessId_business_id_fk" FOREIGN KEY ("businessId") REFERENCES "public"."business"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "category_business_id_idx" ON "category" USING btree ("businessId");--> statement-breakpoint
CREATE INDEX "category_type_idx" ON "category" USING btree ("type");--> statement-breakpoint
CREATE INDEX "product_cogs_product_id_idx" ON "product_cogs" USING btree ("productId");--> statement-breakpoint
CREATE INDEX "product_cogs_business_id_idx" ON "product_cogs" USING btree ("businessId");--> statement-breakpoint
CREATE INDEX "product_cogs_calculation_date_idx" ON "product_cogs" USING btree ("calculationDate");--> statement-breakpoint
CREATE INDEX "product_cogs_history_product_id_idx" ON "product_cogs_history" USING btree ("productId");--> statement-breakpoint
CREATE INDEX "product_cogs_history_business_id_idx" ON "product_cogs_history" USING btree ("businessId");--> statement-breakpoint
CREATE INDEX "product_cogs_history_created_at_idx" ON "product_cogs_history" USING btree ("createdAt");