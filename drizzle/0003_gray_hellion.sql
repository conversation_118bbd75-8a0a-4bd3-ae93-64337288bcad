ALTER TABLE "ingredient" ADD COLUMN "categoryId" uuid;--> statement-breakpoint
ALTER TABLE "product" ADD COLUMN "categoryId" uuid;--> statement-breakpoint
ALTER TABLE "ingredient" ADD CONSTRAINT "ingredient_categoryId_category_id_fk" FOREIGN KEY ("categoryId") REFERENCES "public"."category"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "product" ADD CONSTRAINT "product_categoryId_category_id_fk" FOREIGN KEY ("categoryId") REFERENCES "public"."category"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "ingredient" DROP COLUMN "category";--> statement-breakpoint
ALTER TABLE "product" DROP COLUMN "category";