{"id": "f46b42f3-e945-4bde-8251-94e76ba697f9", "prevId": "8cdf51a6-a7a2-439c-a6ca-b153990c143a", "version": "7", "dialect": "postgresql", "tables": {"public.account": {"name": "account", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "accountId": {"name": "accountId", "type": "text", "primaryKey": false, "notNull": true}, "providerId": {"name": "providerId", "type": "text", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "accessToken": {"name": "accessToken", "type": "text", "primaryKey": false, "notNull": false}, "refreshToken": {"name": "refreshToken", "type": "text", "primaryKey": false, "notNull": false}, "idToken": {"name": "idToken", "type": "text", "primaryKey": false, "notNull": false}, "accessTokenExpiresAt": {"name": "accessTokenExpiresAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "refreshTokenExpiresAt": {"name": "refreshTokenExpiresAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_account_userId": {"name": "idx_account_userId", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_account_providerId": {"name": "idx_account_providerId", "columns": [{"expression": "providerId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"account_userId_user_id_fk": {"name": "account_userId_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.business": {"name": "business", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "note": {"name": "note", "type": "text", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "default": "'IDR'"}, "logo": {"name": "logo", "type": "text", "primaryKey": false, "notNull": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"business_user_id_idx": {"name": "business_user_id_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"business_userId_user_id_fk": {"name": "business_userId_user_id_fk", "tableFrom": "business", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"business_user_name_unique": {"name": "business_user_name_unique", "nullsNotDistinct": false, "columns": ["userId", "name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.category": {"name": "category", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "parentId": {"name": "parentId", "type": "uuid", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": false}, "sortOrder": {"name": "sortOrder", "type": "numeric(5, 0)", "primaryKey": false, "notNull": false, "default": "'0'"}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "businessId": {"name": "businessId", "type": "uuid", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"category_business_id_idx": {"name": "category_business_id_idx", "columns": [{"expression": "businessId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "category_type_idx": {"name": "category_type_idx", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"category_businessId_business_id_fk": {"name": "category_businessId_business_id_fk", "tableFrom": "category", "tableTo": "business", "columnsFrom": ["businessId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"category_business_type_name_unique": {"name": "category_business_type_name_unique", "nullsNotDistinct": false, "columns": ["businessId", "type", "name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ingredient": {"name": "ingredient", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "baseUnitCost": {"name": "baseUnitCost", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "baseUnitQuantity": {"name": "baseUnitQuantity", "type": "numeric(10, 4)", "primaryKey": false, "notNull": true, "default": "'1'"}, "unit": {"name": "unit", "type": "text", "primaryKey": false, "notNull": true}, "categoryId": {"name": "categoryId", "type": "uuid", "primaryKey": false, "notNull": false}, "supplierInfo": {"name": "supplierInfo", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "businessId": {"name": "businessId", "type": "uuid", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"ingredient_business_id_idx": {"name": "ingredient_business_id_idx", "columns": [{"expression": "businessId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"ingredient_categoryId_category_id_fk": {"name": "ingredient_categoryId_category_id_fk", "tableFrom": "ingredient", "tableTo": "category", "columnsFrom": ["categoryId"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "ingredient_businessId_business_id_fk": {"name": "ingredient_businessId_business_id_fk", "tableFrom": "ingredient", "tableTo": "business", "columnsFrom": ["businessId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"ingredient_business_name_unique": {"name": "ingredient_business_name_unique", "nullsNotDistinct": false, "columns": ["businessId", "name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.permission": {"name": "permission", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "displayName": {"name": "displayName", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "resource": {"name": "resource", "type": "text", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "text", "primaryKey": false, "notNull": true}, "isSystemPermission": {"name": "isSystemPermission", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_permission_resource_action": {"name": "idx_permission_resource_action", "columns": [{"expression": "resource", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "action", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"permission_name_unique": {"name": "permission_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.product_cogs": {"name": "product_cogs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "productId": {"name": "productId", "type": "uuid", "primaryKey": false, "notNull": true}, "businessId": {"name": "businessId", "type": "uuid", "primaryKey": false, "notNull": true}, "ingredientCosts": {"name": "ingredientCosts", "type": "numeric(12, 4)", "primaryKey": false, "notNull": true, "default": "'0'"}, "laborCosts": {"name": "laborCosts", "type": "numeric(12, 4)", "primaryKey": false, "notNull": true, "default": "'0'"}, "overheadCosts": {"name": "overheadCosts", "type": "numeric(12, 4)", "primaryKey": false, "notNull": true, "default": "'0'"}, "totalCogs": {"name": "totalCogs", "type": "numeric(12, 4)", "primaryKey": false, "notNull": true, "default": "'0'"}, "calculationDate": {"name": "calculationDate", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "calculationMethod": {"name": "calculationMethod", "type": "text", "primaryKey": false, "notNull": true, "default": "'automatic'"}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"product_cogs_product_id_idx": {"name": "product_cogs_product_id_idx", "columns": [{"expression": "productId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "product_cogs_business_id_idx": {"name": "product_cogs_business_id_idx", "columns": [{"expression": "businessId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "product_cogs_calculation_date_idx": {"name": "product_cogs_calculation_date_idx", "columns": [{"expression": "calculationDate", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"product_cogs_productId_product_id_fk": {"name": "product_cogs_productId_product_id_fk", "tableFrom": "product_cogs", "tableTo": "product", "columnsFrom": ["productId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "product_cogs_businessId_business_id_fk": {"name": "product_cogs_businessId_business_id_fk", "tableFrom": "product_cogs", "tableTo": "business", "columnsFrom": ["businessId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"product_cogs_product_active_unique": {"name": "product_cogs_product_active_unique", "nullsNotDistinct": false, "columns": ["productId", "isActive"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.product_cogs_history": {"name": "product_cogs_history", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "productCogsId": {"name": "productCogsId", "type": "uuid", "primaryKey": false, "notNull": true}, "productId": {"name": "productId", "type": "uuid", "primaryKey": false, "notNull": true}, "businessId": {"name": "businessId", "type": "uuid", "primaryKey": false, "notNull": true}, "previousIngredientCosts": {"name": "previousIngredientCosts", "type": "numeric(12, 4)", "primaryKey": false, "notNull": false}, "previousLaborCosts": {"name": "previousLaborCosts", "type": "numeric(12, 4)", "primaryKey": false, "notNull": false}, "previousOverheadCosts": {"name": "previousOverheadCosts", "type": "numeric(12, 4)", "primaryKey": false, "notNull": false}, "previousTotalCogs": {"name": "previousTotalCogs", "type": "numeric(12, 4)", "primaryKey": false, "notNull": false}, "newIngredientCosts": {"name": "newIngredientCosts", "type": "numeric(12, 4)", "primaryKey": false, "notNull": true}, "newLaborCosts": {"name": "newLaborCosts", "type": "numeric(12, 4)", "primaryKey": false, "notNull": true}, "newOverheadCosts": {"name": "newOverheadCosts", "type": "numeric(12, 4)", "primaryKey": false, "notNull": true}, "newTotalCogs": {"name": "newTotalCogs", "type": "numeric(12, 4)", "primaryKey": false, "notNull": true}, "changeReason": {"name": "changeReason", "type": "text", "primaryKey": false, "notNull": false}, "changeDescription": {"name": "changeDescription", "type": "text", "primaryKey": false, "notNull": false}, "triggeredBy": {"name": "triggered<PERSON>y", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"product_cogs_history_product_id_idx": {"name": "product_cogs_history_product_id_idx", "columns": [{"expression": "productId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "product_cogs_history_business_id_idx": {"name": "product_cogs_history_business_id_idx", "columns": [{"expression": "businessId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "product_cogs_history_created_at_idx": {"name": "product_cogs_history_created_at_idx", "columns": [{"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"product_cogs_history_productCogsId_product_cogs_id_fk": {"name": "product_cogs_history_productCogsId_product_cogs_id_fk", "tableFrom": "product_cogs_history", "tableTo": "product_cogs", "columnsFrom": ["productCogsId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "product_cogs_history_productId_product_id_fk": {"name": "product_cogs_history_productId_product_id_fk", "tableFrom": "product_cogs_history", "tableTo": "product", "columnsFrom": ["productId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "product_cogs_history_businessId_business_id_fk": {"name": "product_cogs_history_businessId_business_id_fk", "tableFrom": "product_cogs_history", "tableTo": "business", "columnsFrom": ["businessId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.product_ingredient": {"name": "product_ingredient", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "productId": {"name": "productId", "type": "uuid", "primaryKey": false, "notNull": true}, "ingredientId": {"name": "ingredientId", "type": "uuid", "primaryKey": false, "notNull": true}, "quantityNeeded": {"name": "quantityNeeded", "type": "numeric(10, 4)", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"product_ingredient_product_id_idx": {"name": "product_ingredient_product_id_idx", "columns": [{"expression": "productId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "product_ingredient_ingredient_id_idx": {"name": "product_ingredient_ingredient_id_idx", "columns": [{"expression": "ingredientId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"product_ingredient_productId_product_id_fk": {"name": "product_ingredient_productId_product_id_fk", "tableFrom": "product_ingredient", "tableTo": "product", "columnsFrom": ["productId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "product_ingredient_ingredientId_ingredient_id_fk": {"name": "product_ingredient_ingredientId_ingredient_id_fk", "tableFrom": "product_ingredient", "tableTo": "ingredient", "columnsFrom": ["ingredientId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"product_ingredient_unique": {"name": "product_ingredient_unique", "nullsNotDistinct": false, "columns": ["productId", "ingredientId"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.product": {"name": "product", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "note": {"name": "note", "type": "text", "primaryKey": false, "notNull": false}, "categoryId": {"name": "categoryId", "type": "uuid", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "cogsPerCup": {"name": "cogsPerCup", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "businessId": {"name": "businessId", "type": "uuid", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"product_business_id_idx": {"name": "product_business_id_idx", "columns": [{"expression": "businessId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"product_categoryId_category_id_fk": {"name": "product_categoryId_category_id_fk", "tableFrom": "product", "tableTo": "category", "columnsFrom": ["categoryId"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "product_businessId_business_id_fk": {"name": "product_businessId_business_id_fk", "tableFrom": "product", "tableTo": "business", "columnsFrom": ["businessId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"product_business_name_unique": {"name": "product_business_name_unique", "nullsNotDistinct": false, "columns": ["businessId", "name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.role_permission": {"name": "role_permission", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "roleId": {"name": "roleId", "type": "uuid", "primaryKey": false, "notNull": true}, "permissionId": {"name": "permissionId", "type": "uuid", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_role_permission_role": {"name": "idx_role_permission_role", "columns": [{"expression": "roleId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_role_permission_permission": {"name": "idx_role_permission_permission", "columns": [{"expression": "permissionId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"role_permission_roleId_role_id_fk": {"name": "role_permission_roleId_role_id_fk", "tableFrom": "role_permission", "tableTo": "role", "columnsFrom": ["roleId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "role_permission_permissionId_permission_id_fk": {"name": "role_permission_permissionId_permission_id_fk", "tableFrom": "role_permission", "tableTo": "permission", "columnsFrom": ["permissionId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"role_permission_unique": {"name": "role_permission_unique", "nullsNotDistinct": false, "columns": ["roleId", "permissionId"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.role": {"name": "role", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "displayName": {"name": "displayName", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "isSystemRole": {"name": "isSystemRole", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_role_name": {"name": "idx_role_name", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_role_active": {"name": "idx_role_active", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"role_name_unique": {"name": "role_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.session": {"name": "session", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "expiresAt": {"name": "expiresAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "ipAddress": {"name": "ip<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "userAgent": {"name": "userAgent", "type": "text", "primaryKey": false, "notNull": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"idx_session_userId": {"name": "idx_session_userId", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_session_token": {"name": "idx_session_token", "columns": [{"expression": "token", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"session_userId_user_id_fk": {"name": "session_userId_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"session_token_unique": {"name": "session_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_invitation": {"name": "user_invitation", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "businessId": {"name": "businessId", "type": "uuid", "primaryKey": false, "notNull": true}, "roleId": {"name": "roleId", "type": "uuid", "primaryKey": false, "notNull": true}, "invitedBy": {"name": "invited<PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": true}, "invitationToken": {"name": "invitationToken", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "expiresAt": {"name": "expiresAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "acceptedAt": {"name": "acceptedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "acceptedBy": {"name": "acceptedBy", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_invitation_email": {"name": "idx_invitation_email", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_invitation_business": {"name": "idx_invitation_business", "columns": [{"expression": "businessId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_invitation_token": {"name": "idx_invitation_token", "columns": [{"expression": "invitationToken", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_invitation_status": {"name": "idx_invitation_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_invitation_expires": {"name": "idx_invitation_expires", "columns": [{"expression": "expiresAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_invitation_businessId_business_id_fk": {"name": "user_invitation_businessId_business_id_fk", "tableFrom": "user_invitation", "tableTo": "business", "columnsFrom": ["businessId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_invitation_roleId_role_id_fk": {"name": "user_invitation_roleId_role_id_fk", "tableFrom": "user_invitation", "tableTo": "role", "columnsFrom": ["roleId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_invitation_invitedBy_user_id_fk": {"name": "user_invitation_invitedBy_user_id_fk", "tableFrom": "user_invitation", "tableTo": "user", "columnsFrom": ["invited<PERSON><PERSON>"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_invitation_acceptedBy_user_id_fk": {"name": "user_invitation_acceptedBy_user_id_fk", "tableFrom": "user_invitation", "tableTo": "user", "columnsFrom": ["acceptedBy"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_invitation_invitationToken_unique": {"name": "user_invitation_invitationToken_unique", "nullsNotDistinct": false, "columns": ["invitationToken"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_role": {"name": "user_role", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "roleId": {"name": "roleId", "type": "uuid", "primaryKey": false, "notNull": true}, "businessId": {"name": "businessId", "type": "uuid", "primaryKey": false, "notNull": false}, "assignedBy": {"name": "assignedBy", "type": "text", "primaryKey": false, "notNull": false}, "assignedAt": {"name": "assignedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "expiresAt": {"name": "expiresAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_user_role_user": {"name": "idx_user_role_user", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_role_role": {"name": "idx_user_role_role", "columns": [{"expression": "roleId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_role_business": {"name": "idx_user_role_business", "columns": [{"expression": "businessId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_role_active": {"name": "idx_user_role_active", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_role_userId_user_id_fk": {"name": "user_role_userId_user_id_fk", "tableFrom": "user_role", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_role_roleId_role_id_fk": {"name": "user_role_roleId_role_id_fk", "tableFrom": "user_role", "tableTo": "role", "columnsFrom": ["roleId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_role_businessId_business_id_fk": {"name": "user_role_businessId_business_id_fk", "tableFrom": "user_role", "tableTo": "business", "columnsFrom": ["businessId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_role_assignedBy_user_id_fk": {"name": "user_role_assignedBy_user_id_fk", "tableFrom": "user_role", "tableTo": "user", "columnsFrom": ["assignedBy"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_role_business_unique": {"name": "user_role_business_unique", "nullsNotDistinct": false, "columns": ["userId", "roleId", "businessId"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "emailVerified": {"name": "emailVerified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "firstName": {"name": "firstName", "type": "text", "primaryKey": false, "notNull": false}, "lastName": {"name": "lastName", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification": {"name": "verification", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expiresAt": {"name": "expiresAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_verification_identifier": {"name": "idx_verification_identifier", "columns": [{"expression": "identifier", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}