import { auth } from "~/lib/auth.server";
import type { Session, User } from "~/lib/auth.server";

/**
 * Get the current session from the server-side
 * This should be used in loaders and actions
 */
export async function getSession(request: Request): Promise<{ user: User; session: Session } | null> {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    });
    
    return session;
  } catch (error) {
    console.error("Failed to get session:", error);
    return null;
  }
}

/**
 * Require authentication for a route
 * Throws a redirect response if not authenticated
 */
export async function requireAuth(request: Request): Promise<{ user: User; session: Session }> {
  const session = await getSession(request);
  
  if (!session) {
    throw new Response("Unauthorized", { status: 401 });
  }
  
  return session;
}
