import { eq, and, sql } from 'drizzle-orm';
import { db } from '~/lib/db/connection';
import { userInvitations, users, businesses, roles } from '~/lib/db/schema';
import { RBACService } from './rbacService.server';
import { randomUUID } from 'crypto';

export interface InvitationData {
  email: string;
  businessId: string;
  roleName: string;
  invitedBy: string;
  expiresInDays?: number;
}

export interface InvitationWithDetails {
  id: string;
  email: string;
  businessName: string;
  roleName: string;
  roleDisplayName: string;
  invitedByName: string;
  status: string;
  expiresAt: Date;
  createdAt: Date;
}

export class InvitationService {
  /**
   * Create a new user invitation
   */
  static async createInvitation(data: InvitationData): Promise<string> {
    const { email, businessId, roleName, invitedBy, expiresInDays = 7 } = data;

    // Check if user already exists
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.email, email))
      .limit(1);

    if (existingUser.length > 0) {
      // User exists, check if they already have access to this business
      const hasAccess = await RBACService.canAccessBusiness(existingUser[0].id, businessId);
      if (hasAccess) {
        throw new Error('User already has access to this business');
      }
    }

    // Get role by name
    const role = await db
      .select()
      .from(roles)
      .where(and(eq(roles.name, roleName), eq(roles.isActive, true)))
      .limit(1);

    if (!role.length) {
      throw new Error(`Role '${roleName}' not found`);
    }

    // Check for existing pending invitation
    const existingInvitation = await db
      .select()
      .from(userInvitations)
      .where(
        and(
          eq(userInvitations.email, email),
          eq(userInvitations.businessId, businessId),
          eq(userInvitations.status, 'pending')
        )
      )
      .limit(1);

    if (existingInvitation.length > 0) {
      throw new Error('Pending invitation already exists for this email and business');
    }

    // Generate invitation token
    const invitationToken = randomUUID();
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + expiresInDays);

    // Create invitation
    const [invitation] = await db
      .insert(userInvitations)
      .values({
        email,
        businessId,
        roleId: role[0].id,
        invitedBy,
        invitationToken,
        expiresAt,
        status: 'pending',
      })
      .returning();

    return invitationToken;
  }

  /**
   * Get invitation by token
   */
  static async getInvitationByToken(token: string) {
    const [invitation] = await db
      .select({
        id: userInvitations.id,
        email: userInvitations.email,
        businessId: userInvitations.businessId,
        businessName: businesses.name,
        roleId: userInvitations.roleId,
        roleName: roles.name,
        roleDisplayName: roles.displayName,
        invitedBy: userInvitations.invitedBy,
        status: userInvitations.status,
        expiresAt: userInvitations.expiresAt,
        createdAt: userInvitations.createdAt,
      })
      .from(userInvitations)
      .innerJoin(businesses, eq(userInvitations.businessId, businesses.id))
      .innerJoin(roles, eq(userInvitations.roleId, roles.id))
      .where(eq(userInvitations.invitationToken, token))
      .limit(1);

    return invitation;
  }

  /**
   * Accept an invitation
   */
  static async acceptInvitation(token: string, userId: string): Promise<void> {
    const invitation = await this.getInvitationByToken(token);

    if (!invitation) {
      throw new Error('Invitation not found');
    }

    if (invitation.status !== 'pending') {
      throw new Error('Invitation is no longer valid');
    }

    if (new Date() > invitation.expiresAt) {
      // Mark as expired
      await db
        .update(userInvitations)
        .set({ status: 'expired', updatedAt: new Date() })
        .where(eq(userInvitations.invitationToken, token));
      
      throw new Error('Invitation has expired');
    }

    // Assign role to user
    await RBACService.assignRole(
      userId,
      invitation.roleName,
      invitation.invitedBy,
      invitation.businessId
    );

    // Mark invitation as accepted
    await db
      .update(userInvitations)
      .set({
        status: 'accepted',
        acceptedAt: new Date(),
        acceptedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(userInvitations.invitationToken, token));
  }

  /**
   * Get pending invitations for a business
   */
  static async getBusinessInvitations(businessId: string): Promise<InvitationWithDetails[]> {
    const invitations = await db
      .select({
        id: userInvitations.id,
        email: userInvitations.email,
        businessName: businesses.name,
        roleName: roles.name,
        roleDisplayName: roles.displayName,
        invitedByName: users.name,
        status: userInvitations.status,
        expiresAt: userInvitations.expiresAt,
        createdAt: userInvitations.createdAt,
      })
      .from(userInvitations)
      .innerJoin(businesses, eq(userInvitations.businessId, businesses.id))
      .innerJoin(roles, eq(userInvitations.roleId, roles.id))
      .innerJoin(users, eq(userInvitations.invitedBy, users.id))
      .where(eq(userInvitations.businessId, businessId))
      .orderBy(userInvitations.createdAt);

    return invitations;
  }

  /**
   * Cancel an invitation
   */
  static async cancelInvitation(invitationId: string): Promise<void> {
    await db
      .update(userInvitations)
      .set({ status: 'cancelled', updatedAt: new Date() })
      .where(eq(userInvitations.id, invitationId));
  }

  /**
   * Get pending invitations for a user by email
   */
  static async getUserPendingInvitations(userEmail: string): Promise<InvitationWithDetails[]> {
    const invitations = await db
      .select({
        id: userInvitations.id,
        email: userInvitations.email,
        businessName: businesses.name,
        roleName: roles.name,
        roleDisplayName: roles.displayName,
        invitedByName: users.name,
        status: userInvitations.status,
        expiresAt: userInvitations.expiresAt,
        createdAt: userInvitations.createdAt,
        invitationToken: userInvitations.invitationToken,
        businessId: userInvitations.businessId,
      })
      .from(userInvitations)
      .innerJoin(businesses, eq(userInvitations.businessId, businesses.id))
      .innerJoin(roles, eq(userInvitations.roleId, roles.id))
      .innerJoin(users, eq(userInvitations.invitedBy, users.id))
      .where(
        and(
          eq(userInvitations.email, userEmail),
          eq(userInvitations.status, 'pending'),
          sql`${userInvitations.expiresAt} > NOW()` // Only non-expired invitations
        )
      )
      .orderBy(userInvitations.createdAt);

    return invitations;
  }

  /**
   * Get count of pending invitations for a user
   */
  static async getUserInvitationCount(userEmail: string): Promise<number> {
    const result = await db
      .select({ count: sql<number>`count(*)` })
      .from(userInvitations)
      .where(
        and(
          eq(userInvitations.email, userEmail),
          eq(userInvitations.status, 'pending'),
          sql`${userInvitations.expiresAt} > NOW()`
        )
      );

    return result[0]?.count || 0;
  }

  /**
   * Accept invitation by invitation ID (alternative to token-based acceptance)
   */
  static async acceptInvitationById(invitationId: string, userId: string): Promise<void> {
    // Get invitation details
    const [invitation] = await db
      .select({
        id: userInvitations.id,
        email: userInvitations.email,
        businessId: userInvitations.businessId,
        roleId: userInvitations.roleId,
        roleName: roles.name,
        invitedBy: userInvitations.invitedBy,
        status: userInvitations.status,
        expiresAt: userInvitations.expiresAt,
      })
      .from(userInvitations)
      .innerJoin(roles, eq(userInvitations.roleId, roles.id))
      .where(eq(userInvitations.id, invitationId))
      .limit(1);

    if (!invitation) {
      throw new Error('Invitation not found');
    }

    if (invitation.status !== 'pending') {
      throw new Error('Invitation is no longer valid');
    }

    if (new Date() > invitation.expiresAt) {
      // Mark as expired
      await db
        .update(userInvitations)
        .set({ status: 'expired', updatedAt: new Date() })
        .where(eq(userInvitations.id, invitationId));

      throw new Error('Invitation has expired');
    }

    // Assign role to user
    await RBACService.assignRole(
      userId,
      invitation.roleName,
      invitation.invitedBy,
      invitation.businessId
    );

    // Mark invitation as accepted
    await db
      .update(userInvitations)
      .set({
        status: 'accepted',
        acceptedAt: new Date(),
        acceptedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(userInvitations.id, invitationId));
  }

  /**
   * Decline invitation
   */
  static async declineInvitation(invitationId: string): Promise<void> {
    await db
      .update(userInvitations)
      .set({ status: 'cancelled', updatedAt: new Date() })
      .where(eq(userInvitations.id, invitationId));
  }

  /**
   * Clean up expired invitations
   */
  static async cleanupExpiredInvitations(): Promise<number> {
    const result = await db
      .update(userInvitations)
      .set({ status: 'expired', updatedAt: new Date() })
      .where(
        and(
          eq(userInvitations.status, 'pending'),
          sql`${userInvitations.expiresAt} < NOW()`
        )
      );

    return result.rowCount || 0;
  }
}
