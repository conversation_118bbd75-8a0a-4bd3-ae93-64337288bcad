import type { Product, ProductWithIngredients, ProductFormData, BasicProductFormData, ProductFilters } from '~/lib/types/inventory'

/**
 * Client-side product service that makes API calls
 * This can be used in both client and server components
 */
export class ProductService {
  /**
   * Create a new product for a specific business
   */
  static async create(businessId: string, data: ProductFormData | BasicProductFormData): Promise<Product> {
    const formData = new FormData()
    formData.append('_action', 'create')
    formData.append('businessId', businessId)
    formData.append('name', data.name)
    if (data.description) formData.append('description', data.description)
    if (data.note) formData.append('note', data.note)
    if (data.categoryId) formData.append('categoryId', data.categoryId)
    formData.append('isActive', data.isActive.toString())
    // cogsPerCup removed - will be calculated automatically
    
    // Add ingredients (if present)
    if ('ingredients' in data && data.ingredients) {
      data.ingredients.forEach((ingredient, index) => {
        formData.append(`ingredients[${index}][ingredientId]`, ingredient.ingredientId)
        formData.append(`ingredients[${index}][quantityNeeded]`, ingredient.quantityNeeded)
      })
    }

    const response = await fetch('/api/products', {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || 'Failed to create product')
    }

    const result = await response.json()
    return result.product
  }

  /**
   * Get all products for a specific business
   */
  static async getAllByBusiness(businessId: string, filters?: ProductFilters): Promise<Product[]> {
    const params = new URLSearchParams({ businessId })
    
    if (filters?.search) params.append('search', filters.search)
    if (filters?.category) params.append('category', filters.category)
    if (filters?.status) params.append('status', filters.status)
    if (filters?.minPrice !== undefined) params.append('minPrice', filters.minPrice.toString())
    if (filters?.maxPrice !== undefined) params.append('maxPrice', filters.maxPrice.toString())

    const response = await fetch(`/api/products?${params.toString()}`)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || 'Failed to load products')
    }

    const result = await response.json()
    return result.products
  }

  /**
   * Get product by ID with ingredients
   */
  static async getByIdWithIngredients(id: string, businessId: string): Promise<ProductWithIngredients | undefined> {
    const response = await fetch(`/api/products/${id}/with-ingredients?businessId=${businessId}`)

    if (!response.ok) {
      if (response.status === 404) {
        return undefined
      }
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || 'Failed to load product')
    }

    const result = await response.json()
    return result.product
  }

  /**
   * Get product by ID and business
   */
  static async getByIdAndBusiness(id: string, businessId: string): Promise<Product | undefined> {
    const response = await fetch(`/api/products/${id}?businessId=${businessId}`)

    if (!response.ok) {
      if (response.status === 404) {
        return undefined
      }
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || 'Failed to load product')
    }

    const result = await response.json()
    return result.product
  }

  /**
   * Update a product
   */
  static async update(id: string, businessId: string, data: Partial<ProductFormData> | Partial<BasicProductFormData>): Promise<void> {
    const formData = new FormData()
    formData.append('_action', 'update')
    formData.append('id', id)
    formData.append('businessId', businessId)
    if (data.name) formData.append('name', data.name)
    if (data.description) formData.append('description', data.description)
    if (data.note) formData.append('note', data.note)
    if (data.categoryId) formData.append('categoryId', data.categoryId)
    if (data.isActive !== undefined) formData.append('isActive', data.isActive.toString())
    // cogsPerCup removed - will be calculated automatically
    
    // Add ingredients if provided
    if (data.ingredients) {
      data.ingredients.forEach((ingredient, index) => {
        formData.append(`ingredients[${index}][ingredientId]`, ingredient.ingredientId)
        formData.append(`ingredients[${index}][quantityNeeded]`, ingredient.quantityNeeded)
      })
    }

    const response = await fetch('/api/products', {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || 'Failed to update product')
    }
  }

  /**
   * Delete a product
   */
  static async delete(id: string, businessId: string): Promise<void> {
    const formData = new FormData()
    formData.append('_action', 'delete')
    formData.append('id', id)
    formData.append('businessId', businessId)

    const response = await fetch('/api/products', {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || 'Failed to delete product')
    }
  }

  /**
   * Search products by name for a specific business
   */
  static async searchByNameAndBusiness(query: string, businessId: string): Promise<Product[]> {
    const params = new URLSearchParams({ 
      businessId,
      search: query 
    })

    const response = await fetch(`/api/products?${params.toString()}`)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || 'Failed to search products')
    }

    const result = await response.json()
    return result.products
  }

  /**
   * Get product count for a business
   */
  static async countByBusiness(businessId: string): Promise<number> {
    const response = await fetch(`/api/products/count?businessId=${businessId}`)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || 'Failed to get product count')
    }

    const result = await response.json()
    return result.count
  }

  /**
   * Check if product name already exists in business (for validation)
   */
  static async nameExistsInBusiness(name: string, businessId: string, excludeId?: string): Promise<boolean> {
    const params = new URLSearchParams({ 
      businessId,
      name 
    })
    
    if (excludeId) {
      params.append('excludeId', excludeId)
    }

    const response = await fetch(`/api/products/check-name?${params.toString()}`)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || 'Failed to check product name')
    }

    const result = await response.json()
    return result.exists
  }

  /**
   * Add ingredient to product
   */
  static async addIngredientToProduct(
    productId: string,
    ingredientId: string,
    quantityNeeded: number,
    businessId: string
  ): Promise<void> {
    const formData = new FormData()
    formData.append('_action', 'add')
    formData.append('businessId', businessId)
    formData.append('ingredientId', ingredientId)
    formData.append('quantityNeeded', quantityNeeded.toString())

    const response = await fetch(`/api/products/${productId}/ingredients`, {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || 'Failed to add ingredient to product')
    }
  }

  /**
   * Update product ingredient quantity
   */
  static async updateProductIngredient(
    productId: string,
    ingredientId: string,
    quantityNeeded: number,
    businessId: string
  ): Promise<void> {
    const formData = new FormData()
    formData.append('_action', 'update')
    formData.append('businessId', businessId)
    formData.append('ingredientId', ingredientId)
    formData.append('quantityNeeded', quantityNeeded.toString())

    const response = await fetch(`/api/products/${productId}/ingredients`, {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || 'Failed to update product ingredient')
    }
  }

  /**
   * Remove ingredient from product
   */
  static async removeIngredientFromProduct(
    productId: string,
    ingredientId: string,
    businessId: string
  ): Promise<void> {
    const formData = new FormData()
    formData.append('_action', 'remove')
    formData.append('businessId', businessId)
    formData.append('ingredientId', ingredientId)

    const response = await fetch(`/api/products/${productId}/ingredients`, {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || 'Failed to remove ingredient from product')
    }
  }
}
