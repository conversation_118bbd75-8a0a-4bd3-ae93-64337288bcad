import { eq, and, ilike, count } from 'drizzle-orm'
import { db } from '~/lib/db/connection'
import { businesses } from '~/lib/db/schema'
import type { Business, BusinessFormData } from '~/lib/types/business'
import { UserServiceServer } from './userService.server'

/**
 * Server-only business service that uses the database
 * This should only be imported in server-side code (loaders, actions)
 */
export class BusinessServiceServer {
  /**
   * Create a new business for a specific user
   */
  static async create(userId: string, data: BusinessFormData): Promise<Business> {
    // Ensure user exists before creating business
    await UserServiceServer.ensureUserExists(userId);

    const [business] = await db.insert(businesses).values({
      name: data.name,
      description: data.description || null,
      note: data.note || null,
      currency: data.currency,
      logo: data.logo || null,
      userId,
    }).returning()

    return business
  }

  /**
   * Get all businesses for a user (owned or has role access)
   */
  static async getAllByUser(userId: string): Promise<Business[]> {
    const { RBACService } = await import('./rbacService.server');
    return await RBACService.getUserBusinesses(userId);
  }

  /**
   * Get business by ID and user (ensures user can access businesses they own or have roles for)
   */
  static async getByIdAndUser(id: string, userId: string): Promise<Business | undefined> {
    // Get all businesses the user has access to (owned or through roles)
    const userBusinesses = await this.getAllByUser(userId);
    
    // Find the specific business by ID
    return userBusinesses.find(business => business.id === id);
  }

  /**
   * Update a business (ensures user can only update their own businesses)
   */
  static async update(id: string, userId: string, data: Partial<BusinessFormData>): Promise<void> {
    await db.update(businesses)
      .set({
        name: data.name,
        description: data.description || null,
        note: data.note || null,
        currency: data.currency,
        logo: data.logo || null,
        updatedAt: new Date(),
      })
      .where(and(eq(businesses.id, id), eq(businesses.userId, userId)))
  }

  /**
   * Delete a business (ensures user can only delete their own businesses)
   */
  static async delete(id: string, userId: string): Promise<void> {
    await db.delete(businesses)
      .where(and(eq(businesses.id, id), eq(businesses.userId, userId)))
  }

  /**
   * Get the default business for a user (first business they have access to)
   */
  static async getDefaultByUser(userId: string): Promise<Business | undefined> {
    const businesses = await this.getAllByUser(userId);
    return businesses.length > 0 ? businesses[0] : undefined;
  }



  /**
   * Check if user has any businesses
   */
  static async hasAnyForUser(userId: string): Promise<boolean> {
    const [result] = await db.select({ count: count() })
      .from(businesses)
      .where(eq(businesses.userId, userId))
    
    return (result?.count ?? 0) > 0
  }

  /**
   * Get business count for a user
   */
  static async countByUser(userId: string): Promise<number> {
    const [result] = await db.select({ count: count() })
      .from(businesses)
      .where(eq(businesses.userId, userId))
    
    return result?.count ?? 0
  }

  /**
   * Search businesses by name for a specific user
   */
  static async searchByNameAndUser(query: string, userId: string): Promise<Business[]> {
    return await db.select()
      .from(businesses)
      .where(and(
        eq(businesses.userId, userId),
        ilike(businesses.name, `%${query}%`)
      ))
      .orderBy(businesses.name)
  }
}
