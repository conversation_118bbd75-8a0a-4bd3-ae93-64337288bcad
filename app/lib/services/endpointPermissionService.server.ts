import { db } from '~/lib/db/connection';
import { permissions, type NewPermission } from '~/lib/db/schema';
import { eq } from 'drizzle-orm';

// Define all API endpoints and their CRUD operations
export interface EndpointOperation {
  endpoint: string;
  resource: string;
  operations: {
    action: string;
    method: string;
    description: string;
  }[];
}

// Comprehensive list of all API endpoints with their CRUD operations
export const API_ENDPOINTS: EndpointOperation[] = [
  {
    endpoint: '/api/businesses',
    resource: 'businesses',
    operations: [
      { action: 'read', method: 'GET', description: 'List all businesses for user' },
      { action: 'create', method: 'POST', description: 'Create a new business' },
      { action: 'update', method: 'POST', description: 'Update business information' },
      { action: 'delete', method: 'POST', description: 'Delete a business' },
    ],
  },
  {
    endpoint: '/api/products',
    resource: 'products',
    operations: [
      { action: 'read', method: 'GET', description: 'List products with filters' },
      { action: 'create', method: 'POST', description: 'Create a new product' },
      { action: 'update', method: 'POST', description: 'Update product information' },
      { action: 'delete', method: 'POST', description: 'Delete a product' },
    ],
  },
  {
    endpoint: '/api/products/$id',
    resource: 'products',
    operations: [
      { action: 'read', method: 'GET', description: 'Get specific product by ID' },
    ],
  },
  {
    endpoint: '/api/products/count',
    resource: 'products',
    operations: [
      { action: 'read', method: 'GET', description: 'Get product count statistics' },
    ],
  },
  {
    endpoint: '/api/ingredients',
    resource: 'ingredients',
    operations: [
      { action: 'read', method: 'GET', description: 'List ingredients with filters' },
      { action: 'create', method: 'POST', description: 'Create a new ingredient' },
      { action: 'update', method: 'POST', description: 'Update ingredient information' },
      { action: 'delete', method: 'POST', description: 'Delete an ingredient' },
    ],
  },
  {
    endpoint: '/api/ingredients/$id',
    resource: 'ingredients',
    operations: [
      { action: 'read', method: 'GET', description: 'Get specific ingredient by ID' },
    ],
  },
  {
    endpoint: '/api/categories',
    resource: 'categories',
    operations: [
      { action: 'read', method: 'GET', description: 'List categories by type and business' },
      { action: 'create', method: 'POST', description: 'Create a new category' },
    ],
  },
  {
    endpoint: '/api/categories/$id',
    resource: 'categories',
    operations: [
      { action: 'read', method: 'GET', description: 'Get specific category by ID' },
      { action: 'update', method: 'PUT', description: 'Update category information' },
      { action: 'delete', method: 'DELETE', description: 'Delete a category' },
    ],
  },
  {
    endpoint: '/api/cogs/calculate',
    resource: 'cogs',
    operations: [
      { action: 'calculate', method: 'POST', description: 'Calculate COGS for products' },
    ],
  },
  {
    endpoint: '/api/cogs/history',
    resource: 'cogs',
    operations: [
      { action: 'read', method: 'GET', description: 'Get COGS calculation history' },
    ],
  },
  {
    endpoint: '/api/auth/session',
    resource: 'auth',
    operations: [
      { action: 'read', method: 'GET', description: 'Get current user session' },
    ],
  },
  {
    endpoint: '/api/health/redis',
    resource: 'health',
    operations: [
      { action: 'read', method: 'GET', description: 'Check Redis health status' },
    ],
  },
  {
    endpoint: '/api/rbac/users',
    resource: 'rbac_users',
    operations: [
      { action: 'read', method: 'GET', description: 'List users with roles' },
      { action: 'create', method: 'POST', description: 'Invite new user' },
      { action: 'update', method: 'POST', description: 'Update user roles' },
      { action: 'delete', method: 'POST', description: 'Remove user from business' },
    ],
  },
  {
    endpoint: '/api/rbac/roles',
    resource: 'rbac_roles',
    operations: [
      { action: 'read', method: 'GET', description: 'List available roles' },
      { action: 'create', method: 'POST', description: 'Create custom role' },
      { action: 'update', method: 'POST', description: 'Update role permissions' },
      { action: 'delete', method: 'POST', description: 'Delete custom role' },
    ],
  },
  {
    endpoint: '/api/rbac/permissions',
    resource: 'rbac_permissions',
    operations: [
      { action: 'read', method: 'GET', description: 'List all available permissions' },
      { action: 'create', method: 'POST', description: 'Create custom permission' },
      { action: 'update', method: 'POST', description: 'Update permission details' },
      { action: 'delete', method: 'POST', description: 'Delete custom permission' },
    ],
  },
];

export class EndpointPermissionService {
  /**
   * Generate permissions for all API endpoints
   */
  static generateEndpointPermissions(): NewPermission[] {
    const permissions: NewPermission[] = [];

    for (const endpoint of API_ENDPOINTS) {
      for (const operation of endpoint.operations) {
        const permissionName = `${endpoint.resource}.${operation.action}`;
        const displayName = `${operation.action.charAt(0).toUpperCase() + operation.action.slice(1)} ${endpoint.resource.charAt(0).toUpperCase() + endpoint.resource.slice(1)}`;
        
        // Check if permission already exists in the array
        const existingPermission = permissions.find(p => p.name === permissionName);
        if (!existingPermission) {
          permissions.push({
            name: permissionName,
            displayName,
            description: operation.description,
            resource: endpoint.resource,
            action: operation.action,
            isSystemPermission: false, // These are endpoint-based permissions
          });
        }
      }
    }

    return permissions;
  }

  /**
   * Get all endpoint operations grouped by resource
   */
  static getEndpointsByResource(): Record<string, EndpointOperation[]> {
    const grouped: Record<string, EndpointOperation[]> = {};
    
    for (const endpoint of API_ENDPOINTS) {
      if (!grouped[endpoint.resource]) {
        grouped[endpoint.resource] = [];
      }
      grouped[endpoint.resource].push(endpoint);
    }
    
    return grouped;
  }

  /**
   * Get all unique operations for a specific resource
   */
  static getOperationsForResource(resource: string): { action: string; description: string }[] {
    const operations = new Map<string, string>();
    
    for (const endpoint of API_ENDPOINTS) {
      if (endpoint.resource === resource) {
        for (const operation of endpoint.operations) {
          if (!operations.has(operation.action)) {
            operations.set(operation.action, operation.description);
          }
        }
      }
    }
    
    return Array.from(operations.entries()).map(([action, description]) => ({
      action,
      description,
    }));
  }

  /**
   * Seed endpoint-based permissions to database
   */
  static async seedEndpointPermissions(): Promise<void> {
    console.log('🌱 Seeding endpoint-based permissions...');
    
    const endpointPermissions = this.generateEndpointPermissions();
    
    for (const permission of endpointPermissions) {
      try {
        // Check if permission already exists
        const existing = await db
          .select()
          .from(permissions)
          .where(eq(permissions.name, permission.name))
          .limit(1);
        
        if (existing.length === 0) {
          await db.insert(permissions).values(permission);
          console.log(`  ✅ Created permission: ${permission.displayName}`);
        } else {
          console.log(`  ⏭️  Permission already exists: ${permission.displayName}`);
        }
      } catch (error) {
        console.error(`  ❌ Error creating permission ${permission.name}:`, error);
      }
    }
    
    console.log('🎉 Endpoint permissions seeding completed!');
  }

  /**
   * Get all available permissions from database
   */
  static async getAllPermissions() {
    return await db.select().from(permissions).orderBy(permissions.resource, permissions.action);
  }

  /**
   * Get permissions grouped by resource
   */
  static async getPermissionsGroupedByResource() {
    const allPermissions = await this.getAllPermissions();
    const grouped: Record<string, typeof allPermissions> = {};
    
    for (const permission of allPermissions) {
      if (!grouped[permission.resource]) {
        grouped[permission.resource] = [];
      }
      grouped[permission.resource].push(permission);
    }
    
    return grouped;
  }
}