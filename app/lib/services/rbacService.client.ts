export interface UserPermissions {
  userId: string
  businessId?: string
  permissions: string[]
  roles: string[]
}

export class RBACServiceClient {
  /**
   * Check if user has a specific permission
   */
  static async hasPermission(
    userId: string,
    permission: string,
    businessId?: string
  ): Promise<boolean> {
    try {
      const params = new URLSearchParams({
        userId,
        permission,
        ...(businessId && { businessId }),
      })
      
      const response = await fetch(`/api/rbac/has-permission?${params}`)
      if (!response.ok) {
        throw new Error('Failed to check permission')
      }
      
      const data = await response.json()
      return data.hasPermission
    } catch (error) {
      console.error('Error checking permission:', error)
      return false
    }
  }

  /**
   * Check if user is business owner
   */
  static async isBusinessOwner(
    userId: string,
    businessId: string
  ): Promise<boolean> {
    try {
      const params = new URLSearchParams({
        userId,
        businessId,
      })
      
      const response = await fetch(`/api/rbac/is-business-owner?${params}`)
      if (!response.ok) {
        throw new Error('Failed to check business ownership')
      }
      
      const data = await response.json()
      return data.isOwner
    } catch (error) {
      console.error('Error checking business ownership:', error)
      return false
    }
  }

  /**
   * Get user permissions for a business
   */
  static async getUserPermissions(
    userId: string,
    businessId?: string
  ): Promise<UserPermissions> {
    try {
      const params = new URLSearchParams({
        userId,
        ...(businessId && { businessId }),
      })
      
      const response = await fetch(`/api/rbac/user-permissions?${params}`)
      if (!response.ok) {
        throw new Error('Failed to get user permissions')
      }
      
      return await response.json()
    } catch (error) {
      console.error('Error getting user permissions:', error)
      return {
        userId,
        businessId,
        permissions: [],
        roles: [],
      }
    }
  }
}