import { eq, and, ilike, count } from 'drizzle-orm'
import { db } from '~/lib/db/connection'
import { products, productIngredients, ingredients, categories } from '~/lib/db/schema'
import type { Product, ProductWithIngredients, ProductWithCategory, ProductFormData, ProductFilters, ProductIngredient } from '~/lib/types/inventory'
import { RBACService } from './rbacService.server'

/**
 * Server-only product service that uses the database
 * This should only be imported in server-side code (loaders, actions)
 */
export class ProductServiceServer {
  /**
   * Create a new product for a specific business
   */
  static async create(businessId: string, userId: string, data: ProductFormData): Promise<Product> {
    // Check RBAC permissions
    const hasPermission = await RBACService.hasPermission(
      userId,
      'products.create',
      businessId
    );

    if (!hasPermission) {
      throw new Error('Access denied: insufficient permissions')
    }

    // Start a transaction to create product and its ingredients
    return await db.transaction(async (tx) => {
      // Create the product
      const [product] = await tx.insert(products).values({
        name: data.name,
        description: data.description || null,
        note: data.note || null,
        categoryId: data.categoryId || null,
        isActive: data.isActive,
        cogsPerCup: null, // Will be calculated automatically after ingredients are added
        businessId,
      }).returning()

      // Create product-ingredient relationships
      if (data.ingredients && data.ingredients.length > 0) {
        const productIngredientData = data.ingredients.map(ingredient => ({
          productId: product.id,
          ingredientId: ingredient.ingredientId,
          quantityNeeded: ingredient.quantityNeeded,
        }))

        await tx.insert(productIngredients).values(productIngredientData)
      }

      // Calculate and update COGS automatically if ingredients were added
      if (data.ingredients && data.ingredients.length > 0) {
        await this.calculateAndUpdateProductCogs(product.id, businessId, tx, userId)
      }

      return product
    })
  }

  /**
   * Calculate and update COGS for a product using the dedicated COGS tables
   */
  static async calculateAndUpdateProductCogs(
    productId: string,
    businessId: string,
    tx?: any, // Transaction object
    userId: string = 'system', // User who triggered the calculation
    laborCosts: number = 0, // Labor costs per cup
    overheadCosts: number = 0 // Overhead costs per cup
  ): Promise<void> {
    const dbInstance = tx || db

    // Get product ingredients with ingredient details
    const productIngredientsData = await dbInstance.select({
      quantityNeeded: productIngredients.quantityNeeded,
      ingredient: {
        baseUnitCost: ingredients.baseUnitCost,
        baseUnitQuantity: ingredients.baseUnitQuantity,
      }
    })
    .from(productIngredients)
    .innerJoin(ingredients, eq(productIngredients.ingredientId, ingredients.id))
    .where(eq(productIngredients.productId, productId))

    // Calculate ingredient costs
    let ingredientCosts = 0
    for (const item of productIngredientsData) {
      const baseUnitCost = parseFloat(item.ingredient.baseUnitCost)
      const baseUnitQuantity = parseFloat(item.ingredient.baseUnitQuantity)
      const quantityNeeded = parseFloat(item.quantityNeeded)

      // Calculate cost per unit and total cost for this ingredient
      const costPerUnit = baseUnitCost / baseUnitQuantity
      const ingredientCost = costPerUnit * quantityNeeded

      ingredientCosts += ingredientCost
    }

    const totalCogs = ingredientCosts + laborCosts + overheadCosts

    // Update the product's cogsPerCup field for quick access
    await dbInstance.update(products)
      .set({
        cogsPerCup: totalCogs.toString(),
        updatedAt: new Date(),
      })
      .where(and(eq(products.id, productId), eq(products.businessId, businessId)))

    // Use the dedicated COGS service to save detailed COGS data
    // Only save to COGS tables if we have a real user (not system operations)
    if (userId !== 'system') {
      try {
        const { CogsServiceServer } = await import('./cogsService.server')
        await CogsServiceServer.saveProductCogs(
          productId,
          businessId,
          userId,
          {
            ingredientCosts,
            laborCosts,
            overheadCosts,
            totalCogs,
            calculationMethod: 'automatic'
          },
          'ingredient_change'
        )
      } catch (error) {
        console.warn('Could not save detailed COGS data:', error)
        // Continue execution - the basic cogsPerCup is still updated
      }
    }
  }

  /**
   * Get all products for a specific business
   */
  static async getAllByBusiness(businessId: string, userId: string, filters?: ProductFilters): Promise<ProductWithCategory[]> {
    // Check RBAC permissions
    const hasPermission = await RBACService.hasPermission(
      userId,
      'products.read',
      businessId
    );

    if (!hasPermission) {
      throw new Error('Access denied: insufficient permissions')
    }

    // Join with categories to get category names and colors
    let query = db.select({
      id: products.id,
      name: products.name,
      description: products.description,
      note: products.note,
      categoryId: products.categoryId,
      categoryName: categories.name,
      categoryColor: categories.color,
      isActive: products.isActive,
      cogsPerCup: products.cogsPerCup,
      businessId: products.businessId,
      createdAt: products.createdAt,
      updatedAt: products.updatedAt,
    })
      .from(products)
      .leftJoin(categories, eq(products.categoryId, categories.id))
      .where(eq(products.businessId, businessId))

    // Apply filters
    if (filters?.search) {
      query = query.where(ilike(products.name, `%${filters.search}%`))
    }

    if (filters?.category) {
      query = query.where(eq(products.categoryId, filters.category))
    }

    if (filters?.status) {
      query = query.where(eq(products.status, filters.status))
    }

    return await query.orderBy(products.name)
  }

  /**
   * Get product by ID with ingredients
   */
  static async getByIdWithIngredients(id: string, businessId: string, userId: string): Promise<ProductWithIngredients | undefined> {
    // Check RBAC permissions
    const hasPermission = await RBACService.hasPermission(
      userId,
      'products.read',
      businessId
    );

    if (!hasPermission) {
      throw new Error('Access denied: insufficient permissions')
    }

    // Get the product
    const [product] = await db.select()
      .from(products)
      .where(and(eq(products.id, id), eq(products.businessId, businessId)))
      .limit(1)

    if (!product) {
      return undefined
    }

    // Get product ingredients with ingredient details
    const productIngredientsData = await db.select({
      id: productIngredients.id,
      productId: productIngredients.productId,
      ingredientId: productIngredients.ingredientId,
      quantityNeeded: productIngredients.quantityNeeded,
      createdAt: productIngredients.createdAt,
      ingredient: {
        id: ingredients.id,
        name: ingredients.name,
        notes: ingredients.notes,
        unit: ingredients.unit,
        baseUnitCost: ingredients.baseUnitCost,
        baseUnitQuantity: ingredients.baseUnitQuantity,
        supplierInfo: ingredients.supplierInfo,
        isActive: ingredients.isActive,
        businessId: ingredients.businessId,
        createdAt: ingredients.createdAt,
        updatedAt: ingredients.updatedAt,
      }
    })
    .from(productIngredients)
    .innerJoin(ingredients, eq(productIngredients.ingredientId, ingredients.id))
    .where(eq(productIngredients.productId, id))

    return {
      ...product,
      productIngredients: productIngredientsData
    }
  }

  /**
   * Get product by ID and business (ensures user can only access their own products)
   */
  static async getByIdAndBusiness(id: string, businessId: string, userId: string): Promise<Product | undefined> {
    // Check RBAC permissions
    const hasPermission = await RBACService.hasPermission(
      userId,
      'products.read',
      businessId
    );

    if (!hasPermission) {
      throw new Error('Access denied: insufficient permissions')
    }

    const [product] = await db.select()
      .from(products)
      .where(and(eq(products.id, id), eq(products.businessId, businessId)))
      .limit(1)
    
    return product
  }

  /**
   * Update a product (ensures user can only update their own products)
   */
  static async update(id: string, businessId: string, userId: string, data: Partial<ProductFormData>): Promise<void> {
    // Check RBAC permissions
    const hasPermission = await RBACService.hasPermission(
      userId,
      'products.update',
      businessId
    );

    if (!hasPermission) {
      throw new Error('Access denied: insufficient permissions')
    }

    // Start a transaction to update product and its ingredients
    await db.transaction(async (tx) => {
      // Update the product
      await tx.update(products)
        .set({
          name: data.name,
          description: data.description || null,
          note: data.note || null,
          categoryId: data.categoryId || null,
          isActive: data.isActive,
          // cogsPerCup will be recalculated automatically after ingredients are updated
          updatedAt: new Date(),
        })
        .where(and(eq(products.id, id), eq(products.businessId, businessId)))

      // Update product-ingredient relationships if provided
      if (data.ingredients !== undefined) {
        // Delete existing relationships
        await tx.delete(productIngredients)
          .where(eq(productIngredients.productId, id))

        // Create new relationships
        if (data.ingredients.length > 0) {
          const productIngredientData = data.ingredients.map(ingredient => ({
            productId: id,
            ingredientId: ingredient.ingredientId,
            quantityNeeded: ingredient.quantityNeeded,
          }))

          await tx.insert(productIngredients).values(productIngredientData)
        }

        // Recalculate COGS after updating ingredients
        await this.calculateAndUpdateProductCogs(id, businessId, tx, userId)
      }
    })
  }

  /**
   * Delete a product (ensures user can only delete their own products)
   */
  static async delete(id: string, businessId: string, userId: string): Promise<void> {
    // Check RBAC permissions
    const hasPermission = await RBACService.hasPermission(
      userId,
      'products.delete',
      businessId
    );

    if (!hasPermission) {
      throw new Error('Access denied: insufficient permissions')
    }

    // Delete product (cascade will handle product_ingredients)
    await db.delete(products)
      .where(and(eq(products.id, id), eq(products.businessId, businessId)))
  }

  /**
   * Get product count for a business
   */
  static async countByBusiness(businessId: string, userId: string): Promise<number> {
    // Check RBAC permissions
    const hasPermission = await RBACService.hasPermission(
      userId,
      'products.read',
      businessId
    );

    if (!hasPermission) {
      throw new Error('Access denied: insufficient permissions')
    }

    const [result] = await db.select({ count: count() })
      .from(products)
      .where(eq(products.businessId, businessId))
    
    return result?.count ?? 0
  }

  /**
   * Search products by name for a specific business
   */
  static async searchByNameAndBusiness(query: string, businessId: string, userId: string): Promise<Product[]> {
    // Check RBAC permissions
    const hasPermission = await RBACService.hasPermission(
      userId,
      'products.read',
      businessId
    );

    if (!hasPermission) {
      throw new Error('Access denied: insufficient permissions')
    }

    return await db.select()
      .from(products)
      .where(and(
        eq(products.businessId, businessId),
        ilike(products.name, `%${query}%`)
      ))
      .orderBy(products.name)
  }

  /**
   * Check if product name already exists in business (for validation)
   */
  static async nameExistsInBusiness(name: string, businessId: string, userId: string, excludeId?: string): Promise<boolean> {
    // Check RBAC permissions
    const hasPermission = await RBACService.hasPermission(
      userId,
      'products.read',
      businessId
    );

    if (!hasPermission) {
      throw new Error('Access denied: insufficient permissions')
    }

    let query = db.select({ count: count() })
      .from(products)
      .where(and(
        eq(products.businessId, businessId),
        ilike(products.name, name)
      ))

    if (excludeId) {
      query = query.where(and(
        eq(products.businessId, businessId),
        ilike(products.name, name),
        eq(products.id, excludeId)
      ))
    }

    const [result] = await query
    return (result?.count ?? 0) > 0
  }

  /**
   * Add ingredient to product
   */
  static async addIngredientToProduct(
    productId: string,
    ingredientId: string,
    quantityNeeded: number,
    businessId: string,
    userId: string
  ): Promise<ProductIngredient> {
    // Check RBAC permissions
    const hasPermission = await RBACService.hasPermission(
      userId,
      'products.update',
      businessId
    );

    if (!hasPermission) {
      throw new Error('Access denied: insufficient permissions')
    }

    // Verify product belongs to business
    const product = await db.select()
      .from(products)
      .where(and(eq(products.id, productId), eq(products.businessId, businessId)))
      .limit(1)

    if (!product.length) {
      throw new Error('Product not found or access denied')
    }

    // Verify ingredient belongs to business
    const ingredient = await db.select()
      .from(ingredients)
      .where(and(eq(ingredients.id, ingredientId), eq(ingredients.businessId, businessId)))
      .limit(1)

    if (!ingredient.length) {
      throw new Error('Ingredient not found or access denied')
    }

    // Check if relationship already exists
    const existing = await db.select()
      .from(productIngredients)
      .where(and(
        eq(productIngredients.productId, productId),
        eq(productIngredients.ingredientId, ingredientId)
      ))
      .limit(1)

    if (existing.length > 0) {
      throw new Error('Ingredient is already added to this product')
    }

    // Create the relationship
    const [productIngredient] = await db.insert(productIngredients).values({
      productId,
      ingredientId,
      quantityNeeded: quantityNeeded.toString(),
    }).returning()

    // Recalculate COGS after adding ingredient
    await this.calculateAndUpdateProductCogs(productId, businessId, undefined, 'system')

    return productIngredient
  }

  /**
   * Update product ingredient quantity
   */
  static async updateProductIngredient(
    productId: string,
    ingredientId: string,
    quantityNeeded: number,
    businessId: string,
    userId: string
  ): Promise<void> {
    // Check RBAC permissions
    const hasPermission = await RBACService.hasPermission(
      userId,
      'products.update',
      businessId
    );

    if (!hasPermission) {
      throw new Error('Access denied: insufficient permissions')
    }

    // Verify the relationship exists and belongs to the user's business
    const existing = await db.select({
      productIngredient: productIngredients,
      product: products
    })
      .from(productIngredients)
      .innerJoin(products, eq(productIngredients.productId, products.id))
      .where(and(
        eq(productIngredients.productId, productId),
        eq(productIngredients.ingredientId, ingredientId),
        eq(products.businessId, businessId)
      ))
      .limit(1)

    if (!existing.length) {
      throw new Error('Product ingredient relationship not found or access denied')
    }

    // Update the quantity
    await db.update(productIngredients)
      .set({
        quantityNeeded: quantityNeeded.toString(),
      })
      .where(and(
        eq(productIngredients.productId, productId),
        eq(productIngredients.ingredientId, ingredientId)
      ))

    // Recalculate COGS after updating ingredient quantity
    await this.calculateAndUpdateProductCogs(productId, businessId, undefined, 'system')
  }

  /**
   * Remove ingredient from product
   */
  static async removeIngredientFromProduct(
    productId: string,
    ingredientId: string,
    businessId: string,
    userId: string
  ): Promise<void> {
    // Check RBAC permissions
    const hasPermission = await RBACService.hasPermission(
      userId,
      'products.update',
      businessId
    );

    if (!hasPermission) {
      throw new Error('Access denied: insufficient permissions')
    }

    // Verify the relationship exists and belongs to the user's business
    const existing = await db.select({
      productIngredient: productIngredients,
      product: products
    })
      .from(productIngredients)
      .innerJoin(products, eq(productIngredients.productId, products.id))
      .where(and(
        eq(productIngredients.productId, productId),
        eq(productIngredients.ingredientId, ingredientId),
        eq(products.businessId, businessId)
      ))
      .limit(1)

    if (!existing.length) {
      throw new Error('Product ingredient relationship not found or access denied')
    }

    // Remove the relationship
    await db.delete(productIngredients)
      .where(and(
        eq(productIngredients.productId, productId),
        eq(productIngredients.ingredientId, ingredientId)
      ))

    // Recalculate COGS after removing ingredient
    await this.calculateAndUpdateProductCogs(productId, businessId, undefined, 'system')
  }
}
