import { useLocation } from "@remix-run/react";
import { useTranslation } from "react-i18next";
import { useMemo } from "react";

export interface BreadcrumbItem {
  title: string;
  url: string;
  isActive?: boolean;
  isClickable?: boolean;
}

// Navigation mapping based on AppSidebar structure
const getNavigationMapping = (t: (key: string, options?: Record<string, unknown>) => string) => ({
  "/": {
    title: t('navigation:breadcrumb.dashboard', { defaultValue: 'Dashboard' }),
    parent: null,
    isClickable: true
  },
  "/inventory": {
    title: t('navigation:sections.inventorySupply'),
    parent: "/",
    isClickable: false // This corresponds to url: "#" in AppSidebar
  },
  "/inventory/ingredients": {
    title: t('navigation:inventory.ingredient'),
    parent: "/inventory",
    isClickable: true
  },
  "/inventory/products": {
    title: t('navigation:inventory.product'),
    parent: "/inventory",
    isClickable: true
  },
  "/categories": {
    title: t('navigation:inventory.categories'),
    parent: "/inventory",
    isClickable: true
  },
  "/cogs": {
    title: t('navigation:financials.cogsCalculator'),
    parent: "/",
    isClickable: true
  },
  "/financials": {
    title: t('navigation:sections.financials'),
    parent: "/",
    isClickable: false // This corresponds to url: "#" in AppSidebar
  },
  "/test-inventory-breadcrumb": {
    title: "Test Inventory Breadcrumb",
    parent: "/inventory",
    isClickable: true
  }
});

export function useBreadcrumbs(): BreadcrumbItem[] {
  const location = useLocation();
  const { t } = useTranslation(['navigation', 'common']);
  
  return useMemo(() => {
    const navigationMap = getNavigationMapping(t);
    const currentPath = location.pathname;
    
    // Build breadcrumb trail by following parent chain
    const buildBreadcrumbTrail = (path: string): BreadcrumbItem[] => {
      const navItem = navigationMap[path as keyof typeof navigationMap];
      
      if (!navItem) {
        // Fallback for unmapped routes
        const pathSegments = path.split('/').filter(Boolean);
        const lastSegment = pathSegments[pathSegments.length - 1];
        const fallbackTitle = lastSegment
          .split('-')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
        
        return [{
          title: fallbackTitle,
          url: path,
          isActive: path === currentPath,
          isClickable: true
        }];
      }
      
      const breadcrumbs: BreadcrumbItem[] = [];
      
      // Recursively build parent breadcrumbs
      if (navItem.parent) {
        breadcrumbs.push(...buildBreadcrumbTrail(navItem.parent));
      }
      
      // Add current item
      breadcrumbs.push({
        title: navItem.title,
        url: path,
        isActive: path === currentPath,
        isClickable: navItem.isClickable
      });
      
      return breadcrumbs;
    };
    
    // Always start with home/app name
    const breadcrumbs: BreadcrumbItem[] = [{
      title: 'KWACI Grow',
      url: "/",
      isActive: currentPath === "/",
      isClickable: true
    }];
    
    // If we're not on the home page, build the breadcrumb trail
    if (currentPath !== "/") {
      const trail = buildBreadcrumbTrail(currentPath);
      // Skip the first item if it's the root (already added)
      const filteredTrail = trail.filter(item => item.url !== "/");
      breadcrumbs.push(...filteredTrail);
    }
    
    return breadcrumbs;
  }, [location.pathname, t]);
}

// Helper function to get breadcrumb items for a specific path
export function getBreadcrumbsForPath(pathname: string, t: (key: string, options?: Record<string, unknown>) => string): BreadcrumbItem[] {
  const navigationMap = getNavigationMapping(t);
  const breadcrumbs: BreadcrumbItem[] = [];
  
  // Always start with home/app name
  breadcrumbs.push({
    title: 'KWACI Grow',
    url: "/",
    isActive: pathname === "/",
    isClickable: true
  });
  
  // If we're not on the home page, build the breadcrumb trail
  if (pathname !== "/") {
    const pathSegments = pathname.split('/').filter(Boolean);
    let currentUrl = "";
    
    for (let i = 0; i < pathSegments.length; i++) {
      currentUrl += "/" + pathSegments[i];
      const navItem = navigationMap[currentUrl as keyof typeof navigationMap];
      
      if (navItem) {
        breadcrumbs.push({
          title: navItem.title,
          url: currentUrl,
          isActive: i === pathSegments.length - 1,
          isClickable: navItem.isClickable
        });
      } else {
        // Fallback for unmapped routes - use the segment name
        const fallbackTitle = pathSegments[i]
          .split('-')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
        
        breadcrumbs.push({
          title: fallbackTitle,
          url: currentUrl,
          isActive: i === pathSegments.length - 1,
          isClickable: true // Default to clickable for unmapped routes
        });
      }
    }
  }
  
  return breadcrumbs;
}