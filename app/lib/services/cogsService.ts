import type { CogsCalculationResult, CogsBreakdown, ProductCogs, CogsFormData } from '~/lib/types/inventory';

export class CogsService {
  /**
   * Calculate COGS for a product
   */
  static async calculateProductCogs(
    productId: string,
    businessId: string,
    laborCosts: number = 0,
    overheadCosts: number = 0
  ): Promise<CogsCalculationResult> {
    const response = await fetch(`/api/products/${productId}/cogs/calculate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        businessId,
        laborCosts,
        overheadCosts,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || 'Failed to calculate COGS');
    }

    const result = await response.json();
    return result.calculation;
  }

  /**
   * Get COGS breakdown by ingredient for a product
   */
  static async getCogsBreakdown(
    productId: string,
    businessId: string
  ): Promise<CogsBreakdown[]> {
    const response = await fetch(`/api/products/${productId}/cogs/breakdown?businessId=${businessId}`);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || 'Failed to get COGS breakdown');
    }

    const result = await response.json();
    return result.breakdown;
  }

  /**
   * Save COGS for a product
   */
  static async saveProductCogs(
    productId: string,
    businessId: string,
    cogsData: CogsFormData
  ): Promise<ProductCogs> {
    const response = await fetch(`/api/products/${productId}/cogs`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        businessId,
        ...cogsData,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || 'Failed to save COGS');
    }

    const result = await response.json();
    return result.cogs;
  }

  /**
   * Get COGS for a product
   */
  static async getProductCogs(
    productId: string,
    businessId: string
  ): Promise<ProductCogs | null> {
    const response = await fetch(`/api/products/${productId}/cogs?businessId=${businessId}`);

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || 'Failed to get COGS');
    }

    const result = await response.json();
    return result.cogs;
  }

  /**
   * Get COGS history for a product
   */
  static async getProductCogsHistory(
    productId: string,
    businessId: string,
    limit: number = 10
  ) {
    const response = await fetch(
      `/api/products/${productId}/cogs/history?businessId=${businessId}&limit=${limit}`
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || 'Failed to get COGS history');
    }

    const result = await response.json();
    return result.history;
  }

  /**
   * Update COGS for a product
   */
  static async updateProductCogs(
    productId: string,
    businessId: string,
    cogsData: CogsFormData
  ): Promise<ProductCogs> {
    const response = await fetch(`/api/products/${productId}/cogs`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        businessId,
        ...cogsData,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || 'Failed to update COGS');
    }

    const result = await response.json();
    return result.cogs;
  }

  /**
   * Delete COGS for a product
   */
  static async deleteProductCogs(
    productId: string,
    businessId: string
  ): Promise<void> {
    const response = await fetch(`/api/products/${productId}/cogs`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        businessId,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || 'Failed to delete COGS');
    }
  }

  /**
   * Trigger COGS recalculation for all products affected by ingredient change
   */
  static async recalculateCogsForIngredientChange(
    ingredientId: string,
    businessId: string
  ): Promise<void> {
    const response = await fetch(`/api/ingredients/${ingredientId}/recalculate-cogs`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        businessId,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || 'Failed to recalculate COGS');
    }
  }

  /**
   * Calculate profit margin and amount
   */
  static calculateProfitMetrics(sellingPrice: number, totalCogs: number) {
    const profitAmount = sellingPrice - totalCogs;
    const profitMargin = sellingPrice > 0 ? (profitAmount / sellingPrice) * 100 : 0;
    
    return {
      profitAmount,
      profitMargin,
    };
  }

  /**
   * Format currency for display
   */
  static formatCurrency(amount: number | string, currency: string = 'IDR'): string {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount;
    if (isNaN(num)) return 'Rp 0';
    
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(num);
  }

  /**
   * Format percentage for display
   */
  static formatPercentage(percentage: number): string {
    return new Intl.NumberFormat('id-ID', {
      style: 'percent',
      minimumFractionDigits: 1,
      maximumFractionDigits: 2,
    }).format(percentage / 100);
  }

  /**
   * Get profit margin color based on percentage
   */
  static getProfitMarginColor(profitMargin: number): string {
    if (profitMargin >= 30) return 'text-green-600';
    if (profitMargin >= 20) return 'text-yellow-600';
    if (profitMargin >= 10) return 'text-orange-600';
    return 'text-red-600';
  }

  /**
   * Get profit margin status text
   */
  static getProfitMarginStatus(profitMargin: number): string {
    if (profitMargin >= 30) return 'Excellent';
    if (profitMargin >= 20) return 'Good';
    if (profitMargin >= 10) return 'Fair';
    if (profitMargin >= 0) return 'Low';
    return 'Loss';
  }

  /**
   * Validate COGS form data
   */
  static validateCogsFormData(data: CogsFormData): string[] {
    const errors: string[] = [];

    const laborCosts = parseFloat(data.laborCosts);
    if (isNaN(laborCosts) || laborCosts < 0) {
      errors.push('Labor costs must be a valid positive number');
    }

    const overheadCosts = parseFloat(data.overheadCosts);
    if (isNaN(overheadCosts) || overheadCosts < 0) {
      errors.push('Overhead costs must be a valid positive number');
    }

    if (!['automatic', 'manual', 'estimated'].includes(data.calculationMethod)) {
      errors.push('Invalid calculation method');
    }

    return errors;
  }
}
