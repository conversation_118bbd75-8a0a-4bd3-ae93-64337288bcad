import { categories, businesses } from '~/lib/db/schema';
import { eq, and, isNull, asc } from 'drizzle-orm';
import type { Category, CategoryWithChildren, CategoryType } from '~/lib/types/inventory';
import { db } from '../db/connection';
import { RBACService } from './rbacService.server';

export class CategoryServiceServer {
  /**
   * Get all categories for a business by type
   */
  static async getAllByBusinessAndType(
    businessId: string,
    userId: string,
    type: CategoryType
  ): Promise<Category[]> {
    // Check RBAC permissions
    const hasPermission = await RBACService.hasPermission(
      userId,
      'categories.read',
      businessId
    );

    if (!hasPermission) {
      throw new Error('Access denied: insufficient permissions');
    }

    return await db.select()
      .from(categories)
      .where(and(
        eq(categories.businessId, businessId),
        eq(categories.type, type),
        eq(categories.isActive, true)
      ))
      .orderBy(asc(categories.sortOrder), asc(categories.name));
  }

  /**
   * Get hierarchical categories (with parent-child relationships)
   */
  static async getHierarchicalCategories(
    businessId: string,
    userId: string,
    type: CategoryType
  ): Promise<CategoryWithChildren[]> {
    // Check RBAC permissions
    const hasPermission = await RBACService.hasPermission(
      userId,
      'categories.read',
      businessId
    );

    if (!hasPermission) {
      throw new Error('Access denied: insufficient permissions');
    }

    // Get all categories for the type
    const allCategories = await db.select()
      .from(categories)
      .where(and(
        eq(categories.businessId, businessId),
        eq(categories.type, type),
        eq(categories.isActive, true)
      ))
      .orderBy(asc(categories.sortOrder), asc(categories.name));

    // Build hierarchy
    const categoryMap = new Map<string, CategoryWithChildren>();
    const rootCategories: CategoryWithChildren[] = [];

    // First pass: create map of all categories
    allCategories.forEach(category => {
      categoryMap.set(category.id, { ...category, children: [] });
    });

    // Second pass: build hierarchy
    allCategories.forEach(category => {
      const categoryWithChildren = categoryMap.get(category.id)!;
      
      if (category.parentId) {
        const parent = categoryMap.get(category.parentId);
        if (parent) {
          parent.children!.push(categoryWithChildren);
          categoryWithChildren.parent = allCategories.find(c => c.id === category.parentId);
        } else {
          // Parent not found, treat as root
          rootCategories.push(categoryWithChildren);
        }
      } else {
        rootCategories.push(categoryWithChildren);
      }
    });

    return rootCategories;
  }

  /**
   * Create a new category
   */
  static async create(
    businessId: string,
    userId: string,
    categoryData: {
      name: string;
      description?: string;
      type: CategoryType;
      parentId?: string;
      color?: string;
      sortOrder?: number;
    }
  ): Promise<Category> {
    // Verify business belongs to user
    const business = await db.select()
      .from(businesses)
      .where(and(eq(businesses.id, businessId), eq(businesses.userId, userId)))
      .limit(1);

    if (!business.length) {
      throw new Error('Business not found or access denied');
    }

    // Check if category name already exists for this business and type
    const existingCategory = await db.select()
      .from(categories)
      .where(and(
        eq(categories.businessId, businessId),
        eq(categories.type, categoryData.type),
        eq(categories.name, categoryData.name),
        eq(categories.isActive, true)
      ))
      .limit(1);

    if (existingCategory.length > 0) {
      throw new Error('A category with this name already exists for this type');
    }

    // Validate parent category if provided
    if (categoryData.parentId) {
      const parentCategory = await db.select()
        .from(categories)
        .where(and(
          eq(categories.id, categoryData.parentId),
          eq(categories.businessId, businessId),
          eq(categories.type, categoryData.type),
          eq(categories.isActive, true)
        ))
        .limit(1);

      if (!parentCategory.length) {
        throw new Error('Parent category not found or invalid');
      }
    }

    // Get next sort order if not provided
    let sortOrder = categoryData.sortOrder;
    if (sortOrder === undefined) {
      const lastCategory = await db.select({ sortOrder: categories.sortOrder })
        .from(categories)
        .where(and(
          eq(categories.businessId, businessId),
          eq(categories.type, categoryData.type),
          categoryData.parentId ? eq(categories.parentId, categoryData.parentId) : isNull(categories.parentId)
        ))
        .orderBy(asc(categories.sortOrder))
        .limit(1);

      sortOrder = lastCategory.length > 0 
        ? (parseFloat(lastCategory[0].sortOrder || '0') + 10)
        : 10;
    }

    const [newCategory] = await db.insert(categories).values({
      businessId,
      name: categoryData.name,
      description: categoryData.description,
      type: categoryData.type,
      parentId: categoryData.parentId,
      color: categoryData.color,
      sortOrder: sortOrder.toString(),
      isActive: true,
    }).returning();

    return newCategory;
  }

  /**
   * Update a category
   */
  static async update(
    categoryId: string,
    businessId: string,
    userId: string,
    updateData: {
      name?: string;
      description?: string;
      parentId?: string;
      color?: string;
      sortOrder?: number;
      isActive?: boolean;
    }
  ): Promise<Category> {
    // Verify business belongs to user
    const business = await db.select()
      .from(businesses)
      .where(and(eq(businesses.id, businessId), eq(businesses.userId, userId)))
      .limit(1);

    if (!business.length) {
      throw new Error('Business not found or access denied');
    }

    // Get existing category
    const [existingCategory] = await db.select()
      .from(categories)
      .where(and(
        eq(categories.id, categoryId),
        eq(categories.businessId, businessId)
      ))
      .limit(1);

    if (!existingCategory) {
      throw new Error('Category not found');
    }

    // Check for name conflicts if name is being updated
    if (updateData.name && updateData.name !== existingCategory.name) {
      const conflictingCategory = await db.select()
        .from(categories)
        .where(and(
          eq(categories.businessId, businessId),
          eq(categories.type, existingCategory.type),
          eq(categories.name, updateData.name),
          eq(categories.isActive, true)
        ))
        .limit(1);

      if (conflictingCategory.length > 0) {
        throw new Error('A category with this name already exists for this type');
      }
    }

    // Validate parent category if being updated
    if (updateData.parentId && updateData.parentId !== existingCategory.parentId) {
      // Prevent circular references
      if (updateData.parentId === categoryId) {
        throw new Error('A category cannot be its own parent');
      }

      const parentCategory = await db.select()
        .from(categories)
        .where(and(
          eq(categories.id, updateData.parentId),
          eq(categories.businessId, businessId),
          eq(categories.type, existingCategory.type),
          eq(categories.isActive, true)
        ))
        .limit(1);

      if (!parentCategory.length) {
        throw new Error('Parent category not found or invalid');
      }
    }

    const updateFields: Partial<{
      name: string;
      description: string | null;
      parentId: string | null;
      color: string | null;
      sortOrder: string;
      isActive: boolean;
      updatedAt: Date;
    }> = {
      updatedAt: new Date(),
    };

    if (updateData.name !== undefined) updateFields.name = updateData.name;
    if (updateData.description !== undefined) updateFields.description = updateData.description;
    if (updateData.parentId !== undefined) updateFields.parentId = updateData.parentId;
    if (updateData.color !== undefined) updateFields.color = updateData.color;
    if (updateData.sortOrder !== undefined) updateFields.sortOrder = updateData.sortOrder.toString();
    if (updateData.isActive !== undefined) updateFields.isActive = updateData.isActive;

    const [updatedCategory] = await db.update(categories)
      .set(updateFields)
      .where(eq(categories.id, categoryId))
      .returning();

    return updatedCategory;
  }

  /**
   * Delete a category (soft delete by setting isActive to false)
   */
  static async delete(
    categoryId: string,
    businessId: string,
    userId: string
  ): Promise<void> {
    // Verify business belongs to user
    const business = await db.select()
      .from(businesses)
      .where(and(eq(businesses.id, businessId), eq(businesses.userId, userId)))
      .limit(1);

    if (!business.length) {
      throw new Error('Business not found or access denied');
    }

    // Check if category exists
    const [existingCategory] = await db.select()
      .from(categories)
      .where(and(
        eq(categories.id, categoryId),
        eq(categories.businessId, businessId)
      ))
      .limit(1);

    if (!existingCategory) {
      throw new Error('Category not found');
    }

    // Check if category has children
    const children = await db.select()
      .from(categories)
      .where(and(
        eq(categories.parentId, categoryId),
        eq(categories.isActive, true)
      ))
      .limit(1);

    if (children.length > 0) {
      throw new Error('Cannot delete category that has child categories. Please delete or move child categories first.');
    }

    // Soft delete the category
    await db.update(categories)
      .set({
        isActive: false,
        updatedAt: new Date(),
      })
      .where(eq(categories.id, categoryId));
  }

  /**
   * Get category by ID
   */
  static async getById(
    categoryId: string,
    businessId: string,
    userId: string
  ): Promise<Category | undefined> {
    // Verify business belongs to user
    const business = await db.select()
      .from(businesses)
      .where(and(eq(businesses.id, businessId), eq(businesses.userId, userId)))
      .limit(1);

    if (!business.length) {
      throw new Error('Business not found or access denied');
    }

    const [category] = await db.select()
      .from(categories)
      .where(and(
        eq(categories.id, categoryId),
        eq(categories.businessId, businessId),
        eq(categories.isActive, true)
      ))
      .limit(1);

    return category;
  }

  /**
   * Check if category name exists in business for a specific type
   */
  static async nameExistsInBusiness(
    name: string,
    type: CategoryType,
    businessId: string,
    userId: string,
    excludeCategoryId?: string
  ): Promise<boolean> {
    // Verify business belongs to user
    const business = await db.select()
      .from(businesses)
      .where(and(eq(businesses.id, businessId), eq(businesses.userId, userId)))
      .limit(1);

    if (!business.length) {
      throw new Error('Business not found or access denied');
    }

    const conditions = [
      eq(categories.businessId, businessId),
      eq(categories.type, type),
      eq(categories.name, name),
      eq(categories.isActive, true)
    ];

    if (excludeCategoryId) {
      conditions.push(eq(categories.id, excludeCategoryId));
    }

    const existingCategories = await db.select()
      .from(categories)
      .where(and(...conditions))
      .limit(1);

    return existingCategories.length > 0;
  }
}
