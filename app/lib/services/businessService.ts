import type { Business, BusinessFormData } from '~/lib/types/business'

export class BusinessService {
  /**
   * Create a new business for a specific user
   */
  static async create(userId: string, data: BusinessFormData): Promise<Business> {
    const formData = new FormData()
    formData.append('_action', 'create')
    formData.append('name', data.name)
    if (data.description) formData.append('description', data.description)
    if (data.note) formData.append('note', data.note)
    formData.append('currency', data.currency)
    if (data.logo) formData.append('logo', data.logo)

    const response = await fetch('/api/businesses', {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      throw new Error('Failed to create business')
    }

    const result = await response.json()
    return result.business
  }

  /**
   * Get all businesses for a specific user
   */
  static async getAllByUser(userId: string): Promise<Business[]> {
    const response = await fetch('/api/businesses')

    if (!response.ok) {
      throw new Error('Failed to load businesses')
    }

    const result = await response.json()
    return result.businesses
  }

  /**
   * Get business by ID and user (ensures user can only access their own businesses)
   */
  static async getByIdAndUser(id: string, userId: string): Promise<Business | undefined> {
    const businesses = await this.getAllByUser(userId)
    return businesses.find(b => b.id === id)
  }

  /**
   * Update a business (ensures user can only update their own businesses)
   */
  static async update(id: string, userId: string, data: Partial<BusinessFormData>): Promise<void> {
    const formData = new FormData()
    formData.append('_action', 'update')
    formData.append('id', id)
    if (data.name) formData.append('name', data.name)
    if (data.description) formData.append('description', data.description)
    if (data.note) formData.append('note', data.note)
    if (data.currency) formData.append('currency', data.currency)
    if (data.logo) formData.append('logo', data.logo)

    const response = await fetch('/api/businesses', {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      throw new Error('Failed to update business')
    }
  }

  /**
   * Delete a business (ensures user can only delete their own businesses)
   */
  static async delete(id: string, userId: string): Promise<void> {
    const formData = new FormData()
    formData.append('_action', 'delete')
    formData.append('id', id)

    const response = await fetch('/api/businesses', {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      throw new Error('Failed to delete business')
    }
  }

  /**
   * Get the default business for a user (first business created)
   */
  static async getDefaultByUser(userId: string): Promise<Business | undefined> {
    const businesses = await this.getAllByUser(userId)
    return businesses.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())[0]
  }



  /**
   * Check if user has any businesses
   */
  static async hasAnyForUser(userId: string): Promise<boolean> {
    const businesses = await this.getAllByUser(userId)
    return businesses.length > 0
  }

  /**
   * Get business count for a user
   */
  static async countByUser(userId: string): Promise<number> {
    const businesses = await this.getAllByUser(userId)
    return businesses.length
  }

  /**
   * Search businesses by name for a specific user
   */
  static async searchByNameAndUser(query: string, userId: string): Promise<Business[]> {
    const businesses = await this.getAllByUser(userId)
    return businesses.filter(business =>
      business.name.toLowerCase().includes(query.toLowerCase())
    )
  }

  // Legacy methods for backward compatibility (will be updated in store)
  /**
   * @deprecated Use getAllByUser instead
   */
  static async getAll(): Promise<Business[]> {
    throw new Error('getAll() is deprecated. Use getAllByUser(userId) instead.')
  }

  /**
   * @deprecated Use getByIdAndUser instead
   */
  static async getById(id: string): Promise<Business | undefined> {
    throw new Error('getById() is deprecated. Use getByIdAndUser(id, userId) instead.')
  }
}
