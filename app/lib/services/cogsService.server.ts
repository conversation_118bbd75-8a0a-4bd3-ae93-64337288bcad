import { productCogs, productCogsHistory, productIngredients, ingredients } from '~/lib/db/schema';
import { eq, and, desc } from 'drizzle-orm';
import type { ProductCogs, CogsCalculationResult, CogsBreakdown } from '~/lib/types/inventory';
import { db } from '../db/connection';

export class CogsServiceServer {
  /**
   * Calculate COGS for a product based on its ingredients
   */
  static async calculateProductCogs(
    productId: string, 
    businessId: string, 
    userId: string,
    laborCosts: number = 0,
    overheadCosts: number = 0
  ): Promise<CogsCalculationResult> {
    // Import RBACService
    const { RBACService } = await import('~/lib/services/rbacService.server');
    
    // Verify user has access to business through RBAC
    const canAccess = await RBACService.canAccessBusiness(userId, businessId);
    if (!canAccess) {
      throw new Error('Business not found or access denied');
    }

    // Get product ingredients with ingredient details
    const productIngredientsData = await db.select({
      quantityNeeded: productIngredients.quantityNeeded,
      ingredient: {
        id: ingredients.id,
        name: ingredients.name,
        baseUnitCost: ingredients.baseUnitCost,
        baseUnitQuantity: ingredients.baseUnitQuantity,
        unit: ingredients.unit,
      }
    })
    .from(productIngredients)
    .innerJoin(ingredients, eq(productIngredients.ingredientId, ingredients.id))
    .where(eq(productIngredients.productId, productId));

    // Calculate ingredient costs
    let ingredientCosts = 0;
    for (const item of productIngredientsData) {
      const baseUnitCost = parseFloat(item.ingredient.baseUnitCost);
      const baseUnitQuantity = parseFloat(item.ingredient.baseUnitQuantity);
      const quantityNeeded = parseFloat(item.quantityNeeded);
      
      // Calculate cost per unit
      const costPerUnit = baseUnitCost / baseUnitQuantity;
      const totalIngredientCost = costPerUnit * quantityNeeded;
      
      ingredientCosts += totalIngredientCost;
    }

    const totalCogs = ingredientCosts + laborCosts + overheadCosts;

    // Note: Profit calculation removed as sellingPrice is not available in schema
    const profitAmount = 0;
    const profitMargin = 0;

    return {
      ingredientCosts,
      laborCosts,
      overheadCosts,
      totalCogs,
      profitMargin,
      profitAmount,
    };
  }

  /**
   * Get COGS breakdown by ingredient for a product
   */
  static async getCogsBreakdown(
    productId: string, 
    businessId: string, 
    userId: string
  ): Promise<CogsBreakdown[]> {
    // Check RBAC permissions for products, ingredients, and COGS
    const { RBACService } = await import('~/lib/services/rbacService.server');
    
    const hasProductsRead = await RBACService.hasPermission(
      userId,
      'products.read',
      businessId
    );
    
    const hasIngredientsRead = await RBACService.hasPermission(
      userId,
      'ingredients.read',
      businessId
    );
    
    const hasCogsRead = await RBACService.hasPermission(
      userId,
      'cogs.read',
      businessId
    );

    if (!hasProductsRead || !hasIngredientsRead || !hasCogsRead) {
      throw new Error('Access denied: insufficient permissions to view COGS breakdown');
    }

    // Verify user has access to business through RBAC
    const canAccess = await RBACService.canAccessBusiness(userId, businessId);
    if (!canAccess) {
      throw new Error('Business not found or access denied');
    }

    // Get product ingredients with ingredient details
    const productIngredientsData = await db.select({
      quantityNeeded: productIngredients.quantityNeeded,
      ingredient: {
        id: ingredients.id,
        name: ingredients.name,
        baseUnitCost: ingredients.baseUnitCost,
        baseUnitQuantity: ingredients.baseUnitQuantity,
        unit: ingredients.unit,
        categoryId: ingredients.categoryId,
        notes: ingredients.notes,
        supplierInfo: ingredients.supplierInfo,
        isActive: ingredients.isActive,
        businessId: ingredients.businessId,
        createdAt: ingredients.createdAt,
        updatedAt: ingredients.updatedAt,
      }
    })
    .from(productIngredients)
    .innerJoin(ingredients, eq(productIngredients.ingredientId, ingredients.id))
    .where(eq(productIngredients.productId, productId));

    // Calculate breakdown with the expected format
    const breakdown = productIngredientsData.map(item => {
      const baseUnitCost = parseFloat(item.ingredient.baseUnitCost || '0');
      const baseUnitQuantity = parseFloat(item.ingredient.baseUnitQuantity || '1');
      const quantityNeeded = parseFloat(item.quantityNeeded || '0');

      // Calculate cost per unit and cost per cup
      const unitCost = baseUnitQuantity > 0 ? baseUnitCost / baseUnitQuantity : 0;
      const costPerCup = unitCost * quantityNeeded;

      return {
        ingredientId: item.ingredient.id,
        ingredientName: item.ingredient.name,
        usagePerCup: quantityNeeded,
        unit: item.ingredient.unit || '',
        unitCost: unitCost.toString(),
        costPerCup: costPerCup.toString(),
        note: item.ingredient.notes || '',
        // Legacy format for compatibility
        ingredient: item.ingredient,
        quantityNeeded,
        totalCost: costPerCup,
        percentageOfTotal: 0, // Will be calculated below
      };
    });

    // Calculate total and percentages
    const totalIngredientCosts = breakdown.reduce((sum, item) => sum + item.totalCost, 0);

    breakdown.forEach(item => {
      item.percentageOfTotal = totalIngredientCosts > 0
        ? (item.totalCost / totalIngredientCosts) * 100
        : 0;
    });

    return breakdown;
  }

  /**
   * Save or update COGS for a product
   */
  static async saveProductCogs(
    productId: string,
    businessId: string,
    userId: string,
    cogsData: {
      ingredientCosts: number;
      laborCosts: number;
      overheadCosts: number;
      totalCogs: number;
      calculationMethod?: 'automatic' | 'manual' | 'estimated';
    },
    changeReason?: string
  ): Promise<ProductCogs> {
    // Import RBACService
    const { RBACService } = await import('~/lib/services/rbacService.server');
    
    // Verify user has access to business through RBAC
    const canAccess = await RBACService.canAccessBusiness(userId, businessId);
    if (!canAccess) {
      throw new Error('Business not found or access denied');
    }

    // Check if COGS record already exists
    const [existingCogs] = await db.select()
      .from(productCogs)
      .where(and(
        eq(productCogs.productId, productId),
        eq(productCogs.isActive, true)
      ))
      .limit(1);

    if (existingCogs) {
      // Create history record before updating
      await db.insert(productCogsHistory).values({
        productCogsId: existingCogs.id,
        productId: existingCogs.productId,
        businessId: existingCogs.businessId,
        previousIngredientCosts: existingCogs.ingredientCosts,
        previousLaborCosts: existingCogs.laborCosts,
        previousOverheadCosts: existingCogs.overheadCosts,
        previousTotalCogs: existingCogs.totalCogs,
        newIngredientCosts: cogsData.ingredientCosts.toString(),
        newLaborCosts: cogsData.laborCosts.toString(),
        newOverheadCosts: cogsData.overheadCosts.toString(),
        newTotalCogs: cogsData.totalCogs.toString(),
        changeReason: changeReason || 'manual_update',
        changeDescription: `COGS updated from ${existingCogs.totalCogs} to ${cogsData.totalCogs}`,
        triggeredBy: userId,
      });

      // Update existing record
      const [updatedCogs] = await db.update(productCogs)
        .set({
          ingredientCosts: cogsData.ingredientCosts.toString(),
          laborCosts: cogsData.laborCosts.toString(),
          overheadCosts: cogsData.overheadCosts.toString(),
          totalCogs: cogsData.totalCogs.toString(),
          calculationMethod: cogsData.calculationMethod || 'manual',
          calculationDate: new Date(),
          updatedAt: new Date(),
        })
        .where(eq(productCogs.id, existingCogs.id))
        .returning();

      return updatedCogs;
    } else {
      // Create new COGS record
      const [newCogs] = await db.insert(productCogs).values({
        productId,
        businessId,
        ingredientCosts: cogsData.ingredientCosts.toString(),
        laborCosts: cogsData.laborCosts.toString(),
        overheadCosts: cogsData.overheadCosts.toString(),
        totalCogs: cogsData.totalCogs.toString(),
        calculationMethod: cogsData.calculationMethod || 'manual',
        calculationDate: new Date(),
        isActive: true,
      }).returning();

      return newCogs;
    }
  }

  /**
   * Get COGS for a product
   */
  static async getProductCogs(
    productId: string,
    businessId: string,
    userId: string
  ): Promise<ProductCogs | undefined> {
    // Import RBACService
    const { RBACService } = await import('~/lib/services/rbacService.server');
    
    // Verify user has access to business through RBAC
    const canAccess = await RBACService.canAccessBusiness(userId, businessId);
    if (!canAccess) {
      throw new Error('Business not found or access denied');
    }

    const [cogs] = await db.select()
      .from(productCogs)
      .where(and(
        eq(productCogs.productId, productId),
        eq(productCogs.businessId, businessId),
        eq(productCogs.isActive, true)
      ))
      .limit(1);

    return cogs;
  }

  /**
   * Get COGS history for a product
   */
  static async getProductCogsHistory(
    productId: string,
    businessId: string,
    userId: string,
    limit: number = 10
  ) {
    // Import RBACService
    const { RBACService } = await import('~/lib/services/rbacService.server');
    
    // Verify user has access to business through RBAC
    const canAccess = await RBACService.canAccessBusiness(userId, businessId);
    if (!canAccess) {
      throw new Error('Business not found or access denied');
    }

    return await db.select()
      .from(productCogsHistory)
      .where(and(
        eq(productCogsHistory.productId, productId),
        eq(productCogsHistory.businessId, businessId)
      ))
      .orderBy(desc(productCogsHistory.createdAt))
      .limit(limit);
  }

  /**
   * Recalculate COGS for all products when ingredient costs change
   */
  static async recalculateCogsForIngredientChange(
    ingredientId: string,
    businessId: string,
    userId: string
  ): Promise<void> {
    // Get all products that use this ingredient
    const affectedProducts = await db.select({
      productId: productIngredients.productId
    })
    .from(productIngredients)
    .where(eq(productIngredients.ingredientId, ingredientId));

    // Recalculate COGS for each affected product
    for (const { productId } of affectedProducts) {
      try {
        // Get existing COGS to preserve labor and overhead costs
        const existingCogs = await this.getProductCogs(productId, businessId, userId);
        const laborCosts = existingCogs ? parseFloat(existingCogs.laborCosts) : 0;
        const overheadCosts = existingCogs ? parseFloat(existingCogs.overheadCosts) : 0;

        // Recalculate with preserved labor and overhead costs
        const calculationResult = await this.calculateProductCogs(
          productId,
          businessId,
          userId,
          laborCosts,
          overheadCosts
        );

        // Save updated COGS
        await this.saveProductCogs(
          productId,
          businessId,
          userId,
          calculationResult,
          'ingredient_cost_change'
        );
      } catch (error) {
        console.error(`Failed to recalculate COGS for product ${productId}:`, error);
        // Continue with other products even if one fails
      }
    }
  }
}
