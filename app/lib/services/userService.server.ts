import { eq } from 'drizzle-orm';
import { db } from '~/lib/db/connection';
import { users } from '~/lib/db/schema';

/**
 * Server-only user service for handling user operations
 * This ensures users exist before business operations
 */
export class UserServiceServer {
  /**
   * Get user by ID, create if doesn't exist (for Better Auth compatibility)
   */
  static async ensureUserExists(userId: string, userData?: {
    name?: string;
    email?: string;
    emailVerified?: boolean;
    image?: string | null;
  }): Promise<void> {
    try {
      // Check if user exists
      const [existingUser] = await db.select()
        .from(users)
        .where(eq(users.id, userId))
        .limit(1);

      if (!existingUser) {
        console.log(`Creating missing user with ID: ${userId}`);
        
        // Create user with provided data or defaults
        await db.insert(users).values({
          id: userId,
          name: userData?.name || 'User',
          email: userData?.email || `user-${userId.slice(0, 8)}@example.com`,
          emailVerified: userData?.emailVerified || false,
          image: userData?.image || null,
          createdAt: new Date(),
          updatedAt: new Date(),
        });
        
        console.log(`✅ Created user: ${userId}`);
      }
    } catch (error) {
      console.error('Error ensuring user exists:', error);
      throw error;
    }
  }

  /**
   * Get user by ID
   */
  static async getById(userId: string) {
    const [user] = await db.select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);
    
    return user;
  }

  /**
   * Check if user exists
   */
  static async exists(userId: string): Promise<boolean> {
    const user = await this.getById(userId);
    return !!user;
  }
}
