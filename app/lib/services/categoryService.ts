import type { Category, CategoryWithChildren, CategoryFormData, CategoryType } from '~/lib/types/inventory';

export class CategoryService {
  /**
   * Get all categories for a business by type
   */
  static async getAllByBusinessAndType(
    businessId: string,
    type: CategoryType
  ): Promise<Category[]> {
    const response = await fetch(`/api/categories?businessId=${businessId}&type=${type}`);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || 'Failed to load categories');
    }

    const result = await response.json();
    return result.categories;
  }

  /**
   * Get hierarchical categories (with parent-child relationships)
   */
  static async getHierarchicalCategories(
    businessId: string,
    type: CategoryType
  ): Promise<CategoryWithChildren[]> {
    const response = await fetch(`/api/categories/hierarchical?businessId=${businessId}&type=${type}`);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || 'Failed to load hierarchical categories');
    }

    const result = await response.json();
    return result.categories;
  }

  /**
   * Create a new category
   */
  static async create(
    businessId: string,
    categoryData: CategoryFormData
  ): Promise<Category> {
    const response = await fetch('/api/categories', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        businessId,
        ...categoryData,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || 'Failed to create category');
    }

    const result = await response.json();
    return result.category;
  }

  /**
   * Update a category
   */
  static async update(
    categoryId: string,
    businessId: string,
    updateData: Partial<CategoryFormData>
  ): Promise<Category> {
    const response = await fetch(`/api/categories/${categoryId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        businessId,
        ...updateData,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || 'Failed to update category');
    }

    const result = await response.json();
    return result.category;
  }

  /**
   * Delete a category
   */
  static async delete(categoryId: string, businessId: string): Promise<void> {
    const response = await fetch(`/api/categories/${categoryId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        businessId,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || 'Failed to delete category');
    }
  }

  /**
   * Get category by ID
   */
  static async getById(categoryId: string, businessId: string): Promise<Category> {
    const response = await fetch(`/api/categories/${categoryId}?businessId=${businessId}`);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || 'Failed to load category');
    }

    const result = await response.json();
    return result.category;
  }

  /**
   * Check if category name exists
   */
  static async nameExists(
    name: string,
    type: CategoryType,
    businessId: string,
    excludeCategoryId?: string
  ): Promise<boolean> {
    const params = new URLSearchParams({
      name,
      type,
      businessId,
    });

    if (excludeCategoryId) {
      params.append('excludeId', excludeCategoryId);
    }

    const response = await fetch(`/api/categories/check-name?${params}`);

    if (!response.ok) {
      return false; // Assume name doesn't exist if check fails
    }

    const result = await response.json();
    return result.exists;
  }

  /**
   * Validate category form data
   */
  static validateCategoryFormData(data: CategoryFormData): string[] {
    const errors: string[] = [];

    if (!data.name || data.name.trim().length === 0) {
      errors.push('Category name is required');
    }

    if (data.name && data.name.length > 100) {
      errors.push('Category name must be less than 100 characters');
    }

    if (data.description && data.description.length > 500) {
      errors.push('Description must be less than 500 characters');
    }

    if (!['ingredient', 'product', 'supplier', 'customer'].includes(data.type)) {
      errors.push('Invalid category type');
    }

    if (data.color && !/^#[0-9A-F]{6}$/i.test(data.color)) {
      errors.push('Color must be a valid hex color code');
    }

    if (data.sortOrder !== undefined && (isNaN(data.sortOrder) || data.sortOrder < 0)) {
      errors.push('Sort order must be a positive number');
    }

    return errors;
  }

  /**
   * Get default colors for categories
   */
  static getDefaultColors(): string[] {
    return [
      '#3B82F6', // Blue
      '#10B981', // Green
      '#F59E0B', // Yellow
      '#EF4444', // Red
      '#8B5CF6', // Purple
      '#F97316', // Orange
      '#06B6D4', // Cyan
      '#84CC16', // Lime
      '#EC4899', // Pink
      '#6B7280', // Gray
    ];
  }

  /**
   * Generate a random color for new categories
   */
  static generateRandomColor(): string {
    const colors = this.getDefaultColors();
    return colors[Math.floor(Math.random() * colors.length)];
  }

  /**
   * Sort categories by hierarchy and name
   */
  static sortCategoriesHierarchically(categories: CategoryWithChildren[]): CategoryWithChildren[] {
    const sortRecursively = (cats: CategoryWithChildren[]): CategoryWithChildren[] => {
      return cats
        .sort((a, b) => {
          // First sort by sortOrder, then by name
          const sortOrderA = parseFloat(a.sortOrder || '0');
          const sortOrderB = parseFloat(b.sortOrder || '0');
          
          if (sortOrderA !== sortOrderB) {
            return sortOrderA - sortOrderB;
          }
          
          return a.name.localeCompare(b.name);
        })
        .map(cat => ({
          ...cat,
          children: cat.children ? sortRecursively(cat.children) : [],
        }));
    };

    return sortRecursively(categories);
  }

  /**
   * Flatten hierarchical categories into a flat list with indentation info
   */
  static flattenHierarchicalCategories(
    categories: CategoryWithChildren[],
    level: number = 0
  ): Array<CategoryWithChildren & { level: number; hasChildren: boolean }> {
    const result: Array<CategoryWithChildren & { level: number; hasChildren: boolean }> = [];

    categories.forEach(category => {
      result.push({
        ...category,
        level,
        hasChildren: (category.children?.length || 0) > 0,
      });

      if (category.children && category.children.length > 0) {
        result.push(...this.flattenHierarchicalCategories(category.children, level + 1));
      }
    });

    return result;
  }

  /**
   * Get category breadcrumb path
   */
  static getCategoryBreadcrumb(
    category: CategoryWithChildren,
    allCategories: CategoryWithChildren[]
  ): Category[] {
    const breadcrumb: Category[] = [];
    let current: CategoryWithChildren | undefined = category;

    while (current) {
      breadcrumb.unshift(current);
      
      if (current.parentId) {
        current = this.findCategoryById(current.parentId, allCategories);
      } else {
        current = undefined;
      }
    }

    return breadcrumb;
  }

  /**
   * Find category by ID in hierarchical structure
   */
  private static findCategoryById(
    categoryId: string,
    categories: CategoryWithChildren[]
  ): CategoryWithChildren | undefined {
    for (const category of categories) {
      if (category.id === categoryId) {
        return category;
      }
      
      if (category.children) {
        const found = this.findCategoryById(categoryId, category.children);
        if (found) {
          return found;
        }
      }
    }
    
    return undefined;
  }

  /**
   * Check if a category can be moved to a new parent (prevent circular references)
   */
  static canMoveToParent(
    categoryId: string,
    newParentId: string,
    allCategories: CategoryWithChildren[]
  ): boolean {
    if (categoryId === newParentId) {
      return false; // Can't be its own parent
    }

    // Check if newParentId is a descendant of categoryId
    const isDescendant = (parentId: string, targetId: string): boolean => {
      const parent = this.findCategoryById(parentId, allCategories);
      if (!parent || !parent.children) {
        return false;
      }

      for (const child of parent.children) {
        if (child.id === targetId) {
          return true;
        }
        if (isDescendant(child.id, targetId)) {
          return true;
        }
      }

      return false;
    };

    return !isDescendant(categoryId, newParentId);
  }
}
