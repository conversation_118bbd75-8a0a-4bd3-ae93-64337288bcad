import { eq, and, ilike, count, sql } from 'drizzle-orm'
import { db } from '~/lib/db/connection'
import { ingredients, productIngredients, products, categories } from '~/lib/db/schema'
import { CogsServiceServer } from './cogsService.server'
import type { Ingredient, IngredientFormData, IngredientFilters, IngredientWithCategory } from '~/lib/types/inventory'

/**
 * Server-only ingredient service that uses the database
 * This should only be imported in server-side code (loaders, actions)
 */
export class IngredientServiceServer {
  /**
   * Create a new ingredient for a specific business
   */
  static async create(businessId: string, data: IngredientFormData): Promise<Ingredient> {
    const [ingredient] = await db.insert(ingredients).values({
      name: data.name,
      baseUnitCost: data.baseUnitCost,
      baseUnitQuantity: data.baseUnitQuantity,
      unit: data.unit,
      categoryId: data.categoryId || null,
      supplierInfo: data.supplierInfo || null,
      notes: data.notes || null,
      isActive: data.isActive,
      businessId,
    }).returning()

    return ingredient
  }

  /**
   * Get all ingredients for a specific business
   */
  static async getAllByBusiness(businessId: string, filters?: IngredientFilters): Promise<Ingredient[]> {

    // Build where conditions
    const whereConditions = [eq(ingredients.businessId, businessId)]

    // Apply filters
    if (filters?.search) {
      whereConditions.push(ilike(ingredients.name, `%${filters.search}%`))
    }

    if (filters?.unit) {
      whereConditions.push(eq(ingredients.unit, filters.unit))
    }

    if (filters?.isActive !== undefined) {
      whereConditions.push(eq(ingredients.isActive, filters.isActive))
    }

    return await db.select()
      .from(ingredients)
      .where(and(...whereConditions))
      .orderBy(ingredients.name)
  }

  /**
   * Get all ingredients for a specific business with category names
   */
  static async getAllByBusinessWithCategories(businessId: string, filters?: IngredientFilters): Promise<IngredientWithCategory[]> {

    // Build where conditions
    const whereConditions = [eq(ingredients.businessId, businessId)]

    // Apply filters
    if (filters?.search) {
      whereConditions.push(ilike(ingredients.name, `%${filters.search}%`))
    }

    if (filters?.unit) {
      whereConditions.push(eq(ingredients.unit, filters.unit))
    }

    if (filters?.isActive !== undefined) {
      whereConditions.push(eq(ingredients.isActive, filters.isActive))
    }

    return await db.select({
      id: ingredients.id,
      name: ingredients.name,
      baseUnitCost: ingredients.baseUnitCost,
      baseUnitQuantity: ingredients.baseUnitQuantity,
      unit: ingredients.unit,
      categoryId: ingredients.categoryId,
      supplierInfo: ingredients.supplierInfo,
      notes: ingredients.notes,
      isActive: ingredients.isActive,
      businessId: ingredients.businessId,
      createdAt: ingredients.createdAt,
      updatedAt: ingredients.updatedAt,
      categoryName: categories.name ?? undefined,
      categoryColor: categories.color ?? undefined,
      usageCount: sql<number>`(
        SELECT COUNT(DISTINCT ${productIngredients.productId})::int
        FROM ${productIngredients}
        WHERE ${productIngredients.ingredientId} = ${ingredients.id}
      )`.as('usageCount'),
    })
    .from(ingredients)
    .leftJoin(categories, eq(ingredients.categoryId, categories.id))
    .where(and(...whereConditions))
    .orderBy(ingredients.name)
  }

  /**
   * Get ingredient by ID and business (ensures user can only access their own ingredients)
   */
  static async getByIdAndBusiness(id: string, businessId: string): Promise<Ingredient | undefined> {

    const [ingredient] = await db.select()
      .from(ingredients)
      .where(and(eq(ingredients.id, id), eq(ingredients.businessId, businessId)))
      .limit(1)
    
    return ingredient
  }

  /**
   * Update an ingredient (ensures user can only update their own ingredients)
   */
  static async update(id: string, businessId: string, data: Partial<IngredientFormData>): Promise<void> {

    // Get current ingredient data to check for cost changes
    const [currentIngredient] = await db.select()
      .from(ingredients)
      .where(and(eq(ingredients.id, id), eq(ingredients.businessId, businessId)))
      .limit(1)

    if (!currentIngredient) {
      throw new Error('Ingredient not found')
    }

    // Check if cost-related fields are changing
    const costChanged = (
      (data.baseUnitCost && data.baseUnitCost !== currentIngredient.baseUnitCost) ||
      (data.baseUnitQuantity && data.baseUnitQuantity !== currentIngredient.baseUnitQuantity)
    )

    // Update the ingredient
    await db.update(ingredients)
      .set({
        name: data.name,
        baseUnitCost: data.baseUnitCost,
        baseUnitQuantity: data.baseUnitQuantity,
        unit: data.unit,
        categoryId: data.categoryId || null,
        supplierInfo: data.supplierInfo || null,
        notes: data.notes || null,
        isActive: data.isActive,
        updatedAt: new Date(),
      })
      .where(and(eq(ingredients.id, id), eq(ingredients.businessId, businessId)))

    // Trigger COGS recalculation if cost changed
    if (costChanged) {
      try {
        // Note: RBAC middleware will provide userId context
        await CogsServiceServer.recalculateCogsForIngredientChange(
          id,
          businessId,
          businessId // Using businessId as placeholder for userId until RBAC provides it
        )
      } catch (error) {
        console.error('Failed to recalculate COGS after ingredient update:', error)
        // Don't throw error here to avoid breaking the ingredient update
        // COGS recalculation failure should not prevent ingredient updates
      }
    }
  }

  /**
   * Delete an ingredient
   */
  static async delete(id: string, businessId: string): Promise<void> {
    await db.delete(ingredients)
      .where(and(eq(ingredients.id, id), eq(ingredients.businessId, businessId)))
  }

  /**
   * Get ingredient count for a business
   */
  static async countByBusiness(businessId: string): Promise<number> {
    const [result] = await db.select({ count: count() })
      .from(ingredients)
      .where(eq(ingredients.businessId, businessId))
    
    return result?.count ?? 0
  }

  /**
   * Search ingredients by name for a specific business
   */
  static async searchByNameAndBusiness(query: string, businessId: string): Promise<Ingredient[]> {

    return await db.select()
      .from(ingredients)
      .where(and(
        eq(ingredients.businessId, businessId),
        ilike(ingredients.name, `%${query}%`)
      ))
      .orderBy(ingredients.name)
  }

  /**
   * Check if ingredient name already exists in business (for validation)
   */
  static async nameExistsInBusiness(name: string, businessId: string, excludeId?: string): Promise<boolean> {

    // Build where conditions
    const whereConditions = [
      eq(ingredients.businessId, businessId),
      ilike(ingredients.name, name)
    ]

    if (excludeId) {
      whereConditions.push(eq(ingredients.id, excludeId))
    }

    const query = db.select({ count: count() })
      .from(ingredients)
      .where(and(...whereConditions))

    const [result] = await query
    return (result?.count ?? 0) > 0
  }

  /**
   * Get ingredient with usage information (which products use it)
   */
  static async getWithUsage(id: string, businessId: string) {

    // Get the ingredient with category information
    const [ingredientWithCategory] = await db.select({
      id: ingredients.id,
      name: ingredients.name,
      baseUnitCost: ingredients.baseUnitCost,
      baseUnitQuantity: ingredients.baseUnitQuantity,
      unit: ingredients.unit,
      categoryId: ingredients.categoryId,
      supplierInfo: ingredients.supplierInfo,
      notes: ingredients.notes,
      isActive: ingredients.isActive,
      businessId: ingredients.businessId,
      createdAt: ingredients.createdAt,
      updatedAt: ingredients.updatedAt,
      categoryName: categories.name,
      categoryColor: categories.color,
    })
    .from(ingredients)
    .leftJoin(categories, eq(ingredients.categoryId, categories.id))
    .where(and(eq(ingredients.id, id), eq(ingredients.businessId, businessId)))
    .limit(1)

    if (!ingredientWithCategory) {
      return undefined
    }

    // Get product ingredients that use this ingredient with product details and category names
    const productUsageData = await db.select({
      id: productIngredients.id,
      productId: productIngredients.productId,
      ingredientId: productIngredients.ingredientId,
      quantityNeeded: productIngredients.quantityNeeded,
      createdAt: productIngredients.createdAt,
      product: {
        id: products.id,
        name: products.name,
        description: products.description,
        note: products.note,
        categoryId: products.categoryId,
        isActive: products.isActive,
        cogsPerCup: products.cogsPerCup,
        businessId: products.businessId,
        createdAt: products.createdAt,
        updatedAt: products.updatedAt,
      }
    })
    .from(productIngredients)
    .innerJoin(products, eq(productIngredients.productId, products.id))
    .where(and(
      eq(productIngredients.ingredientId, id),
      eq(products.businessId, businessId) // Ensure products belong to the same business
    ))

    // Calculate cost per unit for the ingredient with safety checks
    const baseUnitCost = parseFloat(ingredientWithCategory.baseUnitCost) || 0
    const baseUnitQuantity = parseFloat(ingredientWithCategory.baseUnitQuantity) || 1
    const costPerUnit = baseUnitQuantity > 0 ? baseUnitCost / baseUnitQuantity : 0

    // Calculate usage statistics with safety checks
    const totalProducts = productUsageData.length
    const totalUsage = productUsageData.reduce((sum, usage) => {
      const quantity = parseFloat(usage.quantityNeeded) || 0
      return sum + quantity
    }, 0)
    const averageUsage = totalProducts > 0 ? totalUsage / totalProducts : 0
    const averageCost = averageUsage * costPerUnit

    return {
      ingredient: ingredientWithCategory,
      productUsage: productUsageData.map(usage => {
        const quantityNeeded = parseFloat(usage.quantityNeeded) || 0
        return {
          ...usage,
          costPerCup: quantityNeeded * costPerUnit
        }
      }),
      statistics: {
        totalProducts,
        averageUsage,
        averageCost
      }
    }
  }
}
