import type { Ingredient, IngredientFormData, IngredientFilters } from '~/lib/types/inventory'

/**
 * Client-side ingredient service that makes API calls
 * This can be used in both client and server components
 */
export class IngredientService {
  /**
   * Create a new ingredient for a specific business
   */
  static async create(businessId: string, data: IngredientFormData): Promise<Ingredient> {
    const formData = new FormData()
    formData.append('_action', 'create')
    formData.append('businessId', businessId)
    formData.append('name', data.name)
    formData.append('baseUnitCost', data.baseUnitCost)
    formData.append('baseUnitQuantity', data.baseUnitQuantity)
    formData.append('unit', data.unit)
    if (data.supplierInfo) formData.append('supplierInfo', data.supplierInfo)
    if (data.notes) formData.append('notes', data.notes)
    formData.append('isActive', data.isActive.toString())

    const response = await fetch('/api/ingredients', {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || 'Failed to create ingredient')
    }

    const result = await response.json()
    return result.ingredient
  }

  /**
   * Get all ingredients for a specific business
   */
  static async getAllByBusiness(businessId: string, filters?: IngredientFilters): Promise<Ingredient[]> {
    const params = new URLSearchParams({ businessId })
    
    if (filters?.search) params.append('search', filters.search)
    if (filters?.unit) params.append('unit', filters.unit)
    if (filters?.minCost !== undefined) params.append('minCost', filters.minCost.toString())
    if (filters?.maxCost !== undefined) params.append('maxCost', filters.maxCost.toString())
    if (filters?.isActive !== undefined) params.append('isActive', filters.isActive.toString())

    const response = await fetch(`/api/ingredients?${params.toString()}`)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || 'Failed to load ingredients')
    }

    const result = await response.json()
    return result.ingredients
  }

  /**
   * Get ingredient by ID and business
   */
  static async getByIdAndBusiness(id: string, businessId: string): Promise<Ingredient | undefined> {
    const response = await fetch(`/api/ingredients/${id}?businessId=${businessId}`)

    if (!response.ok) {
      if (response.status === 404) {
        return undefined
      }
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || 'Failed to load ingredient')
    }

    const result = await response.json()
    return result.ingredient
  }

  /**
   * Update an ingredient
   */
  static async update(id: string, businessId: string, data: Partial<IngredientFormData>): Promise<void> {
    const formData = new FormData()
    formData.append('_action', 'update')
    formData.append('id', id)
    formData.append('businessId', businessId)
    if (data.name) formData.append('name', data.name)
    if (data.baseUnitCost) formData.append('baseUnitCost', data.baseUnitCost)
    if (data.baseUnitQuantity) formData.append('baseUnitQuantity', data.baseUnitQuantity)
    if (data.unit) formData.append('unit', data.unit)
    if (data.supplierInfo) formData.append('supplierInfo', data.supplierInfo)
    if (data.notes) formData.append('notes', data.notes)
    if (data.isActive !== undefined) formData.append('isActive', data.isActive.toString())

    const response = await fetch('/api/ingredients', {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || 'Failed to update ingredient')
    }
  }

  /**
   * Delete an ingredient
   */
  static async delete(id: string, businessId: string): Promise<void> {
    const formData = new FormData()
    formData.append('_action', 'delete')
    formData.append('id', id)
    formData.append('businessId', businessId)

    const response = await fetch('/api/ingredients', {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || 'Failed to delete ingredient')
    }
  }

  /**
   * Search ingredients by name for a specific business
   */
  static async searchByNameAndBusiness(query: string, businessId: string): Promise<Ingredient[]> {
    const params = new URLSearchParams({ 
      businessId,
      search: query 
    })

    const response = await fetch(`/api/ingredients?${params.toString()}`)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || 'Failed to search ingredients')
    }

    const result = await response.json()
    return result.ingredients
  }

  /**
   * Get ingredient count for a business
   */
  static async countByBusiness(businessId: string): Promise<number> {
    const response = await fetch(`/api/ingredients/count?businessId=${businessId}`)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || 'Failed to get ingredient count')
    }

    const result = await response.json()
    return result.count
  }

  /**
   * Check if ingredient name already exists in business (for validation)
   */
  static async nameExistsInBusiness(name: string, businessId: string, excludeId?: string): Promise<boolean> {
    const params = new URLSearchParams({ 
      businessId,
      name 
    })
    
    if (excludeId) {
      params.append('excludeId', excludeId)
    }

    const response = await fetch(`/api/ingredients/check-name?${params.toString()}`)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || 'Failed to check ingredient name')
    }

    const result = await response.json()
    return result.exists
  }
}
