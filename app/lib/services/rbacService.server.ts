import { eq, and, inArray, sql } from 'drizzle-orm';
import { db } from '~/lib/db/connection';
import {
  roles,
  permissions,
  rolePermissions,
  userRoles,
  users,
  businesses,
  type Role,
  type Permission,
  type UserRole,
  type NewUserRole,
  type NewRole,
  type Business,
} from '~/lib/db/schema';

export interface UserPermissions {
  userId: string;
  businessId?: string;
  permissions: string[];
  roles: string[];
}

export interface RoleWithPermissions extends Role {
  permissions: Permission[];
}

export interface UserWithRoles {
  id: string;
  name: string;
  email: string;
  roles: Array<{
    role: Role;
    businessId?: string;
    assignedAt: Date;
    expiresAt?: Date;
  }>;
}

export class RBACService {
  /**
   * Get all permissions for a user in a specific business context
   */
  static async getUserPermissions(userId: string, businessId?: string): Promise<UserPermissions> {
    // Normalize businessId - treat "undefined", "null", empty string as undefined
    const normalizedBusinessId = businessId && businessId !== 'undefined' && businessId !== 'null' && businessId.trim() !== ''
      ? businessId
      : undefined;

    // Handle temp business IDs - return empty permissions since temp businesses don't have RBAC
    if (normalizedBusinessId && normalizedBusinessId.startsWith('temp-')) {
      return {
        userId,
        businessId: normalizedBusinessId,
        permissions: [],
        roles: [],
      };
    }

    const query = db
      .select({
        permission: permissions.name,
        role: roles.name,
      })
      .from(userRoles)
      .innerJoin(roles, eq(userRoles.roleId, roles.id))
      .innerJoin(rolePermissions, eq(roles.id, rolePermissions.roleId))
      .innerJoin(permissions, eq(rolePermissions.permissionId, permissions.id))
      .where(
        and(
          eq(userRoles.userId, userId),
          eq(userRoles.isActive, true),
          eq(roles.isActive, true),
          normalizedBusinessId ? eq(userRoles.businessId, normalizedBusinessId) : sql`true`
        )
      );

    const results = await query;

    const uniquePermissions = [...new Set(results.map(r => r.permission))];
    const uniqueRoles = [...new Set(results.map(r => r.role))];

    return {
      userId,
      businessId: normalizedBusinessId,
      permissions: uniquePermissions,
      roles: uniqueRoles,
    };
  }

  /**
   * Check if a user has a specific permission
   */
  static async hasPermission(
    userId: string,
    permission: string,
    businessId?: string
  ): Promise<boolean> {
    const userPermissions = await this.getUserPermissions(userId, businessId);
    return userPermissions.permissions.includes(permission);
  }

  /**
   * Check if a user has any of the specified permissions
   */
  static async hasAnyPermission(
    userId: string,
    permissions: string[],
    businessId?: string
  ): Promise<boolean> {
    const userPermissions = await this.getUserPermissions(userId, businessId);
    return permissions.some(permission => userPermissions.permissions.includes(permission));
  }

  /**
   * Check if a user has all of the specified permissions
   */
  static async hasAllPermissions(
    userId: string,
    permissions: string[],
    businessId?: string
  ): Promise<boolean> {
    const userPermissions = await this.getUserPermissions(userId, businessId);
    return permissions.every(permission => userPermissions.permissions.includes(permission));
  }

  /**
   * Check if a user has a specific role
   */
  static async hasRole(
    userId: string,
    roleName: string,
    businessId?: string
  ): Promise<boolean> {
    const userPermissions = await this.getUserPermissions(userId, businessId);
    return userPermissions.roles.includes(roleName);
  }

  /**
   * Assign a role to a user
   */
  static async assignRole(
    userId: string,
    roleName: string,
    assignedBy: string,
    businessId?: string,
    expiresAt?: Date
  ): Promise<UserRole> {
    // Get role by name
    const role = await db
      .select()
      .from(roles)
      .where(and(eq(roles.name, roleName), eq(roles.isActive, true)))
      .limit(1);

    if (!role.length) {
      throw new Error(`Role '${roleName}' not found`);
    }

    // Check if assignment already exists
    const existing = await db
      .select()
      .from(userRoles)
      .where(
        and(
          eq(userRoles.userId, userId),
          eq(userRoles.roleId, role[0].id),
          businessId ? eq(userRoles.businessId, businessId) : sql`"businessId" IS NULL`
        )
      )
      .limit(1);

    if (existing.length) {
      // Update existing assignment
      const updated = await db
        .update(userRoles)
        .set({
          isActive: true,
          assignedBy,
          assignedAt: new Date(),
          expiresAt,
          updatedAt: new Date(),
        })
        .where(eq(userRoles.id, existing[0].id))
        .returning();
      
      return updated[0];
    }

    // Create new assignment
    const newAssignment: NewUserRole = {
      userId,
      roleId: role[0].id,
      businessId,
      assignedBy,
      assignedAt: new Date(),
      expiresAt,
      isActive: true,
    };

    const created = await db.insert(userRoles).values(newAssignment).returning();
    return created[0];
  }

  /**
   * Remove a role from a user
   */
  static async removeRole(
    userId: string,
    roleName: string,
    businessId?: string
  ): Promise<boolean> {
    const role = await db
      .select()
      .from(roles)
      .where(eq(roles.name, roleName))
      .limit(1);

    if (!role.length) {
      return false;
    }

    const result = await db
      .update(userRoles)
      .set({ isActive: false, updatedAt: new Date() })
      .where(
        and(
          eq(userRoles.userId, userId),
          eq(userRoles.roleId, role[0].id),
          businessId ? eq(userRoles.businessId, businessId) : sql`"businessId" IS NULL`
        )
      );

    return (result.rowCount ?? 0) > 0;
  }

  /**
   * Get all roles with their permissions
   */
  static async getAllRoles(): Promise<RoleWithPermissions[]> {
    const rolesWithPermissions = await db
      .select({
        role: roles,
        permission: permissions,
      })
      .from(roles)
      .leftJoin(rolePermissions, eq(roles.id, rolePermissions.roleId))
      .leftJoin(permissions, eq(rolePermissions.permissionId, permissions.id))
      .where(eq(roles.isActive, true))
      .orderBy(roles.name);

    // Group permissions by role
    const roleMap = new Map<string, RoleWithPermissions>();
    
    for (const row of rolesWithPermissions) {
      const roleId = row.role.id;
      
      if (!roleMap.has(roleId)) {
        roleMap.set(roleId, {
          ...row.role,
          permissions: [],
        });
      }
      
      if (row.permission) {
        roleMap.get(roleId)!.permissions.push(row.permission);
      }
    }

    return Array.from(roleMap.values());
  }

  /**
   * Get all permissions
   */
  static async getAllPermissions(): Promise<Permission[]> {
    return await db
      .select()
      .from(permissions)
      .orderBy(permissions.resource, permissions.action);
  }

  /**
   * Get users with their roles for a specific business
   */
  static async getBusinessUsers(businessId: string): Promise<UserWithRoles[]> {
    // Handle temp business IDs - return empty array since temp businesses don't have users
    if (businessId.startsWith('temp-')) {
      return [];
    }

    const usersWithRoles = await db
      .select({
        user: {
          id: users.id,
          name: users.name,
          email: users.email,
        },
        role: roles,
        userRole: {
          businessId: userRoles.businessId,
          assignedAt: userRoles.assignedAt,
          expiresAt: userRoles.expiresAt,
        },
      })
      .from(users)
      .innerJoin(userRoles, eq(users.id, userRoles.userId))
      .innerJoin(roles, eq(userRoles.roleId, roles.id))
      .where(
        and(
          eq(userRoles.businessId, businessId),
          eq(userRoles.isActive, true),
          eq(roles.isActive, true)
        )
      )
      .orderBy(users.name);

    // Group roles by user
    const userMap = new Map<string, UserWithRoles>();
    
    for (const row of usersWithRoles) {
      const userId = row.user.id;
      
      if (!userMap.has(userId)) {
        userMap.set(userId, {
          ...row.user,
          roles: [],
        });
      }
      
      userMap.get(userId)!.roles.push({
        role: row.role,
        businessId: row.userRole.businessId || undefined,
        assignedAt: row.userRole.assignedAt,
        expiresAt: row.userRole.expiresAt || undefined,
      });
    }

    return Array.from(userMap.values());
  }

  /**
   * Create a custom role
   */
  static async createRole(
    name: string,
    displayName: string,
    description?: string,
    permissionIds: string[] = []
  ): Promise<Role> {
    // Create the role
    const newRole: NewRole = {
      name,
      displayName,
      description,
      isSystemRole: false,
      isActive: true,
    };

    const createdRole = await db.insert(roles).values(newRole).returning();
    const role = createdRole[0];

    // Assign permissions if provided
    if (permissionIds.length > 0) {
      await this.assignPermissionsToRoleByIds(role.id, permissionIds);
    }

    return role;
  }

  /**
   * Assign permissions to a role by permission names
   */
  static async assignPermissionsToRole(
    roleId: string,
    permissionNames: string[]
  ): Promise<void> {
    // Get permission IDs
    const permissionList = await db
      .select({ id: permissions.id })
      .from(permissions)
      .where(inArray(permissions.name, permissionNames));

    if (permissionList.length !== permissionNames.length) {
      throw new Error('Some permissions not found');
    }

    // Create role-permission assignments
    const assignments = permissionList.map(permission => ({
      roleId,
      permissionId: permission.id,
    }));

    await db.insert(rolePermissions).values(assignments).onConflictDoNothing();
  }

  /**
   * Assign permissions to a role by permission IDs
   */
  static async assignPermissionsToRoleByIds(
    roleId: string,
    permissionIds: string[]
  ): Promise<void> {
    if (permissionIds.length === 0) {
      return;
    }

    // Verify all permission IDs exist
    const existingPermissions = await db
      .select({ id: permissions.id })
      .from(permissions)
      .where(inArray(permissions.id, permissionIds));

    if (existingPermissions.length !== permissionIds.length) {
      throw new Error('Some permissions not found');
    }

    // Create role-permission assignments
    const assignments = permissionIds.map(permissionId => ({
      roleId,
      permissionId,
    }));

    await db.insert(rolePermissions).values(assignments).onConflictDoNothing();
  }

  /**
   * Auto-assign business owner role to user when they create a business
   */
  static async assignBusinessOwnerRole(
    userId: string,
    businessId: string
  ): Promise<UserRole> {
    return await this.assignRole(userId, 'business_owner', userId, businessId);
  }

  /**
   * Get user permissions grouped by business context
   */
  static async getUserPermissionsGroupedByBusiness(userId: string): Promise<{
    globalPermissions: UserPermissions;
    businessPermissions: Array<UserPermissions & { businessName: string }>;
  }> {
    // Get global permissions (where businessId is null)
    const globalPermissions = await db
      .select({
        permission: permissions.name,
        role: roles.name,
      })
      .from(userRoles)
      .innerJoin(roles, eq(userRoles.roleId, roles.id))
      .innerJoin(rolePermissions, eq(roles.id, rolePermissions.roleId))
      .innerJoin(permissions, eq(rolePermissions.permissionId, permissions.id))
      .where(
        and(
          eq(userRoles.userId, userId),
          eq(userRoles.isActive, true),
          eq(roles.isActive, true),
          sql`${userRoles.businessId} IS NULL`
        )
      );

    // Get business-specific permissions with business names
    const businessPermissions = await db
      .select({
        permission: permissions.name,
        role: roles.name,
        businessId: userRoles.businessId,
        businessName: sql<string>`COALESCE(${businesses.name}, 'Unknown Business')`,
      })
      .from(userRoles)
      .innerJoin(roles, eq(userRoles.roleId, roles.id))
      .innerJoin(rolePermissions, eq(roles.id, rolePermissions.roleId))
      .innerJoin(permissions, eq(rolePermissions.permissionId, permissions.id))
      .leftJoin(businesses, eq(userRoles.businessId, businesses.id))
      .where(
        and(
          eq(userRoles.userId, userId),
          eq(userRoles.isActive, true),
          eq(roles.isActive, true),
          sql`${userRoles.businessId} IS NOT NULL`
        )
      );

    // Process global permissions
    const globalUniquePermissions = [...new Set(globalPermissions.map(r => r.permission))];
    const globalUniqueRoles = [...new Set(globalPermissions.map(r => r.role))];

    // Group business permissions by business
    const businessMap = new Map<string, {
      businessId: string;
      businessName: string;
      permissions: string[];
      roles: string[];
    }>();

    for (const row of businessPermissions) {
      const businessId = row.businessId!;
      if (!businessMap.has(businessId)) {
        businessMap.set(businessId, {
          businessId,
          businessName: row.businessName,
          permissions: [],
          roles: [],
        });
      }
      
      const business = businessMap.get(businessId)!;
      if (!business.permissions.includes(row.permission)) {
        business.permissions.push(row.permission);
      }
      if (!business.roles.includes(row.role)) {
        business.roles.push(row.role);
      }
    }

    return {
      globalPermissions: {
        userId,
        permissions: globalUniquePermissions,
        roles: globalUniqueRoles,
      },
      businessPermissions: Array.from(businessMap.values()).map(business => ({
        userId,
        businessId: business.businessId,
        businessName: business.businessName,
        permissions: business.permissions,
        roles: business.roles,
      })),
    };
  }

  /**
   * Get user permissions flattened by permission with business contexts
   */
  static async getUserPermissionsFlattened(userId: string): Promise<{
    permissions: Array<{
      permission: string;
      contexts: string[]; // Array of business names or 'Global'
    }>;
    roles: Array<{
      role: string;
      contexts: string[]; // Array of business names or 'Global'
    }>;
  }> {
    // Get all permissions with their contexts
    const allPermissions = await db
      .select({
        permission: permissions.name,
        role: roles.name,
        businessId: userRoles.businessId,
        businessName: sql<string>`CASE WHEN ${userRoles.businessId} IS NULL THEN 'Global' ELSE COALESCE(${businesses.name}, 'Unknown Business') END`,
      })
      .from(userRoles)
      .innerJoin(roles, eq(userRoles.roleId, roles.id))
      .innerJoin(rolePermissions, eq(roles.id, rolePermissions.roleId))
      .innerJoin(permissions, eq(rolePermissions.permissionId, permissions.id))
      .leftJoin(businesses, eq(userRoles.businessId, businesses.id))
      .where(
        and(
          eq(userRoles.userId, userId),
          eq(userRoles.isActive, true),
          eq(roles.isActive, true)
        )
      );

    // Group permissions by permission name
    const permissionMap = new Map<string, Set<string>>();
    const roleMap = new Map<string, Set<string>>();

    for (const row of allPermissions) {
      // Group permissions
      if (!permissionMap.has(row.permission)) {
        permissionMap.set(row.permission, new Set());
      }
      permissionMap.get(row.permission)!.add(row.businessName);

      // Group roles
      if (!roleMap.has(row.role)) {
        roleMap.set(row.role, new Set());
      }
      roleMap.get(row.role)!.add(row.businessName);
    }

    // Convert to arrays and sort
    const permissionsList = Array.from(permissionMap.entries())
      .map(([permission, contexts]) => ({
        permission,
        contexts: Array.from(contexts).sort((a, b) => {
          // Sort Global first, then alphabetically
          if (a === 'Global' && b !== 'Global') return -1;
          if (a !== 'Global' && b === 'Global') return 1;
          return a.localeCompare(b);
        }),
      }))
      .sort((a, b) => a.permission.localeCompare(b.permission));

    const rolesList = Array.from(roleMap.entries())
      .map(([role, contexts]) => ({
        role,
        contexts: Array.from(contexts).sort((a, b) => {
          // Sort Global first, then alphabetically
          if (a === 'Global' && b !== 'Global') return -1;
          if (a !== 'Global' && b === 'Global') return 1;
          return a.localeCompare(b);
        }),
      }))
      .sort((a, b) => a.role.localeCompare(b.role));

    return {
      permissions: permissionsList,
      roles: rolesList,
    };
  }

  /**
   * Check if user is business owner
   */
  static async isBusinessOwner(
    userId: string,
    businessId: string
  ): Promise<boolean> {
    // Handle temp business IDs - assume user is owner of temp businesses
    if (businessId.startsWith('temp-')) {
      return true;
    }
    return await this.hasRole(userId, 'business_owner', businessId);
  }

  /**
   * Check if user can access business
   */
  static async canAccessBusiness(
    userId: string,
    businessId: string
  ): Promise<boolean> {
    // Handle temp business IDs - allow access to temp businesses
    if (businessId.startsWith('temp-')) {
      return true;
    }
    const userPermissions = await this.getUserPermissions(userId, businessId);
    return userPermissions.permissions.includes('business.read');
  }

  /**
   * Get all businesses a user has access to (either as owner or through roles)
   */
  static async getUserBusinesses(userId: string): Promise<Business[]> {
    // Get businesses where user is the owner
    const ownedBusinesses = await db.select()
      .from(businesses)
      .where(eq(businesses.userId, userId))
      .orderBy(businesses.name);

    // Get businesses where user has roles
    const businessesWithRoles = await db
      .select({
        business: businesses,
      })
      .from(userRoles)
      .innerJoin(businesses, eq(userRoles.businessId, businesses.id))
      .where(
        and(
          eq(userRoles.userId, userId),
          eq(userRoles.isActive, true),
          sql`${userRoles.businessId} IS NOT NULL`
        )
      )
      .groupBy(businesses.id, businesses.name, businesses.description, businesses.note, businesses.currency, businesses.logo, businesses.userId, businesses.createdAt, businesses.updatedAt)
      .orderBy(businesses.name);

    // Combine and deduplicate businesses
    const allBusinesses = new Map<string, Business>();
    
    // Add owned businesses
    for (const business of ownedBusinesses) {
      allBusinesses.set(business.id, business);
    }
    
    // Add businesses with roles (avoid duplicates)
    for (const { business } of businessesWithRoles) {
      if (!allBusinesses.has(business.id)) {
        allBusinesses.set(business.id, business);
      }
    }

    console.log("allBusinesses", allBusinesses)

    return Array.from(allBusinesses.values()).sort((a, b) => a.name.localeCompare(b.name));
  }

  /**
   * Update an existing role
   */
  static async updateRole(
    roleId: string,
    updates: {
      name?: string;
      description?: string;
      isActive?: boolean;
    }
  ): Promise<void> {
    await db
      .update(roles)
      .set({
        ...updates,
        updatedAt: new Date(),
      })
      .where(eq(roles.id, roleId));
  }

  /**
   * Delete a role (soft delete by setting isActive to false)
   */
  static async deleteRole(roleId: string): Promise<void> {
    // Get the current role to append timestamp to name
    const [currentRole] = await db
      .select({ name: roles.name })
      .from(roles)
      .where(eq(roles.id, roleId))
      .limit(1);

    if (!currentRole) {
      throw new Error('Role not found');
    }

    // First, remove all user assignments for this role
    await db
      .update(userRoles)
      .set({ isActive: false, updatedAt: new Date() })
      .where(eq(userRoles.roleId, roleId));

    // Then soft delete the role and append timestamp to name to avoid unique constraint issues
    const timestamp = Date.now();
    const deletedName = `${currentRole.name}_deleted_${timestamp}`;
    
    await db
      .update(roles)
      .set({ 
        isActive: false, 
        name: deletedName,
        updatedAt: new Date() 
      })
      .where(eq(roles.id, roleId));
  }

  /**
   * Remove specific permissions from a role
   */
  static async removePermissionsFromRole(
    roleId: string,
    permissionIds: string[]
  ): Promise<void> {
    await db
      .delete(rolePermissions)
      .where(
        and(
          eq(rolePermissions.roleId, roleId),
          inArray(rolePermissions.permissionId, permissionIds)
        )
      );
  }

  /**
   * Get all permissions for a specific role
   */
  static async getRolePermissions(roleId: string): Promise<Array<{
    id: string;
    name: string;
    description: string | null;
  }>> {
    const result = await db
      .select({
        id: permissions.id,
        name: permissions.name,
        description: permissions.description,
      })
      .from(rolePermissions)
      .innerJoin(permissions, eq(rolePermissions.permissionId, permissions.id))
      .where(eq(rolePermissions.roleId, roleId));

    return result;
  }

  /**
   * Update role permissions (replace all existing permissions)
   */
  static async updateRolePermissions(
    roleId: string,
    permissionIds: string[]
  ): Promise<void> {
    // Remove all existing permissions for this role
    await db
      .delete(rolePermissions)
      .where(eq(rolePermissions.roleId, roleId));

    // Add new permissions
    if (permissionIds.length > 0) {
      await this.assignPermissionsToRoleByIds(roleId, permissionIds);
    }
  }
}