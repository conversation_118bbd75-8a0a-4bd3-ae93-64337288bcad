/**
 * Comprehensive error handling and fallback mechanisms for session management
 */

import { toast } from 'sonner';

/**
 * Types of session errors
 */
export enum SessionErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  REDIS_ERROR = 'REDIS_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

/**
 * Session error details
 */
export interface SessionError {
  type: SessionErrorType;
  message: string;
  originalError?: Error;
  timestamp: number;
  context?: Record<string, any>;
  retryable: boolean;
}

/**
 * Error handling configuration
 */
export interface SessionErrorConfig {
  /** Whether to show user notifications for errors */
  showNotifications: boolean;
  /** Whether to log errors to console */
  logErrors: boolean;
  /** Whether to attempt automatic recovery */
  autoRecover: boolean;
  /** Maximum number of retry attempts */
  maxRetries: number;
  /** Base delay for exponential backoff (ms) */
  baseRetryDelay: number;
}

const DEFAULT_ERROR_CONFIG: SessionErrorConfig = {
  showNotifications: true,
  logErrors: true,
  autoRecover: true,
  maxRetries: 3,
  baseRetryDelay: 1000,
};

/**
 * Session error handler class
 */
export class SessionErrorHandler {
  private config: SessionErrorConfig;
  private errorHistory: SessionError[] = [];
  private retryAttempts: Map<string, number> = new Map();

  constructor(config: Partial<SessionErrorConfig> = {}) {
    this.config = { ...DEFAULT_ERROR_CONFIG, ...config };
  }

  /**
   * Handle a session error with appropriate fallback mechanisms
   */
  public async handleError(
    error: Error | SessionError,
    context?: Record<string, any>
  ): Promise<SessionError> {
    const sessionError = this.normalizeError(error, context);
    
    // Log error if enabled
    if (this.config.logErrors) {
      this.logError(sessionError);
    }

    // Add to error history
    this.errorHistory.push(sessionError);
    
    // Keep only last 50 errors
    if (this.errorHistory.length > 50) {
      this.errorHistory = this.errorHistory.slice(-50);
    }

    // Show user notification if enabled
    if (this.config.showNotifications) {
      this.showErrorNotification(sessionError);
    }

    // Attempt automatic recovery if enabled and error is retryable
    if (this.config.autoRecover && sessionError.retryable) {
      await this.attemptRecovery(sessionError);
    }

    return sessionError;
  }

  /**
   * Normalize different error types into SessionError
   */
  private normalizeError(error: Error | SessionError, context?: Record<string, any>): SessionError {
    if (this.isSessionError(error)) {
      return error;
    }

    const errorMessage = error.message.toLowerCase();
    let type = SessionErrorType.UNKNOWN_ERROR;
    let retryable = false;

    // Classify error type based on message
    if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
      type = SessionErrorType.NETWORK_ERROR;
      retryable = true;
    } else if (errorMessage.includes('timeout') || errorMessage.includes('aborted')) {
      type = SessionErrorType.TIMEOUT_ERROR;
      retryable = true;
    } else if (errorMessage.includes('401') || errorMessage.includes('unauthorized')) {
      type = SessionErrorType.AUTHENTICATION_ERROR;
      retryable = false;
    } else if (errorMessage.includes('500') || errorMessage.includes('server')) {
      type = SessionErrorType.SERVER_ERROR;
      retryable = true;
    } else if (errorMessage.includes('redis') || errorMessage.includes('cache')) {
      type = SessionErrorType.REDIS_ERROR;
      retryable = true;
    } else if (errorMessage.includes('validation') || errorMessage.includes('invalid')) {
      type = SessionErrorType.VALIDATION_ERROR;
      retryable = false;
    }

    return {
      type,
      message: error.message,
      originalError: error,
      timestamp: Date.now(),
      context,
      retryable,
    };
  }

  /**
   * Check if error is already a SessionError
   */
  private isSessionError(error: any): error is SessionError {
    return error && typeof error === 'object' && 'type' in error && 'timestamp' in error;
  }

  /**
   * Log error to console with context
   */
  private logError(error: SessionError): void {
    const logLevel = error.retryable ? 'warn' : 'error';
    console[logLevel]('[SessionError]', {
      type: error.type,
      message: error.message,
      timestamp: new Date(error.timestamp).toISOString(),
      context: error.context,
      originalError: error.originalError,
    });
  }

  /**
   * Show user-friendly error notification
   */
  private showErrorNotification(error: SessionError): void {
    const userMessage = this.getUserFriendlyMessage(error);
    
    if (error.retryable) {
      toast.warning(userMessage, {
        description: 'We\'ll try to recover automatically.',
      });
    } else {
      toast.error(userMessage, {
        description: 'Please refresh the page or contact support if the issue persists.',
      });
    }
  }

  /**
   * Get user-friendly error message
   */
  private getUserFriendlyMessage(error: SessionError): string {
    switch (error.type) {
      case SessionErrorType.NETWORK_ERROR:
        return 'Network connection issue detected';
      case SessionErrorType.TIMEOUT_ERROR:
        return 'Request timed out';
      case SessionErrorType.AUTHENTICATION_ERROR:
        return 'Authentication failed';
      case SessionErrorType.SERVER_ERROR:
        return 'Server error occurred';
      case SessionErrorType.REDIS_ERROR:
        return 'Session storage issue';
      case SessionErrorType.VALIDATION_ERROR:
        return 'Invalid request';
      default:
        return 'An unexpected error occurred';
    }
  }

  /**
   * Attempt automatic recovery for retryable errors
   */
  private async attemptRecovery(error: SessionError): Promise<void> {
    const errorKey = `${error.type}-${error.message}`;
    const currentAttempts = this.retryAttempts.get(errorKey) || 0;

    if (currentAttempts >= this.config.maxRetries) {
      console.warn('[SessionError] Max retry attempts reached for:', errorKey);
      return;
    }

    // Calculate delay with exponential backoff
    const delay = this.config.baseRetryDelay * Math.pow(2, currentAttempts);
    
    console.log(`[SessionError] Attempting recovery in ${delay}ms (attempt ${currentAttempts + 1})`);
    
    this.retryAttempts.set(errorKey, currentAttempts + 1);
    
    // Wait before retry
    await new Promise(resolve => setTimeout(resolve, delay));
    
    // Reset retry count on successful recovery (this would be called externally)
    // For now, we just log the attempt
    console.log('[SessionError] Recovery attempt completed');
  }

  /**
   * Reset retry attempts for a specific error
   */
  public resetRetryAttempts(errorType?: SessionErrorType): void {
    if (errorType) {
      // Reset attempts for specific error type
      for (const [key] of this.retryAttempts) {
        if (key.startsWith(errorType)) {
          this.retryAttempts.delete(key);
        }
      }
    } else {
      // Reset all retry attempts
      this.retryAttempts.clear();
    }
  }

  /**
   * Get error statistics
   */
  public getErrorStats(): {
    totalErrors: number;
    errorsByType: Record<SessionErrorType, number>;
    recentErrors: SessionError[];
  } {
    const errorsByType = {} as Record<SessionErrorType, number>;
    
    // Initialize counts
    Object.values(SessionErrorType).forEach(type => {
      errorsByType[type] = 0;
    });

    // Count errors by type
    this.errorHistory.forEach(error => {
      errorsByType[error.type]++;
    });

    // Get recent errors (last 10)
    const recentErrors = this.errorHistory.slice(-10);

    return {
      totalErrors: this.errorHistory.length,
      errorsByType,
      recentErrors,
    };
  }

  /**
   * Check if system is experiencing high error rates
   */
  public isHighErrorRate(): boolean {
    const recentErrors = this.errorHistory.filter(
      error => Date.now() - error.timestamp < 5 * 60 * 1000 // Last 5 minutes
    );
    
    return recentErrors.length > 10; // More than 10 errors in 5 minutes
  }

  /**
   * Update configuration
   */
  public updateConfig(config: Partial<SessionErrorConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Clear error history
   */
  public clearErrorHistory(): void {
    this.errorHistory = [];
    this.retryAttempts.clear();
  }
}

/**
 * Global error handler instance
 */
let globalErrorHandler: SessionErrorHandler | null = null;

/**
 * Get or create global error handler
 */
export function getSessionErrorHandler(config?: Partial<SessionErrorConfig>): SessionErrorHandler {
  if (!globalErrorHandler) {
    globalErrorHandler = new SessionErrorHandler(config);
  }
  return globalErrorHandler;
}

/**
 * Convenience function for handling session errors
 */
export async function handleSessionError(
  error: Error | SessionError,
  context?: Record<string, any>
): Promise<SessionError> {
  const handler = getSessionErrorHandler();
  return handler.handleError(error, context);
}

/**
 * Fallback mechanism for when session management fails completely
 */
export function emergencySessionFallback(): void {
  console.error('[SessionError] Emergency fallback activated - redirecting to login');
  
  // Clear all local storage
  try {
    localStorage.clear();
  } catch (error) {
    console.error('[SessionError] Failed to clear localStorage:', error);
  }
  
  // Show emergency notification
  toast.error('Session management failed. Redirecting to login page.', {
    duration: 5000,
  });
  
  // Redirect to login after a short delay
  setTimeout(() => {
    if (typeof window !== 'undefined') {
      window.location.href = '/';
    }
  }, 2000);
}
