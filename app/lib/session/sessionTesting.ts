/**
 * Testing utilities and validation for session management system
 */

import type { SessionInfo, SessionMonitorConfig, SessionActivityConfig } from './types';
import { SessionMonitor } from './sessionMonitor';
import { SessionActivityTracker } from './sessionActivity';
import { getSessionErrorHandler } from './sessionErrorHandling';

/**
 * Test results interface
 */
export interface SessionTestResult {
  testName: string;
  passed: boolean;
  message: string;
  duration: number;
  details?: Record<string, any>;
}

/**
 * Test suite results
 */
export interface SessionTestSuite {
  suiteName: string;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  results: SessionTestResult[];
  duration: number;
}

/**
 * Session management test runner
 */
export class SessionTestRunner {
  private results: SessionTestResult[] = [];

  /**
   * Run all session management tests
   */
  public async runAllTests(): Promise<SessionTestSuite> {
    const startTime = Date.now();
    this.results = [];

    console.log('[SessionTest] Starting comprehensive session management tests...');

    // Core functionality tests
    await this.testSessionMonitorCreation();
    await this.testSessionMonitorConfiguration();
    await this.testSessionExpirationDetection();
    await this.testSessionWarningThresholds();
    await this.testSessionExtension();

    // Activity tracking tests
    await this.testActivityTrackerCreation();
    await this.testActivityDetection();
    await this.testActivityDebouncing();

    // Error handling tests
    await this.testErrorHandling();
    await this.testNetworkFailureRecovery();
    await this.testMultipleTabHandling();

    // Performance tests
    await this.testPerformanceImpact();
    await this.testMemoryLeaks();

    const endTime = Date.now();
    const duration = endTime - startTime;

    const passedTests = this.results.filter(r => r.passed).length;
    const failedTests = this.results.filter(r => !r.passed).length;

    const suite: SessionTestSuite = {
      suiteName: 'Session Management Test Suite',
      totalTests: this.results.length,
      passedTests,
      failedTests,
      results: this.results,
      duration,
    };

    console.log(`[SessionTest] Tests completed: ${passedTests}/${this.results.length} passed in ${duration}ms`);
    
    return suite;
  }

  /**
   * Test session monitor creation and basic functionality
   */
  private async testSessionMonitorCreation(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const monitor = new SessionMonitor();
      const config = monitor.getConfig();
      
      const passed = config.enabled && config.warningThresholds.length > 0;
      
      this.addResult({
        testName: 'Session Monitor Creation',
        passed,
        message: passed ? 'Session monitor created successfully' : 'Failed to create session monitor',
        duration: Date.now() - startTime,
        details: { config },
      });
    } catch (error) {
      this.addResult({
        testName: 'Session Monitor Creation',
        passed: false,
        message: `Error creating session monitor: ${error}`,
        duration: Date.now() - startTime,
      });
    }
  }

  /**
   * Test session monitor configuration
   */
  private async testSessionMonitorConfiguration(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const monitor = new SessionMonitor();
      const customConfig: Partial<SessionMonitorConfig> = {
        warningThresholds: [15, 10, 5],
        checkInterval: 10000,
        debug: true,
      };
      
      monitor.updateConfig(customConfig);
      const updatedConfig = monitor.getConfig();
      
      const passed = 
        updatedConfig.warningThresholds.includes(15) &&
        updatedConfig.checkInterval === 10000 &&
        updatedConfig.debug === true;
      
      this.addResult({
        testName: 'Session Monitor Configuration',
        passed,
        message: passed ? 'Configuration updated successfully' : 'Failed to update configuration',
        duration: Date.now() - startTime,
        details: { updatedConfig },
      });
    } catch (error) {
      this.addResult({
        testName: 'Session Monitor Configuration',
        passed: false,
        message: `Error updating configuration: ${error}`,
        duration: Date.now() - startTime,
      });
    }
  }

  /**
   * Test session expiration detection
   */
  private async testSessionExpirationDetection(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const monitor = new SessionMonitor({
        checkInterval: 100, // Fast checking for testing
        warningThresholds: [1], // 1 minute warning
      });
      
      // Create a session that expires in 30 seconds
      const testSession: SessionInfo = {
        sessionId: 'test-session-1',
        userId: 'test-user-1',
        expiresAt: Date.now() + 30000, // 30 seconds
        createdAt: Date.now() - 60000, // 1 minute ago
        lastActivity: Date.now(),
      };
      
      let warningTriggered = false;
      monitor.addEventListener('session-warning', () => {
        warningTriggered = true;
      });
      
      monitor.startMonitoring(testSession);
      
      // Wait for a short time to see if monitoring works
      await new Promise(resolve => setTimeout(resolve, 200));
      
      const timeRemaining = monitor.getTimeRemaining();
      const isValid = monitor.isSessionValid();
      
      monitor.stopMonitoring();
      
      const passed = timeRemaining > 0 && isValid;
      
      this.addResult({
        testName: 'Session Expiration Detection',
        passed,
        message: passed ? 'Session expiration detected correctly' : 'Failed to detect session expiration',
        duration: Date.now() - startTime,
        details: { timeRemaining, isValid, warningTriggered },
      });
    } catch (error) {
      this.addResult({
        testName: 'Session Expiration Detection',
        passed: false,
        message: `Error testing expiration detection: ${error}`,
        duration: Date.now() - startTime,
      });
    }
  }

  /**
   * Test session warning thresholds
   */
  private async testSessionWarningThresholds(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const monitor = new SessionMonitor({
        checkInterval: 50,
        warningThresholds: [2, 1], // 2 and 1 minute warnings
      });
      
      // Create a session that expires in 90 seconds (1.5 minutes)
      const testSession: SessionInfo = {
        sessionId: 'test-session-2',
        userId: 'test-user-2',
        expiresAt: Date.now() + 90000, // 90 seconds
        createdAt: Date.now() - 60000,
        lastActivity: Date.now(),
      };
      
      const warnings: number[] = [];
      monitor.addEventListener('session-warning', (event) => {
        if (event.minutesRemaining) {
          warnings.push(event.minutesRemaining);
        }
      });
      
      monitor.startMonitoring(testSession);
      
      // Wait for warnings to potentially trigger
      await new Promise(resolve => setTimeout(resolve, 300));
      
      monitor.stopMonitoring();
      
      // Should trigger 1-minute warning since session expires in 1.5 minutes
      const passed = warnings.length > 0;
      
      this.addResult({
        testName: 'Session Warning Thresholds',
        passed,
        message: passed ? 'Warning thresholds working correctly' : 'Warning thresholds not triggered',
        duration: Date.now() - startTime,
        details: { warnings },
      });
    } catch (error) {
      this.addResult({
        testName: 'Session Warning Thresholds',
        passed: false,
        message: `Error testing warning thresholds: ${error}`,
        duration: Date.now() - startTime,
      });
    }
  }

  /**
   * Test session extension functionality
   */
  private async testSessionExtension(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const monitor = new SessionMonitor();
      
      const originalSession: SessionInfo = {
        sessionId: 'test-session-3',
        userId: 'test-user-3',
        expiresAt: Date.now() + 60000, // 1 minute
        createdAt: Date.now() - 60000,
        lastActivity: Date.now(),
      };
      
      monitor.startMonitoring(originalSession);
      
      const originalExpiry = monitor.getCurrentSession()?.expiresAt;
      
      // Simulate session extension
      const extendedSession: SessionInfo = {
        ...originalSession,
        expiresAt: Date.now() + 120000, // 2 minutes
        lastActivity: Date.now(),
      };
      
      monitor.updateSession(extendedSession);
      
      const newExpiry = monitor.getCurrentSession()?.expiresAt;
      
      monitor.stopMonitoring();
      
      const passed = newExpiry && originalExpiry && newExpiry > originalExpiry;
      
      this.addResult({
        testName: 'Session Extension',
        passed,
        message: passed ? 'Session extension working correctly' : 'Session extension failed',
        duration: Date.now() - startTime,
        details: { originalExpiry, newExpiry },
      });
    } catch (error) {
      this.addResult({
        testName: 'Session Extension',
        passed: false,
        message: `Error testing session extension: ${error}`,
        duration: Date.now() - startTime,
      });
    }
  }

  /**
   * Test activity tracker creation
   */
  private async testActivityTrackerCreation(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const tracker = new SessionActivityTracker();
      const config = tracker.getConfig();
      
      const passed = config.debounceDelay > 0 && config.trackedEvents.length > 0;
      
      this.addResult({
        testName: 'Activity Tracker Creation',
        passed,
        message: passed ? 'Activity tracker created successfully' : 'Failed to create activity tracker',
        duration: Date.now() - startTime,
        details: { config },
      });
    } catch (error) {
      this.addResult({
        testName: 'Activity Tracker Creation',
        passed: false,
        message: `Error creating activity tracker: ${error}`,
        duration: Date.now() - startTime,
      });
    }
  }

  /**
   * Test activity detection
   */
  private async testActivityDetection(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const tracker = new SessionActivityTracker({
        debounceDelay: 100, // Fast debounce for testing
      });
      
      let activityDetected = false;
      tracker.setOnActivityCallback(() => {
        activityDetected = true;
      });
      
      const initialActivity = tracker.getLastActivity();
      
      // Simulate activity
      tracker.recordActivity();
      
      // Wait for debounce
      await new Promise(resolve => setTimeout(resolve, 150));
      
      const newActivity = tracker.getLastActivity();
      
      const passed = newActivity > initialActivity;
      
      this.addResult({
        testName: 'Activity Detection',
        passed,
        message: passed ? 'Activity detection working correctly' : 'Activity detection failed',
        duration: Date.now() - startTime,
        details: { initialActivity, newActivity, activityDetected },
      });
    } catch (error) {
      this.addResult({
        testName: 'Activity Detection',
        passed: false,
        message: `Error testing activity detection: ${error}`,
        duration: Date.now() - startTime,
      });
    }
  }

  /**
   * Test activity debouncing
   */
  private async testActivityDebouncing(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const tracker = new SessionActivityTracker({
        debounceDelay: 200,
      });
      
      let callbackCount = 0;
      tracker.setOnActivityCallback(() => {
        callbackCount++;
      });
      
      // Record multiple activities quickly
      tracker.recordActivity();
      tracker.recordActivity();
      tracker.recordActivity();
      
      // Wait for debounce
      await new Promise(resolve => setTimeout(resolve, 250));
      
      // Should only trigger callback once due to debouncing
      const passed = callbackCount === 1;
      
      this.addResult({
        testName: 'Activity Debouncing',
        passed,
        message: passed ? 'Activity debouncing working correctly' : 'Activity debouncing failed',
        duration: Date.now() - startTime,
        details: { callbackCount },
      });
    } catch (error) {
      this.addResult({
        testName: 'Activity Debouncing',
        passed: false,
        message: `Error testing activity debouncing: ${error}`,
        duration: Date.now() - startTime,
      });
    }
  }

  /**
   * Test error handling
   */
  private async testErrorHandling(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const errorHandler = getSessionErrorHandler();
      
      const testError = new Error('Test network error');
      const sessionError = await errorHandler.handleError(testError, { test: true });
      
      const passed = sessionError.type !== undefined && sessionError.timestamp > 0;
      
      this.addResult({
        testName: 'Error Handling',
        passed,
        message: passed ? 'Error handling working correctly' : 'Error handling failed',
        duration: Date.now() - startTime,
        details: { sessionError },
      });
    } catch (error) {
      this.addResult({
        testName: 'Error Handling',
        passed: false,
        message: `Error testing error handling: ${error}`,
        duration: Date.now() - startTime,
      });
    }
  }

  /**
   * Test network failure recovery
   */
  private async testNetworkFailureRecovery(): Promise<void> {
    const startTime = Date.now();
    
    try {
      // This is a simplified test - in a real scenario, we'd mock network failures
      const passed = true; // Placeholder for actual network failure testing
      
      this.addResult({
        testName: 'Network Failure Recovery',
        passed,
        message: 'Network failure recovery test completed (placeholder)',
        duration: Date.now() - startTime,
      });
    } catch (error) {
      this.addResult({
        testName: 'Network Failure Recovery',
        passed: false,
        message: `Error testing network failure recovery: ${error}`,
        duration: Date.now() - startTime,
      });
    }
  }

  /**
   * Test multiple tab handling
   */
  private async testMultipleTabHandling(): Promise<void> {
    const startTime = Date.now();
    
    try {
      // This would require more complex testing in a real browser environment
      const passed = true; // Placeholder for actual multi-tab testing
      
      this.addResult({
        testName: 'Multiple Tab Handling',
        passed,
        message: 'Multiple tab handling test completed (placeholder)',
        duration: Date.now() - startTime,
      });
    } catch (error) {
      this.addResult({
        testName: 'Multiple Tab Handling',
        passed: false,
        message: `Error testing multiple tab handling: ${error}`,
        duration: Date.now() - startTime,
      });
    }
  }

  /**
   * Test performance impact
   */
  private async testPerformanceImpact(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const monitor = new SessionMonitor({
        checkInterval: 10, // Very frequent checking
      });
      
      const testSession: SessionInfo = {
        sessionId: 'perf-test',
        userId: 'perf-user',
        expiresAt: Date.now() + 60000,
        createdAt: Date.now(),
        lastActivity: Date.now(),
      };
      
      const perfStart = performance.now();
      
      monitor.startMonitoring(testSession);
      
      // Let it run for a short time
      await new Promise(resolve => setTimeout(resolve, 100));
      
      monitor.stopMonitoring();
      
      const perfEnd = performance.now();
      const duration = perfEnd - perfStart;
      
      // Should complete quickly (under 200ms)
      const passed = duration < 200;
      
      this.addResult({
        testName: 'Performance Impact',
        passed,
        message: passed ? 'Performance impact acceptable' : 'Performance impact too high',
        duration: Date.now() - startTime,
        details: { performanceDuration: duration },
      });
    } catch (error) {
      this.addResult({
        testName: 'Performance Impact',
        passed: false,
        message: `Error testing performance impact: ${error}`,
        duration: Date.now() - startTime,
      });
    }
  }

  /**
   * Test for memory leaks
   */
  private async testMemoryLeaks(): Promise<void> {
    const startTime = Date.now();
    
    try {
      // Create and destroy multiple monitors to check for leaks
      for (let i = 0; i < 10; i++) {
        const monitor = new SessionMonitor();
        const testSession: SessionInfo = {
          sessionId: `leak-test-${i}`,
          userId: `leak-user-${i}`,
          expiresAt: Date.now() + 60000,
          createdAt: Date.now(),
          lastActivity: Date.now(),
        };
        
        monitor.startMonitoring(testSession);
        monitor.stopMonitoring();
        monitor.destroy();
      }
      
      // If we get here without errors, assume no major memory leaks
      const passed = true;
      
      this.addResult({
        testName: 'Memory Leaks',
        passed,
        message: 'Memory leak test completed successfully',
        duration: Date.now() - startTime,
      });
    } catch (error) {
      this.addResult({
        testName: 'Memory Leaks',
        passed: false,
        message: `Error testing memory leaks: ${error}`,
        duration: Date.now() - startTime,
      });
    }
  }

  /**
   * Add a test result
   */
  private addResult(result: SessionTestResult): void {
    this.results.push(result);
    
    const status = result.passed ? '✅' : '❌';
    console.log(`[SessionTest] ${status} ${result.testName}: ${result.message} (${result.duration}ms)`);
  }
}

/**
 * Run session management tests
 */
export async function runSessionTests(): Promise<SessionTestSuite> {
  const runner = new SessionTestRunner();
  return runner.runAllTests();
}

/**
 * Validate session management system health
 */
export function validateSessionHealth(): {
  healthy: boolean;
  issues: string[];
  recommendations: string[];
} {
  const issues: string[] = [];
  const recommendations: string[] = [];

  // Check if running in browser environment
  if (typeof window === 'undefined') {
    issues.push('Session management requires browser environment');
    recommendations.push('Ensure session management only runs on client-side');
  }

  // Check for required APIs
  if (typeof fetch === 'undefined') {
    issues.push('Fetch API not available');
    recommendations.push('Add fetch polyfill for older browsers');
  }

  if (typeof localStorage === 'undefined') {
    issues.push('localStorage not available');
    recommendations.push('Add localStorage fallback or polyfill');
  }

  // Check error handler
  try {
    getSessionErrorHandler();
  } catch (error) {
    issues.push('Error handler initialization failed');
    recommendations.push('Check error handler configuration');
  }

  const healthy = issues.length === 0;

  return {
    healthy,
    issues,
    recommendations,
  };
}
