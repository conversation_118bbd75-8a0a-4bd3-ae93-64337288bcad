/**
 * Session timeout warning modal component
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription,
  DialogFooter 
} from '~/components/ui/dialog';
import { Button } from '~/components/ui/button';
import { AlertTriangle, Clock, Wifi, WifiOff } from 'lucide-react';
import { cn } from '~/lib/utils';

interface SessionWarningModalProps {
  /** Whether the modal is open */
  isOpen: boolean;
  /** Minutes remaining until session expiry */
  minutesRemaining: number;
  /** Whether session extension is in progress */
  isExtending?: boolean;
  /** Whether there are network connectivity issues */
  hasNetworkIssues?: boolean;
  /** Callback when user chooses to extend session */
  onExtendSession: () => Promise<boolean>;
  /** Callback when user chooses to logout */
  onLogout: () => void;
  /** Callback when modal is dismissed (optional) */
  onDismiss?: () => void;
  /** Whether to show dismiss option */
  allowDismiss?: boolean;
}

/**
 * Format time remaining for display
 */
function formatTimeRemaining(minutes: number): { minutes: number; seconds: number } {
  const totalSeconds = minutes * 60;
  const displayMinutes = Math.floor(totalSeconds / 60);
  const displaySeconds = totalSeconds % 60;
  return { minutes: displayMinutes, seconds: displaySeconds };
}

/**
 * Get urgency level based on time remaining
 */
function getUrgencyLevel(minutes: number): 'low' | 'medium' | 'high' | 'critical' {
  if (minutes <= 1) return 'critical';
  if (minutes <= 2) return 'high';
  if (minutes <= 5) return 'medium';
  return 'low';
}

export function SessionWarningModal({
  isOpen,
  minutesRemaining,
  isExtending = false,
  hasNetworkIssues = false,
  onExtendSession,
  onLogout,
  onDismiss,
  allowDismiss = false,
}: SessionWarningModalProps) {
  const { t } = useTranslation('session');
  const [countdown, setCountdown] = useState(minutesRemaining * 60);
  const [isExtensionFailed, setIsExtensionFailed] = useState(false);

  // Update countdown timer
  useEffect(() => {
    if (!isOpen) return;

    setCountdown(minutesRemaining * 60);
    
    const interval = setInterval(() => {
      setCountdown(prev => {
        const newValue = Math.max(0, prev - 1);
        if (newValue === 0) {
          // Session has expired, trigger logout
          onLogout();
        }
        return newValue;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [isOpen, minutesRemaining, onLogout]);

  // Reset extension failed state when modal opens
  useEffect(() => {
    if (isOpen) {
      setIsExtensionFailed(false);
    }
  }, [isOpen]);

  const handleExtendSession = useCallback(async () => {
    try {
      setIsExtensionFailed(false);
      const success = await onExtendSession();
      if (!success) {
        setIsExtensionFailed(true);
      }
    } catch (error) {
      console.error('Session extension failed:', error);
      setIsExtensionFailed(true);
    }
  }, [onExtendSession]);

  const { minutes, seconds } = formatTimeRemaining(Math.ceil(countdown / 60));
  const urgencyLevel = getUrgencyLevel(Math.ceil(countdown / 60));

  // Don't render if not open
  if (!isOpen) return null;

  const urgencyColors = {
    low: 'text-yellow-600 dark:text-yellow-400',
    medium: 'text-orange-600 dark:text-orange-400',
    high: 'text-red-600 dark:text-red-400',
    critical: 'text-red-700 dark:text-red-300 animate-pulse',
  };

  const urgencyBgColors = {
    low: 'bg-yellow-50 dark:bg-yellow-950/20 border-yellow-200 dark:border-yellow-800',
    medium: 'bg-orange-50 dark:bg-orange-950/20 border-orange-200 dark:border-orange-800',
    high: 'bg-red-50 dark:bg-red-950/20 border-red-200 dark:border-red-800',
    critical: 'bg-red-100 dark:bg-red-950/40 border-red-300 dark:border-red-700',
  };

  return (
    <Dialog open={isOpen} onOpenChange={allowDismiss ? onDismiss : undefined}>
      <DialogContent 
        className="sm:max-w-md"
        // Prevent closing with escape key if dismiss is not allowed
        onEscapeKeyDown={allowDismiss ? undefined : (e) => e.preventDefault()}
        // Prevent closing by clicking outside if dismiss is not allowed
        onPointerDownOutside={allowDismiss ? undefined : (e) => e.preventDefault()}
      >
        <DialogHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className={cn(
              "p-3 rounded-full",
              urgencyBgColors[urgencyLevel]
            )}>
              <AlertTriangle className={cn("h-6 w-6", urgencyColors[urgencyLevel])} />
            </div>
          </div>
          
          <DialogTitle className="text-xl font-semibold">
            {t('warning.title')}
          </DialogTitle>
          
          <DialogDescription className="text-center space-y-2">
            <p>{t('warning.description')}</p>
            
            {/* Countdown Display */}
            <div className={cn(
              "inline-flex items-center gap-2 px-4 py-2 rounded-lg border",
              urgencyBgColors[urgencyLevel]
            )}>
              <Clock className={cn("h-4 w-4", urgencyColors[urgencyLevel])} />
              <span className={cn("font-mono text-lg font-bold", urgencyColors[urgencyLevel])}>
                {minutes}:{seconds.toString().padStart(2, '0')}
              </span>
            </div>
            
            {/* Network Issues Warning */}
            {hasNetworkIssues && (
              <div className="flex items-center justify-center gap-2 text-amber-600 dark:text-amber-400">
                <WifiOff className="h-4 w-4" />
                <span className="text-sm">{t('warning.networkIssues')}</span>
              </div>
            )}
            
            {/* Extension Failed Warning */}
            {isExtensionFailed && (
              <div className="text-red-600 dark:text-red-400 text-sm">
                {t('warning.extensionFailed')}
              </div>
            )}
          </DialogDescription>
        </DialogHeader>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          {/* Extend Session Button */}
          <Button
            onClick={handleExtendSession}
            disabled={isExtending}
            className="w-full sm:w-auto"
            size="lg"
          >
            {isExtending ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                {t('buttons.extending')}
              </>
            ) : (
              <>
                <Wifi className="h-4 w-4 mr-2" />
                {t('buttons.extendSession')}
              </>
            )}
          </Button>

          {/* Logout Button */}
          <Button
            variant="outline"
            onClick={onLogout}
            disabled={isExtending}
            className="w-full sm:w-auto"
            size="lg"
          >
            {t('buttons.logout')}
          </Button>

          {/* Dismiss Button (if allowed) */}
          {allowDismiss && onDismiss && (
            <Button
              variant="ghost"
              onClick={onDismiss}
              disabled={isExtending}
              className="w-full sm:w-auto"
              size="sm"
            >
              {t('buttons.dismiss')}
            </Button>
          )}
        </DialogFooter>

        {/* Additional Information */}
        <div className="text-xs text-muted-foreground text-center mt-4 space-y-1">
          <p>{t('warning.autoLogoutNote')}</p>
          {urgencyLevel === 'critical' && (
            <p className="text-red-600 dark:text-red-400 font-medium">
              {t('warning.criticalWarning')}
            </p>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

/**
 * Hook for managing session warning modal state
 */
export function useSessionWarningModal() {
  const [isOpen, setIsOpen] = useState(false);
  const [minutesRemaining, setMinutesRemaining] = useState(0);
  const [hasNetworkIssues, setHasNetworkIssues] = useState(false);

  const showWarning = useCallback((minutes: number, networkIssues = false) => {
    setMinutesRemaining(minutes);
    setHasNetworkIssues(networkIssues);
    setIsOpen(true);
  }, []);

  const hideWarning = useCallback(() => {
    setIsOpen(false);
  }, []);

  const updateTimeRemaining = useCallback((minutes: number) => {
    setMinutesRemaining(minutes);
  }, []);

  return {
    isOpen,
    minutesRemaining,
    hasNetworkIssues,
    showWarning,
    hideWarning,
    updateTimeRemaining,
    setHasNetworkIssues,
  };
}
