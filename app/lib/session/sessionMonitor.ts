/**
 * Core session monitoring service for tracking session expiration and triggering warnings
 */

import type { 
  SessionInfo, 
  SessionMonitorConfig, 
  SessionWarningEvent, 
  SessionEventType, 
  SessionEventListener 
} from './types';

// Default configuration for session monitoring
const DEFAULT_CONFIG: SessionMonitorConfig = {
  warningThresholds: [10, 5, 1], // Warning at 10, 5, and 1 minutes before expiry
  checkInterval: 30 * 1000, // Check every 30 seconds
  enabled: true,
  debug: false,
};

/**
 * Session monitoring service that tracks session expiration and emits warning events
 */
export class SessionMonitor {
  private config: SessionMonitorConfig;
  private session: SessionInfo | null = null;
  private intervalId: NodeJS.Timeout | null = null;
  private eventListeners: Map<SessionEventType, Set<SessionEventListener>> = new Map();
  private triggeredWarnings: Set<number> = new Set();

  constructor(config: Partial<SessionMonitorConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.initializeEventListeners();
  }

  /**
   * Initialize event listener maps
   */
  private initializeEventListeners(): void {
    this.eventListeners.set('session-warning', new Set());
    this.eventListeners.set('session-expired', new Set());
    this.eventListeners.set('session-extended', new Set());
    this.eventListeners.set('session-error', new Set());
  }

  /**
   * Start monitoring a session
   */
  public startMonitoring(session: SessionInfo): void {
    if (!this.config.enabled) {
      this.log('Session monitoring is disabled');
      return;
    }

    // Validate session data before starting
    if (!this.validateSessionData(session)) {
      console.error('[SessionMonitor] ❌ Cannot start monitoring - invalid session data:', session);
      return;
    }

    // Check if already monitoring the same session
    if (this.session?.sessionId === session.sessionId && this.intervalId) {
      this.log('Already monitoring this session, skipping restart');
      return;
    }

    // Clear any existing interval first
    this.stopMonitoring();

    this.session = session;
    this.triggeredWarnings.clear();

    this.log('✅ Starting session monitoring', {
      sessionId: session.sessionId,
      userId: session.userId,
      expiresAt: new Date(session.expiresAt).toLocaleString(),
      timeRemaining: Math.floor((session.expiresAt - Date.now()) / (1000 * 60)) + ' minutes'
    });

    // Start monitoring interval
    this.intervalId = setInterval(() => {
      this.checkSessionStatus();
    }, this.config.checkInterval);

    // Perform initial check
    this.checkSessionStatus();
  }

  /**
   * Validate session data before starting monitoring
   */
  private validateSessionData(session: SessionInfo): boolean {
    if (!session) {
      console.error('[SessionMonitor] Session is null/undefined');
      return false;
    }

    const requiredFields = [
      { field: 'sessionId', value: session.sessionId },
      { field: 'userId', value: session.userId },
      { field: 'expiresAt', value: session.expiresAt },
    ];

    for (const { field, value } of requiredFields) {
      if (!value) {
        console.error(`[SessionMonitor] Missing required field: ${field}`);
        return false;
      }
    }

    // Check if session is already expired
    if (session.expiresAt <= Date.now()) {
      console.error('[SessionMonitor] Session is already expired:', new Date(session.expiresAt));
      return false;
    }

    // Check if expiration time is reasonable (not too far in the future)
    const maxSessionDuration = 30 * 24 * 60 * 60 * 1000; // 30 days
    if (session.expiresAt > Date.now() + maxSessionDuration) {
      console.warn('[SessionMonitor] Session expiration seems too far in the future:', new Date(session.expiresAt));
    }

    return true;
  }

  /**
   * Stop session monitoring
   */
  public stopMonitoring(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.session = null;
    this.triggeredWarnings.clear();
    this.log('Session monitoring stopped');
  }

  /**
   * Check current session status and emit events if needed
   */
  private checkSessionStatus(): void {
    if (!this.session) {
      return;
    }

    const now = Date.now();
    const timeRemaining = this.session.expiresAt - now;
    const minutesRemaining = Math.floor(timeRemaining / (1000 * 60));

    this.log('Checking session status', { 
      minutesRemaining, 
      timeRemaining,
      sessionId: this.session.sessionId 
    });

    // Check if session has expired
    if (timeRemaining <= 0) {
      this.emitEvent('session-expired', {
        type: 'expired',
        session: this.session,
        timestamp: now,
      });
      this.stopMonitoring();
      return;
    }

    // Check warning thresholds
    for (const threshold of this.config.warningThresholds) {
      if (minutesRemaining <= threshold && !this.triggeredWarnings.has(threshold)) {
        this.triggeredWarnings.add(threshold);
        this.emitEvent('session-warning', {
          type: 'warning',
          minutesRemaining: threshold,
          session: this.session,
          timestamp: now,
        });
        this.log(`Session warning triggered: ${threshold} minutes remaining`);
      }
    }
  }

  /**
   * Update session information (e.g., after extension)
   */
  public updateSession(session: SessionInfo): void {
    const oldSession = this.session;
    this.session = session;
    
    // Reset triggered warnings if session was extended
    if (oldSession && session.expiresAt > oldSession.expiresAt) {
      this.triggeredWarnings.clear();
      this.emitEvent('session-extended', {
        type: 'extended',
        session: this.session,
        timestamp: Date.now(),
      });
      this.log('Session extended', { 
        oldExpiresAt: new Date(oldSession.expiresAt),
        newExpiresAt: new Date(session.expiresAt)
      });
    }
  }

  /**
   * Get current session information
   */
  public getCurrentSession(): SessionInfo | null {
    return this.session;
  }

  /**
   * Get time remaining until session expiry in milliseconds
   */
  public getTimeRemaining(): number {
    if (!this.session) {
      return 0;
    }
    return Math.max(0, this.session.expiresAt - Date.now());
  }

  /**
   * Get time remaining until session expiry in minutes
   */
  public getMinutesRemaining(): number {
    return Math.floor(this.getTimeRemaining() / (1000 * 60));
  }

  /**
   * Check if session is currently valid
   */
  public isSessionValid(): boolean {
    return this.getTimeRemaining() > 0;
  }

  /**
   * Add event listener for session events
   */
  public addEventListener(type: SessionEventType, listener: SessionEventListener): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      listeners.add(listener);
    }
  }

  /**
   * Remove event listener
   */
  public removeEventListener(type: SessionEventType, listener: SessionEventListener): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      listeners.delete(listener);
    }
  }

  /**
   * Emit session event to all registered listeners
   */
  private emitEvent(type: SessionEventType, event: SessionWarningEvent): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error(`Error in session event listener for ${type}:`, error);
        }
      });
    }
  }

  /**
   * Update configuration
   */
  public updateConfig(config: Partial<SessionMonitorConfig>): void {
    this.config = { ...this.config, ...config };
    this.log('Configuration updated', this.config);
  }

  /**
   * Get current configuration
   */
  public getConfig(): SessionMonitorConfig {
    return { ...this.config };
  }

  /**
   * Log debug messages if debug mode is enabled
   */
  private log(message: string, data?: any): void {
    if (this.config.debug) {
      console.log(`[SessionMonitor] ${message}`, data || '');
    }
  }

  /**
   * Cleanup resources
   */
  public destroy(): void {
    this.stopMonitoring();
    this.eventListeners.clear();
    this.log('SessionMonitor destroyed');
  }
}

/**
 * Create a new session monitor instance with default configuration
 */
export function createSessionMonitor(config?: Partial<SessionMonitorConfig>): SessionMonitor {
  return new SessionMonitor(config);
}

/**
 * Utility function to convert Better Auth session to SessionInfo
 */
export function convertBetterAuthSession(betterAuthSession: any): SessionInfo | null {
  console.log('[SessionMonitor] 🔍 Converting session data:', betterAuthSession);

  if (!betterAuthSession) {
    console.error('[SessionMonitor] ❌ Session data is null/undefined');
    return null;
  }

  // Handle different session data formats
  let session, user;

  if (betterAuthSession?.session && betterAuthSession?.user) {
    // Format: { session: {...}, user: {...} }
    session = betterAuthSession.session;
    user = betterAuthSession.user;
    console.log('[SessionMonitor] ✅ Found standard format: session + user');
    console.log('[SessionMonitor] 🔍 Session keys:', Object.keys(session));
    console.log('[SessionMonitor] 🔍 User keys:', Object.keys(user));
  } else if (betterAuthSession?.data?.session && betterAuthSession?.data?.user) {
    // Nested format: { data: { session: {...}, user: {...} } }
    session = betterAuthSession.data.session;
    user = betterAuthSession.data.user;
    console.log('[SessionMonitor] ✅ Found nested format: data.session + data.user');
    console.log('[SessionMonitor] 🔍 Session keys:', Object.keys(session));
    console.log('[SessionMonitor] 🔍 User keys:', Object.keys(user));
  } else {
    console.error('[SessionMonitor] ❌ Invalid session data format. Available keys:', Object.keys(betterAuthSession));
    console.error('[SessionMonitor] Expected: { session: {...}, user: {...} } or { data: { session: {...}, user: {...} } }');
    return null;
  }

  // Validate required fields - Better Auth uses different field names
  const validationErrors = [];

  // Better Auth session might use 'token' as identifier instead of 'id'
  const sessionId = session?.id || session?.token || session?.sessionId;
  console.log('[SessionMonitor] 🔍 Session ID candidates:', {
    'session.id': session?.id,
    'session.token': session?.token,
    'session.sessionId': session?.sessionId,
    'selected': sessionId
  });
  if (!sessionId) validationErrors.push('session identifier (id/token/sessionId) is missing');

  // User ID might be in session.userId or user.id
  const userId = user?.id || session?.userId;
  console.log('[SessionMonitor] 🔍 User ID candidates:', {
    'user.id': user?.id,
    'session.userId': session?.userId,
    'selected': userId
  });
  if (!userId) validationErrors.push('user.id is missing');

  if (!session?.expiresAt) validationErrors.push('session.expiresAt is missing');

  if (validationErrors.length > 0) {
    console.error('[SessionMonitor] ❌ Validation failed:', validationErrors);
    console.error('[SessionMonitor] Session object:', session);
    console.error('[SessionMonitor] User object:', user);
    return null;
  }

  try {
    // Use the validated identifiers
    const sessionInfo: SessionInfo = {
      sessionId: sessionId!,
      userId: userId!,
      expiresAt: new Date(session.expiresAt).getTime(),
      createdAt: session.createdAt ? new Date(session.createdAt).getTime() : Date.now(),
      lastActivity: Date.now(),
    };

    // Validate the converted session
    if (!sessionInfo.sessionId || !sessionInfo.userId || !sessionInfo.expiresAt) {
      console.error('[SessionMonitor] ❌ Conversion resulted in invalid session info:', sessionInfo);
      return null;
    }

    // Check if session is already expired
    if (sessionInfo.expiresAt <= Date.now()) {
      console.warn('[SessionMonitor] ⚠️ Session is already expired:', new Date(sessionInfo.expiresAt));
      return null;
    }

    console.log('[SessionMonitor] ✅ Successfully converted session:', {
      sessionId: sessionInfo.sessionId,
      userId: sessionInfo.userId,
      expiresAt: new Date(sessionInfo.expiresAt).toLocaleString(),
      timeRemaining: Math.floor((sessionInfo.expiresAt - Date.now()) / (1000 * 60)) + ' minutes'
    });

    return sessionInfo;
  } catch (error) {
    console.error('[SessionMonitor] ❌ Error during conversion:', error);
    return null;
  }
}
