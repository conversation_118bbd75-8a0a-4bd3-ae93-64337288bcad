/**
 * Session logout utilities for handling automatic logout on 401 errors
 */

import { QueryClient } from '@tanstack/react-query';
import { clearAllCachedData } from '../query/queryClient';
import { authClient } from '../auth.client';

/**
 * Configuration for session logout behavior
 */
export interface SessionLogoutConfig {
  /** Whether to clear React Query cache on logout */
  clearCache: boolean;
  /** Whether to redirect to login page after logout */
  redirectToLogin: boolean;
  /** Custom redirect URL (defaults to '/') */
  redirectUrl?: string;
  /** Whether to show logout notification */
  showNotification: boolean;
  /** Custom logout message */
  logoutMessage?: string;
}

const DEFAULT_LOGOUT_CONFIG: SessionLogoutConfig = {
  clearCache: true,
  redirectToLogin: true,
  redirectUrl: '/',
  showNotification: true,
  logoutMessage: 'Your session has expired. Please log in again.',
};

/**
 * Comprehensive logout handler that cleans up all session-related data
 */
export async function performSessionLogout(
  queryClient: QueryClient,
  config: Partial<SessionLogoutConfig> = {}
): Promise<void> {
  const finalConfig = { ...DEFAULT_LOGOUT_CONFIG, ...config };
  
  console.log('[SessionLogout] Performing automatic logout due to session expiry/401 error');

  try {
    // 1. Clear React Query cache if configured
    if (finalConfig.clearCache) {
      clearAllCachedData(queryClient);
    }

    // 2. Sign out from Better Auth
    await authClient.signOut({
      fetchOptions: {
        onSuccess: () => {
          console.log('[SessionLogout] Successfully signed out from Better Auth');
        },
        onError: (error) => {
          console.error('[SessionLogout] Error during Better Auth signout:', error);
          // Continue with logout process even if Better Auth signout fails
        }
      }
    });

    // 3. Clear any additional local storage items
    clearLocalStorageData();

    // 4. Show notification if configured
    if (finalConfig.showNotification && typeof window !== 'undefined') {
      // Import toast dynamically to avoid SSR issues
      const { toast } = await import('sonner');
      toast.error(finalConfig.logoutMessage || DEFAULT_LOGOUT_CONFIG.logoutMessage);
    }

    // 5. Redirect if configured and we're in the browser
    if (finalConfig.redirectToLogin && typeof window !== 'undefined') {
      const redirectUrl = finalConfig.redirectUrl || DEFAULT_LOGOUT_CONFIG.redirectUrl;
      
      // Use a small delay to ensure cleanup is complete
      setTimeout(() => {
        window.location.href = redirectUrl!;
      }, 100);
    }

  } catch (error) {
    console.error('[SessionLogout] Error during logout process:', error);
    
    // Even if logout fails, still try to redirect to clear the UI state
    if (finalConfig.redirectToLogin && typeof window !== 'undefined') {
      const redirectUrl = finalConfig.redirectUrl || DEFAULT_LOGOUT_CONFIG.redirectUrl;
      window.location.href = redirectUrl!;
    }
  }
}

/**
 * Clear session-related data from localStorage
 */
function clearLocalStorageData(): void {
  if (typeof window === 'undefined') return;

  try {
    // List of localStorage keys that should be cleared on logout
    const keysToRemove = [
      'better-auth.session',
      'better-auth.session.token',
      'auth-session',
      'session-token',
      // Add any other session-related keys your app uses
    ];

    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
    });

    // Also clear any keys that start with 'auth-' or 'session-'
    const allKeys = Object.keys(localStorage);
    allKeys.forEach(key => {
      if (key.startsWith('auth-') || key.startsWith('session-')) {
        localStorage.removeItem(key);
      }
    });

    console.log('[SessionLogout] Cleared localStorage session data');
  } catch (error) {
    console.error('[SessionLogout] Error clearing localStorage:', error);
  }
}

/**
 * Create a logout handler function that can be passed to the global error interceptor
 */
export function createLogoutHandler(
  queryClient: QueryClient,
  config?: Partial<SessionLogoutConfig>
): () => void {
  return () => {
    performSessionLogout(queryClient, config);
  };
}

/**
 * Utility to check if we're currently in a logout process
 * This can help prevent multiple simultaneous logout attempts
 */
let isLoggingOut = false;

export function isCurrentlyLoggingOut(): boolean {
  return isLoggingOut;
}

export function setLoggingOutState(state: boolean): void {
  isLoggingOut = state;
}

/**
 * Safe logout wrapper that prevents multiple simultaneous logout attempts
 */
export async function safePerformSessionLogout(
  queryClient: QueryClient,
  config: Partial<SessionLogoutConfig> = {}
): Promise<void> {
  if (isLoggingOut) {
    console.log('[SessionLogout] Logout already in progress, skipping duplicate request');
    return;
  }

  setLoggingOutState(true);
  
  try {
    await performSessionLogout(queryClient, config);
  } finally {
    // Reset logout state after a delay to prevent rapid re-attempts
    setTimeout(() => {
      setLoggingOutState(false);
    }, 2000);
  }
}

/**
 * Emergency logout function for critical session security issues
 * This performs an immediate logout without waiting for async operations
 */
export function emergencyLogout(): void {
  console.warn('[SessionLogout] Performing emergency logout');
  
  // Clear localStorage immediately
  clearLocalStorageData();
  
  // Redirect immediately
  if (typeof window !== 'undefined') {
    window.location.href = '/';
  }
}
