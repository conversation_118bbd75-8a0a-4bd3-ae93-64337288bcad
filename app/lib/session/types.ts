/**
 * TypeScript types for session management system
 */

export interface SessionInfo {
  /** Session ID from Better Auth */
  sessionId: string;
  /** User ID associated with the session */
  userId: string;
  /** Session expiration timestamp (Unix timestamp in milliseconds) */
  expiresAt: number;
  /** Session creation timestamp (Unix timestamp in milliseconds) */
  createdAt: number;
  /** Last activity timestamp (Unix timestamp in milliseconds) */
  lastActivity: number;
}

export interface SessionMonitorConfig {
  /** Warning thresholds in minutes before session expiry */
  warningThresholds: number[];
  /** How often to check session status (in milliseconds) */
  checkInterval: number;
  /** Whether to enable session monitoring */
  enabled: boolean;
  /** Whether to enable debug logging */
  debug: boolean;
}

export interface SessionWarningEvent {
  /** Type of warning event */
  type: 'warning' | 'expired' | 'extended';
  /** Minutes remaining until expiry (for warning events) */
  minutesRemaining?: number;
  /** Session information */
  session: SessionInfo;
  /** Timestamp when event was triggered */
  timestamp: number;
}

export interface SessionActivityConfig {
  /** Debounce delay for activity tracking (in milliseconds) */
  debounceDelay: number;
  /** Types of events to track for activity */
  trackedEvents: string[];
  /** Whether to track API calls as activity */
  trackApiCalls: boolean;
  /** Minimum time between session extensions (in milliseconds) */
  extensionCooldown: number;
}

export interface SessionExtensionRequest {
  /** Current session ID */
  sessionId: string;
  /** Requested extension duration (in seconds) */
  extensionDuration?: number;
}

export interface SessionExtensionResponse {
  /** Whether the extension was successful */
  success: boolean;
  /** New session expiration timestamp */
  newExpiresAt?: number;
  /** Error message if extension failed */
  error?: string;
  /** Remaining session time in seconds */
  remainingTime?: number;
}

export interface SessionMonitorState {
  /** Current session information */
  session: SessionInfo | null;
  /** Whether session monitoring is active */
  isMonitoring: boolean;
  /** Whether a warning is currently being shown */
  isWarningShown: boolean;
  /** Last warning threshold that was triggered */
  lastWarningThreshold: number | null;
  /** Whether session extension is in progress */
  isExtending: boolean;
  /** Error state for session monitoring */
  error: string | null;
}

export type SessionEventType = 'session-warning' | 'session-expired' | 'session-extended' | 'session-error';

export interface SessionEventListener {
  (event: SessionWarningEvent): void;
}

export interface SessionMonitorHookReturn {
  /** Current session monitor state */
  state: SessionMonitorState;
  /** Start session monitoring */
  startMonitoring: (session: SessionInfo) => void;
  /** Stop session monitoring */
  stopMonitoring: () => void;
  /** Manually trigger session check */
  checkSession: () => Promise<void>;
  /** Extend current session */
  extendSession: () => Promise<boolean>;
  /** Add event listener for session events */
  addEventListener: (type: SessionEventType, listener: SessionEventListener) => void;
  /** Remove event listener */
  removeEventListener: (type: SessionEventType, listener: SessionEventListener) => void;
}

export interface ActivityTracker {
  /** Start tracking user activity */
  start: () => void;
  /** Stop tracking user activity */
  stop: () => void;
  /** Get last activity timestamp */
  getLastActivity: () => number;
  /** Manually record activity */
  recordActivity: () => void;
}

export interface SessionManagerConfig extends SessionMonitorConfig, SessionActivityConfig {
  /** API endpoint for session extension */
  extensionEndpoint: string;
  /** API endpoint for session validation */
  validationEndpoint: string;
}
