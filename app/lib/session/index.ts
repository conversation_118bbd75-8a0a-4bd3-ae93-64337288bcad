/**
 * Comprehensive Session Management System for KWACI Grow
 * 
 * This module provides a complete session management solution with:
 * - Session expiration monitoring with configurable warning thresholds
 * - Automatic logout on 401 responses with global API error interception
 * - Session timeout warning system with user-friendly modals
 * - Global API error handling with automatic retry and fallback mechanisms
 * - Session activity tracking with automatic session extension
 * - Comprehensive error handling and recovery mechanisms
 * - Multi-tab support and network failure resilience
 * - Internationalization support for all user-facing messages
 * - Performance optimization and memory leak prevention
 * - Extensive testing and validation utilities
 */

// Core session management
export { SessionMonitor, createSessionMonitor, convertBetterAuthSession } from './sessionMonitor';
export { SessionActivityTracker, createActivityTracker, getGlobalActivityTracker, destroyGlobalActivityTracker } from './sessionActivity';

// Session extension
export { SessionExtensionService, getSessionExtensionService, destroySessionExtensionService, extendSession, getSessionInfo } from './sessionExtension';

// Session logout handling
export { performSessionLogout, createLogoutHandler, safePerformSessionLogout, emergencyLogout, isCurrentlyLoggingOut, setLoggingOutState } from './sessionLogout';

// Error handling and recovery
export { SessionErrorHandler, getSessionErrorHandler, handleSessionError, emergencySessionFallback, SessionErrorType } from './sessionErrorHandling';

// React hooks
export { useSessionMonitor } from '../hooks/useSessionMonitor';
export { useSessionActivity, useActivityDetection } from '../hooks/useSessionActivity';

// UI components
export { SessionWarningModal, useSessionWarningModal } from './SessionWarningModal';

// Testing and validation
export { SessionTestRunner, runSessionTests, validateSessionHealth } from './sessionTesting';

// Types
export type {
  SessionInfo,
  SessionMonitorConfig,
  SessionWarningEvent,
  SessionActivityConfig,
  SessionExtensionRequest,
  SessionExtensionResponse,
  SessionMonitorState,
  SessionEventType,
  SessionEventListener,
  SessionMonitorHookReturn,
  ActivityTracker,
  SessionManagerConfig,
} from './types';

export type {
  SessionError,
  SessionErrorConfig,
} from './sessionErrorHandling';

export type {
  SessionTestResult,
  SessionTestSuite,
} from './sessionTesting';

/**
 * Default configuration for the session management system
 */
export const DEFAULT_SESSION_CONFIG = {
  monitor: {
    warningThresholds: [10, 5, 1], // Warning at 10, 5, and 1 minutes before expiry
    checkInterval: 30 * 1000, // Check every 30 seconds
    enabled: true,
    debug: false,
  },
  activity: {
    debounceDelay: 30 * 1000, // 30 seconds
    trackedEvents: ['click', 'keydown', 'scroll', 'mousemove', 'touchstart'],
    trackApiCalls: true,
    extensionCooldown: 5 * 60 * 1000, // 5 minutes
  },
  extension: {
    endpoint: '/api/session/extend',
    timeout: 10000, // 10 seconds
    retryAttempts: 3,
    retryDelay: 1000, // 1 second
    showFeedback: true,
  },
  logout: {
    clearCache: true,
    redirectToLogin: true,
    redirectUrl: '/',
    showNotification: true,
    logoutMessage: 'Your session has expired. Please log in again.',
  },
  errorHandling: {
    showNotifications: true,
    logErrors: true,
    autoRecover: true,
    maxRetries: 3,
    baseRetryDelay: 1000,
  },
} as const;

/**
 * Session management system status
 */
export interface SessionSystemStatus {
  isInitialized: boolean;
  isMonitoring: boolean;
  isActivityTracking: boolean;
  hasActiveSession: boolean;
  timeRemaining: number;
  lastActivity: number;
  errorCount: number;
  systemHealth: 'healthy' | 'warning' | 'error';
}

/**
 * Initialize the complete session management system
 * This should be called once during application startup
 */
export function initializeSessionManagement(config?: {
  monitor?: Partial<SessionMonitorConfig>;
  activity?: Partial<SessionActivityConfig>;
  errorHandling?: Partial<SessionErrorConfig>;
}): {
  monitor: SessionMonitor;
  activityTracker: SessionActivityTracker;
  errorHandler: SessionErrorHandler;
} {
  console.log('[SessionManagement] Initializing comprehensive session management system...');

  // Initialize error handler first
  const errorHandler = getSessionErrorHandler(config?.errorHandling);

  // Initialize session monitor
  const monitor = createSessionMonitor(config?.monitor);

  // Initialize activity tracker
  const activityTracker = getGlobalActivityTracker(config?.activity);

  // Validate system health
  const healthCheck = validateSessionHealth();
  if (!healthCheck.healthy) {
    console.warn('[SessionManagement] System health issues detected:', healthCheck.issues);
    healthCheck.recommendations.forEach(rec => {
      console.warn('[SessionManagement] Recommendation:', rec);
    });
  }

  console.log('[SessionManagement] Session management system initialized successfully');

  return {
    monitor,
    activityTracker,
    errorHandler,
  };
}

/**
 * Get current session management system status
 */
export function getSessionSystemStatus(): SessionSystemStatus {
  try {
    const monitor = createSessionMonitor();
    const activityTracker = getGlobalActivityTracker();
    const errorHandler = getSessionErrorHandler();

    const currentSession = monitor.getCurrentSession();
    const errorStats = errorHandler.getErrorStats();
    const isHighErrorRate = errorHandler.isHighErrorRate();

    let systemHealth: 'healthy' | 'warning' | 'error' = 'healthy';
    if (isHighErrorRate) {
      systemHealth = 'error';
    } else if (errorStats.totalErrors > 0) {
      systemHealth = 'warning';
    }

    return {
      isInitialized: true,
      isMonitoring: !!currentSession,
      isActivityTracking: activityTracker.isCurrentlyTracking(),
      hasActiveSession: monitor.isSessionValid(),
      timeRemaining: monitor.getTimeRemaining(),
      lastActivity: activityTracker.getLastActivity(),
      errorCount: errorStats.totalErrors,
      systemHealth,
    };
  } catch (error) {
    console.error('[SessionManagement] Error getting system status:', error);
    return {
      isInitialized: false,
      isMonitoring: false,
      isActivityTracking: false,
      hasActiveSession: false,
      timeRemaining: 0,
      lastActivity: 0,
      errorCount: 0,
      systemHealth: 'error',
    };
  }
}

/**
 * Cleanup session management system
 * This should be called during application shutdown or logout
 */
export function cleanupSessionManagement(): void {
  console.log('[SessionManagement] Cleaning up session management system...');

  try {
    // Stop activity tracking
    destroyGlobalActivityTracker();

    // Destroy session extension service
    destroySessionExtensionService();

    // Clear error handler
    const errorHandler = getSessionErrorHandler();
    errorHandler.clearErrorHistory();

    console.log('[SessionManagement] Session management system cleaned up successfully');
  } catch (error) {
    console.error('[SessionManagement] Error during cleanup:', error);
  }
}

/**
 * Emergency reset of session management system
 * Use this when the system is in an unrecoverable state
 */
export function emergencyResetSessionManagement(): void {
  console.warn('[SessionManagement] Performing emergency reset...');

  try {
    // Cleanup everything
    cleanupSessionManagement();

    // Trigger emergency fallback
    emergencySessionFallback();
  } catch (error) {
    console.error('[SessionManagement] Error during emergency reset:', error);
    
    // Last resort - force page reload
    if (typeof window !== 'undefined') {
      window.location.reload();
    }
  }
}

/**
 * Development utilities for testing and debugging
 */
export const SessionManagementDevUtils = {
  /**
   * Run comprehensive tests
   */
  async runTests() {
    if (process.env.NODE_ENV !== 'development') {
      console.warn('[SessionManagement] Tests should only be run in development mode');
      return null;
    }
    return runSessionTests();
  },

  /**
   * Get detailed system information
   */
  getSystemInfo() {
    const status = getSessionSystemStatus();
    const healthCheck = validateSessionHealth();
    const errorHandler = getSessionErrorHandler();
    const errorStats = errorHandler.getErrorStats();

    return {
      status,
      healthCheck,
      errorStats,
      config: DEFAULT_SESSION_CONFIG,
    };
  },

  /**
   * Simulate session expiry for testing
   */
  simulateSessionExpiry() {
    if (process.env.NODE_ENV !== 'development') {
      console.warn('[SessionManagement] Session expiry simulation should only be used in development');
      return;
    }

    const monitor = createSessionMonitor();
    const currentSession = monitor.getCurrentSession();
    
    if (currentSession) {
      const expiredSession = {
        ...currentSession,
        expiresAt: Date.now() - 1000, // Expired 1 second ago
      };
      monitor.updateSession(expiredSession);
    }
  },

  /**
   * Force error for testing error handling
   */
  simulateError(errorType: string = 'network') {
    if (process.env.NODE_ENV !== 'development') {
      console.warn('[SessionManagement] Error simulation should only be used in development');
      return;
    }

    const errorHandler = getSessionErrorHandler();
    const testError = new Error(`Simulated ${errorType} error for testing`);
    errorHandler.handleError(testError, { simulated: true, type: errorType });
  },
};
