/**
 * Session activity tracker for monitoring user interactions and extending sessions
 */

import type { SessionActivityConfig, ActivityTracker } from './types';

// Default configuration for activity tracking
const DEFAULT_ACTIVITY_CONFIG: SessionActivityConfig = {
  debounceDelay: 30 * 1000, // 30 seconds
  trackedEvents: ['click', 'keydown', 'scroll', 'mousemove', 'touchstart'],
  trackApiCalls: true,
  extensionCooldown: 5 * 60 * 1000, // 5 minutes
};

/**
 * Activity tracker that monitors user interactions and triggers session extensions
 */
export class SessionActivityTracker implements ActivityTracker {
  private config: SessionActivityConfig;
  private lastActivity: number = 0;
  private lastExtension: number = 0;
  private isTracking: boolean = false;
  private debounceTimer: NodeJS.Timeout | null = null;
  private eventListeners: Map<string, EventListener> = new Map();
  private onActivityCallback?: () => void;

  constructor(config: Partial<SessionActivityConfig> = {}) {
    this.config = { ...DEFAULT_ACTIVITY_CONFIG, ...config };
    this.lastActivity = Date.now();
  }

  /**
   * Start tracking user activity
   */
  public start(): void {
    if (this.isTracking || typeof window === 'undefined') {
      return;
    }

    this.isTracking = true;
    this.lastActivity = Date.now();
    
    console.log('[ActivityTracker] Starting activity tracking');

    // Set up event listeners for tracked events
    this.config.trackedEvents.forEach(eventType => {
      const listener = this.createEventListener();
      this.eventListeners.set(eventType, listener);
      
      // Add event listener with passive option for better performance
      document.addEventListener(eventType, listener, { 
        passive: true, 
        capture: false 
      });
    });

    // Set up API call tracking if enabled
    if (this.config.trackApiCalls) {
      this.setupApiCallTracking();
    }
  }

  /**
   * Stop tracking user activity
   */
  public stop(): void {
    if (!this.isTracking) {
      return;
    }

    this.isTracking = false;
    
    console.log('[ActivityTracker] Stopping activity tracking');

    // Remove all event listeners
    this.eventListeners.forEach((listener, eventType) => {
      document.removeEventListener(eventType, listener);
    });
    this.eventListeners.clear();

    // Clear any pending debounce timer
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }

    // Clean up API call tracking
    this.cleanupApiCallTracking();
  }

  /**
   * Get the timestamp of the last recorded activity
   */
  public getLastActivity(): number {
    return this.lastActivity;
  }

  /**
   * Manually record activity (useful for programmatic activity recording)
   */
  public recordActivity(): void {
    this.handleActivity();
  }

  /**
   * Set callback function to be called when activity is detected
   */
  public setOnActivityCallback(callback: () => void): void {
    this.onActivityCallback = callback;
  }

  /**
   * Create a debounced event listener for activity tracking
   */
  private createEventListener(): EventListener {
    return () => {
      this.handleActivity();
    };
  }

  /**
   * Handle detected activity with debouncing
   */
  private handleActivity(): void {
    if (!this.isTracking) {
      return;
    }

    // Clear existing debounce timer
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    // Set up new debounce timer
    this.debounceTimer = setTimeout(() => {
      this.processActivity();
    }, this.config.debounceDelay);
  }

  /**
   * Process the activity after debounce delay
   */
  private processActivity(): void {
    const now = Date.now();
    this.lastActivity = now;

    // Check if we should trigger session extension
    const timeSinceLastExtension = now - this.lastExtension;
    
    if (timeSinceLastExtension >= this.config.extensionCooldown) {
      this.lastExtension = now;
      
      // Call the activity callback if set
      if (this.onActivityCallback) {
        try {
          this.onActivityCallback();
        } catch (error) {
          console.error('[ActivityTracker] Error in activity callback:', error);
        }
      }
    }
  }

  /**
   * Set up tracking for API calls (fetch and XMLHttpRequest)
   */
  private setupApiCallTracking(): void {
    if (typeof window === 'undefined') {
      return;
    }

    // Track fetch API calls
    const originalFetch = window.fetch;
    window.fetch = (...args) => {
      this.recordActivity();
      return originalFetch.apply(window, args);
    };

    // Track XMLHttpRequest
    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(...args) {
      // Record activity when XHR is opened
      this.recordActivity();
      return originalXHROpen.apply(this, args);
    }.bind(this);
  }

  /**
   * Clean up API call tracking by restoring original methods
   */
  private cleanupApiCallTracking(): void {
    // Note: In a real implementation, you might want to store references
    // to the original methods to properly restore them
    // For now, we'll just log that cleanup is happening
    console.log('[ActivityTracker] API call tracking cleanup completed');
  }

  /**
   * Get current configuration
   */
  public getConfig(): SessionActivityConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  public updateConfig(config: Partial<SessionActivityConfig>): void {
    const wasTracking = this.isTracking;
    
    if (wasTracking) {
      this.stop();
    }
    
    this.config = { ...this.config, ...config };
    
    if (wasTracking) {
      this.start();
    }
  }

  /**
   * Check if currently tracking activity
   */
  public isCurrentlyTracking(): boolean {
    return this.isTracking;
  }

  /**
   * Get time since last activity in milliseconds
   */
  public getTimeSinceLastActivity(): number {
    return Date.now() - this.lastActivity;
  }

  /**
   * Get time since last session extension in milliseconds
   */
  public getTimeSinceLastExtension(): number {
    return Date.now() - this.lastExtension;
  }

  /**
   * Check if session extension cooldown has passed
   */
  public canExtendSession(): boolean {
    return this.getTimeSinceLastExtension() >= this.config.extensionCooldown;
  }
}

/**
 * Create a new activity tracker instance
 */
export function createActivityTracker(config?: Partial<SessionActivityConfig>): SessionActivityTracker {
  return new SessionActivityTracker(config);
}

/**
 * Global activity tracker instance (singleton pattern)
 */
let globalActivityTracker: SessionActivityTracker | null = null;

/**
 * Get or create the global activity tracker instance
 */
export function getGlobalActivityTracker(config?: Partial<SessionActivityConfig>): SessionActivityTracker {
  if (!globalActivityTracker) {
    globalActivityTracker = createActivityTracker(config);
  }
  return globalActivityTracker;
}

/**
 * Destroy the global activity tracker instance
 */
export function destroyGlobalActivityTracker(): void {
  if (globalActivityTracker) {
    globalActivityTracker.stop();
    globalActivityTracker = null;
  }
}
