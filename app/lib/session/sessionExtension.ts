/**
 * Client-side session extension service
 */

import type { SessionExtensionRequest, SessionExtensionResponse } from './types';

/**
 * Configuration for session extension behavior
 */
export interface SessionExtensionConfig {
  /** API endpoint for session extension */
  endpoint: string;
  /** Request timeout in milliseconds */
  timeout: number;
  /** Number of retry attempts */
  retryAttempts: number;
  /** Delay between retry attempts in milliseconds */
  retryDelay: number;
  /** Whether to show user feedback during extension */
  showFeedback: boolean;
}

const DEFAULT_CONFIG: SessionExtensionConfig = {
  endpoint: '/api/session/extend',
  timeout: 10000, // 10 seconds
  retryAttempts: 3,
  retryDelay: 1000, // 1 second
  showFeedback: true,
};

/**
 * Session extension service for handling client-side session extensions
 */
export class SessionExtensionService {
  private config: SessionExtensionConfig;
  private activeRequests: Map<string, Promise<SessionExtensionResponse>> = new Map();

  constructor(config: Partial<SessionExtensionConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Extend a session with retry logic and concurrent request handling
   */
  public async extendSession(
    sessionId: string,
    extensionDuration?: number
  ): Promise<SessionExtensionResponse> {
    // Check if there's already an active request for this session
    const existingRequest = this.activeRequests.get(sessionId);
    if (existingRequest) {
      console.log('[SessionExtension] Using existing request for session:', sessionId);
      return existingRequest;
    }

    // Create new extension request
    const requestPromise = this.performExtensionWithRetry(sessionId, extensionDuration);
    
    // Store the request to prevent concurrent requests
    this.activeRequests.set(sessionId, requestPromise);

    try {
      const result = await requestPromise;
      return result;
    } finally {
      // Clean up the active request
      this.activeRequests.delete(sessionId);
    }
  }

  /**
   * Perform session extension with retry logic
   */
  private async performExtensionWithRetry(
    sessionId: string,
    extensionDuration?: number,
    attempt: number = 1
  ): Promise<SessionExtensionResponse> {
    try {
      const result = await this.performExtension(sessionId, extensionDuration);
      
      if (result.success) {
        console.log('[SessionExtension] Session extended successfully:', {
          sessionId,
          newExpiresAt: result.newExpiresAt,
          remainingTime: result.remainingTime,
        });
        return result;
      } else {
        throw new Error(result.error || 'Session extension failed');
      }
    } catch (error) {
      console.error(`[SessionExtension] Attempt ${attempt} failed:`, error);

      // If we haven't exhausted retry attempts, try again
      if (attempt < this.config.retryAttempts) {
        console.log(`[SessionExtension] Retrying in ${this.config.retryDelay}ms...`);
        
        await this.delay(this.config.retryDelay);
        return this.performExtensionWithRetry(sessionId, extensionDuration, attempt + 1);
      }

      // All retry attempts exhausted
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        success: false,
        error: `Failed to extend session after ${this.config.retryAttempts} attempts: ${errorMessage}`,
      };
    }
  }

  /**
   * Perform a single session extension request
   */
  private async performExtension(
    sessionId: string,
    extensionDuration?: number
  ): Promise<SessionExtensionResponse> {
    const requestData: SessionExtensionRequest = {
      sessionId,
      extensionDuration,
    };

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

    try {
      const response = await fetch(this.config.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const result: SessionExtensionResponse = await response.json();
      return result;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Session extension request timed out');
      }
      
      throw error;
    }
  }

  /**
   * Get current session information without extending it
   */
  public async getSessionInfo(): Promise<SessionExtensionResponse> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

    try {
      const response = await fetch(this.config.endpoint, {
        method: 'GET',
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const result: SessionExtensionResponse = await response.json();
      return result;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Session info request timed out');
      }
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get session info',
      };
    }
  }

  /**
   * Check if there are any active extension requests
   */
  public hasActiveRequests(): boolean {
    return this.activeRequests.size > 0;
  }

  /**
   * Get the number of active extension requests
   */
  public getActiveRequestCount(): number {
    return this.activeRequests.size;
  }

  /**
   * Cancel all active extension requests
   */
  public cancelAllRequests(): void {
    console.log('[SessionExtension] Cancelling all active requests');
    this.activeRequests.clear();
  }

  /**
   * Update configuration
   */
  public updateConfig(config: Partial<SessionExtensionConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Get current configuration
   */
  public getConfig(): SessionExtensionConfig {
    return { ...this.config };
  }

  /**
   * Utility method for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Global session extension service instance
 */
let globalExtensionService: SessionExtensionService | null = null;

/**
 * Get or create the global session extension service
 */
export function getSessionExtensionService(
  config?: Partial<SessionExtensionConfig>
): SessionExtensionService {
  if (!globalExtensionService) {
    globalExtensionService = new SessionExtensionService(config);
  }
  return globalExtensionService;
}

/**
 * Destroy the global session extension service
 */
export function destroySessionExtensionService(): void {
  if (globalExtensionService) {
    globalExtensionService.cancelAllRequests();
    globalExtensionService = null;
  }
}

/**
 * Convenience function for extending a session
 */
export async function extendSession(
  sessionId: string,
  extensionDuration?: number
): Promise<SessionExtensionResponse> {
  const service = getSessionExtensionService();
  return service.extendSession(sessionId, extensionDuration);
}

/**
 * Convenience function for getting session information
 */
export async function getSessionInfo(): Promise<SessionExtensionResponse> {
  const service = getSessionExtensionService();
  return service.getSessionInfo();
}
