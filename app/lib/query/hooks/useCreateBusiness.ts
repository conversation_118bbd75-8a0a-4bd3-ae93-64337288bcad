import { useMutation, useQueryClient } from '@tanstack/react-query'
import { BusinessService } from '~/lib/services/businessService'
import { businessQueryKeys } from '../queryKeys'
import { toast } from 'sonner'
import type { Business, BusinessFormData } from '~/lib/types/business'

/**
 * Hook for creating a new business with optimistic updates
 * 
 * @param userId - The user ID creating the business
 * @returns Mutation object with mutate function and state
 */
export function useCreateBusiness(userId: string | undefined) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (data: BusinessFormData): Promise<Business> => {
      if (!userId) {
        throw new Error('User ID is required to create a business')
      }
      return BusinessService.create(userId, data)
    },
    
    // Optimistic update
    onMutate: async (newBusinessData: BusinessFormData) => {
      if (!userId) return

      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ 
        queryKey: businessQueryKeys.byUser(userId) 
      })

      // Snapshot the previous value
      const previousBusinesses = queryClient.getQueryData<Business[]>(
        businessQueryKeys.byUser(userId)
      )

      // Create optimistic business object
      const optimisticBusiness: Business = {
        id: `temp-${Date.now()}`, // Temporary ID
        name: newBusinessData.name,
        description: newBusinessData.description || null,
        note: newBusinessData.note || null,
        currency: newBusinessData.currency,
        logo: newBusinessData.logo || null,
        userId,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      // Optimistically update the cache
      queryClient.setQueryData<Business[]>(
        businessQueryKeys.byUser(userId),
        (old = []) => [...old, optimisticBusiness]
      )

      // Return context with previous data for rollback
      return { previousBusinesses, optimisticBusiness }
    },

    // On success, invalidate and refetch
    onSuccess: (newBusiness, variables, context) => {
      if (!userId) return

      // Remove the optimistic update and add the real data
      queryClient.setQueryData<Business[]>(
        businessQueryKeys.byUser(userId),
        (old = []) => {
          // Remove the optimistic business and add the real one
          const filtered = old.filter(b => b.id !== context?.optimisticBusiness.id)
          return [...filtered, newBusiness]
        }
      )

      // Invalidate to ensure we have the latest data
      queryClient.invalidateQueries({ 
        queryKey: businessQueryKeys.byUser(userId) 
      })

      toast.success('Business created successfully!')
    },

    // On error, rollback the optimistic update
    onError: (error, variables, context) => {
      if (!userId) return

      // Rollback to previous state
      if (context?.previousBusinesses) {
        queryClient.setQueryData(
          businessQueryKeys.byUser(userId),
          context.previousBusinesses
        )
      }

      console.error('Failed to create business:', error)
      toast.error('Failed to create business. Please try again.')
    },

    // Always refetch after error or success
    onSettled: () => {
      if (!userId) return
      
      queryClient.invalidateQueries({ 
        queryKey: businessQueryKeys.byUser(userId) 
      })
    }
  })
}
