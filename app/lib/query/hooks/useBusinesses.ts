import { useQuery } from '@tanstack/react-query'
import { BusinessService } from '~/lib/services/businessService'
import { businessQueryKeys } from '../queryKeys'
import type { Business } from '~/lib/types/business'

/**
 * Hook for fetching all businesses for a user
 * 
 * @param userId - The user ID to fetch businesses for
 * @param options - Additional query options
 * @returns Query result with businesses data, loading state, and error
 */
export function useBusinesses(
  userId: string | undefined,
  options?: {
    enabled?: boolean
    staleTime?: number
    refetchInterval?: number
  }
) {
  return useQuery({
    queryKey: businessQueryKeys.byUser(userId || ''),
    queryFn: async (): Promise<Business[]> => {
      if (!userId) {
        throw new Error('User ID is required to fetch businesses')
      }
      return BusinessService.getAllByUser(userId)
    },
    enabled: !!userId && (options?.enabled !== false),
    staleTime: options?.staleTime,
    refetchInterval: options?.refetchInterval,
    // Provide empty array as initial data to prevent undefined state
    placeholderData: [],
    // Retry configuration for this specific query
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      if (error.message.includes('Unauthorized') || error.message.includes('401')) {
        return false
      }
      // Retry up to 3 times for other errors
      return failureCount < 3
    }
  })
}

/**
 * Hook for getting businesses data without triggering a fetch
 * Useful for accessing cached data
 */
export function useBusinessesData(userId: string | undefined) {
  return useQuery({
    queryKey: businessQueryKeys.byUser(userId || ''),
    queryFn: () => Promise.resolve([]),
    enabled: false,
    placeholderData: []
  })
}
