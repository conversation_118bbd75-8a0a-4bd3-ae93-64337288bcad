import { useMutation, useQueryClient } from '@tanstack/react-query'
import { BusinessService } from '~/lib/services/businessService'
import { businessQueryKeys } from '../queryKeys'
import { toast } from 'sonner'
import type { Business, BusinessFormData } from '~/lib/types/business'

/**
 * Hook for updating a business with optimistic updates
 * 
 * @param businessId - The business ID to update
 * @param userId - The user ID (for authorization)
 * @returns Mutation object with mutate function and state
 */
export function useUpdateBusiness(
  businessId: string | undefined,
  userId: string | undefined
) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (data: Partial<BusinessFormData>): Promise<void> => {
      if (!businessId || !userId) {
        throw new Error('Business ID and User ID are required to update a business')
      }
      return BusinessService.update(businessId, userId, data)
    },
    
    // Optimistic update
    onMutate: async (updateData: Partial<BusinessFormData>) => {
      if (!businessId || !userId) return

      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ 
        queryKey: businessQueryKeys.byUser(userId) 
      })
      await queryClient.cancelQueries({ 
        queryKey: businessQueryKeys.byIdAndUser(userId, businessId) 
      })

      // Snapshot the previous values
      const previousBusinesses = queryClient.getQueryData<Business[]>(
        businessQueryKeys.byUser(userId)
      )
      const previousBusiness = queryClient.getQueryData<Business>(
        businessQueryKeys.byIdAndUser(userId, businessId)
      )

      // Optimistically update the businesses list
      queryClient.setQueryData<Business[]>(
        businessQueryKeys.byUser(userId),
        (old = []) => old.map(business => 
          business.id === businessId 
            ? { 
                ...business, 
                ...updateData,
                updatedAt: new Date()
              }
            : business
        )
      )

      // Optimistically update the individual business cache
      if (previousBusiness) {
        queryClient.setQueryData<Business>(
          businessQueryKeys.byIdAndUser(userId, businessId),
          {
            ...previousBusiness,
            ...updateData,
            updatedAt: new Date()
          }
        )
      }

      // Return context with previous data for rollback
      return { previousBusinesses, previousBusiness }
    },

    // On success, invalidate and refetch
    onSuccess: (_, updateData) => {
      if (!businessId || !userId) return

      // Invalidate both the businesses list and individual business
      queryClient.invalidateQueries({
        queryKey: businessQueryKeys.byUser(userId)
      })
      queryClient.invalidateQueries({
        queryKey: businessQueryKeys.byIdAndUser(userId, businessId)
      })

      // Also invalidate any other related queries that might depend on this business
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey
          return Array.isArray(queryKey) &&
                 queryKey.includes('businesses') &&
                 queryKey.includes(businessId)
        }
      })

      toast.success('Business updated successfully!')
    },

    // On error, rollback the optimistic update
    onError: (error, variables, context) => {
      if (!businessId || !userId) return

      // Rollback to previous state
      if (context?.previousBusinesses) {
        queryClient.setQueryData(
          businessQueryKeys.byUser(userId),
          context.previousBusinesses
        )
      }
      if (context?.previousBusiness) {
        queryClient.setQueryData(
          businessQueryKeys.byIdAndUser(userId, businessId),
          context.previousBusiness
        )
      }

      console.error('Failed to update business:', error)
      toast.error('Failed to update business. Please try again.')
    },

    // Always refetch after error or success
    onSettled: () => {
      if (!businessId || !userId) return
      
      queryClient.invalidateQueries({ 
        queryKey: businessQueryKeys.byUser(userId) 
      })
      queryClient.invalidateQueries({ 
        queryKey: businessQueryKeys.byIdAndUser(userId, businessId) 
      })
    }
  })
}
