import { useMutation, useQueryClient } from '@tanstack/react-query'
import { BusinessService } from '~/lib/services/businessService'
import { businessQueryKeys } from '../queryKeys'
import { toast } from 'sonner'
import type { Business } from '~/lib/types/business'

/**
 * Hook for deleting a business with optimistic updates and proper cache invalidation
 * 
 * @param userId - The user ID (for authorization)
 * @returns Mutation object with mutate function and state
 */
export function useDeleteBusiness(userId: string | undefined) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (businessId: string): Promise<void> => {
      if (!userId) {
        throw new Error('User ID is required to delete a business')
      }
      return BusinessService.delete(businessId, userId)
    },
    
    // Optimistic update
    onMutate: async (businessId: string) => {
      if (!userId) return

      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ 
        queryKey: businessQueryKeys.byUser(userId) 
      })
      await queryClient.cancelQueries({ 
        queryKey: businessQueryKeys.byIdAndUser(userId, businessId) 
      })

      // Snapshot the previous values
      const previousBusinesses = queryClient.getQueryData<Business[]>(
        businessQueryKeys.byUser(userId)
      )
      const previousBusiness = queryClient.getQueryData<Business>(
        businessQueryKeys.byIdAndUser(userId, businessId)
      )

      // Optimistically remove the business from the list
      queryClient.setQueryData<Business[]>(
        businessQueryKeys.byUser(userId),
        (old = []) => old.filter(business => business.id !== businessId)
      )

      // Remove the individual business from cache
      queryClient.removeQueries({ 
        queryKey: businessQueryKeys.byIdAndUser(userId, businessId) 
      })

      // Return context with previous data for rollback
      return { previousBusinesses, previousBusiness, businessId }
    },

    // On success, ensure cache is properly cleaned up
    onSuccess: (_, businessId, context) => {
      if (!userId) return

      // Remove the specific business query from cache
      queryClient.removeQueries({ 
        queryKey: businessQueryKeys.byIdAndUser(userId, businessId) 
      })

      // Invalidate the businesses list to ensure consistency
      queryClient.invalidateQueries({ 
        queryKey: businessQueryKeys.byUser(userId) 
      })

      // Also invalidate any other related queries that might depend on this business
      queryClient.invalidateQueries({ 
        predicate: (query) => {
          const queryKey = query.queryKey
          return Array.isArray(queryKey) && 
                 queryKey.includes('businesses') && 
                 queryKey.includes(businessId)
        }
      })

      toast.success('Business deleted successfully!')
    },

    // On error, rollback the optimistic update
    onError: (error, businessId, context) => {
      if (!userId) return

      // Rollback to previous state
      if (context?.previousBusinesses) {
        queryClient.setQueryData(
          businessQueryKeys.byUser(userId),
          context.previousBusinesses
        )
      }
      
      // Restore the individual business if it existed
      if (context?.previousBusiness) {
        queryClient.setQueryData(
          businessQueryKeys.byIdAndUser(userId, businessId),
          context.previousBusiness
        )
      }

      console.error('Failed to delete business:', error)
      toast.error('Failed to delete business. Please try again.')
    },

    // Always refetch after error or success to ensure consistency
    onSettled: (_, __, businessId) => {
      if (!userId) return
      
      // Invalidate the businesses list
      queryClient.invalidateQueries({ 
        queryKey: businessQueryKeys.byUser(userId) 
      })
      
      // Remove any stale individual business queries
      queryClient.removeQueries({ 
        queryKey: businessQueryKeys.byIdAndUser(userId, businessId) 
      })
    }
  })
}
