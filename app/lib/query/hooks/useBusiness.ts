import { useQuery } from '@tanstack/react-query'
import { BusinessService } from '~/lib/services/businessService'
import { businessQueryKeys } from '../queryKeys'
import type { Business } from '~/lib/types/business'

/**
 * Hook for fetching a single business by ID
 * 
 * @param businessId - The business ID to fetch
 * @param userId - The user ID (for authorization)
 * @param options - Additional query options
 * @returns Query result with business data, loading state, and error
 */
export function useBusiness(
  businessId: string | undefined,
  userId: string | undefined,
  options?: {
    enabled?: boolean
    staleTime?: number
  }
) {
  return useQuery({
    queryKey: businessQueryKeys.byIdAndUser(userId || '', businessId || ''),
    queryFn: async (): Promise<Business | undefined> => {
      if (!businessId || !userId) {
        throw new Error('Business ID and User ID are required')
      }
      return BusinessService.getByIdAndUser(businessId, userId)
    },
    enabled: !!businessId && !!userId && (options?.enabled !== false),
    staleTime: options?.staleTime,
    // Retry configuration for this specific query
    retry: (failureCount, error) => {
      // Don't retry on authentication errors or not found errors
      if (
        error.message.includes('Unauthorized') || 
        error.message.includes('401') ||
        error.message.includes('404') ||
        error.message.includes('Not found')
      ) {
        return false
      }
      // Retry up to 3 times for other errors
      return failureCount < 3
    }
  })
}

/**
 * Hook for getting business data from cache without triggering a fetch
 * Useful for accessing cached data
 */
export function useBusinessData(
  businessId: string | undefined,
  userId: string | undefined
) {
  return useQuery({
    queryKey: businessQueryKeys.byIdAndUser(userId || '', businessId || ''),
    queryFn: () => Promise.resolve(undefined),
    enabled: false
  })
}
