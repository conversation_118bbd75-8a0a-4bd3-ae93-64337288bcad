import { QueryClient } from '@tanstack/react-query'
import { businessQueryKeys } from '../queryKeys'

/**
 * Utility functions for managing React Query cache for business data
 */
export class BusinessCacheUtils {
  /**
   * Invalidate all business-related queries for a user
   * @param queryClient - The QueryClient instance
   * @param userId - The user ID
   */
  static invalidateAllBusinessQueries(queryClient: QueryClient, userId: string) {
    return queryClient.invalidateQueries({
      queryKey: businessQueryKeys.byUser(userId)
    })
  }

  /**
   * Invalidate a specific business query
   * @param queryClient - The QueryClient instance
   * @param userId - The user ID
   * @param businessId - The business ID
   */
  static invalidateBusinessQuery(queryClient: QueryClient, userId: string, businessId: string) {
    return queryClient.invalidateQueries({
      queryKey: businessQueryKeys.byIdAndUser(userId, businessId)
    })
  }

  /**
   * Remove all business-related queries from cache for a user (used on logout)
   * @param queryClient - The QueryClient instance
   * @param userId - The user ID (optional, if not provided, removes all business queries)
   */
  static clearBusinessCache(queryClient: QueryClient, userId?: string) {
    if (userId) {
      return queryClient.removeQueries({
        queryKey: businessQueryKeys.byUser(userId)
      })
    } else {
      return queryClient.removeQueries({
        queryKey: businessQueryKeys.all
      })
    }
  }

  /**
   * Prefetch businesses for a user
   * @param queryClient - The QueryClient instance
   * @param userId - The user ID
   * @param fetchFn - Function to fetch the businesses
   */
  static async prefetchBusinesses(
    queryClient: QueryClient, 
    userId: string, 
    fetchFn: () => Promise<any[]>
  ) {
    return queryClient.prefetchQuery({
      queryKey: businessQueryKeys.byUser(userId),
      queryFn: fetchFn,
      staleTime: 5 * 60 * 1000, // 5 minutes
    })
  }

  /**
   * Get cached businesses data without triggering a fetch
   * @param queryClient - The QueryClient instance
   * @param userId - The user ID
   */
  static getCachedBusinesses(queryClient: QueryClient, userId: string) {
    return queryClient.getQueryData(businessQueryKeys.byUser(userId))
  }

  /**
   * Set businesses data in cache
   * @param queryClient - The QueryClient instance
   * @param userId - The user ID
   * @param businesses - The businesses data
   */
  static setCachedBusinesses(queryClient: QueryClient, userId: string, businesses: any[]) {
    return queryClient.setQueryData(businessQueryKeys.byUser(userId), businesses)
  }
}
