import { QueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'

// Global logout handler - will be set by AuthProvider
let globalLogoutHandler: (() => void) | null = null;

/**
 * Set the global logout handler for 401 error handling
 * This should be called by the AuthProvider during initialization
 */
export function setGlobalLogoutHandler(handler: () => void) {
  globalLogoutHandler = handler;
}

/**
 * Check if an error is a 401 Unauthorized error
 */
function is401Error(error: any): boolean {
  // Check for different error formats
  if (error?.status === 401 || error?.response?.status === 401) {
    return true;
  }

  // Check error message for 401 indicators
  const errorMessage = error?.message?.toLowerCase() || '';
  if (errorMessage.includes('unauthorized') || errorMessage.includes('401')) {
    return true;
  }

  // Check for fetch response errors
  if (error?.name === 'ResponseError' && error?.response?.status === 401) {
    return true;
  }

  return false;
}

/**
 * Handle 401 errors by triggering automatic logout
 */
function handle401Error(error: any) {
  console.warn('[Auth] 401 Unauthorized detected, triggering automatic logout', error);

  if (globalLogoutHandler) {
    // Clear all cached data before logout
    // This will be handled by the logout handler
    globalLogoutHandler();
  } else {
    console.error('[Auth] No global logout handler set - cannot handle 401 error automatically');
    toast.error('Session expired. Please log in again.');
  }
}

/**
 * Enhanced error handler for queries that handles 401 errors
 */
function handleQueryError(error: Error) {
  console.error('Query error:', error);

  // Check for 401 errors and handle them specially
  if (is401Error(error)) {
    handle401Error(error);
    return; // Don't show generic error toast for 401s
  }

  // Show generic error toast for other errors
  toast.error('An error occurred while fetching data');
}

/**
 * Enhanced error handler for mutations that handles 401 errors
 */
function handleMutationError(error: Error) {
  console.error('Mutation error:', error);

  // Check for 401 errors and handle them specially
  if (is401Error(error)) {
    handle401Error(error);
    return; // Don't show generic error toast for 401s
  }

  // Show generic error toast for other errors
  toast.error('An error occurred while saving data');
}

/**
 * Enhanced retry function that doesn't retry 401 errors
 */
function shouldRetry(failureCount: number, error: any): boolean {
  // Never retry 401 errors - they indicate authentication issues
  if (is401Error(error)) {
    return false;
  }

  // Don't retry on other client errors (4xx)
  if (error?.status >= 400 && error?.status < 500) {
    return false;
  }

  // Retry up to 3 times for other errors (network issues, server errors, etc.)
  return failureCount < 3;
}

/**
 * Default query options for the application with enhanced 401 handling
 * - staleTime: 5 minutes (data doesn't change frequently)
 * - gcTime: 10 minutes (keep data in cache longer)
 * - refetchOnWindowFocus: true (refresh when user returns to tab)
 * - refetchOnReconnect: true (refresh when internet reconnects)
 * - retry: Enhanced retry logic that handles 401 errors
 */
const defaultQueryOptions = {
  queries: {
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
    retry: shouldRetry,
    // Enhanced error handler for queries with 401 detection
    onError: handleQueryError
  },
  mutations: {
    retry: shouldRetry,
    // Enhanced error handler for mutations with 401 detection
    onError: handleMutationError
  }
}

/**
 * Create a new QueryClient with enhanced 401 error handling
 */
export const createQueryClient = () => new QueryClient({
  defaultOptions: defaultQueryOptions
});

/**
 * Clear all cached data from the query client
 * This should be called during logout to ensure no stale data remains
 */
export function clearAllCachedData(queryClient: QueryClient) {
  console.log('[QueryClient] Clearing all cached data due to logout');

  // Clear all queries and mutations
  queryClient.clear();

  // Reset any error boundaries
  queryClient.resetQueries();

  // Cancel any ongoing queries
  queryClient.cancelQueries();
}

/**
 * Utility to check if the current environment supports the query client enhancements
 */
export function isQueryClientEnhanced(): boolean {
  return typeof globalLogoutHandler !== 'undefined';
}

/**
 * Get the current global logout handler (for testing purposes)
 */
export function getGlobalLogoutHandler(): (() => void) | null {
  return globalLogoutHandler;
}
