/**
 * Centralized query key definitions for business data
 * 
 * Query key structure:
 * - ['businesses', userId] - All businesses for a user
 * - ['businesses', userId, businessId] - Specific business
 * 
 * This structure allows for:
 * - Invalidating all businesses for a user when needed
 * - Targeting specific business data
 * - Proper cache management
 */

export const businessQueryKeys = {
  /**
   * Base key for all business-related queries
   */
  all: ['businesses'] as const,
  
  /**
   * All businesses for a specific user
   * @param userId - The user ID
   */
  byUser: (userId: string) => [...businessQueryKeys.all, userId] as const,
  
  /**
   * Specific business by ID and user
   * @param userId - The user ID
   * @param businessId - The business ID
   */
  byIdAndUser: (userId: string, businessId: string) => 
    [...businessQueryKeys.byUser(userId), businessId] as const,
}

/**
 * Helper function to get all query keys for a user
 * Useful for invalidating all business data for a user
 */
export const getUserBusinessKeys = (userId: string) => ({
  all: businessQueryKeys.byUser(userId),
  specific: (businessId: string) => businessQueryKeys.byIdAndUser(userId, businessId)
})
