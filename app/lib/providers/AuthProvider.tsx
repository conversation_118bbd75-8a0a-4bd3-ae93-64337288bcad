import { createContext, useContext, useState, useEffect, useCallback, useRef, useMemo } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useClientSession } from "~/lib/hooks/useClientSession";
import { useSessionMonitor } from "~/lib/hooks/useSessionMonitor";
import { useSessionActivity } from "~/lib/hooks/useSessionActivity";
import { AuthModal } from "~/components/auth/AuthModal";
import { SessionWarningModal, useSessionWarningModal } from "~/lib/session/SessionWarningModal";
import { setGlobalLogoutHandler, clearAllCachedData } from "~/lib/query/queryClient";
import { createLogoutHandler } from "~/lib/session/sessionLogout";
import { extendSession } from "~/lib/session/sessionExtension";
import { authClient } from "~/lib/auth.client";
import type { User, Session } from "~/lib/auth.server";
import type { SessionWarningEvent } from "~/lib/session/types";

interface AuthContextType {
  user: User | null;
  session: any; // Use any for now to avoid type conflicts
  isLoading: boolean;
  isAuthenticated: boolean;
  showAuthModal: (mode?: "login" | "register") => void;
  hideAuthModal: () => void;
  // Session management features
  sessionTimeRemaining: number;
  isSessionMonitoring: boolean;
  isSessionWarningShown: boolean;
  extendCurrentSession: () => Promise<boolean>;
  performLogout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const { data: session, isLoading } = useClientSession();
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [authModalMode, setAuthModalMode] = useState<"login" | "register">("login");
  const queryClient = useQueryClient();

  const user = session?.user || null;
  const isAuthenticated = !!user;

  // Use refs to track previous values and prevent unnecessary re-renders
  const prevSessionRef = useRef<any>(null);
  const prevIsAuthenticatedRef = useRef<boolean>(false);

  // Session warning modal state
  const {
    isOpen: isWarningOpen,
    minutesRemaining,
    showWarning,
    hideWarning,
    updateTimeRemaining,
  } = useSessionWarningModal();

  // Create stable config object to prevent recreation
  const sessionMonitorConfig = useMemo(() => ({
    warningThresholds: [10, 5, 1], // Warning at 10, 5, and 1 minutes
    checkInterval: 30 * 1000, // Check every 30 seconds
    enabled: true,
    debug: process.env.NODE_ENV === 'development',
  }), []); // Empty dependency array - config never changes

  // Logout handler (defined early to be used in callbacks)
  const performLogout = useCallback(async () => {
    try {
      // Clear React Query cache
      clearAllCachedData(queryClient);

      // Sign out from Better Auth
      await authClient.signOut();

      // Hide any open modals
      hideWarning();
      setAuthModalOpen(false);

      // Redirect to home page
      if (typeof window !== 'undefined') {
        window.location.href = '/';
      }
    } catch (error) {
      console.error('[AuthProvider] Logout error:', error);
      // Force redirect even if logout fails
      if (typeof window !== 'undefined') {
        window.location.href = '/';
      }
    }
  }, [queryClient, hideWarning]);

  // Create stable callback functions
  const handleSessionWarning = useCallback((event: SessionWarningEvent) => {
    if (event.minutesRemaining) {
      showWarning(event.minutesRemaining);
    }
  }, [showWarning]);

  const handleSessionExpired = useCallback(() => {
    performLogout();
  }, [performLogout]);

  const handleSessionExtended = useCallback(() => {
    hideWarning();
  }, [hideWarning]);

  // Session monitoring
  const {
    state: sessionState,
    startMonitoring,
    stopMonitoring,
    extendSession: extendSessionMonitor,
    getTimeRemaining,
  } = useSessionMonitor({
    config: sessionMonitorConfig,
    onWarning: handleSessionWarning,
    onExpired: handleSessionExpired,
    onExtended: handleSessionExtended,
  });

  // Activity tracking for automatic session extension
  useSessionActivity({
    config: {
      debounceDelay: 30 * 1000, // 30 seconds
      trackedEvents: ['click', 'keydown', 'scroll'],
      trackApiCalls: true,
      extensionCooldown: 5 * 60 * 1000, // 5 minutes
    },
    enabled: isAuthenticated,
    onActivityDetected: () => {
      // Automatically extend session on activity if warning is not shown
      if (!isWarningOpen && sessionState.session && sessionState.session.expiresAt - Date.now() < 10 * 60 * 1000) {
        extendCurrentSession();
      }
    },
  });



  // Session extension handler
  const extendCurrentSession = useCallback(async (): Promise<boolean> => {
    if (!sessionState.session) {
      return false;
    }

    try {
      const result = await extendSession(sessionState.session.sessionId);
      if (result.success) {
        // Update session monitor with new expiration time
        if (result.newExpiresAt) {
          // This will be handled by the session monitor's updateSession method
          await extendSessionMonitor();
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error('[AuthProvider] Session extension error:', error);
      return false;
    }
  }, [sessionState.session, extendSessionMonitor]);

  // Set up global logout handler for 401 errors
  useEffect(() => {
    const logoutHandler = createLogoutHandler(queryClient, {
      clearCache: true,
      redirectToLogin: true,
      showNotification: true,
    });

    setGlobalLogoutHandler(logoutHandler);

    return () => {
      setGlobalLogoutHandler(() => {});
    };
  }, [queryClient]);

  // Start/stop session monitoring based on authentication status
  useEffect(() => {
    // Get session identifier for comparison (token or id)
    const currentSessionId = session?.session?.token || session?.session?.id;
    const prevSessionId = prevSessionRef.current?.session?.token || prevSessionRef.current?.session?.id;

    console.log('[AuthProvider] 🔄 Session effect triggered:', {
      isAuthenticated,
      hasSession: !!session,
      currentSessionId,
      prevAuthenticated: prevIsAuthenticatedRef.current,
      prevSessionId
    });

    // Only act if authentication status or session actually changed
    const authChanged = isAuthenticated !== prevIsAuthenticatedRef.current;
    const sessionChanged = currentSessionId !== prevSessionId;

    if (!authChanged && !sessionChanged) {
      console.log('[AuthProvider] ⏭️ No meaningful changes, skipping session monitoring update');
      return;
    }

    if (isAuthenticated && session) {
      console.log('[AuthProvider] 🚀 Starting session monitoring');
      startMonitoring(session);
    } else {
      console.log('[AuthProvider] 🛑 Stopping session monitoring');
      stopMonitoring();
    }

    // Update refs
    prevIsAuthenticatedRef.current = isAuthenticated;
    prevSessionRef.current = session;
  }, [isAuthenticated, session]); // eslint-disable-line react-hooks/exhaustive-deps
  // Note: startMonitoring and stopMonitoring are intentionally excluded to prevent recreation cycles

  const showAuthModal = (mode: "login" | "register" = "login") => {
    setAuthModalMode(mode);
    setAuthModalOpen(true);
  };

  const hideAuthModal = () => {
    setAuthModalOpen(false);
  };

  const contextValue: AuthContextType = {
    user,
    session,
    isLoading,
    isAuthenticated,
    showAuthModal,
    hideAuthModal,
    sessionTimeRemaining: getTimeRemaining(),
    isSessionMonitoring: sessionState.isMonitoring,
    isSessionWarningShown: isWarningOpen,
    extendCurrentSession,
    performLogout,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
      <AuthModal
        isOpen={authModalOpen}
        onClose={hideAuthModal}
        defaultMode={authModalMode}
      />
      <SessionWarningModal
        isOpen={isWarningOpen}
        minutesRemaining={Math.ceil(getTimeRemaining() / (1000 * 60))}
        isExtending={sessionState.isExtending}
        onExtendSession={extendCurrentSession}
        onLogout={performLogout}
        onDismiss={hideWarning}
        allowDismiss={false} // Don't allow dismissing session warnings
      />
    </AuthContext.Provider>
  );
}
