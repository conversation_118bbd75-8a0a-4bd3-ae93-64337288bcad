import React, { useEffect, useState } from 'react'
import { useBusinessWithReactQuery } from '~/lib/hooks/useBusinessWithReactQuery'

interface BusinessProviderProps {
  children: React.ReactNode
}

export function BusinessProvider({ children }: BusinessProviderProps) {
  const [isHydrated, setIsHydrated] = useState(false)

  // Use the React Query integrated business hook
  const { isReady } = useBusinessWithReactQuery()

  useEffect(() => {
    // Handle hydration
    setIsHydrated(true)
  }, [])

  // Prevent hydration mismatch by not rendering until hydrated
  if (!isHydrated) {
    return null
  }

  return <>{children}</>
}
