import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { BusinessState } from '~/lib/types/business'
import { BusinessService } from '~/lib/services/businessService'

export const useBusinessStore = create<BusinessState>()(
  persist(
    (set, get) => ({
      currentBusiness: null,
      businesses: [],
      isLoading: false,
      isInitialized: false,
      isBusinessSwitching: false,

      setCurrentBusiness: (business) => {
        set({ currentBusiness: business })
      },

      switchBusiness: async (business) => {
        const currentBusiness = get().currentBusiness

        console.log('BusinessStore: switchBusiness called, switching from:', currentBusiness?.name, 'to:', business.name)

        // Only proceed if this is actually a different business
        if (currentBusiness?.id === business.id) {
          console.log('BusinessStore: Same business selected, no switch needed')
          return
        }

        try {
          // Set switching state to show loading
          set({ isBusinessSwitching: true })

          // Update the current business
          console.log('BusinessStore: Setting current business to:', business.name)
          set({ currentBusiness: business })

          // Wait a minimum duration for better UX (prevents flicker)
          await new Promise(resolve => setTimeout(resolve, 500))

          console.log('BusinessStore: Business switch completed successfully')

        } catch (error) {
          console.error('Error during business switching:', error)
          // Ensure we still reset the switching state on error
        } finally {
          // Always clear switching state, even on error
          set({ isBusinessSwitching: false })
        }
      },

      loadBusinesses: async (userId?: string) => {
        if (!userId) {
          console.warn('Cannot load businesses: userId is required')
          set({ businesses: [], isLoading: false })
          return
        }

        set({ isLoading: true })
        try {
          const businesses = await BusinessService.getAllByUser(userId)
          set({ businesses })

          // If no current business is selected but businesses exist, select the first one
          const { currentBusiness } = get()
          if (!currentBusiness && businesses.length > 0) {
            set({ currentBusiness: businesses[0] })
          }
        } catch (error) {
          console.error('Failed to load businesses:', error)
          set({ businesses: [] })
        } finally {
          set({ isLoading: false })
        }
      },

      addBusiness: (business) => {
        set((state) => ({
          businesses: [...state.businesses, business]
        }))
      },

      updateBusiness: (id, updates) => {
        set((state) => ({
          businesses: state.businesses.map(business =>
            business.id === id ? { ...business, ...updates } : business
          ),
          currentBusiness: state.currentBusiness?.id === id 
            ? { ...state.currentBusiness, ...updates }
            : state.currentBusiness
        }))
      },

      removeBusiness: (id) => {
        set((state) => {
          const newBusinesses = state.businesses.filter(business => business.id !== id)
          const newCurrentBusiness = state.currentBusiness?.id === id 
            ? (newBusinesses.length > 0 ? newBusinesses[0] : null)
            : state.currentBusiness
          
          return {
            businesses: newBusinesses,
            currentBusiness: newCurrentBusiness
          }
        })
      },

      initializeStore: async (userId?: string) => {
        if (get().isInitialized) return

        if (!userId) {
          console.warn('Cannot initialize business store: userId is required')
          set({ isInitialized: true, isLoading: false })
          return
        }

        set({ isLoading: true })
        try {
          // Load all businesses for the user
          await get().loadBusinesses(userId)

          const { businesses, currentBusiness } = get()

          // If businesses exist but none is selected, select the first one
          if (businesses.length > 0 && !currentBusiness) {
            console.log('BusinessStore: Auto-selecting first business:', businesses[0].name)
            set({ currentBusiness: businesses[0] })
          }

          // Always set as initialized, even if no businesses exist
          // This allows the UI to show proper empty state
          set({ isInitialized: true })
        } catch (error) {
          console.error('Failed to initialize business store:', error)
          set({ businesses: [], currentBusiness: null, isInitialized: true })
        } finally {
          set({ isLoading: false })
        }
      },

      getCurrentBusinessId: () => {
        return get().currentBusiness?.id || null
      },

      isBusinessSelected: () => {
        return get().currentBusiness !== null
      },

      // Clear all business data (used on logout)
      clearBusinessData: () => {
        set({
          currentBusiness: null,
          businesses: [],
          isLoading: false,
          isInitialized: false,
          isBusinessSwitching: false,
        })
        // Also clear persisted data
        localStorage.removeItem('business-store')
      },
    }),
    {
      name: 'business-store',
      // Only persist the current business selection, not the full businesses array
      partialize: (state) => ({
        currentBusiness: state.currentBusiness
      }),
      onRehydrateStorage: () => (state) => {
        console.log('BusinessStore: Hydration completed, persisted business:', state?.currentBusiness?.name)
        if (state) {
          state.isInitialized = true
        }
      },
    }
  )
)

// Helper hooks for common use cases
export const useCurrentBusiness = () => useBusinessStore((state) => state.currentBusiness)
export const useCurrentBusinessId = () => useBusinessStore((state) => state.getCurrentBusinessId())
export const useCurrentBusinessCurrency = () => useBusinessStore((state) => state.currentBusiness?.currency || 'IDR')
export const useBusinesses = () => useBusinessStore((state) => state.businesses)
export const useIsBusinessSelected = () => useBusinessStore((state) => state.isBusinessSelected())
export const useIsBusinessSwitching = () => useBusinessStore((state) => state.isBusinessSwitching)
