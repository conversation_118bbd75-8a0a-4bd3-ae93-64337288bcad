import type { ActionFunctionArgs, LoaderFunctionArgs } from '@remix-run/node';
import { json, redirect } from '@remix-run/node';
import { requirePermissions } from './rbac.server';

/**
 * Configuration for automatic RBAC protection
 */
export interface AutoRBACConfig {
  /** Resource name (e.g., 'products', 'ingredients', 'categories') */
  resource: string;
  /** Business ID extraction method */
  getBusinessId?: (args: LoaderFunctionArgs | ActionFunctionArgs) => string | Promise<string>;
  /** Custom permission mapping for specific actions */
  customPermissions?: {
    [key: string]: string[];
  };
  /** Skip RBAC for specific methods */
  skipMethods?: string[];
  /** Additional permission checks */
  additionalChecks?: (args: LoaderFunctionArgs | ActionFunctionArgs) => Promise<void>;
}

/**
 * Default permission mapping based on HTTP methods and form actions
 */
const DEFAULT_PERMISSION_MAPPING = {
  GET: 'read',
  POST: 'create',
  PUT: 'update',
  PATCH: 'update',
  DELETE: 'delete',
  // Form action mappings
  create: 'create',
  update: 'update',
  delete: 'delete',
  read: 'read',
  list: 'read', // List operations typically use read permission
};

/**
 * Extract business ID from common patterns
 */
function extractBusinessIdFromRequest(request: Request, params?: Record<string, string | undefined>): string | null {
  // Try URL params first
  if (params?.businessId) {
    return params.businessId;
  }

  // Try query parameters
  const url = new URL(request.url);
  const businessId = url.searchParams.get('businessId');
  return businessId;
}

/**
 * Detect if the request is from a browser (vs API client)
 */
function isBrowserRequest(request: Request): boolean {
  const accept = request.headers.get('Accept') || '';
  const userAgent = request.headers.get('User-Agent') || '';

  // Check if the request accepts HTML (typical browser behavior)
  const acceptsHtml = accept.includes('text/html');

  // Check if it's not an explicit API request
  const isApiRequest = accept.includes('application/json') && !acceptsHtml;

  // Check for common browser user agents
  const hasBrowserUserAgent = userAgent.includes('Mozilla') || userAgent.includes('Chrome') || userAgent.includes('Safari') || userAgent.includes('Firefox');

  return acceptsHtml && !isApiRequest && hasBrowserUserAgent;
}

/**
 * Add original URL to redirect for post-auth redirect
 */
function addOriginalUrlToRedirect(redirectUrl: string, originalUrl: string): string {
  const url = new URL(redirectUrl, 'http://localhost'); // Base URL doesn't matter for relative URLs
  url.searchParams.set('redirectTo', originalUrl);
  return url.pathname + url.search;
}

/**
 * Extract action from form data
 */
async function extractActionFromFormData(request: Request): Promise<string | null> {
  try {
    const clonedRequest = request.clone();
    const formData = await clonedRequest.formData();
    return formData.get('_action') as string || formData.get('action') as string || null;
  } catch {
    return null;
  }
}

/**
 * Create an auto-protected loader function
 */
export function createProtectedLoader(
  config: AutoRBACConfig,
  loaderFn: (args: LoaderFunctionArgs) => Promise<Response> | Response
) {
  return async (args: LoaderFunctionArgs): Promise<Response> => {
    const { request, params } = args;
    
    // Skip if GET is in skipMethods
    if (config.skipMethods?.includes('GET')) {
      return loaderFn(args);
    }
    
    try {
      // Get business ID
      let businessId: string | null;
      if (config.getBusinessId) {
        businessId = await config.getBusinessId(args);
      } else {
        businessId = extractBusinessIdFromRequest(request, params as Record<string, string | undefined>);
      }
      
      // If no business ID found, let the route handler deal with validation
      if (!businessId) {
        return loaderFn(args);
      }
      
      // Determine required permissions
      const action = DEFAULT_PERMISSION_MAPPING.GET;
      const permissions = config.customPermissions?.read || [`${config.resource}.${action}`];
      
      // Check permissions
      await requirePermissions(request, {
        permissions,
        businessId,
      });
      
      // Run additional checks if provided
      if (config.additionalChecks) {
        await config.additionalChecks(args);
      }
      
      // Call original loader
      return loaderFn(args);
    } catch (error) {
      if (error instanceof Response) {
        // Handle redirect responses
        if (error.status === 302) {
          const location = error.headers.get('Location');

          // For browser requests, preserve the redirect with original URL
          if (isBrowserRequest(request)) {
            if (location?.includes('/login') || location?.includes('/unauthorized')) {
              // Add original URL for post-auth redirect
              const originalUrl = new URL(request.url).pathname + new URL(request.url).search;
              const redirectWithOriginalUrl = addOriginalUrlToRedirect(location, originalUrl);
              return redirect(redirectWithOriginalUrl);
            }
            // For other redirects, pass through as-is
            return error;
          }

          // For API requests, convert redirects to JSON errors
          if (location?.includes('/login')) {
            return json({ error: 'Authentication required' }, { status: 401 });
          }
          if (location?.includes('/unauthorized')) {
            return json({ error: 'Access denied' }, { status: 403 });
          }
        }
        // For other Response objects (like 400 errors), return as-is
        return error;
      }
      console.error(`Auto RBAC error in loader for ${config.resource}:`, error);
      return json({ error: 'Access denied' }, { status: 403 });
    }
  };
}

/**
 * Create an auto-protected action function
 */
export function createProtectedAction(
  config: AutoRBACConfig,
  actionFn: (args: ActionFunctionArgs) => Promise<Response> | Response
) {
  return async (args: ActionFunctionArgs): Promise<Response> => {
    const { request, params } = args;
    const method = request.method;
    
    // Skip if method is in skipMethods
    if (config.skipMethods?.includes(method)) {
      return actionFn(args);
    }
    
    try {
      // Get business ID
      let businessId: string | null;
      if (config.getBusinessId) {
        businessId = await config.getBusinessId(args);
      } else {
        businessId = extractBusinessIdFromRequest(request, params as Record<string, string | undefined>);
      }
      
      // If no business ID found, let the route handler deal with validation
      if (!businessId) {
        return actionFn(args);
      }
      
      // Determine required permissions based on method or form action
      let requiredAction: string;
      let permissions: string[];
      
      if (method === 'POST') {
        // For POST, check if there's a form action
        const formAction = await extractActionFromFormData(request);
        if (formAction && DEFAULT_PERMISSION_MAPPING[formAction as keyof typeof DEFAULT_PERMISSION_MAPPING]) {
          requiredAction = DEFAULT_PERMISSION_MAPPING[formAction as keyof typeof DEFAULT_PERMISSION_MAPPING];
        } else {
          requiredAction = DEFAULT_PERMISSION_MAPPING.POST;
        }
        
        // Check for custom permissions for this action
        if (formAction && config.customPermissions?.[formAction]) {
          permissions = config.customPermissions[formAction];
        } else {
          permissions = config.customPermissions?.[requiredAction] || [`${config.resource}.${requiredAction}`];
        }
      } else {
        requiredAction = DEFAULT_PERMISSION_MAPPING[method as keyof typeof DEFAULT_PERMISSION_MAPPING] || 'read';
        permissions = config.customPermissions?.[requiredAction] || [`${config.resource}.${requiredAction}`];
      }
      
      // Check permissions
      await requirePermissions(request, {
        permissions,
        businessId,
      });
      
      // Run additional checks if provided
      if (config.additionalChecks) {
        await config.additionalChecks(args);
      }
      
      // Call original action
      return actionFn(args);
    } catch (error) {
      if (error instanceof Response) {
        // Handle redirect responses
        if (error.status === 302) {
          const location = error.headers.get('Location');

          // For browser requests, preserve the redirect with original URL
          if (isBrowserRequest(request)) {
            if (location?.includes('/login') || location?.includes('/unauthorized')) {
              // Add original URL for post-auth redirect
              const originalUrl = new URL(request.url).pathname + new URL(request.url).search;
              const redirectWithOriginalUrl = addOriginalUrlToRedirect(location, originalUrl);
              return redirect(redirectWithOriginalUrl);
            }
            // For other redirects, pass through as-is
            return error;
          }

          // For API requests, convert redirects to JSON errors
          if (location?.includes('/login')) {
            return json({ error: 'Authentication required' }, { status: 401 });
          }
          if (location?.includes('/unauthorized')) {
            return json({ error: 'Access denied' }, { status: 403 });
          }
        }
        // For other Response objects (like 400 errors), return as-is
        return error;
      }
      console.error(`Auto RBAC error in action for ${config.resource}:`, error);
      return json({ error: 'Access denied' }, { status: 403 });
    }
  };
}

/**
 * Create both protected loader and action functions
 */
export function createProtectedRoute(
  config: AutoRBACConfig,
  handlers: {
    loader?: (args: LoaderFunctionArgs) => Promise<Response> | Response;
    action?: (args: ActionFunctionArgs) => Promise<Response> | Response;
  }
) {
  const result: {
    loader?: (args: LoaderFunctionArgs) => Promise<Response>;
    action?: (args: ActionFunctionArgs) => Promise<Response>;
  } = {};
  
  if (handlers.loader) {
    result.loader = createProtectedLoader(config, handlers.loader);
  }
  
  if (handlers.action) {
    result.action = createProtectedAction(config, handlers.action);
  }
  
  return result;
}

/**
 * Utility function to get business ID from query parameters
 * Returns null if not found instead of throwing, allowing route handlers to handle validation
 */
export function getBusinessIdFromQuery(args: LoaderFunctionArgs | ActionFunctionArgs): string | null {
  const url = new URL(args.request.url);
  const businessId = url.searchParams.get('businessId');
  return businessId;
}

/**
 * Utility function to get business ID from route parameters
 */
export function getBusinessIdFromParams(args: LoaderFunctionArgs | ActionFunctionArgs): string | null {
  const businessId = args.params.businessId;
  if (!businessId) {
    return null;
  }
  return businessId;
}

/**
 * Utility function to get business ID from form data
 */
export async function getBusinessIdFromFormData(args: ActionFunctionArgs): Promise<string | null> {
  const formData = await args.request.formData();
  const businessId = formData.get('businessId') as string;
  if (!businessId) {
    return null;
  }
  return businessId;
}

/**
 * Pre-configured RBAC configs for common resources
 */
export const RBAC_CONFIGS = {
  products: {
    resource: 'products',
    // Don't use getBusinessId - let the route handle business ID resolution
  } as AutoRBACConfig,

  ingredients: {
    resource: 'ingredients',
    // Don't use getBusinessId - let the route handle business ID resolution
  } as AutoRBACConfig,
  
  categories: {
    resource: 'categories',
    // Don't use getBusinessId - let the route handle business ID resolution
  } as AutoRBACConfig,

  inventory: {
    resource: 'inventory',
    getBusinessId: getBusinessIdFromParams,
  } as AutoRBACConfig,

  cogs: {
    resource: 'cogs',
    // Don't use getBusinessId - let the route handle business ID resolution
    customPermissions: {
      calculate: ['cogs.calculate'],
      breakdown: ['cogs.read'],
      history: ['cogs.read'],
    },
  } as AutoRBACConfig,
};