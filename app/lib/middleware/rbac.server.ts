import { redirect } from '@remix-run/node';
import { getSession } from '~/lib/auth.session.server';
import { RBACService } from '~/lib/services/rbacService.server';

export interface RBACOptions {
  permissions?: string[];
  roles?: string[];
  requireAll?: boolean; // If true, user must have ALL permissions/roles. If false, user needs ANY.
  businessId?: string;
  redirectTo?: string;
}

/**
 * RBAC middleware to protect routes based on permissions and roles
 */
export async function requirePermissions(
  request: Request,
  options: RBACOptions
): Promise<{ userId: string; businessId?: string }> {
  const session = await getSession(request);
  
  if (!session?.user?.id) {
    throw redirect(options.redirectTo || '/login');
  }

  const userId = session.user.id;
  const businessId = options.businessId;

  // Check permissions if specified
  if (options.permissions && options.permissions.length > 0) {
    const hasPermission = options.requireAll
      ? await RBACService.hasAllPermissions(userId, options.permissions, businessId)
      : await RBACService.hasAnyPermission(userId, options.permissions, businessId);

    if (!hasPermission) {
      throw redirect(options.redirectTo || '/unauthorized');
    }
  }

  // Check roles if specified
  if (options.roles && options.roles.length > 0) {
    const hasRole = options.requireAll
      ? await Promise.all(
          options.roles.map(role => RBACService.hasRole(userId, role, businessId))
        ).then(results => results.every(Boolean))
      : await Promise.all(
          options.roles.map(role => RBACService.hasRole(userId, role, businessId))
        ).then(results => results.some(Boolean));

    if (!hasRole) {
      throw redirect(options.redirectTo || '/unauthorized');
    }
  }

  return { userId, businessId };
}

/**
 * Helper function to require business access
 */
export async function requireBusinessAccess(
  request: Request,
  businessId: string,
  redirectTo?: string
): Promise<{ userId: string; businessId: string }> {
  const result = await requirePermissions(request, {
    permissions: ['business.read'],
    businessId,
    redirectTo,
  });

  return { ...result, businessId };
}

/**
 * Helper function to require business owner role
 */
export async function requireBusinessOwner(
  request: Request,
  businessId: string,
  redirectTo?: string
): Promise<{ userId: string; businessId: string }> {
  const result = await requirePermissions(request, {
    roles: ['business_owner'],
    businessId,
    redirectTo,
  });

  return { ...result, businessId };
}

/**
 * Helper function to require super admin role
 */
export async function requireSuperAdmin(
  request: Request,
  redirectTo?: string
): Promise<{ userId: string }> {
  return await requirePermissions(request, {
    roles: ['super_admin'],
    redirectTo,
  });
}

/**
 * Helper function to require inventory management permissions
 */
export async function requireInventoryAccess(
  request: Request,
  businessId: string,
  action: 'read' | 'create' | 'update' | 'delete' = 'read',
  redirectTo?: string
): Promise<{ userId: string; businessId: string }> {
  const result = await requirePermissions(request, {
    permissions: [`inventory.${action}`],
    businessId,
    redirectTo,
  });

  return { ...result, businessId };
}

/**
 * Helper function to require category management permissions
 */
export async function requireCategoryAccess(
  request: Request,
  businessId: string,
  action: 'read' | 'create' | 'update' | 'delete' = 'read',
  redirectTo?: string
): Promise<{ userId: string; businessId: string }> {
  const result = await requirePermissions(request, {
    permissions: [`categories.${action}`],
    businessId,
    redirectTo,
  });

  return { ...result, businessId };
}

/**
 * Helper function to require COGS access
 */
export async function requireCOGSAccess(
  request: Request,
  businessId: string,
  action: 'read' | 'calculate' | 'update' = 'read',
  redirectTo?: string
): Promise<{ userId: string; businessId: string }> {
  const result = await requirePermissions(request, {
    permissions: [`cogs.${action}`],
    businessId,
    redirectTo,
  });

  return { ...result, businessId };
}

/**
 * Helper function to require user management permissions
 */
export async function requireUserManagement(
  request: Request,
  action: 'read' | 'create' | 'update' | 'delete' | 'assign_roles' = 'read',
  businessId?: string,
  redirectTo?: string
): Promise<{ userId: string; businessId?: string }> {
  return await requirePermissions(request, {
    permissions: [`users.${action}`],
    businessId,
    redirectTo,
  });
}

/**
 * Helper function to require role management permissions
 */
export async function requireRoleManagement(
  request: Request,
  action: 'read' | 'create' | 'update' | 'delete' = 'read',
  redirectTo?: string
): Promise<{ userId: string }> {
  return await requirePermissions(request, {
    permissions: [`roles.${action}`],
    redirectTo,
  });
}

/**
 * Check if user can access a specific business (for UI components)
 */
export async function canAccessBusiness(
  request: Request,
  businessId: string
): Promise<boolean> {
  try {
    await requireBusinessAccess(request, businessId);
    return true;
  } catch {
    return false;
  }
}

/**
 * Get user permissions for a business (for UI components)
 */
export async function getUserBusinessPermissions(
  request: Request,
  businessId?: string
): Promise<{ permissions: string[]; roles: string[] } | null> {
  try {
    const session = await getSession(request);
    if (!session?.user?.id) {
      return null;
    }

    const userPermissions = await RBACService.getUserPermissions(
      session.user.id,
      businessId
    );

    return {
      permissions: userPermissions.permissions,
      roles: userPermissions.roles,
    };
  } catch {
    return null;
  }
}