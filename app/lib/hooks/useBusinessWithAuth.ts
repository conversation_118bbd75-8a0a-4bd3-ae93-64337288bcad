import { useEffect } from 'react';
import { useAuth } from '~/lib/providers/AuthProvider';
import { useBusinessStore } from '~/lib/stores/businessStore';

/**
 * Hook that integrates business store with authentication context
 * Automatically initializes the business store when user is authenticated
 * and clears it when user logs out
 */
export function useBusinessWithAuth() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const {
    initializeStore,
    loadBusinesses,
    isInitialized,
    isLoading: businessLoading,
    businesses,
    currentBusiness,
    setCurrentBusiness,
    clearBusinessData
  } = useBusinessStore();

  // Initialize business store when user is authenticated
  useEffect(() => {
    if (isAuthenticated && user?.id && !isInitialized) {
      console.log('Initializing business store for user:', user.id);
      initializeStore(user.id);
    }
  }, [isAuthenticated, user?.id, isInitialized, initializeStore]);

  // Clear business data when user logs out
  useEffect(() => {
    if (!isAuthenticated) {
      console.log('Clearing business store - user logged out');
      clearBusinessData();
    }
  }, [isAuthenticated, clearBusinessData]);

  // Reload businesses when user changes (edge case)
  useEffect(() => {
    if (isAuthenticated && user?.id && isInitialized) {
      loadBusinesses(user.id);
    }
  }, [user?.id, isAuthenticated, isInitialized, loadBusinesses]);

  return {
    user,
    isAuthenticated,
    isAuthLoading: authLoading,
    isBusinessLoading: businessLoading,
    businesses,
    currentBusiness,
    isReady: isAuthenticated && isInitialized && !authLoading && !businessLoading,
  };
}
