/**
 * React hook for session activity tracking with automatic session extension
 */

import { useEffect, useCallback, useRef, useState } from 'react';
import { SessionActivityTracker, createActivityTracker } from '../session/sessionActivity';
import type { SessionActivityConfig } from '../session/types';

interface UseSessionActivityOptions {
  /** Configuration for activity tracking */
  config?: Partial<SessionActivityConfig>;
  /** Whether to automatically start tracking when hook mounts */
  autoStart?: boolean;
  /** Callback function when activity is detected and session should be extended */
  onActivityDetected?: () => void;
  /** Whether activity tracking is enabled */
  enabled?: boolean;
}

interface UseSessionActivityReturn {
  /** Whether activity tracking is currently active */
  isTracking: boolean;
  /** Timestamp of last recorded activity */
  lastActivity: number;
  /** Time since last activity in milliseconds */
  timeSinceLastActivity: number;
  /** Whether session can be extended (cooldown has passed) */
  canExtendSession: boolean;
  /** Start activity tracking */
  startTracking: () => void;
  /** Stop activity tracking */
  stopTracking: () => void;
  /** Manually record activity */
  recordActivity: () => void;
  /** Get current activity tracker configuration */
  getConfig: () => SessionActivityConfig;
  /** Update activity tracker configuration */
  updateConfig: (config: Partial<SessionActivityConfig>) => void;
}

/**
 * Hook for managing session activity tracking with automatic session extension
 */
export function useSessionActivity(options: UseSessionActivityOptions = {}): UseSessionActivityReturn {
  const {
    config,
    autoStart = true,
    onActivityDetected,
    enabled = true,
  } = options;

  // Activity tracker instance (stable reference)
  const trackerRef = useRef<SessionActivityTracker | null>(null);
  
  // State for tracking status and activity information
  const [isTracking, setIsTracking] = useState(false);
  const [lastActivity, setLastActivity] = useState(Date.now());
  const [timeSinceLastActivity, setTimeSinceLastActivity] = useState(0);

  // Initialize activity tracker
  useEffect(() => {
    if (!enabled) return;

    if (!trackerRef.current) {
      trackerRef.current = createActivityTracker(config);
    }

    return () => {
      if (trackerRef.current) {
        trackerRef.current.stop();
        trackerRef.current = null;
      }
    };
  }, [config, enabled]);

  // Set up activity callback
  useEffect(() => {
    const tracker = trackerRef.current;
    if (!tracker || !enabled) return;

    const handleActivity = () => {
      const now = Date.now();
      setLastActivity(now);
      
      // Call the provided callback
      if (onActivityDetected) {
        try {
          onActivityDetected();
        } catch (error) {
          console.error('[useSessionActivity] Error in activity callback:', error);
        }
      }
    };

    tracker.setOnActivityCallback(handleActivity);
  }, [onActivityDetected, enabled]);

  // Update time since last activity periodically
  useEffect(() => {
    if (!enabled) return;

    const interval = setInterval(() => {
      const tracker = trackerRef.current;
      if (tracker) {
        setTimeSinceLastActivity(tracker.getTimeSinceLastActivity());
      }
    }, 1000); // Update every second

    return () => clearInterval(interval);
  }, [enabled]);

  // Auto-start tracking if enabled
  useEffect(() => {
    if (autoStart && enabled && trackerRef.current && !isTracking) {
      startTracking();
    }
  }, [autoStart, enabled, isTracking]);

  /**
   * Start activity tracking
   */
  const startTracking = useCallback(() => {
    const tracker = trackerRef.current;
    if (!tracker || !enabled) return;

    tracker.start();
    setIsTracking(true);
    setLastActivity(tracker.getLastActivity());
  }, [enabled]);

  /**
   * Stop activity tracking
   */
  const stopTracking = useCallback(() => {
    const tracker = trackerRef.current;
    if (!tracker) return;

    tracker.stop();
    setIsTracking(false);
  }, []);

  /**
   * Manually record activity
   */
  const recordActivity = useCallback(() => {
    const tracker = trackerRef.current;
    if (!tracker || !enabled) return;

    tracker.recordActivity();
    setLastActivity(tracker.getLastActivity());
  }, [enabled]);

  /**
   * Get current configuration
   */
  const getConfig = useCallback((): SessionActivityConfig => {
    const tracker = trackerRef.current;
    if (!tracker) {
      throw new Error('Activity tracker not initialized');
    }
    return tracker.getConfig();
  }, []);

  /**
   * Update configuration
   */
  const updateConfig = useCallback((newConfig: Partial<SessionActivityConfig>) => {
    const tracker = trackerRef.current;
    if (!tracker) return;

    tracker.updateConfig(newConfig);
  }, []);

  /**
   * Check if session can be extended
   */
  const canExtendSession = useCallback((): boolean => {
    const tracker = trackerRef.current;
    return tracker ? tracker.canExtendSession() : false;
  }, []);

  return {
    isTracking,
    lastActivity,
    timeSinceLastActivity,
    canExtendSession: canExtendSession(),
    startTracking,
    stopTracking,
    recordActivity,
    getConfig,
    updateConfig,
  };
}

/**
 * Hook for simple activity detection without session extension logic
 * Useful for components that just need to know when user is active
 */
export function useActivityDetection(
  onActivity?: () => void,
  debounceMs: number = 30000
): {
  lastActivity: number;
  timeSinceLastActivity: number;
  recordActivity: () => void;
} {
  const [lastActivity, setLastActivity] = useState(Date.now());
  const [timeSinceLastActivity, setTimeSinceLastActivity] = useState(0);
  const debounceRef = useRef<NodeJS.Timeout | null>(null);

  const recordActivity = useCallback(() => {
    // Clear existing debounce timer
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    // Set up new debounce timer
    debounceRef.current = setTimeout(() => {
      const now = Date.now();
      setLastActivity(now);
      
      if (onActivity) {
        try {
          onActivity();
        } catch (error) {
          console.error('[useActivityDetection] Error in activity callback:', error);
        }
      }
    }, debounceMs);
  }, [onActivity, debounceMs]);

  // Update time since last activity
  useEffect(() => {
    const interval = setInterval(() => {
      setTimeSinceLastActivity(Date.now() - lastActivity);
    }, 1000);

    return () => clearInterval(interval);
  }, [lastActivity]);

  // Cleanup debounce timer on unmount
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, []);

  return {
    lastActivity,
    timeSinceLastActivity,
    recordActivity,
  };
}
