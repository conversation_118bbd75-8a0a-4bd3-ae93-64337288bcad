import { useEffect, useState } from "react";
import { authClient } from "~/lib/auth.client";

interface SessionData {
  user: any;
  session: any;
}

interface UseClientSessionReturn {
  data: SessionData | null;
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

/**
 * Client-side session hook that works with SSR
 * This hook only runs on the client-side to avoid SSR issues
 */
export function useClientSession(): UseClientSessionReturn {
  const [data, setData] = useState<SessionData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchSession = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await authClient.getSession();
      setData(result.data);
    } catch (err) {
      setError(err instanceof Error ? err : new Error("Failed to fetch session"));
      setData(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Only run on client-side
    if (typeof window !== "undefined") {
      fetchSession();
    }
  }, []);

  return {
    data,
    isLoading,
    error,
    refetch: fetchSession,
  };
}
