/**
 * React hook for session monitoring with Better Auth integration
 */

import { useEffect, useState, useCallback, useRef } from 'react';
import { SessionMonitor, createSessionMonitor, convertBetterAuthSession } from '../session/sessionMonitor';
import type {
  SessionInfo,
  SessionMonitorState,
  SessionWarningEvent,
  SessionEventListener,
  SessionMonitorConfig
} from '../session/types';

interface UseSessionMonitorOptions {
  /** Configuration for session monitoring */
  config?: Partial<SessionMonitorConfig>;
  /** Callback for session warning events */
  onWarning?: (event: SessionWarningEvent) => void;
  /** Callback for session expired events */
  onExpired?: (event: SessionWarningEvent) => void;
  /** Callback for session extended events */
  onExtended?: (event: SessionWarningEvent) => void;
  /** Callback for session error events */
  onError?: (event: SessionWarningEvent) => void;
}

/**
 * Hook for managing session monitoring with React state integration
 */
export function useSessionMonitor(options: UseSessionMonitorOptions = {}) {
  const {
    config,
    onWarning,
    onExpired,
    onExtended,
    onError,
  } = options;

  // Session monitor instance (stable reference)
  const monitorRef = useRef<SessionMonitor | null>(null);
  
  // Session monitor state
  const [state, setState] = useState<SessionMonitorState>({
    session: null,
    isMonitoring: false,
    isWarningShown: false,
    lastWarningThreshold: null,
    isExtending: false,
    error: null,
  });

  // Initialize session monitor (only once)
  const isInitializedRef = useRef(false);

  useEffect(() => {
    if (!isInitializedRef.current && !monitorRef.current) {
      console.log('[useSessionMonitor] 🏗️ Initializing session monitor (one-time)');
      monitorRef.current = createSessionMonitor(config);
      isInitializedRef.current = true;
    }

    // Only destroy on component unmount
    return () => {
      // Don't destroy on every re-render, only on actual unmount
      // We'll handle this in a separate cleanup effect
    };
  }, []); // Empty dependency array - only initialize once

  // Track previous config to prevent unnecessary updates
  const prevConfigRef = useRef<any>(null);

  // Update config when it changes (but don't recreate monitor)
  useEffect(() => {
    if (monitorRef.current && config) {
      // Only update if config actually changed (deep comparison of key values)
      const configChanged = !prevConfigRef.current ||
        JSON.stringify(config) !== JSON.stringify(prevConfigRef.current);

      if (configChanged) {
        console.log('[useSessionMonitor] ⚙️ Updating monitor config');
        monitorRef.current.updateConfig(config);
        prevConfigRef.current = config;
      } else {
        console.log('[useSessionMonitor] ⏭️ Config unchanged, skipping update');
      }
    }
  }, [config]);

  // Cleanup on unmount only
  useEffect(() => {
    return () => {
      if (monitorRef.current) {
        console.log('[useSessionMonitor] 🧹 Destroying session monitor on component unmount');
        monitorRef.current.destroy();
        monitorRef.current = null;
        isInitializedRef.current = false;
      }
    };
  }, []); // Empty dependency - only run on unmount

  // Set up event listeners
  useEffect(() => {
    const monitor = monitorRef.current;
    if (!monitor) return;

    const handleWarning: SessionEventListener = (event) => {
      setState(prev => ({
        ...prev,
        isWarningShown: true,
        lastWarningThreshold: event.minutesRemaining || null,
      }));
      onWarning?.(event);
    };

    const handleExpired: SessionEventListener = (event) => {
      setState(prev => ({
        ...prev,
        session: null,
        isMonitoring: false,
        isWarningShown: false,
        lastWarningThreshold: null,
      }));
      onExpired?.(event);
    };

    const handleExtended: SessionEventListener = (event) => {
      setState(prev => ({
        ...prev,
        session: event.session,
        isWarningShown: false,
        lastWarningThreshold: null,
        isExtending: false,
      }));
      onExtended?.(event);
    };

    const handleError: SessionEventListener = (event) => {
      setState(prev => ({
        ...prev,
        error: 'Session monitoring error',
        isExtending: false,
      }));
      onError?.(event);
    };

    // Add event listeners
    monitor.addEventListener('session-warning', handleWarning);
    monitor.addEventListener('session-expired', handleExpired);
    monitor.addEventListener('session-extended', handleExtended);
    monitor.addEventListener('session-error', handleError);

    // Cleanup event listeners
    return () => {
      monitor.removeEventListener('session-warning', handleWarning);
      monitor.removeEventListener('session-expired', handleExpired);
      monitor.removeEventListener('session-extended', handleExtended);
      monitor.removeEventListener('session-error', handleError);
    };
  }, [onWarning, onExpired, onExtended, onError]);

  /**
   * Start monitoring a session
   */
  const startMonitoring = useCallback((sessionData: any) => {
    const monitor = monitorRef.current;
    if (!monitor) {
      console.error('[useSessionMonitor] Monitor not initialized');
      return;
    }

    console.log('[useSessionMonitor] 🚀 Starting session monitoring with data:', sessionData);

    // Convert Better Auth session to SessionInfo
    const sessionInfo = convertBetterAuthSession(sessionData);
    if (!sessionInfo) {
      console.error('[useSessionMonitor] ❌ Failed to convert session data');
      setState(prev => ({ ...prev, error: 'Invalid session data' }));
      return;
    }

    // Check if we're already monitoring this session
    if (state.session?.sessionId === sessionInfo.sessionId && state.isMonitoring) {
      console.log('[useSessionMonitor] ⏭️ Already monitoring this session, skipping');
      return;
    }

    console.log('[useSessionMonitor] ✅ Session conversion successful, starting monitor');
    monitor.startMonitoring(sessionInfo);
    setState(prev => ({
      ...prev,
      session: sessionInfo,
      isMonitoring: true,
      error: null,
      isWarningShown: false,
      lastWarningThreshold: null,
    }));
  }, [state.session?.sessionId, state.isMonitoring]);

  /**
   * Stop session monitoring
   */
  const stopMonitoring = useCallback(() => {
    const monitor = monitorRef.current;
    if (!monitor) {
      console.log('[useSessionMonitor] ⏭️ Monitor not initialized, nothing to stop');
      return;
    }

    if (!state.isMonitoring) {
      console.log('[useSessionMonitor] ⏭️ Not currently monitoring, nothing to stop');
      return;
    }

    console.log('[useSessionMonitor] 🛑 Stopping session monitoring');
    monitor.stopMonitoring();
    setState(prev => ({
      ...prev,
      session: null,
      isMonitoring: false,
      isWarningShown: false,
      lastWarningThreshold: null,
      error: null,
    }));
  }, [state.isMonitoring]);

  /**
   * Manually check session status
   */
  const checkSession = useCallback(async () => {
    const monitor = monitorRef.current;
    if (!monitor) return;

    try {
      const currentSession = monitor.getCurrentSession();
      if (currentSession) {
        const isValid = monitor.isSessionValid();
        if (!isValid) {
          // Session has expired
          setState(prev => ({
            ...prev,
            session: null,
            isMonitoring: false,
            isWarningShown: false,
            lastWarningThreshold: null,
          }));
        }
      }
    } catch (error) {
      setState(prev => ({ ...prev, error: 'Failed to check session' }));
    }
  }, []);

  /**
   * Extend current session
   */
  const extendSession = useCallback(async (): Promise<boolean> => {
    const monitor = monitorRef.current;
    if (!monitor || !state.session) {
      return false;
    }

    setState(prev => ({ ...prev, isExtending: true, error: null }));

    try {
      // Make API call to extend session
      const response = await fetch('/api/session/extend', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId: state.session.sessionId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to extend session');
      }

      const result = await response.json();
      
      if (result.success && result.newExpiresAt) {
        // Update session with new expiration time
        const updatedSession: SessionInfo = {
          ...state.session,
          expiresAt: result.newExpiresAt,
          lastActivity: Date.now(),
        };

        monitor.updateSession(updatedSession);
        return true;
      } else {
        throw new Error(result.error || 'Session extension failed');
      }
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to extend session',
        isExtending: false,
      }));
      return false;
    }
  }, [state.session]);

  /**
   * Dismiss current warning
   */
  const dismissWarning = useCallback(() => {
    setState(prev => ({
      ...prev,
      isWarningShown: false,
    }));
  }, []);

  /**
   * Get time remaining until session expiry
   */
  const getTimeRemaining = useCallback(() => {
    const monitor = monitorRef.current;
    return monitor ? monitor.getTimeRemaining() : 0;
  }, []);

  /**
   * Get minutes remaining until session expiry
   */
  const getMinutesRemaining = useCallback(() => {
    const monitor = monitorRef.current;
    return monitor ? monitor.getMinutesRemaining() : 0;
  }, []);

  /**
   * Check if session is currently valid
   */
  const isSessionValid = useCallback(() => {
    const monitor = monitorRef.current;
    return monitor ? monitor.isSessionValid() : false;
  }, []);

  return {
    state,
    startMonitoring,
    stopMonitoring,
    checkSession,
    extendSession,
    dismissWarning,
    getTimeRemaining,
    getMinutesRemaining,
    isSessionValid,
  };
}
