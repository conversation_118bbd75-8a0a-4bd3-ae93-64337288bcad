import { useState, useEffect } from 'react'
import { useAuth } from '~/lib/providers/AuthProvider'
import { useCurrentBusiness } from '~/lib/stores/businessStore'
import { RBACServiceClient } from '~/lib/services/rbacService.client'

export interface BusinessPermissions {
  canCreateBusiness: boolean
  canManageBusiness: boolean
  canEditBusiness: boolean
  canDeleteBusiness: boolean
  isBusinessOwner: boolean
  loading: boolean
}

export function useBusinessPermissions(): BusinessPermissions {
  const { user } = useAuth()
  const currentBusiness = useCurrentBusiness()
  const [permissions, setPermissions] = useState<BusinessPermissions>({
    canCreateBusiness: false,
    canManageBusiness: false,
    canEditBusiness: false,
    canDeleteBusiness: false,
    isBusinessOwner: false,
    loading: true,
  })

  useEffect(() => {
    async function checkPermissions() {
      if (!user?.id) {
        setPermissions({
          canCreateBusiness: false,
          canManageBusiness: false,
          canEditBusiness: false,
          canDeleteBusiness: false,
          isBusinessOwner: false,
          loading: false,
        })
        return
      }

      try {
        // Check if user is business owner for current business
        const isOwner = currentBusiness ? 
          await RBACServiceClient.isBusinessOwner(user.id, currentBusiness.id) : false

        // Check business management permissions
        const canManage = currentBusiness ? 
          await RBACServiceClient.hasPermission(user.id, 'business.manage', currentBusiness.id) : false
        
        const canEdit = currentBusiness ? 
          await RBACServiceClient.hasPermission(user.id, 'business.update', currentBusiness.id) : false
        
        const canDelete = currentBusiness ? 
          await RBACServiceClient.hasPermission(user.id, 'business.delete', currentBusiness.id) : false

        // Check if user can create new businesses (global permission or business owner)
        const canCreate = await RBACServiceClient.hasPermission(user.id, 'business.create') || isOwner

        setPermissions({
          canCreateBusiness: canCreate,
          canManageBusiness: canManage || isOwner,
          canEditBusiness: canEdit || isOwner,
          canDeleteBusiness: canDelete || isOwner,
          isBusinessOwner: isOwner,
          loading: false,
        })
      } catch (error) {
        console.error('Error checking business permissions:', error)
        setPermissions({
          canCreateBusiness: false,
          canManageBusiness: false,
          canEditBusiness: false,
          canDeleteBusiness: false,
          isBusinessOwner: false,
          loading: false,
        })
      }
    }

    checkPermissions()
  }, [user?.id, currentBusiness?.id])

  return permissions
}