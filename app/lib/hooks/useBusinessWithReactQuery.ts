import { useEffect } from 'react'
import { useLocation } from '@remix-run/react'
import { useAuth } from '~/lib/providers/AuthProvider'
import { useBusinessStore } from '~/lib/stores/businessStore'
import { useBusinesses } from '~/lib/query/hooks'
import { useQueryClient } from '@tanstack/react-query'
import { BusinessCacheUtils } from '~/lib/query/utils/cacheUtils'
import { extractBusinessIdFromRoute } from './useBusinessNavigation'

/**
 * Hook that integrates React Query with business store and authentication
 * Automatically fetches businesses when user is authenticated
 * and clears cache when user logs out
 */
export function useBusinessWithReactQuery() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth()
  const location = useLocation()
  const queryClient = useQueryClient()
  
  // Get business store methods for UI state (current business selection)
  const {
    isInitialized,
    isLoading: storeLoading,
    currentBusiness,
    setCurrentBusiness,
    clearBusinessData,
    addBusiness,
    updateBusiness,
    removeBusiness,
  } = useBusinessStore()

  // Use React Query for data fetching
  const {
    data: businesses = [],
    isLoading: queryLoading,
    isError,
  } = useBusinesses(user?.id, {
    // Only fetch when authenticated
    enabled: isAuthenticated && !!user?.id,
  })

  // Note: Store initialization happens automatically via onRehydrateStorage in businessStore.ts
  // No manual initialization needed here

  // Clear business data and query cache when user logs out
  // Use a ref to track previous authentication state to avoid clearing on initial load
  useEffect(() => {
    // Only clear if we were previously authenticated and now we're not
    // This prevents clearing on initial page load when auth is still loading
    if (!isAuthenticated && !authLoading && user === null) {
      console.log('Clearing business store and query cache - user logged out')
      clearBusinessData()

      // Clear all query cache related to businesses
      BusinessCacheUtils.clearBusinessCache(queryClient)
    }
  }, [isAuthenticated, authLoading, user, clearBusinessData, queryClient])

  // Sync businesses from React Query to Zustand store
  useEffect(() => {
    if (businesses.length > 0) {
      console.log('useBusinessWithReactQuery: Syncing businesses to store')
      
      // Update the businesses in the store
      useBusinessStore.setState({ businesses })
    }
  }, [businesses])

  // Handle business selection logic separately to avoid race conditions
  useEffect(() => {
    // Wait for store hydration AND businesses to be available
    if (!isInitialized || businesses.length === 0) {
      console.log('useBusinessWithReactQuery: Waiting for store hydration and businesses. Initialized:', isInitialized, 'Businesses count:', businesses.length)
      return
    }

    console.log('useBusinessWithReactQuery: Business selection logic - current business:', currentBusiness?.name)
    
    // If we have a current business from hydration, verify it still exists in the fetched businesses list
    if (currentBusiness) {
      const businessStillExists = businesses.find(b => b.id === currentBusiness.id)
      if (businessStillExists) {
        // Business still exists, update with fresh data only if different
        if (JSON.stringify(currentBusiness) !== JSON.stringify(businessStillExists)) {
          console.log('useBusinessWithReactQuery: Updating current business with fresh data:', businessStillExists.name)
          setCurrentBusiness(businessStillExists)
        } else {
          console.log('useBusinessWithReactQuery: Current business is up to date:', currentBusiness.name)
        }
        // Important: return early to avoid falling through to other selection logic
        return
      } else {
        // Current business no longer exists, need to select a new one
        console.log('useBusinessWithReactQuery: Current business no longer exists, will select new business')
      }
    }
    
    // Try to get business from URL first
    const urlBusinessId = extractBusinessIdFromRoute(location.pathname)
    if (urlBusinessId) {
      const businessFromUrl = businesses.find(b => b.id === urlBusinessId)
      if (businessFromUrl) {
        console.log('useBusinessWithReactQuery: Setting business from URL:', businessFromUrl.name)
        setCurrentBusiness(businessFromUrl)
        return
      }
    }
    
    // Only set default business if we truly have no business selected after hydration
    if (!currentBusiness && businesses.length > 0) {
      console.log('useBusinessWithReactQuery: No business selected after hydration, defaulting to first business:', businesses[0]?.name)
      setCurrentBusiness(businesses[0])
    }
  }, [businesses, isInitialized, currentBusiness, setCurrentBusiness, location.pathname])

  // Combine loading states
  const isLoading = authLoading || queryLoading || storeLoading

  return {
    user,
    isAuthenticated,
    isAuthLoading: authLoading,
    isBusinessLoading: isLoading,
    businesses,
    currentBusiness,
    isReady: isAuthenticated && isInitialized && !isLoading && !isError,
    // Expose store methods for backward compatibility
    setCurrentBusiness,
    addBusiness,
    updateBusiness,
    removeBusiness,
  }
}
