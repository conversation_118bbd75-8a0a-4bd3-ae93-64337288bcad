import { useLoaderData } from '@remix-run/react';
import { useMemo } from 'react';
import type { ReactNode } from 'react';
import type { RBACData } from '~/lib/utils/rbacLoader.server';

/**
 * Hook to check user permissions and roles in React components
 * Requires loader data to include RBAC information
 */
export function useRBAC() {
  const data = useLoaderData<{ rbac?: RBACData }>();
  const rbac = data?.rbac;
  console.log("rbac", rbac)

  const permissions = rbac?.permissions || [];
  const roles = rbac?.roles || [];
  const userId = rbac?.userId;
  const businessId = rbac?.businessId;

  const hasPermission = useMemo(
    () => (permission: string) => permissions.includes(permission),
    [permissions]
  );

  const hasAnyPermission = useMemo(
    () => (permissionList: string[]) => 
      permissionList.some(permission => permissions.includes(permission)),
    [permissions]
  );

  const hasAllPermissions = useMemo(
    () => (permissionList: string[]) => 
      permissionList.every(permission => permissions.includes(permission)),
    [permissions]
  );

  const hasRole = useMemo(
    () => (role: string) => roles.includes(role),
    [roles]
  );

  const hasAnyRole = useMemo(
    () => (roleList: string[]) => 
      roleList.some(role => roles.includes(role)),
    [roles]
  );

  const hasAllRoles = useMemo(
    () => (roleList: string[]) => 
      roleList.every(role => roles.includes(role)),
    [roles]
  );

  // Convenience methods for common permission checks
  const canRead = useMemo(
    () => (resource: string) => hasPermission(`${resource}.read`),
    [hasPermission]
  );

  const canCreate = useMemo(
    () => (resource: string) => hasPermission(`${resource}.create`),
    [hasPermission]
  );

  const canUpdate = useMemo(
    () => (resource: string) => hasPermission(`${resource}.update`),
    [hasPermission]
  );

  const canDelete = useMemo(
    () => (resource: string) => hasPermission(`${resource}.delete`),
    [hasPermission]
  );

  // Business-specific checks
  const isBusinessOwner = useMemo(
    () => hasRole('business_owner'),
    [hasRole]
  );

  const isSuperAdmin = useMemo(
    () => hasRole('super_admin'),
    [hasRole]
  );

  const isBusinessManager = useMemo(
    () => hasRole('business_manager'),
    [hasRole]
  );

  const isInventoryManager = useMemo(
    () => hasRole('inventory_manager'),
    [hasRole]
  );

  const isStaff = useMemo(
    () => hasRole('staff'),
    [hasRole]
  );

  const isViewer = useMemo(
    () => hasRole('viewer'),
    [hasRole]
  );

  // Resource-specific permission checks
  const inventory = useMemo(
    () => ({
      canRead: canRead('inventory'),
      canCreate: canCreate('inventory'),
      canUpdate: canUpdate('inventory'),
      canDelete: canDelete('inventory'),
    }),
    [canRead, canCreate, canUpdate, canDelete]
  );

  const categories = useMemo(
    () => ({
      canRead: canRead('categories'),
      canCreate: canCreate('categories'),
      canUpdate: canUpdate('categories'),
      canDelete: canDelete('categories'),
    }),
    [canRead, canCreate, canUpdate, canDelete]
  );

  const business = useMemo(
    () => ({
      canRead: canRead('business'),
      canCreate: canCreate('business'),
      canUpdate: canUpdate('business'),
      canDelete: canDelete('business'),
      canManageUsers: hasPermission('business.manage_users'),
    }),
    [canRead, canCreate, canUpdate, canDelete, hasPermission]
  );

  const cogs = useMemo(
    () => ({
      canRead: canRead('cogs'),
      canCalculate: hasPermission('cogs.calculate'),
      canUpdate: canUpdate('cogs'),
    }),
    [canRead, canUpdate, hasPermission]
  );

  const users = useMemo(
    () => ({
      canRead: canRead('users'),
      canCreate: canCreate('users'),
      canUpdate: canUpdate('users'),
      canDelete: canDelete('users'),
      canAssignRoles: hasPermission('users.assign_roles'),
    }),
    [canRead, canCreate, canUpdate, canDelete, hasPermission]
  );

  const rolesManagement = useMemo(
    () => ({
      canRead: canRead('roles'),
      canCreate: canCreate('roles'),
      canUpdate: canUpdate('roles'),
      canDelete: canDelete('roles'),
    }),
    [canRead, canCreate, canUpdate, canDelete]
  );

  const ingredients = useMemo(
    () => ({
      canRead: canRead('ingredients'),
      canCreate: canCreate('ingredients'),
      canUpdate: canUpdate('ingredients'),
      canDelete: canDelete('ingredients'),
    }),
    [canRead, canCreate, canUpdate, canDelete]
  );

  const products = useMemo(
    () => ({
      canRead: canRead('products'),
      canCreate: canCreate('products'),
      canUpdate: canUpdate('products'),
      canDelete: canDelete('products'),
    }),
    [canRead, canCreate, canUpdate, canDelete]
  );

  return {
    // Raw data
    permissions,
    roles,
    userId,
    businessId,

    // Permission checks
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,

    // Role checks
    hasRole,
    hasAnyRole,
    hasAllRoles,

    // Convenience methods
    canRead,
    canCreate,
    canUpdate,
    canDelete,

    // Role shortcuts
    isBusinessOwner,
    isSuperAdmin,
    isBusinessManager,
    isInventoryManager,
    isStaff,
    isViewer,

    // Resource-specific permissions
    inventory,
    categories,
    business,
    cogs,
    users,
    rolesManagement,
    ingredients,
    products,
  };
}

/**
 * Component wrapper for conditional rendering based on permissions
 */
export interface ProtectedProps {
  children: ReactNode;
  permissions?: string[];
  roles?: string[];
  requireAll?: boolean;
  fallback?: ReactNode;
}

export function Protected({
  children,
  permissions = [],
  roles = [],
  requireAll = false,
  fallback = null,
}: ProtectedProps): ReactNode {
  const rbac = useRBAC();

  let hasAccess = true;

  // Check permissions
  if (permissions.length > 0) {
    hasAccess = requireAll
      ? rbac.hasAllPermissions(permissions)
      : rbac.hasAnyPermission(permissions);
  }

  // Check roles (only if permissions check passed)
  if (hasAccess && roles.length > 0) {
    hasAccess = requireAll
      ? rbac.hasAllRoles(roles)
      : rbac.hasAnyRole(roles);
  }

  return hasAccess ? children : fallback;
}