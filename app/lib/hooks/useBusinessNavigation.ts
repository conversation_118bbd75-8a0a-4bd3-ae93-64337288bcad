import { useEffect, useRef } from 'react';
import { useLocation } from '@remix-run/react';
import { useCurrentBusiness, useIsBusinessSwitching } from '~/lib/stores/businessStore';

/**
 * Hook that handles navigation when business context changes
 * Automatically redirects users to the correct business-specific URL
 * when they switch businesses while on a business-specific route
 */
export function useBusinessNavigation() {
  const location = useLocation();
  const currentBusiness = useCurrentBusiness();
  const isBusinessSwitching = useIsBusinessSwitching();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastRedirectRef = useRef<string | null>(null);

  useEffect(() => {
    // Only run in browser environment
    if (typeof window === 'undefined') return;
    
    // Don't redirect if business switching is in progress
    if (isBusinessSwitching) return;
    
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    // Debounce the navigation check to prevent race conditions
    timeoutRef.current = setTimeout(() => {
      // Check if current path contains a business ID pattern
      const businessRouteMatch = location.pathname.match(/^\/business\/([a-f0-9-]+)(\/.*)?$/);

      if (businessRouteMatch && currentBusiness) {
        const [, urlBusinessId, remainingPath] = businessRouteMatch;

        // If the URL business ID doesn't match the current business, redirect
        if (urlBusinessId !== currentBusiness.id) {
          const newPath = `/business/${currentBusiness.id}${remainingPath || ''}`;

          // Preserve query parameters if any
          const searchParams = location.search;
          const fullNewPath = newPath + searchParams;

          // Prevent infinite redirect loops
          if (lastRedirectRef.current === fullNewPath) {
            console.log('Preventing redirect loop to:', fullNewPath);
            return;
          }

          lastRedirectRef.current = fullNewPath;
          console.log(`Business context changed: redirecting from ${location.pathname} to ${fullNewPath}`);

          // Use window.location.href for navigation to ensure proper page reload
          // This ensures all server-side loaders run with the correct business context
          window.location.href = fullNewPath;
        }
      } else if (currentBusiness) {
        // Handle global routes that should include business context
        const globalRoutesWithBusinessContext = [
          '/inventory/ingredients',
          '/inventory/products',
          '/cogs',
          '/categories'
        ];

        const isGlobalRouteWithBusinessContext = globalRoutesWithBusinessContext.some(route =>
          location.pathname === route
        );

        if (isGlobalRouteWithBusinessContext) {
          const url = new URL(window.location.href);
          const currentBusinessIdInUrl = url.searchParams.get('businessId');

          // If the business ID in URL doesn't match current business, update it
          if (currentBusinessIdInUrl !== currentBusiness.id) {
            url.searchParams.set('businessId', currentBusiness.id);
            const newUrl = url.toString();

            // Prevent infinite redirect loops
            if (lastRedirectRef.current === newUrl) {
              console.log('Preventing redirect loop to:', newUrl);
              return;
            }

            lastRedirectRef.current = newUrl;
            console.log(`Business context changed: updating URL from ${window.location.href} to ${newUrl}`);

            // Use window.location.href for navigation to ensure proper page reload
            window.location.href = newUrl;
          }
        }
      }
    }, 200); // Increased debounce to 200ms
    
    // Cleanup timeout on unmount
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [location.pathname, location.search, currentBusiness, isBusinessSwitching]);
}

/**
 * Utility function to check if a path is business-specific
 */
export function isBusinessSpecificRoute(pathname: string): boolean {
  return /^\/business\/[a-f0-9-]+/.test(pathname);
}

/**
 * Utility function to extract business ID from a business-specific route
 */
export function extractBusinessIdFromRoute(pathname: string): string | null {
  const match = pathname.match(/^\/business\/([a-f0-9-]+)/);
  return match ? match[1] : null;
}

/**
 * Utility function to build a business-specific route
 */
export function buildBusinessRoute(businessId: string, subPath: string = ''): string {
  const cleanSubPath = subPath.startsWith('/') ? subPath : `/${subPath}`;
  return `/business/${businessId}${cleanSubPath}`;
}