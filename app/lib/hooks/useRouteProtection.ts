import { useEffect, useRef } from 'react';
import { useNavigate, useLocation } from '@remix-run/react';
import { useRB<PERSON> } from './useRBAC';

export interface RouteProtectionOptions {
  /** Required permissions (user needs ANY of these) */
  permissions?: string[];
  /** Required roles (user needs ANY of these) */
  roles?: string[];
  /** If true, user must have ALL permissions/roles. If false, user needs ANY. */
  requireAll?: boolean;
  /** Custom redirect path (defaults to /unauthorized) */
  redirectTo?: string;
  /** Whether to preserve the original URL for post-auth redirect */
  preserveUrl?: boolean;
}

/**
 * Hook to protect routes on the client-side by checking permissions
 * and redirecting unauthorized users. This handles cases where client-side
 * navigation bypasses server-side loader authorization checks.
 * 
 * @param options - Protection configuration
 * 
 * @example
 * ```tsx
 * function COGSPage() {
 *   useRouteProtection({
 *     permissions: ['cogs.read'],
 *     preserveUrl: true
 *   });
 *   
 *   // Component content...
 * }
 * ```
 */
export function useRouteProtection(options: RouteProtectionOptions = {}) {
  const {
    permissions = [],
    roles = [],
    requireAll = false,
    redirectTo = '/unauthorized',
    preserveUrl = true,
  } = options;

  const rbac = useRBAC();
  const navigate = useNavigate();
  const location = useLocation();
  const hasRedirected = useRef(false);

  useEffect(() => {
    // Skip check if no permissions or roles are required
    if (permissions.length === 0 && roles.length === 0) {
      return;
    }

    // Skip check if we're already on the unauthorized page to prevent infinite loops
    if (location.pathname === '/unauthorized' || location.pathname === '/login') {
      return;
    }

    // Prevent multiple redirects
    if (hasRedirected.current) {
      return;
    }

    // Skip check if RBAC data is not available yet (loading state)
    // Only redirect if we have RBAC data but lack permissions
    if (rbac.permissions === undefined || rbac.permissions === null) {
      return; // Still loading, don't redirect yet
    }

    let hasAccess = true;

    // Check permissions
    if (permissions.length > 0) {
      hasAccess = requireAll
        ? rbac.hasAllPermissions(permissions)
        : rbac.hasAnyPermission(permissions);
    }

    // Check roles (only if permissions check passed)
    if (hasAccess && roles.length > 0) {
      hasAccess = requireAll
        ? rbac.hasAllRoles(roles)
        : rbac.hasAnyRole(roles);
    }

    // Redirect if user doesn't have access
    if (!hasAccess) {
      hasRedirected.current = true;
      let redirectUrl = redirectTo;

      // Preserve original URL for post-auth redirect
      if (preserveUrl) {
        const originalUrl = location.pathname + location.search;
        const separator = redirectTo.includes('?') ? '&' : '?';
        redirectUrl = `${redirectTo}${separator}redirectTo=${encodeURIComponent(originalUrl)}`;
      }

      console.log(`[Route Protection] Redirecting unauthorized user from ${location.pathname} to ${redirectUrl}`);
      navigate(redirectUrl, { replace: true });
    }
  }, [
    permissions,
    roles,
    requireAll,
    redirectTo,
    preserveUrl,
    rbac.permissions,
    rbac.roles,
    rbac.hasAllPermissions,
    rbac.hasAnyPermission,
    rbac.hasAllRoles,
    rbac.hasAnyRole,
    navigate,
    location.pathname,
    location.search,
  ]);

  // Return permission status for optional use by components
  const isLoading = rbac.permissions === undefined || rbac.permissions === null;

  return {
    hasAccess: isLoading
      ? false // Don't grant access while loading
      : permissions.length === 0 && roles.length === 0
        ? true
        : (permissions.length > 0
            ? (requireAll ? rbac.hasAllPermissions(permissions) : rbac.hasAnyPermission(permissions))
            : true) &&
          (roles.length > 0
            ? (requireAll ? rbac.hasAllRoles(roles) : rbac.hasAnyRole(roles))
            : true),
    isLoading,
    permissions: rbac.permissions || [],
    roles: rbac.roles || [],
  };
}

/**
 * Convenience hook for protecting routes that require specific permissions
 * 
 * @param permissions - Required permissions (user needs ANY of these)
 * @param requireAll - If true, user must have ALL permissions
 * 
 * @example
 * ```tsx
 * function COGSPage() {
 *   usePermissionProtection(['cogs.read']);
 *   // Component content...
 * }
 * ```
 */
export function usePermissionProtection(
  permissions: string[],
  requireAll: boolean = false
) {
  return useRouteProtection({
    permissions,
    requireAll,
    preserveUrl: true,
  });
}

/**
 * Convenience hook for protecting routes that require specific roles
 * 
 * @param roles - Required roles (user needs ANY of these)
 * @param requireAll - If true, user must have ALL roles
 * 
 * @example
 * ```tsx
 * function AdminPage() {
 *   useRoleProtection(['business_owner', 'super_admin']);
 *   // Component content...
 * }
 * ```
 */
export function useRoleProtection(
  roles: string[],
  requireAll: boolean = false
) {
  return useRouteProtection({
    roles,
    requireAll,
    preserveUrl: true,
  });
}
