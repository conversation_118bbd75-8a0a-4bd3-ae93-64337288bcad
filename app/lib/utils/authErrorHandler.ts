import type { TFunction } from 'react-i18next';

/**
 * Better Auth error types and HTTP status codes
 */
export interface AuthError {
  message: string;
  status?: number;
  statusText?: string;
  code?: string;
  retryAfter?: number;
}

/**
 * Error context for different authentication operations
 */
export type AuthErrorContext = 'login' | 'register' | 'general';

/**
 * Maps Better Auth errors to user-friendly, internationalized messages
 * Follows security best practices:
 * - Generic messages for login errors (don't reveal if email exists)
 * - Specific messages for registration errors (help user fix issues)
 * - Clear guidance for network/server errors
 */
export function mapAuthError(
  error: AuthError,
  context: AuthErrorContext,
  t: TFunction
): string {
  // Handle network and connection errors first
  if (isNetworkError(error)) {
    return t('auth:errors.general.networkError');
  }

  // Handle server errors
  if (isServerError(error)) {
    return t('auth:errors.general.serverError');
  }

  // Handle timeout errors
  if (isTimeoutError(error)) {
    return t('auth:errors.general.timeout');
  }

  // Handle rate limiting with specific messages
  if (isRateLimitError(error)) {
    const retryAfter = error.retryAfter || 60;

    switch (context) {
      case 'login':
        return t('auth:errors.rateLimit.signIn', { retryAfter });
      case 'register':
        return t('auth:errors.rateLimit.signUp', { retryAfter });
      default:
        return t('auth:errors.rateLimit.general', { retryAfter });
    }
  }

  // Context-specific error handling
  switch (context) {
    case 'login':
      return mapLoginError(error, t);
    case 'register':
      return mapRegistrationError(error, t);
    default:
      return mapGeneralError(error, t);
  }
}

/**
 * Maps login errors with security-conscious messaging
 * Always returns generic "invalid credentials" message to prevent user enumeration
 */
function mapLoginError(error: AuthError, t: TFunction): string {
  const { status, message, code } = error;

  // Email verification required
  if (status === 403 || message?.toLowerCase().includes('verify') || message?.toLowerCase().includes('verification')) {
    return t('auth:forms.login.messages.emailNotVerified');
  }

  // Account locked/disabled
  if (status === 423 || message?.toLowerCase().includes('locked') || message?.toLowerCase().includes('disabled')) {
    return t('auth:forms.login.messages.accountLocked');
  }

  // Session expired
  if (status === 401 && message?.toLowerCase().includes('session')) {
    return t('auth:forms.login.messages.sessionExpired');
  }

  // For all authentication failures, return generic message for security
  // This includes: wrong password, user not found, invalid email, etc.
  if (status === 401 || status === 400 || 
      message?.toLowerCase().includes('invalid') ||
      message?.toLowerCase().includes('incorrect') ||
      message?.toLowerCase().includes('not found') ||
      message?.toLowerCase().includes('wrong') ||
      code === 'INVALID_CREDENTIALS' ||
      code === 'USER_NOT_FOUND') {
    return t('auth:forms.login.messages.invalidCredentials');
  }

  // Default login error
  return t('auth:forms.login.messages.defaultError');
}

/**
 * Maps registration errors with specific, helpful messaging
 * Can be more specific since we're not revealing existing user information
 */
function mapRegistrationError(error: AuthError, t: TFunction): string {
  const { status, message, code } = error;

  // Email already exists
  if (status === 409 || 
      message?.toLowerCase().includes('already exists') ||
      message?.toLowerCase().includes('duplicate') ||
      code === 'USER_ALREADY_EXISTS' ||
      code === 'EMAIL_EXISTS') {
    return t('auth:forms.register.messages.emailAlreadyExists');
  }

  // Password validation errors
  if (message?.toLowerCase().includes('password')) {
    if (message?.toLowerCase().includes('weak') || 
        message?.toLowerCase().includes('strength') ||
        code === 'WEAK_PASSWORD') {
      return t('auth:forms.register.messages.weakPassword');
    }
    
    if (message?.toLowerCase().includes('short') || 
        message?.toLowerCase().includes('length') ||
        message?.toLowerCase().includes('minimum')) {
      return t('auth:forms.register.messages.passwordTooShort');
    }
  }

  // Email validation errors
  if (status === 400 && (
      message?.toLowerCase().includes('email') ||
      message?.toLowerCase().includes('invalid') ||
      code === 'INVALID_EMAIL')) {
    return t('auth:forms.register.messages.invalidEmail');
  }

  // General validation errors
  if (status === 400) {
    return t('auth:errors.registration.invalidData');
  }

  // Default registration error
  return t('auth:forms.register.messages.defaultError');
}

/**
 * Maps general authentication errors
 */
function mapGeneralError(error: AuthError, t: TFunction): string {
  const { status, message } = error;

  if (status === 401) {
    return t('auth:errors.authentication.invalidCredentials');
  }

  if (status === 403) {
    return t('auth:errors.authentication.emailNotVerified');
  }

  if (status === 423) {
    return t('auth:errors.authentication.accountLocked');
  }

  return t('auth:errors.general.unknownError');
}

/**
 * Helper functions to identify error types
 */
function isNetworkError(error: AuthError): boolean {
  return !error.status || 
         error.message?.toLowerCase().includes('network') ||
         error.message?.toLowerCase().includes('connection') ||
         error.message?.toLowerCase().includes('fetch');
}

function isServerError(error: AuthError): boolean {
  return (error.status && error.status >= 500) ||
         error.message?.toLowerCase().includes('server error') ||
         error.message?.toLowerCase().includes('internal error');
}

function isTimeoutError(error: AuthError): boolean {
  return error.message?.toLowerCase().includes('timeout') ||
         error.message?.toLowerCase().includes('timed out');
}

function isRateLimitError(error: AuthError): boolean {
  return error.status === 429 ||
         error.code === 'RATE_LIMITED' ||
         error.message?.toLowerCase().includes('rate limit') ||
         error.message?.toLowerCase().includes('too many') ||
         error.message?.toLowerCase().includes('throttle');
}

/**
 * Utility function to create AuthError from various error types
 */
export function createAuthError(error: any): AuthError {
  if (typeof error === 'string') {
    return { message: error };
  }

  if (error && typeof error === 'object') {
    return {
      message: error.message || error.error || 'Unknown error',
      status: error.status || error.statusCode,
      statusText: error.statusText,
      code: error.code || error.errorCode,
      retryAfter: error.retryAfter
    };
  }

  return { message: 'Unknown error occurred' };
}

/**
 * Enhanced error handler that provides consistent error handling across auth forms
 */
export function handleAuthError(
  error: any,
  context: AuthErrorContext,
  t: TFunction
): string {
  const authError = createAuthError(error);
  return mapAuthError(authError, context, t);
}
