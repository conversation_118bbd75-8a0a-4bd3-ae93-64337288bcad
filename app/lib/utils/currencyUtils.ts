export const DEFAULT_CURRENCY = 'IDR'

export interface CurrencyOption {
  value: string
  label: string
  symbol: string
}

export const CURRENCY_OPTIONS: CurrencyOption[] = [
  { value: 'IDR', label: 'Indonesian Rupiah (IDR)', symbol: 'Rp' },
  { value: 'USD', label: 'US Dollar (USD)', symbol: '$' },
  { value: 'EUR', label: 'Euro (EUR)', symbol: '€' },
  { value: 'GBP', label: 'British Pound (GBP)', symbol: '£' },
  { value: 'JPY', label: 'Japanese Yen (JPY)', symbol: '¥' },
  { value: 'SGD', label: 'Singapore Dollar (SGD)', symbol: 'S$' },
  { value: 'MYR', label: 'Malaysian Ringgit (MYR)', symbol: 'RM' },
  { value: 'THB', label: 'Thai Baht (THB)', symbol: '฿' },
  { value: 'PHP', label: 'Philippine Peso (PHP)', symbol: '₱' },
  { value: 'VND', label: 'Vietnamese Dong (VND)', symbol: '₫' },
]

export function getCurrencyOptions(): CurrencyOption[] {
  return CURRENCY_OPTIONS
}

export function getCurrencySymbol(currencyCode: string): string {
  const currency = CURRENCY_OPTIONS.find(c => c.value === currencyCode)
  return currency?.symbol || currencyCode
}

export function formatCurrency(amount: number, currencyCode: string): string {
  const symbol = getCurrencySymbol(currencyCode)
  
  // Format based on currency
  switch (currencyCode) {
    case 'IDR':
      return `${symbol} ${amount.toLocaleString('id-ID')}`
    case 'USD':
    case 'SGD':
      return `${symbol}${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
    case 'EUR':
      return `${symbol}${amount.toLocaleString('de-DE', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
    case 'GBP':
      return `${symbol}${amount.toLocaleString('en-GB', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
    case 'JPY':
      return `${symbol}${amount.toLocaleString('ja-JP')}`
    case 'MYR':
      return `${symbol} ${amount.toLocaleString('en-MY', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
    case 'THB':
      return `${symbol}${amount.toLocaleString('th-TH', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
    case 'PHP':
      return `${symbol}${amount.toLocaleString('en-PH', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
    case 'VND':
      return `${symbol}${amount.toLocaleString('vi-VN')}`
    default:
      return `${symbol}${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
  }
}
