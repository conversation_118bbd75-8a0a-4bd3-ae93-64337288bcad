import { Eye, Plus, Edit, Trash2, Shield, DollarSign, Package, Tag } from 'lucide-react';
import type { PermissionInfo } from '~/components/inventory/PermissionsSheet';

/**
 * Common permission configurations for different modules
 * These can be reused across different routes and components
 */

export const PERMISSION_CONFIGS = {
  // Ingredients module permissions
  ingredients: {
    routeName: '/inventory/ingredients',
    permissions: [
      {
        name: "ingredients.read",
        purpose: "Required to access the ingredients route and view ingredient data",
        icon: <Eye className="h-4 w-4 text-blue-600" />,
        category: "primary" as const
      },
      {
        name: "categories.read",
        purpose: "Optional permission to view ingredient categories and category-based filtering (graceful degradation if missing)",
        icon: <Eye className="h-4 w-4 text-blue-600" />,
        category: "related" as const
      },
      {
        name: "products.read",
        purpose: "Optional permission to view product usage information in ingredient detail view (graceful degradation if missing)",
        icon: <Package className="h-4 w-4 text-blue-600" />,
        category: "related" as const
      },
      {
        name: "cogs.read",
        purpose: "Optional permission to view cost analysis information in ingredient detail view (graceful degradation if missing)",
        icon: <DollarSign className="h-4 w-4 text-blue-600" />,
        category: "related" as const
      },
      {
        name: "ingredients.create",
        purpose: "Allows creating new ingredients via the 'Add New' button",
        icon: <Plus className="h-4 w-4 text-green-600" />,
        category: "action" as const
      },
      {
        name: "ingredients.update",
        purpose: "Enables editing existing ingredients through the Edit button",
        icon: <Edit className="h-4 w-4 text-orange-600" />,
        category: "action" as const
      },
      {
        name: "ingredients.delete",
        purpose: "Permits deletion of ingredients from the detail view",
        icon: <Trash2 className="h-4 w-4 text-red-600" />,
        category: "action" as const
      },
      {
        name: "system.view_permissions",
        purpose: "Required to view this permissions information section",
        icon: <Shield className="h-4 w-4 text-purple-600" />,
        category: "related" as const
      }
    ] as PermissionInfo[]
  },

  // Products module permissions
  products: {
    routeName: '/inventory/products',
    permissions: [
      {
        name: "products.read",
        purpose: "Required to access the products route and view product data",
        icon: <Eye className="h-4 w-4 text-blue-600" />,
        category: "primary" as const
      },
      {
        name: "categories.read",
        purpose: "Optional permission to view product categories and category-based filtering (graceful degradation if missing)",
        icon: <Tag className="h-4 w-4 text-blue-600" />,
        category: "related" as const
      },
      {
        name: "products.create",
        purpose: "Allows creating new products via the 'Add New' button",
        icon: <Plus className="h-4 w-4 text-green-600" />,
        category: "action" as const
      },
      {
        name: "products.update",
        purpose: "Enables editing existing products through the Edit button and managing product ingredients",
        icon: <Edit className="h-4 w-4 text-orange-600" />,
        category: "action" as const
      },
      {
        name: "products.delete",
        purpose: "Permits deletion of products from the detail view",
        icon: <Trash2 className="h-4 w-4 text-red-600" />,
        category: "action" as const
      },
      {
        name: "ingredients.read",
        purpose: "Required to view ingredient information and recipes in product detail view (shows permission message if missing)",
        icon: <Package className="h-4 w-4 text-blue-600" />,
        category: "related" as const
      },
      {
        name: "cogs.read",
        purpose: "Required to view cost analysis information including Total COGS per Cup and ingredient costs in product detail view (shows permission message if missing)",
        icon: <DollarSign className="h-4 w-4 text-blue-600" />,
        category: "related" as const
      },
      {
        name: "system.view_permissions",
        purpose: "Required to view this permissions information section",
        icon: <Shield className="h-4 w-4 text-purple-600" />,
        category: "related" as const
      }
    ] as PermissionInfo[]
  },

  // Categories module permissions (example for future use)
  categories: {
    routeName: '/categories',
    permissions: [
      {
        name: "categories.read",
        purpose: "Required to access the categories route and view category data",
        icon: <Eye className="h-4 w-4 text-blue-600" />,
        category: "primary" as const
      },
      {
        name: "categories.create",
        purpose: "Allows creating new categories via the 'Add New' button",
        icon: <Plus className="h-4 w-4 text-green-600" />,
        category: "action" as const
      },
      {
        name: "categories.update",
        purpose: "Enables editing existing categories through the Edit button",
        icon: <Edit className="h-4 w-4 text-orange-600" />,
        category: "action" as const
      },
      {
        name: "categories.delete",
        purpose: "Permits deletion of categories from the detail view",
        icon: <Trash2 className="h-4 w-4 text-red-600" />,
        category: "action" as const
      },
      {
        name: "system.view_permissions",
        purpose: "Required to view this permissions information section",
        icon: <Shield className="h-4 w-4 text-purple-600" />,
        category: "related" as const
      }
    ] as PermissionInfo[]
  },

  // COGS module permissions (example for future use)
  cogs: {
    routeName: '/cogs',
    permissions: [
      {
        name: "cogs.read",
        purpose: "Required to access the COGS route and view cost analysis data",
        icon: <Eye className="h-4 w-4 text-blue-600" />,
        category: "primary" as const
      },
      {
        name: "cogs.calculate",
        purpose: "Allows calculating and updating COGS for products",
        icon: <DollarSign className="h-4 w-4 text-green-600" />,
        category: "action" as const
      },
      {
        name: "cogs.update",
        purpose: "Enables manual updates to COGS calculations",
        icon: <Edit className="h-4 w-4 text-orange-600" />,
        category: "action" as const
      },
      {
        name: "products.read",
        purpose: "Required to view product information in COGS calculations",
        icon: <Package className="h-4 w-4 text-blue-600" />,
        category: "related" as const
      },
      {
        name: "ingredients.read",
        purpose: "Required to view ingredient costs in COGS calculations",
        icon: <Tag className="h-4 w-4 text-blue-600" />,
        category: "related" as const
      },
      {
        name: "system.view_permissions",
        purpose: "Required to view this permissions information section",
        icon: <Shield className="h-4 w-4 text-purple-600" />,
        category: "related" as const
      }
    ] as PermissionInfo[]
  }
};

/**
 * Helper function to get permission configuration for a specific module
 */
export function getPermissionConfig(module: keyof typeof PERMISSION_CONFIGS) {
  return PERMISSION_CONFIGS[module];
}

/**
 * Helper function to create custom permission configurations
 */
export function createPermissionConfig(
  routeName: string,
  permissions: PermissionInfo[]
) {
  return {
    routeName,
    permissions
  };
}