/**
 * Comprehensive logout handler for Better Auth with Redis integration
 * 
 * This module provides robust logout functionality that:
 * - Works with Redis secondary storage
 * - Handles rate limiting gracefully
 * - Provides multiple logout strategies
 * - Includes proper error handling and user feedback
 * - Supports internationalization
 * - Ensures security best practices
 */

import { authClient } from "~/lib/auth.client";
import { authToasts, dismissToast } from "./toast";
import { mapAuthError } from "./authErrorHandler";
import type { TFunction } from "react-i18next";

export interface LogoutOptions {
  /** Whether to revoke all sessions (logout from all devices) */
  revokeAllSessions?: boolean;
  /** Whether to show toast notifications */
  showToasts?: boolean;
  /** Custom redirect URL after logout */
  redirectTo?: string;
  /** Whether to force logout even if server request fails */
  forceLogout?: boolean;
  /** Custom success callback */
  onSuccess?: () => void;
  /** Custom error callback */
  onError?: (error: any) => void;
  /** Custom cleanup callback */
  onCleanup?: () => void;
}

export interface LogoutResult {
  success: boolean;
  error?: string;
  wasForced?: boolean;
  sessionsCleaned?: boolean;
}

/**
 * Main logout handler with comprehensive error handling and user feedback
 */
export async function handleLogout(
  t: TFunction,
  options: LogoutOptions = {}
): Promise<LogoutResult> {
  const {
    revokeAllSessions = false,
    showToasts = true,
    redirectTo = "/",
    forceLogout = true,
    onSuccess,
    onError,
    onCleanup,
  } = options;

  let loadingToastId: string | number | undefined;
  let result: LogoutResult = { success: false };

  try {
    // Show loading toast
    if (showToasts) {
      loadingToastId = authToasts.logoutLoading(t);
    }

    // Attempt server-side logout
    try {
      if (revokeAllSessions) {
        // Logout from all devices
        await authClient.revokeSessions();
        result.sessionsCleaned = true;
      } else {
        // Standard logout (current session only)
        await authClient.signOut();
      }

      result.success = true;

      // Dismiss loading toast and show success
      if (showToasts && loadingToastId) {
        dismissToast(loadingToastId);
        if (revokeAllSessions) {
          authToasts.logoutAllSuccess(t);
        } else {
          authToasts.logoutSuccess(t);
        }
      }

    } catch (serverError: any) {
      console.warn('Server logout failed:', serverError);
      
      // Handle specific error types
      const errorMessage = handleLogoutError(serverError, t, showToasts);
      
      if (forceLogout) {
        // Force logout locally even if server request failed
        result.success = true;
        result.wasForced = true;
        
        if (showToasts && loadingToastId) {
          dismissToast(loadingToastId);
          authToasts.logoutSuccess(t, {
            action: {
              label: t('auth:logout.cleanupComplete'),
              onClick: () => {},
            },
          });
        }
      } else {
        // Don't force logout, return error
        result.error = errorMessage;
        
        if (showToasts && loadingToastId) {
          dismissToast(loadingToastId);
          authToasts.logoutError(t, errorMessage);
        }
        
        if (onError) {
          onError(serverError);
        }
        
        return result;
      }
    }

    // Perform local cleanup
    await performLocalCleanup();

    // Call success callback
    if (onSuccess) {
      onSuccess();
    }

    // Redirect user
    await handleRedirect(redirectTo, t, showToasts);

    return result;

  } catch (error: any) {
    console.error('Logout handler error:', error);
    
    // Dismiss loading toast
    if (showToasts && loadingToastId) {
      dismissToast(loadingToastId);
    }

    // Force local logout as fallback
    if (forceLogout) {
      await performLocalCleanup();
      result.success = true;
      result.wasForced = true;
      
      if (showToasts) {
        authToasts.logoutSuccess(t, {
          action: {
            label: t('auth:logout.unexpectedError'),
            onClick: () => {},
          },
        });
      }
      
      await handleRedirect(redirectTo, t, showToasts);
    } else {
      result.error = t('auth:logout.unexpectedError');
      
      if (showToasts) {
        authToasts.logoutError(t, result.error);
      }
    }

    if (onError) {
      onError(error);
    }

    return result;

  } finally {
    // Always perform cleanup
    if (onCleanup) {
      onCleanup();
    }
  }
}

/**
 * Handle logout errors with appropriate user feedback
 */
function handleLogoutError(error: any, t: TFunction, showToasts: boolean): string {
  // Check for rate limiting
  if (error.status === 429 || error.code === 'RATE_LIMITED') {
    const retryAfter = error.retryAfter || 60;
    if (showToasts) {
      authToasts.rateLimitError(t, retryAfter);
    }
    return t('auth:logout.rateLimit');
  }

  // Check for network errors
  if (!navigator.onLine || error.name === 'NetworkError' || error.code === 'NETWORK_ERROR') {
    if (showToasts) {
      authToasts.networkError(t);
    }
    return t('auth:logout.networkErrorOffline');
  }

  // Check for server errors
  if (error.status >= 500 || error.code === 'SERVER_ERROR') {
    return t('auth:logout.serverError');
  }

  // Use auth error handler for other errors
  try {
    return mapAuthError(error, 'logout', t);
  } catch {
    return t('auth:logout.error');
  }
}

/**
 * Perform local session cleanup
 */
async function performLocalCleanup(): Promise<void> {
  try {
    // Clear any local storage items related to auth
    if (typeof window !== 'undefined') {
      // Clear auth-related localStorage items
      const authKeys = Object.keys(localStorage).filter(key => 
        key.includes('auth') || 
        key.includes('session') || 
        key.includes('token') ||
        key.includes('better-auth')
      );
      
      authKeys.forEach(key => {
        localStorage.removeItem(key);
      });

      // Clear auth-related sessionStorage items
      const sessionKeys = Object.keys(sessionStorage).filter(key => 
        key.includes('auth') || 
        key.includes('session') || 
        key.includes('token') ||
        key.includes('better-auth')
      );
      
      sessionKeys.forEach(key => {
        sessionStorage.removeItem(key);
      });

      // Clear any auth-related cookies (Better Auth handles this automatically)
      // But we can clear any custom cookies if needed
    }

    // Force refresh session state
    try {
      await authClient.getSession();
    } catch {
      // Ignore errors during session refresh
    }

  } catch (error) {
    console.warn('Local cleanup error:', error);
    // Don't throw error, cleanup is best-effort
  }
}

/**
 * Handle post-logout redirection
 */
async function handleRedirect(redirectTo: string, t: TFunction, showToasts: boolean): Promise<void> {
  try {
    if (typeof window !== 'undefined') {
      // Small delay to ensure cleanup is complete
      await new Promise(resolve => setTimeout(resolve, 500));
      
      if (showToasts) {
        authToasts.logoutSuccess(t, {
          action: {
            label: t('auth:logout.redirecting'),
            onClick: () => {},
          },
        });
      }

      // Use window.location for full page refresh to ensure clean state
      window.location.href = redirectTo;
    }
  } catch (error) {
    console.error('Redirect error:', error);
    // Fallback to reload if redirect fails
    if (typeof window !== 'undefined') {
      window.location.reload();
    }
  }
}

/**
 * Quick logout function for simple use cases
 */
export async function quickLogout(t: TFunction): Promise<void> {
  await handleLogout(t, {
    revokeAllSessions: false,
    showToasts: true,
    forceLogout: true,
  });
}

/**
 * Logout from all devices
 */
export async function logoutFromAllDevices(t: TFunction): Promise<void> {
  await handleLogout(t, {
    revokeAllSessions: true,
    showToasts: true,
    forceLogout: true,
  });
}

/**
 * Silent logout (no toasts, no redirect)
 */
export async function silentLogout(): Promise<LogoutResult> {
  return handleLogout(() => '', {
    showToasts: false,
    forceLogout: true,
    redirectTo: '',
  });
}

/**
 * Check if logout is rate limited
 */
export async function checkLogoutRateLimit(): Promise<boolean> {
  try {
    // Make a quick request to check rate limiting
    const response = await fetch('/api/auth/get-session', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Check rate limit headers
    const rateLimitRemaining = response.headers.get('X-RateLimit-Remaining');
    return rateLimitRemaining ? parseInt(rateLimitRemaining) > 0 : true;

  } catch {
    // If we can't check, assume it's not rate limited
    return true;
  }
}
