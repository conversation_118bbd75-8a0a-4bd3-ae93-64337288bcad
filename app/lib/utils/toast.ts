/**
 * Toast notification utilities using Sonner
 * 
 * Provides consistent toast notifications throughout the application
 * with internationalization support and proper styling.
 */

import { toast } from "sonner";
import type { TFunction } from "react-i18next";

export interface ToastOptions {
  duration?: number;
  dismissible?: boolean;
  action?: {
    label: string;
    onClick: () => void;
  };
}

/**
 * Show success toast notification
 */
export function showSuccessToast(
  message: string,
  options: ToastOptions = {}
) {
  return toast.success(message, {
    duration: options.duration || 4000,
    dismissible: options.dismissible !== false,
    action: options.action,
  });
}

/**
 * Show error toast notification
 */
export function showErrorToast(
  message: string,
  options: ToastOptions = {}
) {
  return toast.error(message, {
    duration: options.duration || 6000,
    dismissible: options.dismissible !== false,
    action: options.action,
  });
}

/**
 * Show warning toast notification
 */
export function showWarningToast(
  message: string,
  options: ToastOptions = {}
) {
  return toast.warning(message, {
    duration: options.duration || 5000,
    dismissible: options.dismissible !== false,
    action: options.action,
  });
}

/**
 * Show info toast notification
 */
export function showInfoToast(
  message: string,
  options: ToastOptions = {}
) {
  return toast.info(message, {
    duration: options.duration || 4000,
    dismissible: options.dismissible !== false,
    action: options.action,
  });
}

/**
 * Show loading toast notification
 */
export function showLoadingToast(
  message: string,
  options: Omit<ToastOptions, 'duration'> = {}
) {
  return toast.loading(message, {
    dismissible: options.dismissible !== false,
    action: options.action,
  });
}

/**
 * Dismiss a specific toast
 */
export function dismissToast(toastId: string | number) {
  toast.dismiss(toastId);
}

/**
 * Dismiss all toasts
 */
export function dismissAllToasts() {
  toast.dismiss();
}

/**
 * Authentication-specific toast utilities
 */
export const authToasts = {
  /**
   * Show logout success toast
   */
  logoutSuccess: (t: TFunction, options: ToastOptions = {}) => {
    return showSuccessToast(
      t('auth:logout.success'),
      {
        duration: 3000,
        ...options,
      }
    );
  },

  /**
   * Show logout error toast
   */
  logoutError: (t: TFunction, error?: string, options: ToastOptions = {}) => {
    const message = error || t('auth:logout.error');
    return showErrorToast(
      message,
      {
        duration: 5000,
        action: {
          label: t('auth:logout.retry'),
          onClick: () => {
            // Retry action will be handled by the calling component
          },
        },
        ...options,
      }
    );
  },

  /**
   * Show logout loading toast
   */
  logoutLoading: (t: TFunction, options: Omit<ToastOptions, 'duration'> = {}) => {
    return showLoadingToast(
      t('auth:logout.loading'),
      options
    );
  },

  /**
   * Show logout from all devices success toast
   */
  logoutAllSuccess: (t: TFunction, options: ToastOptions = {}) => {
    return showSuccessToast(
      t('auth:logout.allDevicesSuccess'),
      {
        duration: 4000,
        ...options,
      }
    );
  },

  /**
   * Show rate limit error toast
   */
  rateLimitError: (t: TFunction, retryAfter: number, options: ToastOptions = {}) => {
    return showWarningToast(
      t('auth:errors.rateLimit.general', { retryAfter }),
      {
        duration: retryAfter * 1000,
        ...options,
      }
    );
  },

  /**
   * Show network error toast
   */
  networkError: (t: TFunction, options: ToastOptions = {}) => {
    return showErrorToast(
      t('auth:errors.general.networkError'),
      {
        duration: 5000,
        action: {
          label: t('auth:logout.continueOffline'),
          onClick: () => {
            // Continue offline action will be handled by the calling component
          },
        },
        ...options,
      }
    );
  },

  /**
   * Show session cleanup warning
   */
  sessionCleanupWarning: (t: TFunction, options: ToastOptions = {}) => {
    return showWarningToast(
      t('auth:logout.sessionCleanupWarning'),
      {
        duration: 6000,
        ...options,
      }
    );
  },
};

/**
 * Promise-based toast for async operations
 */
export function toastPromise<T>(
  promise: Promise<T>,
  messages: {
    loading: string;
    success: string | ((data: T) => string);
    error: string | ((error: any) => string);
  },
  options: ToastOptions = {}
): Promise<T> {
  return toast.promise(promise, {
    loading: messages.loading,
    success: messages.success,
    error: messages.error,
    duration: options.duration,
    dismissible: options.dismissible,
    action: options.action,
  });
}

/**
 * Custom toast with custom JSX content
 */
export function showCustomToast(
  content: React.ReactNode,
  options: ToastOptions = {}
) {
  return toast.custom(content, {
    duration: options.duration || 4000,
    dismissible: options.dismissible !== false,
  });
}

/**
 * Toast utilities for common application scenarios
 */
export const appToasts = {
  /**
   * Show save success toast
   */
  saveSuccess: (t: TFunction, itemName?: string) => {
    const message = itemName 
      ? t('common:messages.saveSuccess', { item: itemName })
      : t('common:messages.saveSuccessGeneric');
    return showSuccessToast(message);
  },

  /**
   * Show delete success toast
   */
  deleteSuccess: (t: TFunction, itemName?: string) => {
    const message = itemName 
      ? t('common:messages.deleteSuccess', { item: itemName })
      : t('common:messages.deleteSuccessGeneric');
    return showSuccessToast(message);
  },

  /**
   * Show copy success toast
   */
  copySuccess: (t: TFunction) => {
    return showSuccessToast(t('common:messages.copySuccess'), {
      duration: 2000,
    });
  },

  /**
   * Show validation error toast
   */
  validationError: (t: TFunction, fieldName?: string) => {
    const message = fieldName 
      ? t('common:errors.validationField', { field: fieldName })
      : t('common:errors.validation');
    return showErrorToast(message);
  },
};
