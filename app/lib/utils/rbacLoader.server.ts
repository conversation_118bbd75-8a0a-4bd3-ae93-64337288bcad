import type { LoaderFunctionArgs } from '@remix-run/node';
import { getUserBusinessPermissions } from '~/lib/middleware/rbac.server';
import { getSession } from '~/lib/auth.session.server';

export interface RBACData {
  permissions: string[];
  roles: string[];
  userId: string;
  businessId?: string;
}

/**
 * Helper function to include RBAC data in loader responses
 * This should be called in route loaders to provide permission data to components
 */
export async function withRBAC(
  request: Request,
  businessId?: string
): Promise<RBACData | null> {
  try {
    const session = await getSession(request);
    if (!session?.user?.id) {
      return null;
    }

    const rbacData = await getUserBusinessPermissions(request, businessId);
    if (!rbacData) {
      return null;
    }

    return {
      permissions: rbacData.permissions,
      roles: rbacData.roles,
      userId: session.user.id,
      businessId,
    };
  } catch {
    return null;
  }
}

/**
 * Enhanced loader helper that includes RBAC data in the response
 * Usage in route loaders:
 * 
 * export async function loader({ request, params }: LoaderFunctionArgs) {
 *   const businessId = params.businessId;
 *   const rbac = await withRBAC(request, businessId);
 *   
 *   // Your other loader logic here
 *   const data = await getYourData();
 *   
 *   return json({ data, rbac });
 * }
 */
export function createRBACLoader<T = Record<string, unknown>>(
  loaderFn: (args: LoaderFunctionArgs & { rbac: RBACData | null }) => Promise<T> | T,
  options?: {
    businessId?: string | ((args: LoaderFunctionArgs) => string | undefined);
    requireAuth?: boolean;
  }
) {
  return async (args: LoaderFunctionArgs) => {
    const { request } = args;
    
    // Determine business ID
    let businessId: string | undefined;
    if (typeof options?.businessId === 'function') {
      businessId = options.businessId(args);
    } else if (typeof options?.businessId === 'string') {
      businessId = options.businessId;
    }

    // Get RBAC data
    const rbac = await withRBAC(request, businessId);

    // Check if auth is required
    if (options?.requireAuth && !rbac) {
      throw new Response('Unauthorized', { status: 401 });
    }

    // Call the original loader function with RBAC data
    const result = await loaderFn({ ...args, rbac });

    // If result is a Response object, return it as-is
    if (result instanceof Response) {
      return result;
    }

    // If result is an object, merge RBAC data
    if (typeof result === 'object' && result !== null) {
      return { ...result, rbac };
    }

    // For other types, wrap in an object
    return { data: result, rbac };
  };
}

/**
 * Utility to extract business ID from route parameters
 * Common pattern: /business/:businessId/...
 */
export function getBusinessIdFromParams(args: LoaderFunctionArgs): string | undefined {
  return args.params.businessId;
}

/**
 * Utility to extract business ID from URL search params
 * Common pattern: ?businessId=...
 */
export function getBusinessIdFromSearchParams(args: LoaderFunctionArgs): string | undefined {
  const url = new URL(args.request.url);
  return url.searchParams.get('businessId') || undefined;
}

/**
 * Example usage patterns:
 * 
 * // Basic usage with business ID from params
 * export const loader = createRBACLoader(
 *   async ({ request, params, rbac }) => {
 *     // Your loader logic here
 *     return { someData: 'value' };
 *   },
 *   { 
 *     businessId: getBusinessIdFromParams,
 *     requireAuth: true 
 *   }
 * );
 * 
 * // Manual RBAC inclusion
 * export async function loader({ request, params }: LoaderFunctionArgs) {
 *   const rbac = await withRBAC(request, params.businessId);
 *   const data = await getSomeData();
 *   return json({ data, rbac });
 * }
 */