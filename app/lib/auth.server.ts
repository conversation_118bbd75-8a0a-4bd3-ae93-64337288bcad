import { betterAuth } from "better-auth";
import { Pool } from "pg";
import { redisSecondaryStorage, getRateLimiterForPath, getRateLimitIdentifier, checkRateLimit, createRateLimitErrorResponse } from "./redis.server";

// Environment detection
const isDevelopment = process.env.NODE_ENV !== 'production';
const isProduction = process.env.NODE_ENV === 'production';

// Log rate limiting configuration on startup
if (isDevelopment) {
  console.log('🔧 Better Auth: Development mode - Relaxed rate limiting enabled');
  console.log('   📊 Sign-in: 100 attempts per minute');
  console.log('   🚀 Use "bun run dev:clear-rate-limits" to reset limits if needed');
} else {
  console.log('🔒 Better Auth: Production mode - Strict rate limiting enabled');
  console.log('   📊 Sign-in: 5 attempts per 15 minutes');
}

// Create PostgreSQL connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  // Connection pool settings for better performance
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

export const auth = betterAuth({
  // Database configuration using PostgreSQL (primary storage)
  database: pool,

  // Redis secondary storage for sessions and rate limiting
  secondaryStorage: redisSecondaryStorage,

  // Basic authentication configuration
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false, // Set to true if you want email verification
    minPasswordLength: 8,
    maxPasswordLength: 128,
  },

  // Enhanced session configuration with Redis storage
  // Optimized to eliminate 5-10 minute timeout issues while maintaining security
  session: {
    expiresIn: 60 * 60 * 24 * 30, // 30 days - Extended for better user experience
    updateAge: 60 * 60 * 6, // 6 hours - More frequent updates to keep sessions fresh
    disableSessionRefresh: false, // Explicitly enable session refresh for active users
    // Store sessions in Redis for better performance
    storeSessionInDatabase: false, // Don't duplicate in PostgreSQL
    // Enable cookie caching for even better performance
    cookieCache: {
      enabled: true,
      maxAge: 60 * 60 * 24, // 24 hours cache - Eliminates frequent validation calls
    },
  },

  // Environment-aware rate limiting configuration
  rateLimit: isProduction ? {
    // PRODUCTION: Strict rate limiting for security
    enabled: true,
    storage: "secondary-storage", // Use Redis for rate limiting
    window: 60, // Default: 60 seconds
    max: 100, // Default: 100 requests per window
    // Custom rules for specific endpoints
    customRules: {
      "/sign-in/email": {
        window: 15 * 60, // 15 minutes
        max: 5, // 5 attempts per 15 minutes
      },
      "/sign-up/email": {
        window: 60 * 60, // 1 hour
        max: 3, // 3 registrations per hour
      },
      "/forget-password": {
        window: 60 * 60, // 1 hour
        max: 3, // 3 password reset requests per hour
      },
      "/reset-password": {
        window: 60 * 60, // 1 hour
        max: 5, // 5 password reset attempts per hour
      },
      "/get-session": {
        window: 60, // 1 minute
        max: 100, // 100 session checks per minute
      },
    },
  } : {
    // DEVELOPMENT: Relaxed rate limiting for testing
    enabled: true,
    storage: "secondary-storage",
    window: 60, // 1 minute window
    max: 1000, // Very high default limit
    // Relaxed rules for development testing
    customRules: {
      "/sign-in/email": {
        window: 60, // 1 minute
        max: 100, // 100 attempts per minute (very generous for testing)
      },
      "/sign-up/email": {
        window: 60, // 1 minute
        max: 50, // 50 registrations per minute
      },
      "/forget-password": {
        window: 60, // 1 minute
        max: 20, // 20 password reset requests per minute
      },
      "/reset-password": {
        window: 60, // 1 minute
        max: 50, // 50 password reset attempts per minute
      },
      "/get-session": {
        window: 60, // 1 minute
        max: 500, // 500 session checks per minute
      },
    },
  },

  // Security configuration
  secret: process.env.BETTER_AUTH_SECRET || "fallback-secret-key-change-in-production",
  baseURL: process.env.BETTER_AUTH_URL || "http://localhost:3000",

  // Advanced configuration
  advanced: {
    useSecureCookies: process.env.NODE_ENV === "production",
    // IP address tracking for rate limiting
    ipAddress: {
      ipAddressHeaders: ["cf-connecting-ip", "x-real-ip", "x-forwarded-for"],
      disableIpTracking: false,
    },
    // Database configuration
    database: {
      generateId: () => crypto.randomUUID(),
    },
  },

  // Optional: Social providers (uncomment to enable)
  // socialProviders: {
  //   github: {
  //     clientId: process.env.GITHUB_CLIENT_ID as string,
  //     clientSecret: process.env.GITHUB_CLIENT_SECRET as string,
  //   },
  //   google: {
  //     clientId: process.env.GOOGLE_CLIENT_ID as string,
  //     clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
  //   },
  // },

  // Optional: Email verification (uncomment to enable)
  // emailVerification: {
  //   sendEmailVerification: async ({ user, url }) => {
  //     // Implement your email sending logic here
  //     console.log(`Send verification email to ${user.email}: ${url}`);
  //   },
  // },

  // User configuration
  user: {
    additionalFields: {
      firstName: {
        type: "string",
        required: false,
      },
      lastName: {
        type: "string",
        required: false,
      },
    },
  },
});

// Export types for TypeScript
import type { Session as BetterAuthSession, User as BetterAuthUser } from "better-auth";

export type Session = BetterAuthSession;
export type User = BetterAuthUser;
