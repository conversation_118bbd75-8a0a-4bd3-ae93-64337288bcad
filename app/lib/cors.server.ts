/**
 * CORS utility for handling cross-origin requests from external applications
 * This is particularly useful for mobile apps and external API clients
 */

export interface CorsOptions {
  allowedOrigins?: string[];
  allowedMethods?: string[];
  allowedHeaders?: string[];
  allowCredentials?: boolean;
  maxAge?: number;
}

const defaultOptions: CorsOptions = {
  allowedOrigins: [
    'http://localhost:3000',
    'http://localhost:5174',
    // Add your production domain
    // 'https://your-production-domain.com',
    // Add mobile app schemes if needed
    // 'your-app://auth-callback',
  ],
  allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Cookie', 'X-Requested-With'],
  allowCredentials: true,
  maxAge: 86400, // 24 hours
};

/**
 * Add CORS headers to a response
 */
export function addCorsHeaders(
  response: Response, 
  requestOrigin?: string | null,
  options: CorsOptions = {}
): Response {
  const config = { ...defaultOptions, ...options };
  const origin = requestOrigin || '*';
  
  // Check if origin is allowed
  const isAllowed = config.allowedOrigins?.includes(origin) || 
                   config.allowedOrigins?.includes('*') ||
                   origin === '*';

  if (isAllowed && origin !== '*') {
    response.headers.set('Access-Control-Allow-Origin', origin);
  } else if (config.allowedOrigins?.includes('*')) {
    response.headers.set('Access-Control-Allow-Origin', '*');
  }
  
  if (config.allowedMethods) {
    response.headers.set('Access-Control-Allow-Methods', config.allowedMethods.join(', '));
  }
  
  if (config.allowedHeaders) {
    response.headers.set('Access-Control-Allow-Headers', config.allowedHeaders.join(', '));
  }
  
  if (config.allowCredentials) {
    response.headers.set('Access-Control-Allow-Credentials', 'true');
  }
  
  if (config.maxAge) {
    response.headers.set('Access-Control-Max-Age', config.maxAge.toString());
  }

  return response;
}

/**
 * Handle preflight OPTIONS requests
 */
export function handlePreflight(
  request: Request,
  options: CorsOptions = {}
): Response {
  const response = new Response(null, { status: 200 });
  return addCorsHeaders(response, request.headers.get('Origin'), options);
}

/**
 * Middleware wrapper for adding CORS to any response
 */
export function withCors(
  handler: (request: Request) => Promise<Response>,
  options: CorsOptions = {}
) {
  return async (request: Request): Promise<Response> => {
    // Handle preflight requests
    if (request.method === 'OPTIONS') {
      return handlePreflight(request, options);
    }
    
    // Process the actual request
    const response = await handler(request);
    
    // Add CORS headers to the response
    return addCorsHeaders(response, request.headers.get('Origin'), options);
  };
}

/**
 * Check if a request origin is allowed
 */
export function isOriginAllowed(
  origin: string | null,
  allowedOrigins: string[] = defaultOptions.allowedOrigins || []
): boolean {
  if (!origin) return false;
  return allowedOrigins.includes(origin) || allowedOrigins.includes('*');
}

/**
 * Extract and validate Bearer token from Authorization header
 */
export function extractBearerToken(request: Request): string | null {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7); // Remove 'Bearer ' prefix
}

/**
 * Create a standardized error response with CORS headers
 */
export function createCorsErrorResponse(
  message: string,
  status: number = 400,
  code?: string,
  requestOrigin?: string | null,
  options: CorsOptions = {}
): Response {
  const errorBody = {
    message,
    ...(code && { code }),
    timestamp: new Date().toISOString(),
  };
  
  const response = new Response(JSON.stringify(errorBody), {
    status,
    headers: {
      'Content-Type': 'application/json',
    },
  });
  
  return addCorsHeaders(response, requestOrigin, options);
}
