// Types for COGS Calculator temporary data structures

export interface TempIngredient {
  id: string // temporary ID for React keys
  name: string
  baseUnitCost: number
  baseUnitQuantity: number
  unit: string
  supplierInfo?: string
  category?: string
  note: string
  isActive: boolean
}

export interface TempProductIngredient {
  ingredientId: string // references TempIngredient.id
  usagePerCup: number
  note: string
}

export interface TempProduct {
  id: string // temporary ID for React keys
  name: string
  description: string
  note: string
  isActive: boolean
  ingredients: TempProductIngredient[]
}

export interface COGSPlaygroundState {
  ingredients: TempIngredient[]
  products: TempProduct[]
}

// Helper function to generate temporary IDs
export function generateTempId(): string {
  return `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// Calculate unit cost for an ingredient
export function calculateUnitCost(ingredient: TempIngredient): number {
  if (ingredient.baseUnitQuantity === 0) return 0
  return ingredient.baseUnitCost / ingredient.baseUnitQuantity
}

// Calculate cost per cup for a product ingredient
export function calculateIngredientCostPerCup(
  ingredient: TempIngredient, 
  usagePerCup: number
): number {
  const unitCost = calculateUnitCost(ingredient)
  return unitCost * usagePerCup
}

// Calculate total COGS for a product
export function calculateProductCOGS(
  product: TempProduct, 
  ingredients: TempIngredient[]
): number {
  return product.ingredients.reduce((total, pi) => {
    const ingredient = ingredients.find(ing => ing.id === pi.ingredientId)
    if (!ingredient) return total
    
    return total + calculateIngredientCostPerCup(ingredient, pi.usagePerCup)
  }, 0)
}

// Unit options for ingredients
export const UNIT_OPTIONS = [
  { value: 'ml', label: 'ml' },
  { value: 'l', label: 'L' },
  { value: 'g', label: 'g' },
  { value: 'kg', label: 'kg' },
  { value: 'oz', label: 'oz' },
  { value: 'lb', label: 'lb' },
  { value: 'cup', label: 'cup' },
  { value: 'tbsp', label: 'tbsp' },
  { value: 'tsp', label: 'tsp' },
  { value: 'piece', label: 'piece' },
  { value: 'pack', label: 'pack' },
  { value: 'bottle', label: 'bottle' },
  { value: 'bag', label: 'bag' },
]

// Common ingredient categories
export const INGREDIENT_CATEGORIES = [
  'Coffee Beans',
  'Dairy',
  'Sweeteners',
  'Syrups',
  'Spices',
  'Packaging',
  'Other'
]

// Note: formatCurrency function removed - use centralized currency system instead
// Import formatCurrency from '~/utils/formatters' in components that need currency formatting
