import type { Ingredient, Product, ProductIngredient, Category, ProductCogs, ProductCogsHistory } from '~/lib/db/schema';

// Fix the type imports to match the actual database schema
export type { Ingredient, Product, ProductIngredient, Category, ProductCogs, ProductCogsHistory } from '~/lib/db/schema';

// Form data types for ingredients
export interface IngredientFormData {
  name: string;
  baseUnitCost: string; // String for form input, will be converted to number
  baseUnitQuantity: string; // String for form input, will be converted to number
  unit: string;
  categoryId?: string; // UUID foreign key to categories table
  supplierInfo?: string;
  notes?: string;
  isActive: boolean;
}

// Form data types for products
export interface ProductFormData {
  name: string;
  description?: string;
  note?: string;
  categoryId?: string; // UUID foreign key to categories table
  isActive: boolean;
  // cogsPerCup removed - will be calculated automatically based on ingredients
  ingredients: ProductIngredientFormData[];
}

// Simplified form data for basic product creation (without ingredients)
export interface BasicProductFormData {
  name: string;
  description?: string;
  note?: string;
  categoryId?: string; // UUID foreign key to categories table
  isActive: boolean;
  // cogsPerCup removed - will be calculated automatically based on ingredients
}

// Form data for product-ingredient relationships
export interface ProductIngredientFormData {
  ingredientId: string;
  quantityNeeded: string; // String for form input, will be converted to number
}

// Form data types for categories
export interface CategoryFormData {
  name: string;
  description?: string;
  type: 'ingredient' | 'product' | 'supplier' | 'customer';
  parentId?: string;
  color?: string;
  sortOrder?: number;
  isActive: boolean;
}

// Form data types for COGS
export interface CogsFormData {
  laborCosts: string; // String for form input, will be converted to number
  overheadCosts: string; // String for form input, will be converted to number
  calculationMethod: 'automatic' | 'manual' | 'estimated';
}

// Extended types with relationships
export interface IngredientWithProducts extends Ingredient {
  productIngredients: (ProductIngredient & {
    product: Product;
  })[];
}

// Ingredient with category information (for list views)
export interface IngredientWithCategory extends Ingredient {
  categoryName?: string;
  categoryColor?: string;
  usageCount?: number;
}

// Product with category information (for list views)
export interface ProductWithCategory extends Product {
  categoryName?: string;
  categoryColor?: string;
}

export interface ProductWithIngredients extends Product {
  productIngredients: (ProductIngredient & {
    ingredient: Ingredient;
  })[];
}

// Extended types with COGS information
export interface ProductWithCogs extends Product {
  productCogs?: ProductCogs;
  profitMargin?: number;
  profitAmount?: number;
}

export interface ProductWithIngredientsAndCogs extends ProductWithIngredients {
  productCogs?: ProductCogs;
  profitMargin?: number;
  profitAmount?: number;
}

// Category types with hierarchy
export interface CategoryWithChildren extends Category {
  children?: CategoryWithChildren[];
  parent?: Category;
}

// COGS calculation types
export interface CogsCalculationResult {
  ingredientCosts: number;
  laborCosts: number;
  overheadCosts: number;
  totalCogs: number;
  profitMargin: number;
  profitAmount: number;
}

export interface CogsBreakdown {
  // New format expected by UI
  ingredientId: string;
  ingredientName: string;
  usagePerCup: number;
  unit: string;
  unitCost: string;
  costPerCup: string;
  note: string;

  // Legacy format for compatibility
  ingredient: Ingredient;
  quantityNeeded: number;
  totalCost: number;
  percentageOfTotal: number;
}

// API response types
export interface IngredientListResponse {
  ingredients: Ingredient[];
}

export interface IngredientResponse {
  ingredient: Ingredient;
}

export interface ProductListResponse {
  products: Product[];
}

export interface ProductResponse {
  product: Product;
}

export interface ProductWithIngredientsResponse {
  product: ProductWithIngredients;
}

// Search and filter types
export interface IngredientFilters {
  search?: string;
  unit?: string;
  minCost?: number;
  maxCost?: number;
  isActive?: boolean;
}

export interface ProductFilters {
  search?: string;
  category?: string;
  status?: 'active' | 'inactive' | 'discontinued';
  minPrice?: number;
  maxPrice?: number;
}

// Validation schemas (for use with Zod)
export interface IngredientValidationSchema {
  name: string;
  baseUnitCost: number;
  baseUnitQuantity: number;
  unit: string;
  supplierInfo?: string;
  notes?: string;
  isActive: boolean;
}

export interface ProductValidationSchema {
  name: string;
  description?: string;
  note?: string;
  category?: string;
  isActive: boolean;
  // cogsPerCup removed - will be calculated automatically
  ingredients: {
    ingredientId: string;
    quantityNeeded: number;
  }[];
}

// Constants for dropdowns and validation
export const INGREDIENT_UNITS = [
  'kg', 'g', 'mg',
  'l', 'ml',
  'pcs', 'pack', 'box',
  'cup', 'tbsp', 'tsp',
  'oz', 'lb'
] as const;

export const PRODUCT_CATEGORIES = [
  'beverage',
  'food',
  'dessert',
  'snack',
  'merchandise',
  'other'
] as const;

// Note: PRODUCT_STATUSES removed - now using isActive boolean instead

export const CATEGORY_TYPES = [
  'ingredient',
  'product',
  'supplier',
  'customer'
] as const;

export const COGS_CALCULATION_METHODS = [
  'automatic',
  'manual',
  'estimated'
] as const;

export type IngredientUnit = typeof INGREDIENT_UNITS[number];
export type ProductCategory = typeof PRODUCT_CATEGORIES[number];
export type ProductStatus = typeof PRODUCT_STATUSES[number];
export type CategoryType = typeof CATEGORY_TYPES[number];
export type CogsCalculationMethod = typeof COGS_CALCULATION_METHODS[number];

// Error types
export interface InventoryError {
  message: string;
  field?: string;
  code?: string;
}

export interface InventoryApiError {
  error: string;
  details?: InventoryError[];
}
