export interface Business {
  id: string
  name: string
  description?: string | null
  note?: string | null
  currency: string
  logo?: string | null
  userId: string
  createdAt: string | Date
  updatedAt: string | Date
}

export interface BusinessFormData {
  name: string
  description?: string
  note?: string
  currency: string
  logo?: string
}

export interface BusinessState {
  // Current selected business
  currentBusiness: Business | null

  // All available businesses
  businesses: Business[]

  // Loading states
  isLoading: boolean
  isInitialized: boolean
  isBusinessSwitching: boolean

  // Actions
  setCurrentBusiness: (business: Business | null) => void
  switchBusiness: (business: Business) => Promise<void>
  loadBusinesses: (userId?: string) => Promise<void>
  addBusiness: (business: Business) => void
  updateBusiness: (id: string, updates: Partial<Business>) => void
  removeBusiness: (id: string) => void
  initializeStore: (userId?: string) => Promise<void>
  clearBusinessData: () => void

  // Getters
  getCurrentBusinessId: () => string | null
  isBusinessSelected: () => boolean
}
