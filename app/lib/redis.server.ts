/**
 * Redis configuration and utilities for Better Auth secondary storage
 * 
 * This module provides:
 * - Upstash Redis client configuration
 * - Secondary storage implementation for Better Auth
 * - Rate limiting utilities
 * - Connection pooling and error handling
 */

import { Redis } from '@upstash/redis';
import { Ratelimit } from '@upstash/ratelimit';
import type { SecondaryStorage } from 'better-auth';

// Redis client configuration
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_URL || 'redis://localhost:6379',
  token: process.env.UPSTASH_REDIS_TOKEN || '',
  // Connection configuration
  retry: {
    retries: 3,
    backoff: (retryCount) => Math.exp(retryCount) * 50,
  },
  // Enable automatic pipelining for better performance
  automaticDeserialization: true,
});

/**
 * Secondary storage implementation for Better Auth using Redis
 * This handles session storage and other temporary data
 */
export const redisSecondaryStorage: SecondaryStorage = {
  async get(key: string): Promise<string | null> {
    try {
      const value = await redis.get(key);
      return value ? String(value) : null;
    } catch (error) {
      console.error('Redis get error:', error);
      return null;
    }
  },

  async set(key: string, value: string, ttl?: number): Promise<void> {
    try {
      if (ttl) {
        // Set with expiration (TTL in seconds)
        await redis.set(key, value, { ex: ttl });
      } else {
        // Set without expiration
        await redis.set(key, value);
      }
    } catch (error) {
      console.error('Redis set error:', error);
      // Don't throw error to prevent auth failures
    }
  },

  async delete(key: string): Promise<void> {
    try {
      await redis.del(key);
    } catch (error) {
      console.error('Redis delete error:', error);
      // Don't throw error to prevent auth failures
    }
  },
};

/**
 * Environment-aware rate limiting configurations
 */
const isDevelopment = process.env.NODE_ENV === 'development';

/**
 * Rate limiting configurations for different authentication endpoints
 */
export const rateLimiters = {
  // Environment-aware rate limiting for login attempts
  signIn: new Ratelimit({
    redis,
    limiter: isDevelopment 
      ? Ratelimit.slidingWindow(100, '1m') // 100 attempts per minute in dev
      : Ratelimit.slidingWindow(5, '15m'), // 5 attempts per 15 minutes in prod
    analytics: true,
    prefix: 'ratelimit:signin',
    ephemeralCache: new Map(), // In-memory cache for performance
  }),

  // Environment-aware rate limiting for registration
  signUp: new Ratelimit({
    redis,
    limiter: isDevelopment
      ? Ratelimit.slidingWindow(50, '1m') // 50 registrations per minute in dev
      : Ratelimit.slidingWindow(3, '1h'), // 3 registrations per hour in prod
    analytics: true,
    prefix: 'ratelimit:signup',
    ephemeralCache: new Map(),
  }),

  // Lenient rate limiting for session checks
  getSession: new Ratelimit({
    redis,
    limiter: isDevelopment
      ? Ratelimit.slidingWindow(1000, '1m') // 1000 requests per minute in dev
      : Ratelimit.slidingWindow(100, '1m'), // 100 requests per minute in prod
    analytics: true,
    prefix: 'ratelimit:session',
    ephemeralCache: new Map(),
  }),

  // Environment-aware rate limiting for password reset requests
  passwordReset: new Ratelimit({
    redis,
    limiter: isDevelopment
      ? Ratelimit.slidingWindow(20, '1m') // 20 reset requests per minute in dev
      : Ratelimit.slidingWindow(3, '1h'), // 3 reset requests per hour in prod
    analytics: true,
    prefix: 'ratelimit:password-reset',
    ephemeralCache: new Map(),
  }),

  // Environment-aware general API rate limiting
  general: new Ratelimit({
    redis,
    limiter: isDevelopment
      ? Ratelimit.slidingWindow(500, '1m') // 500 requests per minute in dev
      : Ratelimit.slidingWindow(60, '1m'), // 60 requests per minute in prod
    analytics: true,
    prefix: 'ratelimit:general',
    ephemeralCache: new Map(),
  }),
};

/**
 * Rate limiting utility function
 */
export async function checkRateLimit(
  limiter: Ratelimit,
  identifier: string,
  customRate?: number
): Promise<{
  success: boolean;
  limit: number;
  remaining: number;
  reset: Date;
  retryAfter?: number;
}> {
  try {
    const result = await limiter.limit(identifier, customRate ? { rate: customRate } : undefined);
    
    return {
      success: result.success,
      limit: result.limit,
      remaining: result.remaining,
      reset: new Date(result.reset),
      retryAfter: result.success ? undefined : Math.ceil((result.reset - Date.now()) / 1000),
    };
  } catch (error) {
    console.error('Rate limit check error:', error);
    // On error, allow the request to proceed (fail open)
    return {
      success: true,
      limit: 0,
      remaining: 0,
      reset: new Date(),
    };
  }
}

/**
 * Get rate limiter based on endpoint path
 */
export function getRateLimiterForPath(path: string): Ratelimit {
  if (path.includes('/sign-in/email')) {
    return rateLimiters.signIn;
  }
  if (path.includes('/sign-up/email')) {
    return rateLimiters.signUp;
  }
  if (path.includes('/get-session')) {
    return rateLimiters.getSession;
  }
  if (path.includes('/forget-password') || path.includes('/reset-password')) {
    return rateLimiters.passwordReset;
  }
  return rateLimiters.general;
}

/**
 * Get identifier for rate limiting (IP address with fallback)
 */
export function getRateLimitIdentifier(request: Request): string {
  // Try to get real IP from various headers
  const forwardedFor = request.headers.get('x-forwarded-for');
  const realIp = request.headers.get('x-real-ip');
  const cfConnectingIp = request.headers.get('cf-connecting-ip');
  
  // Use the first available IP
  const ip = cfConnectingIp || realIp || forwardedFor?.split(',')[0]?.trim() || 'unknown';
  
  return ip;
}

/**
 * Create rate limit error response
 */
export function createRateLimitErrorResponse(
  retryAfter: number,
  message: string = 'Too many requests'
): Response {
  return new Response(
    JSON.stringify({
      message,
      code: 'RATE_LIMITED',
      retryAfter,
    }),
    {
      status: 429,
      headers: {
        'Content-Type': 'application/json',
        'X-Retry-After': retryAfter.toString(),
        'Retry-After': retryAfter.toString(),
      },
    }
  );
}

/**
 * Redis health check utility
 */
export async function checkRedisHealth(): Promise<boolean> {
  try {
    const result = await redis.ping();
    return result === 'PONG';
  } catch (error) {
    console.error('Redis health check failed:', error);
    return false;
  }
}

/**
 * Clean up expired rate limit data (optional maintenance function)
 */
export async function cleanupExpiredRateLimitData(): Promise<void> {
  try {
    // This is handled automatically by Redis TTL, but you can add custom cleanup logic here
    console.log('Rate limit cleanup completed');
  } catch (error) {
    console.error('Rate limit cleanup error:', error);
  }
}

// Export the Redis client for direct use if needed
export { redis };
