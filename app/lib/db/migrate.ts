import { db } from './connection';
import { sql } from 'drizzle-orm';

/**
 * Manual migration to create business table
 * This is needed because the user table already exists from Better Auth
 */
export async function createBusinessTable() {
  try {
    // Check if business table already exists
    const result = await db.execute(sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'business'
      );
    `);
    
    const tableExists = result.rows[0]?.exists;
    
    if (tableExists) {
      console.log('Business table already exists, skipping creation');
      return;
    }

    // Create business table
    await db.execute(sql`
      CREATE TABLE "business" (
        "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
        "name" text NOT NULL,
        "description" text,
        "note" text,
        "currency" text DEFAULT 'IDR' NOT NULL,
        "logo" text,
        "userId" text NOT NULL,
        "createdAt" timestamp DEFAULT now() NOT NULL,
        "updatedAt" timestamp DEFAULT now() NOT NULL,
        CONSTRAINT "business_user_name_unique" UNIQUE("userId","name")
      );
    `);

    // Add foreign key constraint to existing user table
    await db.execute(sql`
      ALTER TABLE "business" 
      ADD CONSTRAINT "business_userId_user_id_fk" 
      FOREIGN KEY ("userId") REFERENCES "public"."user"("id") 
      ON DELETE cascade ON UPDATE no action;
    `);

    // Create index for efficient user queries
    await db.execute(sql`
      CREATE INDEX "business_user_id_idx" ON "business" USING btree ("userId");
    `);

    console.log('Business table created successfully');
  } catch (error) {
    console.error('Error creating business table:', error);
    throw error;
  }
}

/**
 * Manual migration to create inventory tables (ingredients, products, product_ingredients)
 */
export async function createInventoryTables() {
  try {
    // Check if ingredient table already exists
    const ingredientResult = await db.execute(sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'ingredient'
      );
    `);

    const ingredientTableExists = ingredientResult.rows[0]?.exists;

    if (!ingredientTableExists) {
      // Create ingredient table
      await db.execute(sql`
        CREATE TABLE "ingredient" (
          "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
          "name" text NOT NULL,
          "description" text,
          "unitOfMeasurement" text NOT NULL,
          "costPerUnit" numeric(10,2) NOT NULL,
          "supplierInfo" text,
          "businessId" uuid NOT NULL,
          "createdAt" timestamp DEFAULT now() NOT NULL,
          "updatedAt" timestamp DEFAULT now() NOT NULL,
          CONSTRAINT "ingredient_business_name_unique" UNIQUE("businessId","name")
        );
      `);

      // Add foreign key constraint to business table
      await db.execute(sql`
        ALTER TABLE "ingredient"
        ADD CONSTRAINT "ingredient_businessId_business_id_fk"
        FOREIGN KEY ("businessId") REFERENCES "public"."business"("id")
        ON DELETE cascade ON UPDATE no action;
      `);

      // Create index for efficient business queries
      await db.execute(sql`
        CREATE INDEX "ingredient_business_id_idx" ON "ingredient" USING btree ("businessId");
      `);

      console.log('Ingredient table created successfully');
    } else {
      console.log('Ingredient table already exists, skipping creation');
    }

    // Check if product table already exists
    const productResult = await db.execute(sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'product'
      );
    `);

    const productTableExists = productResult.rows[0]?.exists;

    if (!productTableExists) {
      // Create product table
      await db.execute(sql`
        CREATE TABLE "product" (
          "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
          "name" text NOT NULL,
          "description" text,
          "sellingPrice" numeric(10,2) NOT NULL,
          "category" text,
          "status" text DEFAULT 'active' NOT NULL,
          "businessId" uuid NOT NULL,
          "createdAt" timestamp DEFAULT now() NOT NULL,
          "updatedAt" timestamp DEFAULT now() NOT NULL,
          CONSTRAINT "product_business_name_unique" UNIQUE("businessId","name")
        );
      `);

      // Add foreign key constraint to business table
      await db.execute(sql`
        ALTER TABLE "product"
        ADD CONSTRAINT "product_businessId_business_id_fk"
        FOREIGN KEY ("businessId") REFERENCES "public"."business"("id")
        ON DELETE cascade ON UPDATE no action;
      `);

      // Create index for efficient business queries
      await db.execute(sql`
        CREATE INDEX "product_business_id_idx" ON "product" USING btree ("businessId");
      `);

      console.log('Product table created successfully');
    } else {
      console.log('Product table already exists, skipping creation');
    }

    // Check if product_ingredient table already exists
    const productIngredientResult = await db.execute(sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'product_ingredient'
      );
    `);

    const productIngredientTableExists = productIngredientResult.rows[0]?.exists;

    if (!productIngredientTableExists) {
      // Create product_ingredient junction table
      await db.execute(sql`
        CREATE TABLE "product_ingredient" (
          "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
          "productId" uuid NOT NULL,
          "ingredientId" uuid NOT NULL,
          "quantityNeeded" numeric(10,4) NOT NULL,
          "createdAt" timestamp DEFAULT now() NOT NULL,
          CONSTRAINT "product_ingredient_unique" UNIQUE("productId","ingredientId")
        );
      `);

      // Add foreign key constraints
      await db.execute(sql`
        ALTER TABLE "product_ingredient"
        ADD CONSTRAINT "product_ingredient_productId_product_id_fk"
        FOREIGN KEY ("productId") REFERENCES "public"."product"("id")
        ON DELETE cascade ON UPDATE no action;
      `);

      await db.execute(sql`
        ALTER TABLE "product_ingredient"
        ADD CONSTRAINT "product_ingredient_ingredientId_ingredient_id_fk"
        FOREIGN KEY ("ingredientId") REFERENCES "public"."ingredient"("id")
        ON DELETE cascade ON UPDATE no action;
      `);

      // Create indexes for efficient queries
      await db.execute(sql`
        CREATE INDEX "product_ingredient_product_id_idx" ON "product_ingredient" USING btree ("productId");
      `);

      await db.execute(sql`
        CREATE INDEX "product_ingredient_ingredient_id_idx" ON "product_ingredient" USING btree ("ingredientId");
      `);

      console.log('Product-Ingredient junction table created successfully');
    } else {
      console.log('Product-Ingredient table already exists, skipping creation');
    }

    console.log('All inventory tables created successfully');
  } catch (error) {
    console.error('Error creating inventory tables:', error);
    throw error;
  }
}

/**
 * Migration to update ingredient table schema to match new structure
 */
export async function migrateIngredientSchema() {
  try {
    console.log('Starting ingredient schema migration...');

    // Check if the new columns already exist
    const columnCheckResult = await db.execute(sql`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'ingredient'
      AND table_schema = 'public'
      AND column_name IN ('baseUnitCost', 'baseUnitQuantity', 'unit', 'notes', 'isActive');
    `);

    const existingNewColumns = columnCheckResult.rows.map(row => row.column_name);

    // Check if old columns still exist
    const oldColumnCheckResult = await db.execute(sql`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'ingredient'
      AND table_schema = 'public'
      AND column_name IN ('costPerUnit', 'unitOfMeasurement', 'description');
    `);

    const existingOldColumns = oldColumnCheckResult.rows.map(row => row.column_name);

    if (existingNewColumns.length === 5 && existingOldColumns.length === 0) {
      console.log('✅ Ingredient schema already updated, skipping migration');
      return;
    }

    // Start transaction for schema migration
    await db.transaction(async (tx) => {
      // Add new columns if they don't exist
      if (!existingNewColumns.includes('baseUnitCost')) {
        await tx.execute(sql`
          ALTER TABLE "ingredient"
          ADD COLUMN "baseUnitCost" numeric(10,2);
        `);
        console.log('✅ Added baseUnitCost column');
      }

      if (!existingNewColumns.includes('baseUnitQuantity')) {
        await tx.execute(sql`
          ALTER TABLE "ingredient"
          ADD COLUMN "baseUnitQuantity" numeric(10,4) DEFAULT 1;
        `);
        console.log('✅ Added baseUnitQuantity column');
      }

      if (!existingNewColumns.includes('unit')) {
        await tx.execute(sql`
          ALTER TABLE "ingredient"
          ADD COLUMN "unit" text;
        `);
        console.log('✅ Added unit column');
      }

      if (!existingNewColumns.includes('notes')) {
        await tx.execute(sql`
          ALTER TABLE "ingredient"
          ADD COLUMN "notes" text;
        `);
        console.log('✅ Added notes column');
      }

      if (!existingNewColumns.includes('isActive')) {
        await tx.execute(sql`
          ALTER TABLE "ingredient"
          ADD COLUMN "isActive" boolean DEFAULT true;
        `);
        console.log('✅ Added isActive column');
      }

      // Migrate existing data from old columns to new columns
      await tx.execute(sql`
        UPDATE "ingredient"
        SET
          "baseUnitCost" = COALESCE("costPerUnit", 0),
          "unit" = COALESCE("unitOfMeasurement", 'pcs'),
          "notes" = "description",
          "isActive" = true
        WHERE "baseUnitCost" IS NULL OR "unit" IS NULL;
      `);
      console.log('✅ Migrated existing data to new columns');

      // Make required columns NOT NULL after data migration
      await tx.execute(sql`
        ALTER TABLE "ingredient"
        ALTER COLUMN "baseUnitCost" SET NOT NULL;
      `);

      await tx.execute(sql`
        ALTER TABLE "ingredient"
        ALTER COLUMN "unit" SET NOT NULL;
      `);

      await tx.execute(sql`
        ALTER TABLE "ingredient"
        ALTER COLUMN "isActive" SET NOT NULL;
      `);
      console.log('✅ Set NOT NULL constraints on required columns');

      // Drop old columns that are no longer needed
      await tx.execute(sql`
        ALTER TABLE "ingredient"
        DROP COLUMN IF EXISTS "costPerUnit";
      `);
      console.log('✅ Dropped costPerUnit column');

      await tx.execute(sql`
        ALTER TABLE "ingredient"
        DROP COLUMN IF EXISTS "unitOfMeasurement";
      `);
      console.log('✅ Dropped unitOfMeasurement column');

      await tx.execute(sql`
        ALTER TABLE "ingredient"
        DROP COLUMN IF EXISTS "description";
      `);
      console.log('✅ Dropped description column');
    });

    console.log('🎉 Ingredient schema migration completed successfully');
  } catch (error) {
    console.error('❌ Ingredient schema migration failed:', error);
    throw error;
  }
}
