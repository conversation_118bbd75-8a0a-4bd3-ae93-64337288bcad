import { db } from './connection';
import {
  roles,
  permissions,
  rolePermissions,
  type NewRole,
  type NewPermission,
} from './schema';
import { eq, and } from 'drizzle-orm';

// Default system roles
const defaultRoles: NewRole[] = [
  {
    name: 'super_admin',
    displayName: 'Super Administrator',
    description: 'Full system access with all permissions',
    isSystemRole: true,
    isActive: true,
  },
  {
    name: 'business_owner',
    displayName: 'Business Owner',
    description: 'Full access to own business data and settings',
    isSystemRole: true,
    isActive: true,
  },
  {
    name: 'business_manager',
    displayName: 'Business Manager',
    description: 'Manage business operations, inventory, and reports',
    isSystemRole: true,
    isActive: true,
  },
  {
    name: 'inventory_manager',
    displayName: 'Inventory Manager',
    description: 'Manage ingredients, products, and inventory',
    isSystemRole: true,
    isActive: true,
  },
  {
    name: 'staff',
    displayName: 'Staff Member',
    description: 'Basic access to view inventory and products',
    isSystemRole: true,
    isActive: true,
  },
  {
    name: 'viewer',
    displayName: 'Viewer',
    description: 'Read-only access to business data',
    isSystemRole: true,
    isActive: true,
  },
];

// Default system permissions
const defaultPermissions: NewPermission[] = [
  // Business permissions
  {
    name: 'business.create',
    displayName: 'Create Business',
    description: 'Create new businesses',
    resource: 'business',
    action: 'create',
    isSystemPermission: true,
  },
  {
    name: 'business.read',
    displayName: 'View Business',
    description: 'View business information',
    resource: 'business',
    action: 'read',
    isSystemPermission: true,
  },
  {
    name: 'business.update',
    displayName: 'Update Business',
    description: 'Update business settings and information',
    resource: 'business',
    action: 'update',
    isSystemPermission: true,
  },
  {
    name: 'business.delete',
    displayName: 'Delete Business',
    description: 'Delete businesses',
    resource: 'business',
    action: 'delete',
    isSystemPermission: true,
  },
  {
    name: 'business.manage_users',
    displayName: 'Manage Business Users',
    description: 'Add/remove users from business',
    resource: 'business',
    action: 'manage_users',
    isSystemPermission: true,
  },

  // Inventory permissions
  {
    name: 'inventory.read',
    displayName: 'View Inventory',
    description: 'View ingredients and products',
    resource: 'inventory',
    action: 'read',
    isSystemPermission: true,
  },
  {
    name: 'inventory.create',
    displayName: 'Create Inventory Items',
    description: 'Create new ingredients and products',
    resource: 'inventory',
    action: 'create',
    isSystemPermission: true,
  },
  {
    name: 'inventory.update',
    displayName: 'Update Inventory',
    description: 'Update ingredients and products',
    resource: 'inventory',
    action: 'update',
    isSystemPermission: true,
  },
  {
    name: 'inventory.delete',
    displayName: 'Delete Inventory Items',
    description: 'Delete ingredients and products',
    resource: 'inventory',
    action: 'delete',
    isSystemPermission: true,
  },

  // Category permissions
  {
    name: 'categories.read',
    displayName: 'View Categories',
    description: 'View product and ingredient categories',
    resource: 'categories',
    action: 'read',
    isSystemPermission: true,
  },
  {
    name: 'categories.create',
    displayName: 'Create Categories',
    description: 'Create new categories',
    resource: 'categories',
    action: 'create',
    isSystemPermission: true,
  },
  {
    name: 'categories.update',
    displayName: 'Update Categories',
    description: 'Update existing categories',
    resource: 'categories',
    action: 'update',
    isSystemPermission: true,
  },
  {
    name: 'categories.delete',
    displayName: 'Delete Categories',
    description: 'Delete categories',
    resource: 'categories',
    action: 'delete',
    isSystemPermission: true,
  },

  // COGS permissions
  {
    name: 'cogs.read',
    displayName: 'View COGS',
    description: 'View cost calculations and reports',
    resource: 'cogs',
    action: 'read',
    isSystemPermission: true,
  },
  {
    name: 'cogs.calculate',
    displayName: 'Calculate COGS',
    description: 'Perform COGS calculations',
    resource: 'cogs',
    action: 'calculate',
    isSystemPermission: true,
  },
  {
    name: 'cogs.update',
    displayName: 'Update COGS',
    description: 'Update cost parameters',
    resource: 'cogs',
    action: 'update',
    isSystemPermission: true,
  },

  // User management permissions
  {
    name: 'users.read',
    displayName: 'View Users',
    description: 'View user information',
    resource: 'users',
    action: 'read',
    isSystemPermission: true,
  },
  {
    name: 'users.create',
    displayName: 'Create Users',
    description: 'Create new user accounts',
    resource: 'users',
    action: 'create',
    isSystemPermission: true,
  },
  {
    name: 'users.update',
    displayName: 'Update Users',
    description: 'Update user information',
    resource: 'users',
    action: 'update',
    isSystemPermission: true,
  },
  {
    name: 'users.delete',
    displayName: 'Delete Users',
    description: 'Delete user accounts',
    resource: 'users',
    action: 'delete',
    isSystemPermission: true,
  },
  {
    name: 'users.assign_roles',
    displayName: 'Assign Roles',
    description: 'Assign roles to users',
    resource: 'users',
    action: 'assign_roles',
    isSystemPermission: true,
  },

  // Role management permissions
  {
    name: 'roles.read',
    displayName: 'View Roles',
    description: 'View available roles',
    resource: 'roles',
    action: 'read',
    isSystemPermission: true,
  },
  {
    name: 'roles.create',
    displayName: 'Create Roles',
    description: 'Create custom roles',
    resource: 'roles',
    action: 'create',
    isSystemPermission: true,
  },
  {
    name: 'roles.update',
    displayName: 'Update Roles',
    description: 'Update role permissions',
    resource: 'roles',
    action: 'update',
    isSystemPermission: true,
  },
  {
    name: 'roles.delete',
    displayName: 'Delete Roles',
    description: 'Delete custom roles',
    resource: 'roles',
    action: 'delete',
    isSystemPermission: true,
  },

  // Product permissions
  {
    name: 'products.read',
    displayName: 'View Products',
    description: 'View products and product details',
    resource: 'products',
    action: 'read',
    isSystemPermission: true,
  },
  {
    name: 'products.create',
    displayName: 'Create Products',
    description: 'Create new products',
    resource: 'products',
    action: 'create',
    isSystemPermission: true,
  },
  {
    name: 'products.update',
    displayName: 'Update Products',
    description: 'Update existing products',
    resource: 'products',
    action: 'update',
    isSystemPermission: true,
  },
  {
    name: 'products.delete',
    displayName: 'Delete Products',
    description: 'Delete products',
    resource: 'products',
    action: 'delete',
    isSystemPermission: true,
  },

  // Ingredient permissions
  {
    name: 'ingredients.read',
    displayName: 'View Ingredients',
    description: 'View ingredients and ingredient details',
    resource: 'ingredients',
    action: 'read',
    isSystemPermission: true,
  },
  {
    name: 'ingredients.create',
    displayName: 'Create Ingredients',
    description: 'Create new ingredients',
    resource: 'ingredients',
    action: 'create',
    isSystemPermission: true,
  },
  {
    name: 'ingredients.update',
    displayName: 'Update Ingredients',
    description: 'Update existing ingredients',
    resource: 'ingredients',
    action: 'update',
    isSystemPermission: true,
  },
  {
    name: 'ingredients.delete',
    displayName: 'Delete Ingredients',
    description: 'Delete ingredients',
    resource: 'ingredients',
    action: 'delete',
    isSystemPermission: true,
  },

  // RBAC Permissions management
  {
    name: 'rbac_permissions.read',
    displayName: 'View RBAC Permissions',
    description: 'View system permissions',
    resource: 'rbac_permissions',
    action: 'read',
    isSystemPermission: true,
  },
  {
    name: 'rbac_permissions.create',
    displayName: 'Create RBAC Permissions',
    description: 'Create new system permissions',
    resource: 'rbac_permissions',
    action: 'create',
    isSystemPermission: true,
  },
  {
    name: 'rbac_permissions.update',
    displayName: 'Update RBAC Permissions',
    description: 'Update system permissions',
    resource: 'rbac_permissions',
    action: 'update',
    isSystemPermission: true,
  },
  {
    name: 'rbac_permissions.delete',
    displayName: 'Delete RBAC Permissions',
    description: 'Delete system permissions',
    resource: 'rbac_permissions',
    action: 'delete',
    isSystemPermission: true,
  },

  // System permissions
  {
    name: 'system.view_permissions',
    displayName: 'View System Permissions Info',
    description: 'View detailed information about permissions used in routes and components',
    resource: 'system',
    action: 'view_permissions',
    isSystemPermission: true,
  },
];

// Role-permission assignments
const rolePermissionAssignments = {
  super_admin: [
    // Super Admin gets all permissions
    'business.create', 'business.read', 'business.update', 'business.delete', 'business.manage_users',
    'inventory.read', 'inventory.create', 'inventory.update', 'inventory.delete',
    'products.read', 'products.create', 'products.update', 'products.delete',
    'ingredients.read', 'ingredients.create', 'ingredients.update', 'ingredients.delete',
    'categories.read', 'categories.create', 'categories.update', 'categories.delete',
    'cogs.read', 'cogs.calculate', 'cogs.update',
    'users.read', 'users.create', 'users.update', 'users.delete', 'users.assign_roles',
    'roles.read', 'roles.create', 'roles.update', 'roles.delete',
    'rbac_permissions.read', 'rbac_permissions.create', 'rbac_permissions.update', 'rbac_permissions.delete',
    'system.view_permissions',
  ],
  business_owner: [
    'business.read', 'business.update', 'business.manage_users',
    'inventory.read', 'inventory.create', 'inventory.update', 'inventory.delete',
    'products.read', 'products.create', 'products.update', 'products.delete',
    'ingredients.read', 'ingredients.create', 'ingredients.update', 'ingredients.delete',
    'categories.read', 'categories.create', 'categories.update', 'categories.delete',
    'cogs.read', 'cogs.calculate', 'cogs.update',
    'users.read', 'users.assign_roles',
    'roles.read',
    'system.view_permissions',
  ],
  business_manager: [
    'business.read', 'business.manage_users',
    'inventory.read', 'inventory.create', 'inventory.update', 'inventory.delete',
    'products.read', 'products.create', 'products.update', 'products.delete',
    'ingredients.read', 'ingredients.create', 'ingredients.update', 'ingredients.delete',
    'categories.read', 'categories.create', 'categories.update', 'categories.delete',
    'cogs.read', 'cogs.calculate', 'cogs.update',
    'users.read', 'users.assign_roles',
  ],
  inventory_manager: [
    'business.read',
    'inventory.read', 'inventory.create', 'inventory.update', 'inventory.delete',
    'products.read', 'products.create', 'products.update', 'products.delete',
    'ingredients.read', 'ingredients.create', 'ingredients.update', 'ingredients.delete',
    'categories.read', 'categories.create', 'categories.update',
    'cogs.read', 'cogs.calculate',
  ],
  staff: [
    'business.read',
    'inventory.read', 'inventory.create', 'inventory.update',
    'products.read', 'products.create', 'products.update',
    'ingredients.read', 'ingredients.create', 'ingredients.update',
    'categories.read',
    'cogs.read',
  ],
  viewer: [
    'business.read',
    'inventory.read',
    'products.read',
    'ingredients.read',
    'categories.read',
    'cogs.read',
  ],
};

export async function seedRBAC() {
  console.log('🌱 Seeding RBAC data...');

  try {
    // Insert roles
    console.log('📝 Creating roles...');
    const createdRoles = [];
    for (const role of defaultRoles) {
      const existing = await db.select().from(roles).where(eq(roles.name, role.name)).limit(1);
      if (existing.length === 0) {
        const created = await db.insert(roles).values(role).returning();
        createdRoles.push(created[0]);
        console.log(`  ✅ Created role: ${role.displayName}`);
      } else {
        createdRoles.push(existing[0]);
        console.log(`  ⏭️  Role already exists: ${role.displayName}`);
      }
    }

    // Insert permissions
    console.log('🔐 Creating permissions...');
    const createdPermissions = [];
    for (const permission of defaultPermissions) {
      const existing = await db.select().from(permissions).where(eq(permissions.name, permission.name)).limit(1);
      if (existing.length === 0) {
        const created = await db.insert(permissions).values(permission).returning();
        createdPermissions.push(created[0]);
        console.log(`  ✅ Created permission: ${permission.displayName}`);
      } else {
        createdPermissions.push(existing[0]);
        console.log(`  ⏭️  Permission already exists: ${permission.displayName}`);
      }
    }

    // Create role-permission assignments
    console.log('🔗 Assigning permissions to roles...');
    for (const [roleName, permissionNames] of Object.entries(rolePermissionAssignments)) {
      const role = createdRoles.find(r => r.name === roleName);
      if (!role) {
        console.log(`  ❌ Role not found: ${roleName}`);
        continue;
      }

      for (const permissionName of permissionNames) {
        const permission = createdPermissions.find(p => p.name === permissionName);
        if (!permission) {
          console.log(`  ❌ Permission not found: ${permissionName}`);
          continue;
        }

        // Check if assignment already exists
        const existing = await db
          .select()
          .from(rolePermissions)
          .where(
            and(
              eq(rolePermissions.roleId, role.id),
              eq(rolePermissions.permissionId, permission.id)
            )
          )
          .limit(1);

        if (existing.length === 0) {
          await db.insert(rolePermissions).values({
            roleId: role.id,
            permissionId: permission.id,
          });
          console.log(`  ✅ Assigned ${permissionName} to ${roleName}`);
        } else {
          console.log(`  ⏭️  Assignment already exists: ${permissionName} -> ${roleName}`);
        }
      }
    }

    console.log('🎉 RBAC seeding completed successfully!');
  } catch (error) {
    console.error('❌ Error seeding RBAC data:', error);
    throw error;
  }
}

// Run seeding if this file is executed directly
if (process.argv[1] && process.argv[1].endsWith('rbac-seed.ts')) {
  await seedRBAC();
  process.exit(0);
}