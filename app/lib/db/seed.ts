import { drizzle } from 'drizzle-orm/node-postgres';
import { reset } from 'drizzle-seed';
import { Pool } from 'pg';
import { randomUUID } from 'crypto';
import { auth } from '../auth.server';
import * as schema from './schema';
import { categories, businesses, ingredients, products, users, accounts, productIngredients } from './schema';
import { ProductServiceServer } from '../services/productService.server';
import { RBACService } from '../services/rbacService.server';
import { seedRBAC } from './rbac-seed';
import { roles, permissions, rolePermissions } from './schema';
import { eq } from 'drizzle-orm';

// Database connection for seeding
const connectionString = process.env.DATABASE_URL!;
const pool = new Pool({ connectionString });
const db = drizzle(pool, { schema });

// Seed data for categories
const ingredientCategories = [
  {
    name: 'Coffee Beans',
    description: 'Various types of coffee beans and grounds',
    type: 'ingredient' as const,
    color: '#8B4513',
    sortOrder: '1',
  },
  {
    name: 'Dairy',
    description: 'Milk, cream, and dairy products',
    type: 'ingredient' as const,
    color: '#F5F5DC',
    sortOrder: '2',
  },
  {
    name: 'Sweeteners',
    description: 'Sugar, syrups, and sweetening agents',
    type: 'ingredient' as const,
    color: '#FFD700',
    sortOrder: '3',
  },
  {
    name: 'Spices & Flavorings',
    description: 'Cinnamon, vanilla, cocoa, and other flavorings',
    type: 'ingredient' as const,
    color: '#D2691E',
    sortOrder: '4',
  },
  {
    name: 'Alternative Milks',
    description: 'Plant-based milk alternatives',
    type: 'ingredient' as const,
    color: '#90EE90',
    sortOrder: '5',
  },
  {
    name: 'Toppings',
    description: 'Whipped cream, marshmallows, and other toppings',
    type: 'ingredient' as const,
    color: '#FFB6C1',
    sortOrder: '6',
  },
  {
    name: 'Tea',
    description: 'Various types of tea leaves and tea bags',
    type: 'ingredient' as const,
    color: '#228B22',
    sortOrder: '7',
  },
  {
    name: 'Baking Ingredients',
    description: 'Flour, eggs, butter, and baking essentials',
    type: 'ingredient' as const,
    color: '#DEB887',
    sortOrder: '8',
  },
];

const productCategories = [
  {
    name: 'Hot Beverages',
    description: 'Coffee, tea, and other hot drinks',
    type: 'product' as const,
    color: '#CD853F',
    sortOrder: '1',
  },
  {
    name: 'Cold Beverages',
    description: 'Iced coffee, cold brew, and chilled drinks',
    type: 'product' as const,
    color: '#4682B4',
    sortOrder: '2',
  },
  {
    name: 'Pastries',
    description: 'Croissants, muffins, and baked goods',
    type: 'product' as const,
    color: '#F4A460',
    sortOrder: '3',
  },
  {
    name: 'Snacks',
    description: 'Light snacks and quick bites',
    type: 'product' as const,
    color: '#DDA0DD',
    sortOrder: '4',
  },
  {
    name: 'Specialty Drinks',
    description: 'Seasonal and signature beverages',
    type: 'product' as const,
    color: '#FF6347',
    sortOrder: '5',
  },
  {
    name: 'Desserts',
    description: 'Cakes, cookies, and sweet treats',
    type: 'product' as const,
    color: '#FF69B4',
    sortOrder: '6',
  },
  {
    name: 'Breakfast Items',
    description: 'Breakfast sandwiches, bagels, and morning foods',
    type: 'product' as const,
    color: '#FFA500',
    sortOrder: '7',
  },
  {
    name: 'Lunch Items',
    description: 'Sandwiches, salads, and lunch options',
    type: 'product' as const,
    color: '#32CD32',
    sortOrder: '8',
  },
];

// Sample ingredient data
const sampleIngredients = [
  // Coffee Beans
  { name: 'Arabica Coffee Beans', baseUnitCost: '25000', baseUnitQuantity: '1000', unit: 'g', category: 'Coffee Beans' },
  { name: 'Robusta Coffee Beans', baseUnitCost: '20000', baseUnitQuantity: '1000', unit: 'g', category: 'Coffee Beans' },
  { name: 'Espresso Blend', baseUnitCost: '30000', baseUnitQuantity: '1000', unit: 'g', category: 'Coffee Beans' },
  
  // Dairy
  { name: 'Whole Milk', baseUnitCost: '15000', baseUnitQuantity: '1000', unit: 'ml', category: 'Dairy' },
  { name: 'Heavy Cream', baseUnitCost: '25000', baseUnitQuantity: '500', unit: 'ml', category: 'Dairy' },
  { name: 'Skim Milk', baseUnitCost: '12000', baseUnitQuantity: '1000', unit: 'ml', category: 'Dairy' },
  
  // Sweeteners
  { name: 'White Sugar', baseUnitCost: '8000', baseUnitQuantity: '1000', unit: 'g', category: 'Sweeteners' },
  { name: 'Brown Sugar', baseUnitCost: '10000', baseUnitQuantity: '1000', unit: 'g', category: 'Sweeteners' },
  { name: 'Vanilla Syrup', baseUnitCost: '35000', baseUnitQuantity: '750', unit: 'ml', category: 'Sweeteners' },
  { name: 'Caramel Syrup', baseUnitCost: '35000', baseUnitQuantity: '750', unit: 'ml', category: 'Sweeteners' },
  
  // Spices & Flavorings
  { name: 'Cinnamon Powder', baseUnitCost: '15000', baseUnitQuantity: '100', unit: 'g', category: 'Spices & Flavorings' },
  { name: 'Vanilla Extract', baseUnitCost: '45000', baseUnitQuantity: '100', unit: 'ml', category: 'Spices & Flavorings' },
  { name: 'Cocoa Powder', baseUnitCost: '25000', baseUnitQuantity: '250', unit: 'g', category: 'Spices & Flavorings' },
  
  // Alternative Milks
  { name: 'Almond Milk', baseUnitCost: '18000', baseUnitQuantity: '1000', unit: 'ml', category: 'Alternative Milks' },
  { name: 'Oat Milk', baseUnitCost: '20000', baseUnitQuantity: '1000', unit: 'ml', category: 'Alternative Milks' },
  { name: 'Soy Milk', baseUnitCost: '16000', baseUnitQuantity: '1000', unit: 'ml', category: 'Alternative Milks' },

  // Additional ingredients for products
  { name: 'Coffee Beans', baseUnitCost: '150000', baseUnitQuantity: '1000', unit: 'g', category: 'Coffee Beans' }, // Generic coffee beans
  { name: 'Water', baseUnitCost: '1000', baseUnitQuantity: '1000', unit: 'ml', category: 'Dairy' }, // Water (categorized under Dairy for beverages)
  { name: 'Milk', baseUnitCost: '15000', baseUnitQuantity: '1000', unit: 'ml', category: 'Dairy' }, // Generic milk
  { name: 'Ice', baseUnitCost: '2000', baseUnitQuantity: '1000', unit: 'g', category: 'Dairy' }, // Ice (categorized under Dairy for beverages)
  { name: 'Flour', baseUnitCost: '12000', baseUnitQuantity: '1000', unit: 'g', category: 'Baking Ingredients' }, // Flour
  { name: 'Butter', baseUnitCost: '35000', baseUnitQuantity: '500', unit: 'g', category: 'Dairy' }, // Butter
  { name: 'Sugar', baseUnitCost: '15000', baseUnitQuantity: '1000', unit: 'g', category: 'Sweeteners' }, // Generic sugar
  { name: 'Blueberries', baseUnitCost: '45000', baseUnitQuantity: '250', unit: 'g', category: 'Toppings' }, // Blueberries (categorized as toppings)
  { name: 'Chocolate', baseUnitCost: '80000', baseUnitQuantity: '250', unit: 'g', category: 'Baking Ingredients' }, // Chocolate
];

// Sample product data with ingredient relationships
const sampleProducts = [
  // Hot Beverages
  {
    name: 'Espresso',
    description: 'Rich and bold espresso shot',
    note: 'Classic espresso shot',
    category: 'Hot Beverages',
    ingredients: [
      { name: 'Coffee Beans', quantity: 18 }, // 18g coffee beans
      { name: 'Water', quantity: 36 }, // 36ml water
    ]
  },
  {
    name: 'Americano',
    description: 'Espresso with hot water',
    note: 'Simple and clean',
    category: 'Hot Beverages',
    ingredients: [
      { name: 'Coffee Beans', quantity: 18 }, // 18g coffee beans
      { name: 'Water', quantity: 120 }, // 120ml water
    ]
  },
  {
    name: 'Cappuccino',
    description: 'Espresso with steamed milk and foam',
    note: 'Perfect milk foam',
    category: 'Hot Beverages',
    ingredients: [
      { name: 'Coffee Beans', quantity: 18 }, // 18g coffee beans
      { name: 'Milk', quantity: 150 }, // 150ml milk
      { name: 'Water', quantity: 36 }, // 36ml water
    ]
  },
  {
    name: 'Latte',
    description: 'Espresso with steamed milk',
    note: 'Smooth and creamy',
    category: 'Hot Beverages',
    ingredients: [
      { name: 'Coffee Beans', quantity: 18 }, // 18g coffee beans
      { name: 'Milk', quantity: 200 }, // 200ml milk
      { name: 'Water', quantity: 36 }, // 36ml water
    ]
  },

  // Cold Beverages
  {
    name: 'Iced Coffee',
    description: 'Cold brewed coffee over ice',
    note: 'Refreshing cold drink',
    category: 'Cold Beverages',
    ingredients: [
      { name: 'Coffee Beans', quantity: 20 }, // 20g coffee beans
      { name: 'Water', quantity: 150 }, // 150ml water
      { name: 'Ice', quantity: 100 }, // 100g ice
    ]
  },
  {
    name: 'Cold Brew',
    description: 'Smooth cold-extracted coffee',
    note: 'Smooth cold extraction',
    category: 'Cold Beverages',
    ingredients: [
      { name: 'Coffee Beans', quantity: 25 }, // 25g coffee beans
      { name: 'Water', quantity: 200 }, // 200ml water
      { name: 'Ice', quantity: 50 }, // 50g ice
    ]
  },
  {
    name: 'Iced Latte',
    description: 'Espresso with cold milk over ice',
    note: 'Cold version of latte',
    category: 'Cold Beverages',
    ingredients: [
      { name: 'Coffee Beans', quantity: 18 }, // 18g coffee beans
      { name: 'Milk', quantity: 180 }, // 180ml milk
      { name: 'Water', quantity: 36 }, // 36ml water
      { name: 'Ice', quantity: 80 }, // 80g ice
    ]
  },

  // Pastries
  {
    name: 'Croissant',
    description: 'Buttery, flaky pastry',
    note: 'French pastry',
    category: 'Pastries',
    ingredients: [
      { name: 'Flour', quantity: 60 }, // 60g flour
      { name: 'Butter', quantity: 25 }, // 25g butter
      { name: 'Sugar', quantity: 5 }, // 5g sugar
    ]
  },
  {
    name: 'Blueberry Muffin',
    description: 'Fresh blueberry muffin',
    note: 'Fresh blueberries',
    category: 'Pastries',
    ingredients: [
      { name: 'Flour', quantity: 80 }, // 80g flour
      { name: 'Blueberries', quantity: 30 }, // 30g blueberries
      { name: 'Sugar', quantity: 15 }, // 15g sugar
      { name: 'Butter', quantity: 20 }, // 20g butter
    ]
  },
  {
    name: 'Chocolate Croissant',
    description: 'Croissant filled with chocolate',
    note: 'Pain au chocolat',
    category: 'Pastries',
    ingredients: [
      { name: 'Flour', quantity: 60 }, // 60g flour
      { name: 'Butter', quantity: 25 }, // 25g butter
      { name: 'Chocolate', quantity: 15 }, // 15g chocolate
      { name: 'Sugar', quantity: 5 }, // 5g sugar
    ]
  }
];

/**
 * Validates that RBAC seeding was successful by checking:
 * 1. All required roles exist
 * 2. All required permissions exist
 * 3. Business owner role has permissions assigned
 */
async function validateRBACSeeding() {
  // Check if business_owner role exists
  const businessOwnerRole = await db.select().from(roles).where(eq(roles.name, 'business_owner')).limit(1);
  if (businessOwnerRole.length === 0) {
    throw new Error('business_owner role not found after seeding');
  }

  // Check if business_owner role has permissions
  const businessOwnerPermissions = await db
    .select({ permission: permissions.name })
    .from(rolePermissions)
    .innerJoin(permissions, eq(rolePermissions.permissionId, permissions.id))
    .where(eq(rolePermissions.roleId, businessOwnerRole[0].id));

  if (businessOwnerPermissions.length === 0) {
    throw new Error('business_owner role has no permissions assigned after seeding');
  }

  // Check for essential permissions
  const essentialPermissions = ['business.read', 'business.update', 'business.manage_users'];
  const assignedPermissionNames = businessOwnerPermissions.map(p => p.permission);
  
  for (const essential of essentialPermissions) {
    if (!assignedPermissionNames.includes(essential)) {
      throw new Error(`business_owner role missing essential permission: ${essential}`);
    }
  }

  console.log(`  ✅ business_owner role has ${businessOwnerPermissions.length} permissions assigned`);
}

export async function seedDatabase() {
  console.log('🌱 Starting database seeding...');

  try {
    // Reset database to clean state
    console.log('🧹 Resetting database...');
    await reset(db, schema);

    // Step 1: Create users with proper authentication credentials
    console.log('👤 Creating users with authentication...');

    // Get Better Auth context for password hashing
    const authContext = await auth.$context;
    const testPassword = 'password123'; // Common test password
    const hashedPassword = await authContext.password.hash(testPassword);

    const userData = [
      {
        id: randomUUID(),
        name: 'John Doe',
        email: '<EMAIL>',
        emailVerified: true, // Set to true for easier testing
        image: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: randomUUID(),
        name: 'Jane Smith',
        email: '<EMAIL>',
        emailVerified: true,
        image: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: randomUUID(),
        name: 'Mike Johnson',
        email: '<EMAIL>',
        emailVerified: true,
        image: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: randomUUID(),
        name: 'Sarah Wilson',
        email: '<EMAIL>',
        emailVerified: true,
        image: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: randomUUID(),
        name: 'Test User',
        email: '<EMAIL>',
        emailVerified: true,
        image: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    const insertedUsers = await db.insert(users).values(userData).returning();
    console.log(`✅ Created ${insertedUsers.length} users`);

    // Step 1.5: Create authentication accounts for each user
    console.log('🔐 Creating authentication accounts...');
    const accountData = insertedUsers.map(user => ({
      id: randomUUID(),
      accountId: user.email, // Use email as account ID for email/password auth
      providerId: 'credential', // Better Auth uses 'credential' for email/password
      userId: user.id,
      password: hashedPassword,
      createdAt: new Date(),
      updatedAt: new Date(),
    }));

    const insertedAccounts = await db.insert(accounts).values(accountData).returning();
    console.log(`✅ Created ${insertedAccounts.length} authentication accounts`);

    // Log test credentials for easy reference
    console.log('\n🔑 TEST CREDENTIALS:');
    insertedUsers.forEach(user => {
      console.log(`   📧 ${user.email} / 🔒 ${testPassword}`);
    });
    console.log('');

    // Step 2: Create businesses
    console.log('🏢 Creating businesses...');
    const businessData = [
      {
        name: 'KWACI Coffee House',
        description: 'Premium coffee experience with locally sourced beans',
        userId: insertedUsers[0].id,
      },
      {
        name: 'Brew & Bean Cafe',
        description: 'Cozy neighborhood cafe with artisanal drinks',
        userId: insertedUsers[1].id,
      },
      {
        name: 'Morning Glory Coffee',
        description: 'Modern coffee shop with specialty beverages',
        userId: insertedUsers[2].id,
      },
    ];

    const insertedBusinesses = await db.insert(businesses).values(businessData).returning();
    console.log(`✅ Created ${insertedBusinesses.length} businesses`);

    // Step 2.5: Seed RBAC data (roles and permissions)
    console.log('🔐 Seeding RBAC data...');
    try {
      await seedRBAC();
      console.log('✅ RBAC data seeded successfully');
      
      // Validate RBAC seeding was successful
      console.log('🔍 Validating RBAC seeding...');
      await validateRBACSeeding();
      console.log('✅ RBAC validation passed');
    } catch (error) {
      console.error('❌ RBAC seeding failed:', error);
       throw new Error(`RBAC seeding failed: ${error instanceof Error ? error.message : String(error)}`);
    }

    // Step 2.6: Assign business_owner role to business owners
    console.log('👑 Assigning business_owner roles...');
    let successfulAssignments = 0;
    for (const business of insertedBusinesses) {
      try {
        await RBACService.assignBusinessOwnerRole(business.userId, business.id);
        const businessOwner = insertedUsers.find(u => u.id === business.userId);
        console.log(`  ✅ Assigned business_owner role to ${businessOwner?.email} for ${business.name}`);
        
        // Validate the assignment was successful
        const hasRole = await RBACService.hasRole(business.userId, 'business_owner', business.id);
        if (!hasRole) {
          throw new Error('Role assignment validation failed');
        }
        
        successfulAssignments++;
      } catch (error) {
        console.error(`  ❌ Failed to assign business_owner role for business ${business.name}:`, error);
         throw new Error(`Business owner role assignment failed for ${business.name}: ${error instanceof Error ? error.message : String(error)}`);
      }
    }
    
    if (successfulAssignments !== insertedBusinesses.length) {
      throw new Error(`Only ${successfulAssignments}/${insertedBusinesses.length} business owner roles were assigned successfully`);
    }
    
    console.log('✅ All business owner roles assigned and validated successfully');

    // Step 2.7: Assign viewer role to Sarah Wilson for the first business
    console.log('👁️ Assigning viewer role...');
    try {
      const viewerUser = insertedUsers.find(u => u.email === '<EMAIL>');
      const firstBusiness = insertedBusinesses[0]; // Assign to first business (KWACI Coffee House)
      
      if (viewerUser && firstBusiness) {
        await RBACService.assignRole(viewerUser.id, 'viewer', insertedUsers[0].id, firstBusiness.id);
        console.log(`  ✅ Assigned viewer role to ${viewerUser.email} for ${firstBusiness.name}`);
        
        // Validate the assignment was successful
        const hasRole = await RBACService.hasRole(viewerUser.id, 'viewer', firstBusiness.id);
        if (!hasRole) {
          throw new Error('Viewer role assignment validation failed');
        }
        console.log('  ✅ Viewer role assignment validated successfully');
      } else {
        console.warn('⚠️ Could not find viewer user or first business for role assignment');
      }
    } catch (error) {
      console.error('❌ Failed to assign viewer role:', error);
      throw new Error(`Viewer role assignment failed: ${error instanceof Error ? error.message : String(error)}`);
    }

    // Step 3: Create categories for each business
    console.log('📂 Creating categories...');
    const allCategories = [...ingredientCategories, ...productCategories];
    const categoryData = [];

    for (const business of insertedBusinesses) {
      for (const category of allCategories) {
        categoryData.push({
          ...category,
          businessId: business.id,
        });
      }
    }

    const insertedCategories = await db.insert(categories).values(categoryData).returning();
    console.log(`✅ Created ${insertedCategories.length} categories`);

    // Step 4: Use drizzle-seed for ingredients and products with proper category references
    console.log('📊 Generating ingredients and products...');

    // Create ingredients manually to avoid unique constraint violations
    console.log('🥄 Creating ingredients...');
    const ingredientData = [];

    for (const business of insertedBusinesses) {
      // Get ingredient categories for this specific business
      const businessIngredientCategories = insertedCategories
        .filter(cat => cat.type === 'ingredient' && cat.businessId === business.id);
      
      for (const ingredient of sampleIngredients) {
        // Find the correct category by name for this business
        const categoryId = businessIngredientCategories.find(cat => cat.name === ingredient.category)?.id;
        
        if (!categoryId) {
          console.warn(`⚠️ Category "${ingredient.category}" not found for ingredient "${ingredient.name}" in business "${business.name}"`);  
        }
        
        ingredientData.push({
          name: ingredient.name,
          baseUnitCost: ingredient.baseUnitCost,
          baseUnitQuantity: ingredient.baseUnitQuantity,
          unit: ingredient.unit,
          categoryId: categoryId || null, // Use correct category ID or null if not found
          businessId: business.id,
          supplierInfo: 'Local Coffee Roasters',
          notes: 'High quality ingredients',
        });
      }
    }

    const insertedIngredients = await db.insert(ingredients).values(ingredientData).returning();
    console.log(`✅ Created ${insertedIngredients.length} ingredients`);

    // Create products manually to avoid unique constraint violations
    console.log('☕ Creating products...');
    const productData = [];

    for (const business of insertedBusinesses) {
      // Get categories for this business to map by name
      const businessCategories = insertedCategories.filter(cat =>
        cat.businessId === business.id && cat.type === 'product'
      );

      for (const product of sampleProducts) {
        // Find the correct category ID by name
        const categoryId = businessCategories.find(cat => cat.name === product.category)?.id;

        if (!categoryId) {
          console.warn(`⚠️ Category "${product.category}" not found for product "${product.name}"`);
        }

        productData.push({
          name: product.name,
          description: product.description,
          note: product.note,
          categoryId: categoryId || null, // Use correct category ID or null if not found
          isActive: true,
          cogsPerCup: null, // Will be calculated automatically based on ingredients
          businessId: business.id,
        });
      }
    }

    const insertedProducts = await db.insert(products).values(productData).returning();
    console.log(`✅ Created ${insertedProducts.length} products`);

    // Create product-ingredient relationships
    console.log('🔗 Creating product-ingredient relationships...');
    const productIngredientData = [];

    for (const createdProduct of insertedProducts) {
      // Find the original product data to get ingredients
      const originalProduct = sampleProducts.find(p => p.name === createdProduct.name);
      if (originalProduct && originalProduct.ingredients) {
        for (const ingredientData of originalProduct.ingredients) {
          // Find the ingredient by name
          const ingredient = insertedIngredients.find(ing => ing.name === ingredientData.name);
          if (ingredient) {
            productIngredientData.push({
              productId: createdProduct.id,
              ingredientId: ingredient.id,
              quantityNeeded: ingredientData.quantity.toString(),
            });
          }
        }
      }
    }

    // Insert product-ingredient relationships
    if (productIngredientData.length > 0) {
      await db.insert(productIngredients).values(productIngredientData);
      console.log(`✅ Created ${productIngredientData.length} product-ingredient relationships`);
    }

    // Calculate COGS for all products
    console.log('🧮 Calculating COGS for products...');
    for (const createdProduct of insertedProducts) {
      try {
        // Find the business owner for this product
        const business = insertedBusinesses.find(b => b.id === createdProduct.businessId);
        const businessOwner = business ? insertedUsers.find(u => u.id === business.userId) : null;
        const userId = businessOwner?.id || 'system';

        await ProductServiceServer.calculateAndUpdateProductCogs(
          createdProduct.id,
          createdProduct.businessId,
          undefined, // no transaction
          userId
        );
      } catch (error) {
        console.warn(`⚠️ Could not calculate COGS for product ${createdProduct.name}:`, error);
      }
    }
    console.log('✅ COGS calculated for all products');

    console.log('✅ Database seeding completed successfully!');
    console.log('📈 Generated data:');
    console.log(`   - ${insertedUsers.length} users with authentication`);
    console.log(`   - ${insertedAccounts.length} authentication accounts`);
    console.log(`   - ${insertedBusinesses.length} businesses`);
    console.log(`   - ${insertedCategories.length} categories`);
    console.log(`   - ${insertedIngredients.length} ingredients`);
    console.log(`   - ${insertedProducts.length} products`);
    console.log(`   - ${productIngredientData.length} product-ingredient relationships`);
    console.log(`   - COGS automatically calculated for all products`);
    console.log(`   - RBAC roles and permissions seeded`);
    console.log(`   - Business owner roles assigned to all business owners`);
    console.log(`   - Viewer role assigned to Sarah Wilson`);

    console.log('\n🎉 Ready for testing!');
    console.log('You can now log in with any of the test credentials above.');
    console.log('Each business owner has full access to their business.');
    console.log('Sarah Wilson (<EMAIL>) has read-only access to KWACI Coffee House.');
    
  } catch (error) {
    console.error('❌ Error during seeding:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('🎉 Seeding process completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Seeding failed:', error);
      process.exit(1);
    });
}
