import { db } from './connection';
import { businesses, users } from './schema';
import { RBACService } from '../services/rbacService.server';
import { eq } from 'drizzle-orm';

/**
 * Migration script to assign business_owner role to existing business owners
 * This should be run after the RBAC system is implemented to ensure all
 * existing business owners have the proper role assigned.
 */
export async function migrateBusinessOwners() {
  console.log('🔄 Starting business owner role migration...');

  try {
    // Get all businesses with their owners
    const businessesWithOwners = await db
      .select({
        businessId: businesses.id,
        businessName: businesses.name,
        userId: businesses.userId,
        userName: users.name,
        userEmail: users.email,
      })
      .from(businesses)
      .innerJoin(users, eq(businesses.userId, users.id));

    console.log(`📊 Found ${businessesWithOwners.length} businesses to process`);

    let successCount = 0;
    let errorCount = 0;

    for (const business of businessesWithOwners) {
      try {
        // Check if user already has business_owner role for this business
        const hasRole = await RBACService.hasRole(
          business.userId,
          'business_owner',
          business.businessId
        );

        if (hasRole) {
          console.log(
            `  ⏭️  User ${business.userName} (${business.userEmail}) already has business_owner role for "${business.businessName}"`
          );
          continue;
        }

        // Assign business_owner role
        await RBACService.assignBusinessOwnerRole(
          business.userId,
          business.businessId
        );

        console.log(
          `  ✅ Assigned business_owner role to ${business.userName} (${business.userEmail}) for "${business.businessName}"`
        );
        successCount++;
      } catch (error) {
        console.error(
          `  ❌ Failed to assign role to ${business.userName} (${business.userEmail}) for "${business.businessName}":`,
          error
        );
        errorCount++;
      }
    }

    console.log('\n📈 Migration Summary:');
    console.log(`  ✅ Successfully assigned: ${successCount}`);
    console.log(`  ❌ Errors: ${errorCount}`);
    console.log(`  📊 Total processed: ${businessesWithOwners.length}`);

    if (errorCount === 0) {
      console.log('🎉 Business owner role migration completed successfully!');
    } else {
      console.log('⚠️  Migration completed with some errors. Please review the logs above.');
    }
  } catch (error) {
    console.error('❌ Error during business owner migration:', error);
    throw error;
  }
}

// Run migration if this file is executed directly
if (process.argv[1] && process.argv[1].endsWith('migrate-business-owners.ts')) {
  await migrateBusinessOwners();
  process.exit(0);
}