import { pgTable, text, timestamp, uuid, index, unique, numeric, boolean } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// Better Auth Tables
// User table
export const users = pgTable('user', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  email: text('email').notNull().unique(),
  emailVerified: boolean('emailVerified').notNull().default(false),
  image: text('image'),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  updatedAt: timestamp('updatedAt').notNull().defaultNow(),
  firstName: text('firstName'),
  lastName: text('lastName'),
});

// Session table
export const sessions = pgTable('session', {
  id: text('id').primaryKey(),
  expiresAt: timestamp('expiresAt').notNull(),
  token: text('token').notNull().unique(),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  updatedAt: timestamp('updatedAt').notNull().defaultNow(),
  ipAddress: text('ipAddress'),
  userAgent: text('userAgent'),
  userId: text('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
}, (table) => ({
  userIdIdx: index('idx_session_userId').on(table.userId),
  tokenIdx: index('idx_session_token').on(table.token),
}));

// Account table (for OAuth and password authentication)
export const accounts = pgTable('account', {
  id: text('id').primaryKey(),
  accountId: text('accountId').notNull(),
  providerId: text('providerId').notNull(),
  userId: text('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
  accessToken: text('accessToken'),
  refreshToken: text('refreshToken'),
  idToken: text('idToken'),
  accessTokenExpiresAt: timestamp('accessTokenExpiresAt'),
  refreshTokenExpiresAt: timestamp('refreshTokenExpiresAt'),
  scope: text('scope'),
  password: text('password'),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  updatedAt: timestamp('updatedAt').notNull().defaultNow(),
}, (table) => ({
  userIdIdx: index('idx_account_userId').on(table.userId),
  providerIdIdx: index('idx_account_providerId').on(table.providerId),
}));

// Verification table (for email verification, password reset, etc.)
export const verifications = pgTable('verification', {
  id: text('id').primaryKey(),
  identifier: text('identifier').notNull(),
  value: text('value').notNull(),
  expiresAt: timestamp('expiresAt').notNull(),
  createdAt: timestamp('createdAt').defaultNow(),
  updatedAt: timestamp('updatedAt').defaultNow(),
}, (table) => ({
  identifierIdx: index('idx_verification_identifier').on(table.identifier),
}));

// Business table
export const businesses = pgTable('business', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: text('name').notNull(),
  description: text('description'),
  note: text('note'),
  currency: text('currency').notNull().default('IDR'),
  logo: text('logo'),
  userId: text('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  updatedAt: timestamp('updatedAt').notNull().defaultNow(),
}, (table) => ({
  // Index for efficient user queries
  userIdIdx: index('business_user_id_idx').on(table.userId),
  // Unique constraint to prevent duplicate business names per user
  userBusinessNameUnique: unique('business_user_name_unique').on(table.userId, table.name),
}));



// Ingredients table
export const ingredients = pgTable('ingredient', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: text('name').notNull(),
  baseUnitCost: numeric('baseUnitCost', { precision: 10, scale: 2 }).notNull(),
  baseUnitQuantity: numeric('baseUnitQuantity', { precision: 10, scale: 4 }).notNull().default('1'),
  unit: text('unit').notNull(),
  categoryId: uuid('categoryId').references(() => categories.id, { onDelete: 'set null' }),
  supplierInfo: text('supplierInfo'),
  notes: text('notes'),
  isActive: boolean('isActive').notNull().default(true),
  businessId: uuid('businessId').notNull().references(() => businesses.id, { onDelete: 'cascade' }),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  updatedAt: timestamp('updatedAt').notNull().defaultNow(),
}, (table) => ({
  // Index for efficient business queries
  businessIdIdx: index('ingredient_business_id_idx').on(table.businessId),
  // Unique constraint to prevent duplicate ingredient names per business
  businessIngredientNameUnique: unique('ingredient_business_name_unique').on(table.businessId, table.name),
}));

// Products table
export const products = pgTable('product', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: text('name').notNull(),
  description: text('description'),
  note: text('note'),
  categoryId: uuid('categoryId').references(() => categories.id, { onDelete: 'set null' }),
  isActive: boolean('isActive').notNull().default(true), // For soft deletion
  cogsPerCup: numeric('cogsPerCup', { precision: 10, scale: 2 }), // Cost of goods sold per cup (calculated)
  businessId: uuid('businessId').notNull().references(() => businesses.id, { onDelete: 'cascade' }),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  updatedAt: timestamp('updatedAt').notNull().defaultNow(),
}, (table) => ({
  // Index for efficient business queries
  businessIdIdx: index('product_business_id_idx').on(table.businessId),
  // Unique constraint to prevent duplicate product names per business
  businessProductNameUnique: unique('product_business_name_unique').on(table.businessId, table.name),
}));

// Product-Ingredients junction table (many-to-many relationship)
export const productIngredients = pgTable('product_ingredient', {
  id: uuid('id').primaryKey().defaultRandom(),
  productId: uuid('productId').notNull().references(() => products.id, { onDelete: 'cascade' }),
  ingredientId: uuid('ingredientId').notNull().references(() => ingredients.id, { onDelete: 'cascade' }),
  quantityNeeded: numeric('quantityNeeded', { precision: 10, scale: 4 }).notNull(),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
}, (table) => ({
  // Index for efficient product queries
  productIdIdx: index('product_ingredient_product_id_idx').on(table.productId),
  // Index for efficient ingredient queries
  ingredientIdIdx: index('product_ingredient_ingredient_id_idx').on(table.ingredientId),
  // Unique constraint to prevent duplicate ingredient assignments per product
  productIngredientUnique: unique('product_ingredient_unique').on(table.productId, table.ingredientId),
}));

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  businesses: many(businesses),
  sessions: many(sessions),
  accounts: many(accounts),
}));

export const sessionsRelations = relations(sessions, ({ one }) => ({
  user: one(users, {
    fields: [sessions.userId],
    references: [users.id],
  }),
}));

export const accountsRelations = relations(accounts, ({ one }) => ({
  user: one(users, {
    fields: [accounts.userId],
    references: [users.id],
  }),
}));

export const businessesRelations = relations(businesses, ({ one, many }) => ({
  user: one(users, {
    fields: [businesses.userId],
    references: [users.id],
  }),
  ingredients: many(ingredients),
  products: many(products),
  categories: many(categories),
  productCogs: many(productCogs),
  productCogsHistory: many(productCogsHistory),
}));

export const ingredientsRelations = relations(ingredients, ({ one, many }) => ({
  business: one(businesses, {
    fields: [ingredients.businessId],
    references: [businesses.id],
  }),
  productIngredients: many(productIngredients),
}));

export const productsRelations = relations(products, ({ one, many }) => ({
  business: one(businesses, {
    fields: [products.businessId],
    references: [businesses.id],
  }),
  productIngredients: many(productIngredients),
  productCogs: many(productCogs),
  productCogsHistory: many(productCogsHistory),
}));

export const productIngredientsRelations = relations(productIngredients, ({ one }) => ({
  product: one(products, {
    fields: [productIngredients.productId],
    references: [products.id],
  }),
  ingredient: one(ingredients, {
    fields: [productIngredients.ingredientId],
    references: [ingredients.id],
  }),
}));

// Universal Categories table for flexible categorization
export const categories = pgTable('category', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: text('name').notNull(),
  description: text('description'),
  type: text('type').notNull(), // 'ingredient', 'product', 'supplier', etc.
  parentId: uuid('parentId'), // For hierarchical categories - will be set up as foreign key in relations
  color: text('color'), // Hex color code for UI
  sortOrder: numeric('sortOrder', { precision: 5, scale: 0 }).default('0'),
  isActive: boolean('isActive').notNull().default(true),
  businessId: uuid('businessId').notNull().references(() => businesses.id, { onDelete: 'cascade' }),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  updatedAt: timestamp('updatedAt').notNull().defaultNow(),
}, (table) => ({
  // Index for efficient business queries
  businessIdIdx: index('category_business_id_idx').on(table.businessId),
  // Index for efficient type queries
  typeIdx: index('category_type_idx').on(table.type),
  // Unique constraint to prevent duplicate category names per business and type
  businessTypeNameUnique: unique('category_business_type_name_unique').on(table.businessId, table.type, table.name),
}));

// COGS (Cost of Goods Sold) tracking table
export const productCogs = pgTable('product_cogs', {
  id: uuid('id').primaryKey().defaultRandom(),
  productId: uuid('productId').notNull().references(() => products.id, { onDelete: 'cascade' }),
  businessId: uuid('businessId').notNull().references(() => businesses.id, { onDelete: 'cascade' }),

  // Cost breakdown
  ingredientCosts: numeric('ingredientCosts', { precision: 12, scale: 4 }).notNull().default('0'),
  laborCosts: numeric('laborCosts', { precision: 12, scale: 4 }).notNull().default('0'),
  overheadCosts: numeric('overheadCosts', { precision: 12, scale: 4 }).notNull().default('0'),
  totalCogs: numeric('totalCogs', { precision: 12, scale: 4 }).notNull().default('0'),

  // Calculation metadata
  calculationDate: timestamp('calculationDate').notNull().defaultNow(),
  calculationMethod: text('calculationMethod').notNull().default('automatic'), // 'automatic', 'manual', 'estimated'
  isActive: boolean('isActive').notNull().default(true),

  // Audit trail
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  updatedAt: timestamp('updatedAt').notNull().defaultNow(),
}, (table) => ({
  // Index for efficient product queries
  productIdIdx: index('product_cogs_product_id_idx').on(table.productId),
  // Index for efficient business queries
  businessIdIdx: index('product_cogs_business_id_idx').on(table.businessId),
  // Index for efficient date-based queries
  calculationDateIdx: index('product_cogs_calculation_date_idx').on(table.calculationDate),
  // Unique constraint to ensure one active COGS record per product
  productActiveUnique: unique('product_cogs_product_active_unique').on(table.productId, table.isActive),
}));

// COGS History table for audit trail and historical tracking
export const productCogsHistory = pgTable('product_cogs_history', {
  id: uuid('id').primaryKey().defaultRandom(),
  productCogsId: uuid('productCogsId').notNull().references(() => productCogs.id, { onDelete: 'cascade' }),
  productId: uuid('productId').notNull().references(() => products.id, { onDelete: 'cascade' }),
  businessId: uuid('businessId').notNull().references(() => businesses.id, { onDelete: 'cascade' }),

  // Historical cost data
  previousIngredientCosts: numeric('previousIngredientCosts', { precision: 12, scale: 4 }),
  previousLaborCosts: numeric('previousLaborCosts', { precision: 12, scale: 4 }),
  previousOverheadCosts: numeric('previousOverheadCosts', { precision: 12, scale: 4 }),
  previousTotalCogs: numeric('previousTotalCogs', { precision: 12, scale: 4 }),

  newIngredientCosts: numeric('newIngredientCosts', { precision: 12, scale: 4 }).notNull(),
  newLaborCosts: numeric('newLaborCosts', { precision: 12, scale: 4 }).notNull(),
  newOverheadCosts: numeric('newOverheadCosts', { precision: 12, scale: 4 }).notNull(),
  newTotalCogs: numeric('newTotalCogs', { precision: 12, scale: 4 }).notNull(),

  // Change metadata
  changeReason: text('changeReason'), // 'ingredient_cost_change', 'recipe_change', 'manual_adjustment', etc.
  changeDescription: text('changeDescription'),
  triggeredBy: text('triggeredBy'), // 'system', 'user_id', 'batch_update', etc.

  createdAt: timestamp('createdAt').notNull().defaultNow(),
}, (table) => ({
  // Index for efficient product queries
  productIdIdx: index('product_cogs_history_product_id_idx').on(table.productId),
  // Index for efficient business queries
  businessIdIdx: index('product_cogs_history_business_id_idx').on(table.businessId),
  // Index for efficient date-based queries
  createdAtIdx: index('product_cogs_history_created_at_idx').on(table.createdAt),
}));

// Categories relations
export const categoriesRelations = relations(categories, ({ one, many }) => ({
  business: one(businesses, {
    fields: [categories.businessId],
    references: [businesses.id],
  }),
  parent: one(categories, {
    fields: [categories.parentId],
    references: [categories.id],
    relationName: 'categoryParent',
  }),
  children: many(categories, {
    relationName: 'categoryParent',
  }),
}));

// Product COGS relations
export const productCogsRelations = relations(productCogs, ({ one, many }) => ({
  product: one(products, {
    fields: [productCogs.productId],
    references: [products.id],
  }),
  business: one(businesses, {
    fields: [productCogs.businessId],
    references: [businesses.id],
  }),
  history: many(productCogsHistory),
}));

// Product COGS History relations
export const productCogsHistoryRelations = relations(productCogsHistory, ({ one }) => ({
  productCogs: one(productCogs, {
    fields: [productCogsHistory.productCogsId],
    references: [productCogs.id],
  }),
  product: one(products, {
    fields: [productCogsHistory.productId],
    references: [products.id],
  }),
  business: one(businesses, {
    fields: [productCogsHistory.businessId],
    references: [businesses.id],
  }),
}));

// RBAC Tables

// Roles table
export const roles = pgTable('role', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: text('name').notNull().unique(),
  displayName: text('displayName').notNull(),
  description: text('description'),
  isSystemRole: boolean('isSystemRole').notNull().default(false),
  isActive: boolean('isActive').notNull().default(true),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  updatedAt: timestamp('updatedAt').notNull().defaultNow(),
}, (table) => ({
  nameIdx: index('idx_role_name').on(table.name),
  activeIdx: index('idx_role_active').on(table.isActive),
}));

// Permissions table
export const permissions = pgTable('permission', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: text('name').notNull().unique(),
  displayName: text('displayName').notNull(),
  description: text('description'),
  resource: text('resource').notNull(),
  action: text('action').notNull(),
  isSystemPermission: boolean('isSystemPermission').notNull().default(false),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  updatedAt: timestamp('updatedAt').notNull().defaultNow(),
}, (table) => ({
  resourceActionIdx: index('idx_permission_resource_action').on(table.resource, table.action),
}));

// Role-Permission junction table
export const rolePermissions = pgTable('role_permission', {
  id: uuid('id').primaryKey().defaultRandom(),
  roleId: uuid('roleId').notNull().references(() => roles.id, { onDelete: 'cascade' }),
  permissionId: uuid('permissionId').notNull().references(() => permissions.id, { onDelete: 'cascade' }),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
}, (table) => ({
  roleIdx: index('idx_role_permission_role').on(table.roleId),
  permissionIdx: index('idx_role_permission_permission').on(table.permissionId),
  rolePermissionUnique: unique('role_permission_unique').on(table.roleId, table.permissionId),
}));

// User-Role assignments table
export const userRoles = pgTable('user_role', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: text('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
  roleId: uuid('roleId').notNull().references(() => roles.id, { onDelete: 'cascade' }),
  businessId: uuid('businessId').references(() => businesses.id, { onDelete: 'cascade' }),
  assignedBy: text('assignedBy').references(() => users.id),
  assignedAt: timestamp('assignedAt').notNull().defaultNow(),
  expiresAt: timestamp('expiresAt'),
  isActive: boolean('isActive').notNull().default(true),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  updatedAt: timestamp('updatedAt').notNull().defaultNow(),
}, (table) => ({
  userIdx: index('idx_user_role_user').on(table.userId),
  roleIdx: index('idx_user_role_role').on(table.roleId),
  businessIdx: index('idx_user_role_business').on(table.businessId),
  activeIdx: index('idx_user_role_active').on(table.isActive),
  userRoleBusinessUnique: unique('user_role_business_unique').on(table.userId, table.roleId, table.businessId),
}));

// RBAC Relations
export const rolesRelations = relations(roles, ({ many }) => ({
  rolePermissions: many(rolePermissions),
  userRoles: many(userRoles),
}));

export const permissionsRelations = relations(permissions, ({ many }) => ({
  rolePermissions: many(rolePermissions),
}));

export const rolePermissionsRelations = relations(rolePermissions, ({ one }) => ({
  role: one(roles, {
    fields: [rolePermissions.roleId],
    references: [roles.id],
  }),
  permission: one(permissions, {
    fields: [rolePermissions.permissionId],
    references: [permissions.id],
  }),
}));

export const userRolesRelations = relations(userRoles, ({ one }) => ({
  user: one(users, {
    fields: [userRoles.userId],
    references: [users.id],
  }),
  role: one(roles, {
    fields: [userRoles.roleId],
    references: [roles.id],
  }),
  business: one(businesses, {
    fields: [userRoles.businessId],
    references: [businesses.id],
  }),
  assignedByUser: one(users, {
    fields: [userRoles.assignedBy],
    references: [users.id],
    relationName: 'assignedByUser',
  }),
}));

// User Invitations table
export const userInvitations = pgTable('user_invitation', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: text('email').notNull(),
  businessId: uuid('businessId').notNull().references(() => businesses.id, { onDelete: 'cascade' }),
  roleId: uuid('roleId').notNull().references(() => roles.id, { onDelete: 'cascade' }),
  invitedBy: text('invitedBy').notNull().references(() => users.id, { onDelete: 'cascade' }),
  invitationToken: text('invitationToken').notNull().unique(),
  status: text('status', { enum: ['pending', 'accepted', 'expired', 'cancelled'] }).notNull().default('pending'),
  expiresAt: timestamp('expiresAt').notNull(),
  acceptedAt: timestamp('acceptedAt'),
  acceptedBy: text('acceptedBy').references(() => users.id),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  updatedAt: timestamp('updatedAt').notNull().defaultNow(),
}, (table) => ({
  emailIdx: index('idx_invitation_email').on(table.email),
  businessIdx: index('idx_invitation_business').on(table.businessId),
  tokenIdx: index('idx_invitation_token').on(table.invitationToken),
  statusIdx: index('idx_invitation_status').on(table.status),
  expiresIdx: index('idx_invitation_expires').on(table.expiresAt),
}));

export const userInvitationsRelations = relations(userInvitations, ({ one }) => ({
  business: one(businesses, {
    fields: [userInvitations.businessId],
    references: [businesses.id],
  }),
  role: one(roles, {
    fields: [userInvitations.roleId],
    references: [roles.id],
  }),
  invitedByUser: one(users, {
    fields: [userInvitations.invitedBy],
    references: [users.id],
    relationName: 'invitedByUser',
  }),
  acceptedByUser: one(users, {
    fields: [userInvitations.acceptedBy],
    references: [users.id],
    relationName: 'acceptedByUser',
  }),
}));

export type UserInvitation = typeof userInvitations.$inferSelect;
export type NewUserInvitation = typeof userInvitations.$inferInsert;

// Update existing relations to include RBAC
export const usersRelationsUpdated = relations(users, ({ many }) => ({
  businesses: many(businesses),
  sessions: many(sessions),
  accounts: many(accounts),
  userRoles: many(userRoles),
  assignedRoles: many(userRoles, { relationName: 'assignedByUser' }),
}));

// Types
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type Session = typeof sessions.$inferSelect;
export type NewSession = typeof sessions.$inferInsert;
export type Account = typeof accounts.$inferSelect;
export type NewAccount = typeof accounts.$inferInsert;
export type Verification = typeof verifications.$inferSelect;
export type NewVerification = typeof verifications.$inferInsert;
export type Business = typeof businesses.$inferSelect;
export type NewBusiness = typeof businesses.$inferInsert;

export type Ingredient = typeof ingredients.$inferSelect;
export type NewIngredient = typeof ingredients.$inferInsert;
export type Product = typeof products.$inferSelect;
export type NewProduct = typeof products.$inferInsert;
export type ProductIngredient = typeof productIngredients.$inferSelect;
export type NewProductIngredient = typeof productIngredients.$inferInsert;

export type Category = typeof categories.$inferSelect;
export type NewCategory = typeof categories.$inferInsert;
export type ProductCogs = typeof productCogs.$inferSelect;
export type NewProductCogs = typeof productCogs.$inferInsert;
export type ProductCogsHistory = typeof productCogsHistory.$inferSelect;
export type NewProductCogsHistory = typeof productCogsHistory.$inferInsert;

// RBAC Types
export type Role = typeof roles.$inferSelect;
export type NewRole = typeof roles.$inferInsert;
export type Permission = typeof permissions.$inferSelect;
export type NewPermission = typeof permissions.$inferInsert;
export type RolePermission = typeof rolePermissions.$inferSelect;
export type NewRolePermission = typeof rolePermissions.$inferInsert;
export type UserRole = typeof userRoles.$inferSelect;
export type NewUserRole = typeof userRoles.$inferInsert;
