import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import translation files
import commonEn from '../locales/en/common.json';
import authEn from '../locales/en/auth.json';
import navigationEn from '../locales/en/navigation.json';
import dashboardEn from '../locales/en/dashboard.json';
import businessEn from '../locales/en/business.json';
import cogsEn from '../locales/en/cogs.json';
import sessionEn from '../locales/en/session.json';
import inventoryEn from '../locales/en/inventory.json';

import commonId from '../locales/id/common.json';
import authId from '../locales/id/auth.json';
import navigationId from '../locales/id/navigation.json';
import dashboardId from '../locales/id/dashboard.json';
import businessId from '../locales/id/business.json';
import cogsId from '../locales/id/cogs.json';
import sessionId from '../locales/id/session.json';
import inventoryId from '../locales/id/inventory.json';

// Define resources
const resources = {
  en: {
    common: commonEn,
    auth: authEn,
    navigation: navigationEn,
    dashboard: dashboardEn,
    business: businessEn,
    cogs: cogsEn,
    session: sessionEn,
    inventory: inventoryEn,
  },
  id: {
    common: commonId,
    auth: authId,
    navigation: navigationId,
    dashboard: dashboardId,
    business: businessId,
    cogs: cogsId,
    session: sessionId,
    inventory: inventoryId,
  },
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'en',
    defaultNS: 'common',
    ns: ['common', 'auth', 'navigation', 'dashboard', 'business', 'cogs', 'session', 'inventory'],
    
    detection: {
      order: ['localStorage', 'navigator'],
      caches: ['localStorage'],
      lookupLocalStorage: 'language',
    },

    interpolation: {
      escapeValue: false, // React already escapes values
    },

    react: {
      useSuspense: true,
    },
  });

export default i18n;
