/**
 * Enhanced rate limiting middleware for Better Auth endpoints
 * 
 * This module provides:
 * - Advanced rate limiting with Redis backend
 * - Different limits for different endpoints
 * - IP-based and user-based rate limiting
 * - Comprehensive error handling and logging
 * - Integration with Better Auth error handling
 */

import { getRateLimiterForPath, getRateLimitIdentifier, checkRateLimit, createRateLimitErrorResponse } from './redis.server';
import { handleAuthError } from './utils/authErrorHandler';
import type { TFunction } from 'react-i18next';

export interface RateLimitResult {
  allowed: boolean;
  response?: Response;
  retryAfter?: number;
  remaining?: number;
  limit?: number;
  reset?: Date;
}

/**
 * Enhanced rate limiting check with Better Auth integration
 */
export async function checkAuthRateLimit(
  request: Request,
  t?: TFunction
): Promise<RateLimitResult> {
  try {
    const url = new URL(request.url);
    const path = url.pathname;
    
    // Get the appropriate rate limiter for this endpoint
    const rateLimiter = getRateLimiterForPath(path);
    
    // Get identifier for rate limiting (IP address)
    const identifier = getRateLimitIdentifier(request);
    
    // Check rate limit
    const result = await checkRateLimit(rateLimiter, identifier);
    
    if (!result.success) {
      // Create rate limit error response
      const errorMessage = t 
        ? t('auth:forms.login.messages.rateLimited')
        : 'Too many requests. Please wait before trying again.';
      
      const response = new Response(
        JSON.stringify({
          message: errorMessage,
          code: 'RATE_LIMITED',
          retryAfter: result.retryAfter,
        }),
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            'X-Retry-After': result.retryAfter?.toString() || '60',
            'Retry-After': result.retryAfter?.toString() || '60',
            'X-RateLimit-Limit': result.limit.toString(),
            'X-RateLimit-Remaining': result.remaining.toString(),
            'X-RateLimit-Reset': result.reset.getTime().toString(),
          },
        }
      );
      
      return {
        allowed: false,
        response,
        retryAfter: result.retryAfter,
        remaining: result.remaining,
        limit: result.limit,
        reset: result.reset,
      };
    }
    
    return {
      allowed: true,
      remaining: result.remaining,
      limit: result.limit,
      reset: result.reset,
    };
  } catch (error) {
    console.error('Rate limit check error:', error);
    // On error, allow the request (fail open)
    return { allowed: true };
  }
}

/**
 * Add rate limit headers to response
 */
export function addRateLimitHeaders(
  response: Response,
  rateLimitResult: RateLimitResult
): Response {
  if (rateLimitResult.limit !== undefined) {
    response.headers.set('X-RateLimit-Limit', rateLimitResult.limit.toString());
  }
  if (rateLimitResult.remaining !== undefined) {
    response.headers.set('X-RateLimit-Remaining', rateLimitResult.remaining.toString());
  }
  if (rateLimitResult.reset) {
    response.headers.set('X-RateLimit-Reset', rateLimitResult.reset.getTime().toString());
  }
  
  return response;
}

/**
 * Rate limiting middleware wrapper
 */
export function withRateLimit(
  handler: (request: Request) => Promise<Response>,
  options: {
    skipPaths?: string[];
    t?: TFunction;
  } = {}
) {
  return async (request: Request): Promise<Response> => {
    const url = new URL(request.url);
    const path = url.pathname;
    
    // Skip rate limiting for certain paths
    if (options.skipPaths?.some(skipPath => path.includes(skipPath))) {
      return handler(request);
    }
    
    // Check rate limit
    const rateLimitResult = await checkAuthRateLimit(request, options.t);
    
    if (!rateLimitResult.allowed && rateLimitResult.response) {
      return rateLimitResult.response;
    }
    
    // Process the request
    const response = await handler(request);
    
    // Add rate limit headers to successful responses
    return addRateLimitHeaders(response, rateLimitResult);
  };
}

/**
 * User-based rate limiting (for authenticated requests)
 */
export async function checkUserRateLimit(
  userId: string,
  endpoint: string,
  customRate?: number
): Promise<RateLimitResult> {
  try {
    const rateLimiter = getRateLimiterForPath(endpoint);
    const identifier = `user:${userId}`;
    
    const result = await checkRateLimit(rateLimiter, identifier, customRate);
    
    if (!result.success) {
      return {
        allowed: false,
        retryAfter: result.retryAfter,
        remaining: result.remaining,
        limit: result.limit,
        reset: result.reset,
      };
    }
    
    return {
      allowed: true,
      remaining: result.remaining,
      limit: result.limit,
      reset: result.reset,
    };
  } catch (error) {
    console.error('User rate limit check error:', error);
    return { allowed: true };
  }
}

/**
 * API key based rate limiting
 */
export async function checkApiKeyRateLimit(
  apiKey: string,
  endpoint: string,
  customRate?: number
): Promise<RateLimitResult> {
  try {
    const rateLimiter = getRateLimiterForPath(endpoint);
    const identifier = `apikey:${apiKey}`;
    
    const result = await checkRateLimit(rateLimiter, identifier, customRate);
    
    if (!result.success) {
      return {
        allowed: false,
        retryAfter: result.retryAfter,
        remaining: result.remaining,
        limit: result.limit,
        reset: result.reset,
      };
    }
    
    return {
      allowed: true,
      remaining: result.remaining,
      limit: result.limit,
      reset: result.reset,
    };
  } catch (error) {
    console.error('API key rate limit check error:', error);
    return { allowed: true };
  }
}

/**
 * Endpoint-specific rate limiting configurations
 */
export const RATE_LIMIT_CONFIGS = {
  '/sign-in/email': {
    window: 15 * 60, // 15 minutes
    max: 5, // 5 attempts per 15 minutes
    description: 'Login attempts',
  },
  '/sign-up/email': {
    window: 60 * 60, // 1 hour
    max: 3, // 3 registrations per hour
    description: 'Registration attempts',
  },
  '/forget-password': {
    window: 60 * 60, // 1 hour
    max: 3, // 3 password reset requests per hour
    description: 'Password reset requests',
  },
  '/reset-password': {
    window: 60 * 60, // 1 hour
    max: 5, // 5 password reset attempts per hour
    description: 'Password reset attempts',
  },
  '/get-session': {
    window: 60, // 1 minute
    max: 100, // 100 session checks per minute
    description: 'Session checks',
  },
  '/sign-out': {
    window: 60, // 1 minute
    max: 10, // 10 logout attempts per minute
    description: 'Logout attempts',
  },
} as const;

/**
 * Get rate limit configuration for a specific endpoint
 */
export function getRateLimitConfig(path: string) {
  for (const [endpoint, config] of Object.entries(RATE_LIMIT_CONFIGS)) {
    if (path.includes(endpoint)) {
      return config;
    }
  }
  
  // Default configuration
  return {
    window: 60,
    max: 60,
    description: 'General API requests',
  };
}

/**
 * Log rate limit events for monitoring
 */
export function logRateLimitEvent(
  identifier: string,
  endpoint: string,
  allowed: boolean,
  remaining?: number,
  limit?: number
) {
  const logData = {
    timestamp: new Date().toISOString(),
    identifier: identifier.substring(0, 10) + '...', // Partial identifier for privacy
    endpoint,
    allowed,
    remaining,
    limit,
  };
  
  if (process.env.NODE_ENV === 'development') {
    console.log('Rate limit event:', logData);
  }
  
  // In production, you might want to send this to a monitoring service
  // Example: analytics.track('rate_limit_event', logData);
}
