import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>rollRestoration,
} from "@remix-run/react";
import type { LinksFunction } from "@remix-run/node";
import { Suspense } from "react";
import { I18nextProvider } from "react-i18next";
import i18n from "~/lib/i18n";

import "./tailwind.css";
import { AppSidebar } from "~/components/AppSidebar";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "~/components/ui/sidebar";
import { Separator } from "~/components/ui/separator";
import { DynamicBreadcrumb } from "~/components/ui/dynamic-breadcrumb";
import { BusinessProvider } from "~/lib/providers/BusinessProvider";
import { AuthProvider } from "~/lib/providers/AuthProvider";
import { BusinessSwitchingLoader } from "~/components/BusinessSwitchingLoader";
import { TooltipProvider } from "~/components/ui/tooltip";
import { LanguageSwitcher } from "~/components/ui/language-switcher";
import { ThemeToggle } from "~/components/ui/theme-toggle";
import { UserMenu } from "~/components/auth/UserMenu";
import { NotificationBadge } from "~/components/NotificationBadge";
import { Button } from "~/components/ui/button";
import { useAuth } from "~/lib/providers/AuthProvider";
import { useTranslation } from "react-i18next";
import { LoadingSpinner } from "~/components/ui/loading-spinner";
import { Toaster } from "sonner";
import { QueryProvider } from "~/lib/query/QueryProvider";
import { useBusinessNavigation } from "~/lib/hooks/useBusinessNavigation";

export const links: LinksFunction = () => [
  { rel: "preconnect", href: "https://fonts.googleapis.com" },
  {
    rel: "preconnect",
    href: "https://fonts.gstatic.com",
    crossOrigin: "anonymous",
  },
  {
    rel: "stylesheet",
    href: "https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap",
  },
];

function LayoutContent({ children }: { children: React.ReactNode }) {
  const { i18n } = useTranslation();

  return (
    <html lang={i18n.language || 'en'}>
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body>
        {children}
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}

export function Layout({ children }: { children: React.ReactNode }) {
  return (
    <I18nextProvider i18n={i18n}>
      <Suspense fallback={
        <html lang="en">
          <head>
            <meta charSet="utf-8" />
            <meta name="viewport" content="width=device-width, initial-scale=1" />
            <Meta />
            <Links />
          </head>
          <body>
            <div className="flex items-center justify-center min-h-screen">
              <LoadingSpinner size="lg" />
            </div>
            <ScrollRestoration />
            <Scripts />
          </body>
        </html>
      }>
        <LayoutContent>{children}</LayoutContent>
      </Suspense>
    </I18nextProvider>
  );
}

function HeaderContent() {
  const { isAuthenticated, user, showAuthModal } = useAuth();
  const { t } = useTranslation(['common', 'navigation']);

  return (
    <header className="sticky top-0 z-50 bg-background flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
      <div className="flex items-center gap-2 px-4">
        {isAuthenticated && (
          <>
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
          </>
        )}
        <DynamicBreadcrumb />
      </div>
      <div className="ml-auto flex items-center gap-2 px-4">
        <LanguageSwitcher />
        <ThemeToggle />
        {isAuthenticated && user ? (
          <>
            <NotificationBadge />
            <UserMenu user={user} />
          </>
        ) : (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => showAuthModal("login")}
            >
              {t('common:buttons.signIn')}
            </Button>
            <Button
              size="sm"
              onClick={() => showAuthModal("register")}
            >
              {t('common:buttons.signUp')}
            </Button>
          </div>
        )}
      </div>
    </header>
  );
}

function RootComponent() {
  // Handle business navigation when business context changes
  useBusinessNavigation();

  return (
    <QueryProvider>
      <AuthProvider>
        <BusinessProvider>
          <TooltipProvider>
            <SidebarProvider>
              <AppSidebarWrapper />
              {/* Business Switching Loader */}
              <BusinessSwitchingLoader />

              {/* Toast Notifications */}
              <Toaster
                position="top-right"
                expand={true}
                richColors={true}
                closeButton={true}
              />
            </SidebarProvider>
          </TooltipProvider>
        </BusinessProvider>
      </AuthProvider>
    </QueryProvider>
  )
}

function AppSidebarWrapper() {
  const { isAuthenticated } = useAuth();

  if (isAuthenticated) {
    return (
      <>
        <AppSidebar />
        <SidebarInset>
          <HeaderContent />
          <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
            <Outlet />
          </div>
        </SidebarInset>
      </>
    );
  }

  // When not authenticated, render without sidebar
  return (
    <div className="flex min-h-screen w-full flex-col">
      <HeaderContent />
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <Outlet />
      </div>
    </div>
  );
}

export default function App() {
  return <RootComponent />;
}
