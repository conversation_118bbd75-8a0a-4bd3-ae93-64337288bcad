{"title": "COGS Calculator", "description": "Calculate the Cost of Goods Sold for your products with precision", "calculator": {"title": "Cost of Goods Sold Calculator", "description": "Enter your product details, ingredients, labor costs, and overhead to calculate accurate COGS and suggested pricing"}, "playground": {"title": "COGS Playground", "sandboxBadge": "Sandbox", "description": {"welcome": "Welcome to the COGS Playground!", "line1": "This is your sandbox environment for experimenting with ingredients and products.", "line2": "Create temporary ingredients, build products with recipes, and calculate COGS in real-time.", "line3": "When you're satisfied with your calculations, you can save them to your database."}, "badges": {"realTime": "Real-time Calculations", "temporaryIngredients": "Temporary Ingredients", "productExperimentation": "Product Experimentation"}, "tabs": {"ingredients": "Ingredients", "products": "Products"}, "gettingStarted": {"title": "Getting Started", "description": "Follow these simple steps to start calculating your COGS:", "steps": {"createIngredients": {"title": "Create Ingredients", "description": "Add ingredients with their costs and units to build your ingredient library."}, "buildProducts": {"title": "Build Products", "description": "Create products by combining ingredients with specific usage amounts per serving."}, "saveWhenReady": {"title": "Save When Ready", "description": "Once you're happy with your calculations, save them to your permanent database."}}}}, "ingredientList": {"title": "Temporary Ingredients", "sandboxBadge": "Sandbox", "description": "Create and manage temporary ingredients for COGS calculations", "addIngredient": "Add Ingredient", "emptyMessage": "No ingredients created yet. Start by adding your first ingredient.", "addFirst": "Add First Ingredient", "table": {"name": "Name", "category": "Category", "baseCost": "Base Cost", "baseQuantity": "Base Quantity", "unitCost": "Unit Cost", "unit": "Unit", "status": "Status", "actions": "Actions", "active": "Active", "inactive": "Inactive"}}, "ingredientForm": {"addTitle": "Add New Ingredient", "editTitle": "Edit Ingredient", "addDescription": "Create a new temporary ingredient for COGS calculations", "editDescription": "Update the ingredient details", "fields": {"name": {"label": "Ingredient Name", "placeholder": "e.g., Coffee beans, Milk, Sugar"}, "baseUnitCost": {"label": "Base Unit Cost", "description": "Total cost for the base quantity"}, "baseUnitQuantity": {"label": "Base Unit Quantity", "description": "Quantity for the base cost"}, "unit": {"label": "Unit", "placeholder": "Select unit"}, "category": {"label": "Category", "placeholder": "Select category", "none": "No category"}, "supplierInfo": {"label": "Supplier Info", "placeholder": "Supplier name or details", "description": "Optional supplier information"}, "note": {"label": "Notes", "placeholder": "Additional notes about this ingredient"}, "isActive": {"label": "Active Ingredient", "description": "Active ingredients can be used in products"}}, "buttons": {"add": "Add Ingredient"}}, "productList": {"title": "Temporary Products", "sandboxBadge": "Sandbox", "description": "Create and manage temporary products with ingredient recipes", "addProduct": "Add Product", "emptyMessage": "No products created yet. Start by adding your first product.", "addFirst": "Add First Product", "noIngredientsWarning": "You need to create active ingredients before adding products.", "table": {"productName": "Product Name", "description": "Description", "ingredients": "Ingredients", "cogsPerCup": "COGS per Cup", "status": "Status", "actions": "Actions", "active": "Active", "inactive": "Inactive"}, "summary": {"title": "Summary", "totalProducts": "Total Products", "activeProducts": "Active Products", "avgCogsPerCup": "Avg COGS per Cup", "totalIngredientsUsed": "Total Ingredients Used"}}, "productForm": {"addTitle": "Add New Product", "editTitle": "Edit Product", "addDescription": "Create a new temporary product with ingredient recipe", "editDescription": "Update the product details and recipe", "sections": {"basicInfo": "Basic Information", "ingredients": "Ingredients & Recipe", "ingredientsDescription": "Add ingredients and specify how much is used per serving"}, "fields": {"name": {"label": "Product Name", "placeholder": "e.g., Cappuccino, Latte, Americano"}, "description": {"label": "Description", "placeholder": "Brief description of the product"}, "note": {"label": "Notes", "placeholder": "Additional notes about this product"}, "isActive": {"label": "Active Product", "description": "Active products are included in calculations"}}, "addIngredient": {"ingredient": "Ingredient", "selectIngredient": "Select ingredient", "usagePerCup": "Usage per Cup", "note": "Note", "notePlaceholder": "Optional note", "add": "Add"}, "ingredientsList": {"ingredient": "Ingredient", "usagePerCup": "Usage per Cup", "costPerCup": "Cost per Cup", "note": "Note", "actions": "Actions", "empty": "No ingredients added yet. Add ingredients to calculate COGS."}, "summary": {"totalCOGS": "Total COGS per Cup"}, "buttons": {"add": "Add Product"}}, "fields": {"productName": {"label": "Product Name", "placeholder": "Enter product name"}, "batchSize": {"label": "<PERSON><PERSON> Si<PERSON>", "placeholder": "1", "description": "Number of units produced in one batch"}, "ingredientName": {"label": "Ingredient Name", "placeholder": "e.g., Coffee beans"}, "quantity": {"label": "Quantity"}, "unit": {"label": "Unit"}, "costPerUnit": {"label": "Cost per Unit"}, "laborCostPerHour": {"label": "Labor Cost per Hour"}, "laborHours": {"label": "Labor Hours"}, "overheadCosts": {"label": "Overhead Costs", "description": "Rent, utilities, equipment depreciation, etc."}, "profitMargin": {"label": "<PERSON><PERSON> (%)", "description": "Desired profit margin percentage"}}, "buttons": {"addIngredient": "Add Ingredient", "calculate": "Calculate COGS"}, "results": {"ingredientsCost": "Ingredients Cost", "laborCost": "Labor Cost", "totalCOGS": "Total COGS", "suggestedPrice": "Suggested Price", "costPerUnit": "Cost per Unit", "profitPerUnit": "Profit per Unit", "profitMargin": "<PERSON><PERSON>"}, "validation": {"name": {"required": "Name is required"}, "baseUnitCost": {"min": "Base unit cost cannot be negative"}, "baseUnitQuantity": {"min": "Base unit quantity must be greater than 0"}, "unit": {"required": "Unit is required"}}}