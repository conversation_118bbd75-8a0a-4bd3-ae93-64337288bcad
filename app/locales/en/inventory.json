{"common": {"searchAndFilter": "Search & Filter", "filters": "Filters", "actions": "Actions", "edit": "Edit", "delete": "Delete", "view": "View", "save": "Save", "cancel": "Cancel", "create": "Create", "update": "Update", "loading": "Loading...", "error": "Error", "success": "Success", "required": "Required", "optional": "Optional", "refresh": "Refresh", "remove": "Remove"}, "ingredients": {"title": "Ingredients", "subtitle": "Manage ingredients for {{businessName}}", "addNew": "Add Ingredient", "addFirst": "Add Your First Ingredient", "list": "Ingredients List", "listDescription": "{{count}} ingredients found", "searchPlaceholder": "Search ingredients by name or description...", "noIngredients": "No ingredients found", "noSearchResults": "No ingredients match your search", "detail": {"subtitle": "Ingredient details and information", "costInfo": "Cost Information", "metadata": "<PERSON><PERSON><PERSON>"}, "delete": {"confirm": "Are you sure you want to delete this ingredient? This action cannot be undone."}, "fields": {"name": "Name", "description": "Description", "category": "Category", "usage": "Usage", "notes": "Notes", "unit": "Unit", "unitOfMeasurement": "Unit of Measurement", "baseUnitCost": "Base Unit Cost", "costPerUnit": "Cost per Unit", "supplier": "Supplier", "supplierInfo": "Supplier Information", "createdAt": "Created", "updatedAt": "Updated"}, "form": {"title": {"create": "Add New Ingredient", "edit": "Edit Ingredient"}, "description": {"create": "Add a new ingredient to your inventory", "edit": "Update ingredient information"}, "fields": {"name": "Ingredient Name", "category": "Category", "baseUnitCost": "Base Unit Cost", "baseUnitQuantity": "Base Unit Quantity", "unit": "Unit", "supplierInfo": "Supplier Information", "notes": "Notes", "isActive": "Active Status"}, "placeholders": {"name": "Enter ingredient name (e.g., Coffee Beans)", "selectCategory": "Select category", "baseUnitCost": "0.00", "baseUnitQuantity": "1.00", "unit": "Select or enter unit", "supplierInfo": "Supplier name, contact, etc.", "notes": "Additional notes about this ingredient"}, "descriptions": {"isActive": "Ingredient is active"}, "validation": {"nameRequired": "Ingredient name is required", "unitRequired": "Unit is required", "baseUnitCostRequired": "Base unit cost is required", "baseUnitQuantityRequired": "Base unit quantity is required", "costPositive": "Cost must be a positive number", "quantityPositive": "Quantity must be a positive number", "nameExists": "An ingredient with this name already exists"}, "units": {"kg": "Kilogram (kg)", "g": "Gram (g)", "mg": "Milligram (mg)", "l": "Liter (l)", "ml": "Milliliter (ml)", "pcs": "Pieces (pcs)", "pack": "Pack", "box": "Box", "cup": "Cup", "tbsp": "Tablespoon (tbsp)", "tsp": "Teaspoon (tsp)", "oz": "Ounce (oz)", "lb": "Pound (lb)"}}, "messages": {"createSuccess": "Ingredient created successfully", "createError": "Failed to create ingredient", "updateSuccess": "Ingredient updated successfully", "updateError": "Failed to update ingredient", "deleteSuccess": "Ingredient deleted successfully", "deleteError": "Failed to delete ingredient", "deleteConfirm": "Are you sure you want to delete this ingredient?", "deleteWarning": "This action cannot be undone."}}, "products": {"title": "Products", "subtitle": "Manage products for {{businessName}}", "addNew": "Add Product", "addFirst": "Add Your First Product", "list": "Products List", "listDescription": "{{count}} products found", "searchPlaceholder": "Search products by name, description, or category...", "noProducts": "No products found", "noSearchResults": "No products match your search", "detail": {"subtitle": "Product details and information", "pricingInfo": "Pricing Information", "metadata": "<PERSON><PERSON><PERSON>"}, "delete": {"confirm": "Are you sure you want to delete this product? This action cannot be undone."}, "create": {"title": "Create New Product", "subtitle": "Add a new product to your catalog", "nextSteps": "Next steps:", "step1": "After creating the product, you can add ingredients", "step2": "Set usage amounts per cup for each ingredient", "step3": "Use the product in COGS calculations and production"}, "cogs": {"title": "Cost of Goods Sold (COGS)", "description": "Calculate and track the total cost to produce this product", "laborCosts": "Labor Costs", "overheadCosts": "Overhead Costs", "ingredientCosts": "Ingredient Costs", "totalCogs": "Total COGS", "calculate": "Calculate COGS", "save": "Save COGS", "results": "Calculation Results", "profitAnalysis": "Profit Analysis", "profitAmount": "Profit <PERSON>", "profitMargin": "<PERSON><PERSON>", "ingredientBreakdown": "Ingredient Cost Breakdown", "quantityNeeded": "Quantity Needed", "unitCost": "Unit Cost", "totalCost": "Total Cost", "percentage": "% of Total", "ingredients": "Ingredients", "labor": "Labor", "overhead": "Overhead", "notCalculated": "COGS not calculated yet", "info": "COGS is automatically calculated based on ingredient costs. Labor and overhead costs can be added manually.", "tabs": {"overview": "Overview", "breakdown": "Breakdown", "history": "History"}, "historyTitle": "COGS History", "previousCogs": "Previous COGS", "newCogs": "New COGS", "noIngredientData": "No ingredient breakdown available", "noHistoryData": "No COGS history available"}, "fields": {"name": "Name", "description": "Description", "note": "Note", "category": "Category", "cogsPerCup": "COGS per Cup", "status": "Status", "isActive": "Active Status", "ingredients": "Ingredients", "createdAt": "Created", "updatedAt": "Updated"}, "form": {"title": {"create": "Add New Product", "edit": "Edit Product"}, "description": {"create": "Add a new product to your inventory", "edit": "Update product information"}, "fields": {"name": "Product Name", "description": "Description", "note": "Note", "notes": "Notes", "status": "Status", "cogsPerCup": "COGS per Cup", "category": "Category", "isActive": "Active Status", "ingredients": "Ingredients"}, "placeholders": {"name": "Enter product name", "description": "Enter product description", "note": "Additional notes about this product", "notes": "Additional notes about this product", "cogsPerCup": "Enter COGS per cup (e.g., 12000)", "category": "Select category", "noCategory": "No category"}, "descriptions": {"cogsPerCup": "Cost of goods sold per cup (calculated automatically)", "cogsCalculated": "COGS will be calculated automatically based on ingredients and their costs", "cogsAutomatic": "Add ingredients to this product to see the calculated COGS value"}, "statusDescription": "Product is active", "actions": {"create": "Create Product", "update": "Update Product", "save": "Save", "cancel": "Cancel", "edit": "Edit"}, "validation": {"nameRequired": "Product name is required", "priceRequired": "Selling price is required", "pricePositive": "Price must be a positive number", "statusRequired": "Status is required", "nameExists": "A product with this name already exists", "ingredientRequired": "At least one ingredient is required", "quantityPositive": "Ingredient quantity must be positive"}, "categories": {"beverage": "Beverage", "food": "Food", "dessert": "Dessert", "snack": "Snack", "merchandise": "Merchandise", "other": "Other"}, "ingredients": {"title": "Product Ingredients", "description": "Select ingredients and specify quantities needed for this product", "addIngredient": "Add Ingredient", "selectIngredient": "Select ingredient", "ingredient": "Ingredient", "quantity": "Quantity", "quantityPlaceholder": "Enter quantity needed", "estimatedCost": "Estimated cost", "remove": "Remove", "noIngredients": "No ingredients added yet", "noIngredientsAvailable": "No ingredients available. Please add ingredients first."}, "errors": {"categoryLoadFailed": "Failed to load categories. Please refresh the page.", "createFailed": "Failed to create product. Please try again.", "updateFailed": "Failed to update product. Please try again."}}, "status": {"active": "Active", "inactive": "Inactive", "discontinued": "Discontinued"}, "messages": {"createSuccess": "Product created successfully", "createError": "Failed to create product", "updateSuccess": "Product updated successfully", "updateError": "Failed to update product", "deleteSuccess": "Product deleted successfully", "deleteError": "Failed to delete product", "deleteConfirm": "Are you sure you want to delete this product?", "deleteWarning": "This action cannot be undone."}}, "categories": {"title": "Categories", "description": "Manage categories for {{businessName}}", "subtitle": "Manage categories for {{businessName}}", "addNew": "Add Category", "addFirst": "Add Your First Category", "list": {"title": "Categories List", "description": "{{count}} categories found"}, "search": {"placeholder": "Search categories by name..."}, "filters": {"type": "Type"}, "actions": {"edit": "Edit Category", "delete": "Delete Category"}, "empty": {"noCategories": "No categories found", "noCategoriesDescription": "Get started by creating your first category to organize your items", "noResults": "No categories match your search", "noResultsDescription": "Try adjusting your search terms or filters"}, "table": {"name": "Name", "type": "Type", "description": "Description", "status": "Status"}, "searchPlaceholder": "Search categories by name...", "noCategories": "No categories found", "noSearchResults": "No categories match your search", "detail": {"subtitle": "Category details and information", "basicInfo": "Basic Information", "hierarchy": "Hierarchy", "usage": "Usage Statistics", "metadata": "<PERSON><PERSON><PERSON>", "noDescription": "No description provided", "parentCategory": "Parent Category", "childCategories": "Child Categories", "noHierarchy": "This category has no parent or child categories", "totalItems": "Total Items", "activeItems": "Active Items", "inactiveItems": "Inactive Items"}, "create": {"title": "Add New Category", "description": "Add a new category to organize your items"}, "edit": {"title": "Edit Category", "description": "Update category information"}, "delete": {"title": "Delete Category", "description": "Are you sure you want to delete \"{{name}}\"? This action cannot be undone.", "confirm": "Are you sure you want to delete this category? This action cannot be undone.", "hasChildren": "This category has {{count}} child categories that will also be affected."}, "fields": {"name": "Name", "description": "Description", "type": "Type", "color": "Color", "parent": "Parent Category", "children": "Child Categories", "itemCount": "Items", "createdAt": "Created", "updatedAt": "Updated"}, "form": {"name": {"label": "Category Name", "placeholder": "Enter category name"}, "description": {"label": "Description", "placeholder": "Enter category description"}, "type": {"label": "Category Type", "placeholder": "Select category type"}, "parent": {"label": "Parent Category", "placeholder": "Select parent category"}, "color": {"label": "Color"}, "sortOrder": {"label": "Sort Order", "placeholder": "Enter sort order (optional)"}, "isActive": {"label": "Active Status", "description": "Category is active and visible"}, "validation": {"nameRequired": "Category name is required", "typeRequired": "Category type is required", "nameExists": "A category with this name already exists"}}, "types": {"ingredient": "Ingredient", "product": "Product", "supplier": "Supplier", "customer": "Customer"}, "messages": {"createSuccess": "Category created successfully", "createError": "Failed to create category", "updateSuccess": "Category updated successfully", "updateError": "Failed to update category", "deleteSuccess": "Category deleted successfully", "deleteError": "Failed to delete category", "deleteConfirm": "Are you sure you want to delete this category?", "deleteWarning": "This action cannot be undone."}}}