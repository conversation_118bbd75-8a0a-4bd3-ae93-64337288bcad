{"warning": {"title": "Session Expiring Soon", "description": "Your session will expire soon. Would you like to extend it?", "networkIssues": "Network connectivity issues detected", "extensionFailed": "Failed to extend session. Please try again.", "autoLogoutNote": "You will be automatically logged out when the timer reaches zero.", "criticalWarning": "Session expires in less than 1 minute!"}, "buttons": {"extendSession": "Extend Session", "extending": "Extending...", "logout": "Logout Now", "dismiss": "<PERSON><PERSON><PERSON>", "tryAgain": "Try Again"}, "countdown": {"timeRemaining": "Time remaining", "minutes": "minutes", "minute": "minute", "seconds": "seconds", "second": "second", "expired": "Session expired"}, "notifications": {"sessionExtended": "Session extended successfully", "sessionExtensionFailed": "Failed to extend session", "sessionExpired": "Your session has expired", "sessionExpiring": "Session expiring in {{minutes}} minutes", "automaticLogout": "You have been automatically logged out due to session expiry", "networkError": "Network error occurred while extending session"}, "errors": {"invalidSession": "Invalid session", "sessionNotFound": "Session not found", "extensionTimeout": "Session extension request timed out", "serverError": "Server error occurred", "networkUnavailable": "Network is unavailable", "tooManyRequests": "Too many extension requests. Please wait.", "sessionIdMismatch": "Session ID mismatch", "unauthorized": "Unauthorized access"}, "status": {"monitoring": "Session monitoring active", "notMonitoring": "Session monitoring inactive", "extending": "Extending session...", "extended": "Session extended", "expired": "Session expired", "warning": "Session warning active", "healthy": "Session healthy"}, "activity": {"tracking": "Activity tracking enabled", "notTracking": "Activity tracking disabled", "lastActivity": "Last activity", "noActivity": "No recent activity", "activityDetected": "User activity detected", "autoExtension": "Session automatically extended due to activity"}, "settings": {"warningThresholds": "Warning thresholds", "activityTracking": "Activity tracking", "autoExtension": "Automatic extension", "debugMode": "Debug mode", "enabled": "Enabled", "disabled": "Disabled"}, "accessibility": {"sessionTimer": "Session expiration timer", "warningIcon": "Session warning icon", "networkStatus": "Network connectivity status", "extendButton": "Extend session button", "logoutButton": "Logout button", "dismissButton": "Dismiss warning button"}, "help": {"whatIsSessionExtension": "What is session extension?", "sessionExtensionExplanation": "Session extension allows you to continue using the application without having to log in again. Your session will be extended for the same duration as your original login.", "whySessionExpires": "Why do sessions expire?", "sessionExpiryExplanation": "Sessions expire for security reasons. This helps protect your account if you forget to log out on a shared computer.", "activityTrackingExplanation": "Activity tracking monitors your interactions with the application and can automatically extend your session when you're actively using it.", "troubleshooting": "Troubleshooting", "networkIssuesHelp": "If you're experiencing network issues, try refreshing the page or checking your internet connection.", "extensionFailedHelp": "If session extension fails repeatedly, you may need to log out and log back in."}}