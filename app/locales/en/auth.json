{"signIn": "Sign In", "signUp": "Sign Up", "login": "<PERSON><PERSON>", "register": "Register", "logout": {"loading": "Signing out...", "success": "Successfully signed out", "error": "Failed to sign out. Please try again.", "allDevicesSuccess": "Successfully signed out from all devices", "retry": "Retry", "continueOffline": "Continue offline", "sessionCleanupWarning": "Session cleanup may take a moment to complete", "networkErrorOffline": "Network error occurred, but you have been signed out locally", "rateLimit": "Too many logout attempts. Please wait a moment before trying again.", "serverError": "Server error occurred during logout. You have been signed out locally.", "unexpectedError": "An unexpected error occurred. You have been signed out locally.", "redirecting": "Redirecting...", "cleanupComplete": "Session cleanup completed successfully"}, "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot Password?", "rememberMe": "Remember me", "createAccount": "Create Account", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "signInWithGoogle": "Sign in with Google", "signUpWithGoogle": "Sign up with Google", "forms": {"login": {"title": "Sign In", "description": "Enter your email and password to access your account", "fields": {"email": {"label": "Email", "placeholder": "Enter your email"}, "password": {"label": "Password", "placeholder": "Enter your password"}}, "buttons": {"submit": "Sign In", "submitting": "Signing in...", "switchToRegister": "Sign up"}, "messages": {"switchPrompt": "Don't have an account?", "defaultError": "Invalid email or password. Please check your credentials and try again.", "invalidCredentials": "Invalid email or password. Please check your credentials and try again.", "emailNotVerified": "Please verify your email address before signing in. Check your inbox for a verification link.", "accountLocked": "Your account has been temporarily locked due to multiple failed login attempts. Please try again later.", "networkError": "Unable to connect to the server. Please check your internet connection and try again.", "serverError": "We're experiencing technical difficulties. Please try again in a few moments.", "rateLimited": "Too many login attempts. Please wait a moment before trying again.", "sessionExpired": "Your session has expired. Please sign in again."}}, "register": {"title": "Create Account", "description": "Enter your information to create a new account", "fields": {"firstName": {"label": "First Name *", "placeholder": "<PERSON>"}, "lastName": {"label": "Last Name", "placeholder": "<PERSON><PERSON>"}, "email": {"label": "Email *", "placeholder": "<EMAIL>"}, "password": {"label": "Password *", "placeholder": "Enter your password"}, "confirmPassword": {"label": "Confirm Password *", "placeholder": "Confirm your password"}}, "buttons": {"submit": "Create Account", "submitting": "Creating account...", "switchToLogin": "Sign in"}, "messages": {"switchPrompt": "Already have an account?", "defaultError": "Failed to create account. Please try again.", "emailAlreadyExists": "An account with this email address already exists. Please use a different email or try signing in.", "weakPassword": "Password is too weak. Please choose a stronger password with at least 8 characters.", "passwordTooShort": "Password must be at least 8 characters long.", "invalidEmail": "Please enter a valid email address.", "networkError": "Unable to connect to the server. Please check your internet connection and try again.", "serverError": "We're experiencing technical difficulties. Please try again in a few moments.", "rateLimited": "Too many registration attempts. Please wait a moment before trying again.", "success": "Account created successfully! Welcome to KWACI Grow."}}, "validation": {"email": {"required": "Email is required", "invalid": "Please enter a valid email address"}, "password": {"required": "Password is required", "minLength": "Password must be at least 8 characters long", "maxLength": "Password must be no more than 128 characters long", "requirements": "Password must contain at least 8 characters"}, "firstName": {"required": "First name is required"}, "confirmPassword": {"required": "Please confirm your password", "mismatch": "Passwords do not match"}}, "errors": {"general": {"networkError": "Network connection failed. Please check your internet connection and try again.", "serverError": "Server error occurred. Please try again later.", "unknownError": "An unexpected error occurred. Please try again.", "timeout": "Request timed out. Please try again."}, "authentication": {"invalidCredentials": "Invalid email or password. Please check your credentials and try again.", "userNotFound": "Invalid email or password. Please check your credentials and try again.", "incorrectPassword": "Invalid email or password. Please check your credentials and try again.", "emailNotVerified": "Please verify your email address before signing in.", "accountDisabled": "Your account has been disabled. Please contact support.", "accountLocked": "Your account has been temporarily locked. Please try again later."}, "registration": {"emailExists": "An account with this email address already exists. Please use a different email or try signing in.", "weakPassword": "Password is too weak. Please choose a stronger password.", "invalidData": "Please check your information and try again."}, "security": {"rateLimited": "Too many attempts. Please wait before trying again.", "suspiciousActivity": "Suspicious activity detected. Please try again later."}, "rateLimit": {"general": "Too many requests. Please wait {{retryAfter}} seconds before trying again.", "signIn": "Too many login attempts. Please wait {{retryAfter}} seconds before trying again.", "signUp": "Too many registration attempts. Please wait {{retryAfter}} seconds before trying again.", "passwordReset": "Too many password reset requests. Please wait {{retryAfter}} seconds before trying again.", "session": "Too many session requests. Please wait {{retryAfter}} seconds before trying again."}, "authorization": {"accessDenied": "Access Denied", "accessDeniedDescription": "You don't have permission to access this resource.", "insufficientPermissions": "If you believe this is an error, please contact your administrator or business owner to request the necessary permissions.", "originalUrlAttempted": "You were trying to access: {{url}}", "goToDashboard": "Go to Dashboard", "goBack": "Go Back", "requestAccess": "Request Access", "contactAdmin": "Contact Administrator"}}}, "userMenu": {"profile": "Profile", "settings": "Settings", "signOut": "Sign out", "signingOut": "Signing out...", "signOutAllDevices": "Sign out from all devices", "confirmSignOut": "Confirm sign out", "confirmSignOutMessage": "Are you sure you want to sign out?", "confirmSignOutAllMessage": "Are you sure you want to sign out from all devices? This will end all your active sessions."}}