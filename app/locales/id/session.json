{"warning": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON> <PERSON>a akan berakhir dalam waktu dekat. <PERSON><PERSON><PERSON>h Anda ingin memper<PERSON>?", "networkIssues": "Terdeteksi masalah konektivitas jaringan", "extensionFailed": "<PERSON><PERSON> memperpanjang sesi. Silakan coba lagi.", "autoLogoutNote": "<PERSON><PERSON> akan otomatis keluar ketika waktu habis.", "criticalWarning": "<PERSON><PERSON> berakhir dalam kurang dari 1 menit!"}, "buttons": {"extendSession": "Perpan<PERSON><PERSON>", "extending": "Memperpanjang...", "logout": "<PERSON><PERSON><PERSON>", "dismiss": "<PERSON><PERSON><PERSON>", "tryAgain": "<PERSON><PERSON>"}, "countdown": {"timeRemaining": "<PERSON><PERSON><PERSON> ters<PERSON>", "minutes": "menit", "minute": "menit", "seconds": "detik", "second": "detik", "expired": "<PERSON><PERSON>"}, "notifications": {"sessionExtended": "<PERSON><PERSON> be<PERSON>", "sessionExtensionFailed": "<PERSON><PERSON> sesi", "sessionExpired": "<PERSON><PERSON> <PERSON>a telah be<PERSON>", "sessionExpiring": "<PERSON><PERSON> be<PERSON> dalam {{minutes}} menit", "automaticLogout": "<PERSON>a telah otomatis keluar karena sesi be<PERSON>hir", "networkError": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han jaringan saat memperpanjang sesi"}, "errors": {"invalidSession": "<PERSON><PERSON> tidak valid", "sessionNotFound": "Sesi tidak di<PERSON>n", "extensionTimeout": "Permintaan perpanjangan sesi habis waktu", "serverError": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON> server", "networkUnavailable": "<PERSON><PERSON><PERSON> tidak tersedia", "tooManyRequests": "Terlalu banyak permintaan per<PERSON>. <PERSON><PERSON><PERSON> tunggu.", "sessionIdMismatch": "ID sesi tidak cocok", "unauthorized": "<PERSON><PERSON><PERSON> t<PERSON>"}, "status": {"monitoring": "Pemantauan sesi aktif", "notMonitoring": "Pemantauan sesi tidak aktif", "extending": "Memperpanjang sesi...", "extended": "<PERSON><PERSON>", "expired": "<PERSON><PERSON>", "warning": "Peringatan sesi aktif", "healthy": "<PERSON><PERSON> se<PERSON>"}, "activity": {"tracking": "Pelacakan aktivitas diaktifkan", "notTracking": "Pelacakan aktivitas dinonaktifkan", "lastActivity": "Aktivitas terakhir", "noActivity": "Tidak ada aktivitas terbaru", "activityDetected": "Aktivitas pengguna terdeteksi", "autoExtension": "<PERSON><PERSON> otomatis diperpanjang karena aktivitas"}, "settings": {"warningThresholds": "Ambang batas peringatan", "activityTracking": "Pelacakan aktivitas", "autoExtension": "Perpanjangan otomatis", "debugMode": "Mode debug", "enabled": "Diaktifkan", "disabled": "Dinonaktifkan"}, "accessibility": {"sessionTimer": "<PERSON>r <PERSON><PERSON><PERSON><PERSON>si", "warningIcon": "<PERSON><PERSON> per<PERSON>tan sesi", "networkStatus": "Status konektivitas jaringan", "extendButton": "Tombol perpanjang sesi", "logoutButton": "<PERSON><PERSON> keluar", "dismissButton": "Tombol tutup peringatan"}, "help": {"whatIsSessionExtension": "Apa itu perpanjangan sesi?", "sessionExtensionExplanation": "Perpanjangan sesi memungkinkan Anda melanjutkan menggunakan aplikasi tanpa harus masuk lagi. Sesi Anda akan diperpanjang dengan durasi yang sama seperti login awal.", "whySessionExpires": "Mengapa sesi berakhir?", "sessionExpiryExplanation": "<PERSON><PERSON> berakhir untuk alasan keamanan. Ini membantu melindungi akun Anda jika Anda lupa keluar di komputer bersama.", "activityTrackingExplanation": "Pelacakan aktivitas memantau interaksi Anda dengan aplikasi dan dapat secara otomatis memperpanjang sesi ketika Anda aktif menggunakannya.", "troubleshooting": "<PERSON><PERSON><PERSON><PERSON>", "networkIssuesHelp": "<PERSON><PERSON>a mengalami ma<PERSON>ah <PERSON>, coba refresh halaman atau periksa koneksi internet Anda.", "extensionFailedHelp": "<PERSON><PERSON> per<PERSON>gan sesi gagal berulang kali, <PERSON><PERSON> mungkin perlu keluar dan masuk kembali."}}