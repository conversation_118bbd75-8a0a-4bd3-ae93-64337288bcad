{"appName": "KWACI Grow", "buttons": {"signIn": "<PERSON><PERSON><PERSON>", "signUp": "<PERSON><PERSON><PERSON>", "viewDashboard": "Lihat Dashboard", "manageSales": "<PERSON><PERSON><PERSON>", "viewInventory": "<PERSON><PERSON>", "recordSale": "Catat Penjualan", "addProduct": "Tambah Produk", "generateReport": "<PERSON><PERSON><PERSON> La<PERSON>", "planProduction": "<PERSON><PERSON><PERSON><PERSON>", "save": "Simpan", "cancel": "<PERSON><PERSON>", "submit": "<PERSON><PERSON>", "reset": "Reset", "back": "Kembali", "next": "Selanjutnya", "previous": "Sebelumnya", "close": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retry": "<PERSON><PERSON>", "continue": "Lanjutkan", "edit": "Edit", "delete": "Hapus", "remove": "Hapus", "filters": "Filter", "clearFilters": "<PERSON><PERSON><PERSON><PERSON>", "saving": "Menyimpan...", "update": "<PERSON><PERSON><PERSON>", "create": "Buat"}, "navigation": {"dashboard": "Dashboard", "switchLanguage": "<PERSON><PERSON> bahasa", "toggleLanguage": "<PERSON><PERSON> bahasa", "switchToIndonesian": "Ganti ke Bahasa Indonesia", "switchToEnglish": "Ganti ke Bahasa Inggris"}, "labels": {"name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "currency": "<PERSON>", "notes": "Catatan"}, "fields": {"id": "ID", "createdAt": "Dibuat Pada", "updatedAt": "<PERSON><PERSON><PERSON><PERSON>"}, "messages": {"noBusinessSelected": "Tidak ada bisnis yang dipilih", "noBusinessesFound": "Tidak ada bisnis ditemukan", "welcome": "Selamat datang di KWACI Grow, {{name}}!", "platformDescription": "Platform manajemen bisnis komprehensif untuk UKM Asia Tenggara"}, "status": {"active": "Aktif", "inactive": "Tidak Aktif"}, "filters": {"all": "<PERSON><PERSON><PERSON>"}, "table": {"actions": "<PERSON><PERSON><PERSON>"}, "loading": "Memuat...", "rbac": {"permissionsInfo": {"title": "<PERSON><PERSON> yang <PERSON> untuk {{route}}", "description": "Bagian ini menampilkan semua izin yang digunakan dalam rute ini dan tujuannya. <PERSON><PERSON> pengguna dengan izin yang sesuai yang dapat mengakses fitur tertentu.", "primaryAccess": "<PERSON><PERSON>", "actionPermissions": "<PERSON><PERSON>", "relatedPermissions": "<PERSON><PERSON>", "note": "Catatan: Elemen UI secara otomatis disembunyikan jika Anda tidak memiliki izin yang diperlukan. Hubungi administrator <PERSON><PERSON> jika <PERSON>a memerlukan aks<PERSON> tambahan."}}}