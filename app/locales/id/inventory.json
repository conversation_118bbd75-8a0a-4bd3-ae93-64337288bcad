{"common": {"searchAndFilter": "Cari & Filter", "filters": "Filter", "actions": "<PERSON><PERSON><PERSON>", "edit": "Edit", "delete": "Hapus", "view": "Lihat", "save": "Simpan", "cancel": "<PERSON><PERSON>", "create": "Buat", "update": "<PERSON><PERSON><PERSON>", "loading": "Memuat...", "error": "Error", "success": "<PERSON><PERSON><PERSON><PERSON>", "required": "<PERSON><PERSON><PERSON>", "optional": "Opsional", "refresh": "Segarkan", "remove": "Hapus"}, "ingredients": {"title": "Bahan Baku", "subtitle": "<PERSON><PERSON><PERSON> bahan baku untuk {{businessName}}", "addNew": "Tambah Bahan Baku", "addFirst": "Tambah Bahan Baku Pertama", "list": "Daftar Bahan Baku", "listDescription": "{{count}} bahan baku di<PERSON>n", "searchPlaceholder": "<PERSON>i bahan baku berdasarkan nama atau deskripsi...", "noIngredients": "Tidak ada bahan baku di<PERSON>n", "noSearchResults": "Tidak ada bahan baku yang sesuai dengan pencarian", "detail": {"subtitle": "Detail dan informasi bahan baku", "costInfo": "Informasi Biaya", "metadata": "<PERSON><PERSON><PERSON>"}, "delete": {"confirm": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus bahan baku ini? Tindakan ini tidak dapat dibatalkan."}, "fields": {"name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "usage": "<PERSON><PERSON><PERSON><PERSON>", "notes": "Catatan", "unit": "Satuan", "unitOfMeasurement": "<PERSON><PERSON><PERSON>", "baseUnitCost": "<PERSON><PERSON>", "costPerUnit": "<PERSON>rga per <PERSON>", "supplier": "Pemasok", "supplierInfo": "Informasi Pemasok", "createdAt": "Dibuat", "updatedAt": "<PERSON><PERSON><PERSON><PERSON>"}, "form": {"title": {"create": "Tambah Bahan Baku Baru", "edit": "<PERSON>"}, "description": {"create": "<PERSON><PERSON><PERSON> bahan baku baru ke inventori Anda", "edit": "<PERSON><PERSON><PERSON> informasi bahan baku"}, "fields": {"name": "<PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "baseUnitCost": "<PERSON><PERSON>", "baseUnitQuantity": "<PERSON><PERSON><PERSON>", "unit": "Satuan", "supplierInfo": "Informasi Pemasok", "notes": "Catatan", "isActive": "Status Aktif"}, "placeholders": {"name": "<PERSON><PERSON><PERSON><PERSON> nama bahan baku (contoh: <PERSON><PERSON>)", "selectCategory": "<PERSON><PERSON><PERSON> ka<PERSON>i", "baseUnitCost": "0.00", "baseUnitQuantity": "1.00", "unit": "<PERSON><PERSON><PERSON> atau masukkan satuan", "supplierInfo": "<PERSON><PERSON>, kont<PERSON>, dll.", "notes": "Catatan tambahan tentang bahan baku ini"}, "descriptions": {"isActive": "Bahan baku sedang aktif"}, "validation": {"nameRequired": "<PERSON><PERSON> bahan baku wajib diisi", "unitRequired": "<PERSON><PERSON><PERSON> wajib di<PERSON>", "baseUnitCostRequired": "<PERSON>rga satuan dasar wajib diisi", "baseUnitQuantityRequired": "<PERSON><PERSON><PERSON> satuan dasar wajib diisi", "costPositive": "Harga harus berupa angka positif", "quantityPositive": "<PERSON><PERSON><PERSON> harus berupa angka positif", "nameExists": "<PERSON>han baku dengan nama ini sudah ada"}, "units": {"kg": "Kilogram (kg)", "g": "Gram (g)", "mg": "Miligram (mg)", "l": "Liter (l)", "ml": "Mililiter (ml)", "pcs": "Buah (pcs)", "pack": "<PERSON><PERSON><PERSON>", "box": "Kotak", "cup": "Cangkir", "tbsp": "<PERSON><PERSON> Makan (sdm)", "tsp": "Sendok Teh (sdt)", "oz": "Ons (oz)", "lb": "Pon (lb)"}}, "messages": {"createSuccess": "<PERSON><PERSON> baku ber<PERSON><PERSON> dibuat", "createError": "<PERSON><PERSON> membuat bahan baku", "updateSuccess": "<PERSON><PERSON> baku ber<PERSON><PERSON>", "updateError": "<PERSON><PERSON> bahan baku", "deleteSuccess": "<PERSON><PERSON> baku be<PERSON><PERSON><PERSON>", "deleteError": "<PERSON><PERSON><PERSON> bahan baku", "deleteConfirm": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus bahan baku ini?", "deleteWarning": "Tindakan ini tidak dapat dibatalkan."}}, "products": {"title": "Produk", "subtitle": "<PERSON><PERSON><PERSON> produk untuk {{businessName}}", "addNew": "Tambah Produk", "addFirst": "Tambah Produk Pertama", "list": "Daftar Produk", "listDescription": "{{count}} produk di<PERSON><PERSON>n", "searchPlaceholder": "<PERSON>i produk be<PERSON><PERSON><PERSON> na<PERSON>, <PERSON><PERSON><PERSON>, atau kategori...", "noProducts": "Tidak ada produk ditemukan", "noSearchResults": "Tidak ada produk yang sesuai dengan pencarian", "detail": {"subtitle": "Detail dan informasi produk", "pricingInfo": "Informasi <PERSON>", "metadata": "<PERSON><PERSON><PERSON>"}, "delete": {"confirm": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus produk ini? Tindakan ini tidak dapat dibatalkan."}, "create": {"title": "Buat Produk Baru", "subtitle": "Tambahkan produk baru ke katalog Anda", "nextSteps": "Langkah selanju<PERSON>nya:", "step1": "<PERSON><PERSON><PERSON> memb<PERSON>t produk, <PERSON><PERSON> dapat men<PERSON> bahan baku", "step2": "<PERSON>ur jumlah penggunaan per cangkir untuk setiap bahan baku", "step3": "Gunakan produk dalam perhitungan COGS dan produksi"}, "cogs": {"title": "Harga Pokok Penjualan (HPP)", "description": "Hitung dan lacak total biaya untuk memproduksi produk ini", "laborCosts": "Biaya Tenaga Kerja", "overheadCosts": "Biaya Overhead", "ingredientCosts": "<PERSON><PERSON><PERSON>han", "totalCogs": "Total HPP", "calculate": "Hitung HPP", "save": "Simpan HPP", "results": "<PERSON><PERSON>", "profitAnalysis": "<PERSON><PERSON><PERSON>", "profitAmount": "<PERSON><PERSON><PERSON>", "profitMargin": "<PERSON><PERSON>", "ingredientBreakdown": "Rincian Biaya <PERSON>han", "quantityNeeded": "<PERSON><PERSON><PERSON>", "unitCost": "Biaya per Unit", "totalCost": "Total Biaya", "percentage": "% dari Total", "ingredients": "<PERSON><PERSON>", "labor": "Tenaga <PERSON>", "overhead": "Overhead", "notCalculated": "HPP belum dihitung", "info": "HPP dihitung otomatis berdasarkan biaya bahan. Biaya tenaga kerja dan overhead dapat ditambahkan secara manual.", "tabs": {"overview": "<PERSON><PERSON><PERSON>", "breakdown": "<PERSON><PERSON><PERSON>", "history": "Riwayat"}, "historyTitle": "Riwayat HPP", "previousCogs": "HPP Sebelumnya", "newCogs": "HPP Baru", "noIngredientData": "Tidak ada rin<PERSON> bahan tersedia", "noHistoryData": "Tidak ada riwayat HPP tersedia"}, "fields": {"name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "note": "Catatan", "category": "<PERSON><PERSON><PERSON>", "cogsPerCup": "HPP per Cup", "status": "Status", "isActive": "Status Aktif", "ingredients": "Bahan Baku", "createdAt": "Dibuat", "updatedAt": "<PERSON><PERSON><PERSON><PERSON>"}, "form": {"title": {"create": "Tambah Produk Baru", "edit": "Edit Produk"}, "description": {"create": "Tambahkan produk baru ke inventori Anda", "edit": "Perbarui informasi produk"}, "fields": {"name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "note": "Catatan", "notes": "Catatan", "status": "Status", "cogsPerCup": "HPP per Cup", "category": "<PERSON><PERSON><PERSON>", "isActive": "Status Aktif", "ingredients": "Bahan Baku"}, "placeholders": {"name": "<PERSON><PERSON><PERSON><PERSON> nama produk", "description": "<PERSON><PERSON><PERSON><PERSON> produk", "note": "Catatan tambahan tentang produk ini", "notes": "Catatan tambahan tentang produk ini", "cogsPerCup": "Masukkan HPP per cup (contoh: 12000)", "category": "<PERSON><PERSON><PERSON> ka<PERSON>i", "noCategory": "<PERSON><PERSON> kategori"}, "descriptions": {"cogsPerCup": "Harga Pokok Penjualan per cup (dihitung otomatis)", "cogsCalculated": "HPP akan dihitung otomatis berda<PERSON><PERSON> bahan baku dan biayanya", "cogsAutomatic": "<PERSON><PERSON><PERSON> bahan baku ke produk ini untuk melihat nilai HPP yang dihitung"}, "statusDescription": "Produk aktif", "actions": {"create": "Buat Produk", "update": "<PERSON><PERSON><PERSON>", "save": "Simpan", "cancel": "<PERSON><PERSON>", "edit": "Edit"}, "validation": {"nameRequired": "<PERSON><PERSON> produk wajib diisi", "priceRequired": "<PERSON><PERSON> jual wajib diisi", "pricePositive": "Harga harus berupa angka positif", "statusRequired": "Status wajib dipilih", "nameExists": "Produk dengan nama ini sudah ada", "ingredientRequired": "<PERSON><PERSON> satu bahan baku dip<PERSON>lukan", "quantityPositive": "<PERSON><PERSON><PERSON> bahan baku harus positif"}, "categories": {"beverage": "<PERSON><PERSON>", "food": "<PERSON><PERSON><PERSON>", "dessert": "Dessert", "snack": "Camilan", "merchandise": "Merchandise", "other": "<PERSON><PERSON><PERSON>"}, "ingredients": {"title": "Bahan Baku Produk", "description": "<PERSON><PERSON><PERSON> bahan baku dan tentukan jumlah yang diperlukan untuk produk ini", "addIngredient": "Tambah Bahan Baku", "selectIngredient": "<PERSON><PERSON><PERSON> bahan baku", "ingredient": "Bahan Baku", "quantity": "<PERSON><PERSON><PERSON>", "quantityPlaceholder": "<PERSON><PERSON><PERSON><PERSON> jumlah yang <PERSON>an", "estimatedCost": "<PERSON><PERSON><PERSON><PERSON> biaya", "remove": "Hapus", "noIngredients": "Belum ada bahan baku ditambahkan", "noIngredientsAvailable": "Tidak ada bahan baku tersedia. <PERSON><PERSON><PERSON> tamba<PERSON>kan bahan baku terlebih dahulu."}, "errors": {"categoryLoadFailed": "<PERSON>l memuat kategori. <PERSON><PERSON>an segarkan halaman.", "createFailed": "Gagal membuat produk. Silakan coba lagi.", "updateFailed": "<PERSON>l memperbarui produk. Silakan coba lagi."}}, "status": {"active": "Aktif", "inactive": "Tidak Aktif", "discontinued": "Dihentikan"}, "messages": {"createSuccess": "Produk ber<PERSON>il dibuat", "createError": "<PERSON><PERSON> membuat produk", "updateSuccess": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "updateError": "<PERSON><PERSON> produk", "deleteSuccess": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "deleteError": "<PERSON><PERSON> produk", "deleteConfirm": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus produk ini?", "deleteWarning": "Tindakan ini tidak dapat dibatalkan."}}, "categories": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> kategori untuk {{businessName}}", "subtitle": "<PERSON><PERSON><PERSON> kategori untuk {{businessName}}", "addNew": "Tambah Kategori", "addFirst": "Tambah Kategori <PERSON>", "list": {"title": "<PERSON><PERSON><PERSON>", "description": "{{count}} kate<PERSON><PERSON> di<PERSON>n"}, "search": {"placeholder": "<PERSON>i kategori berda<PERSON> nama..."}, "filters": {"type": "Tipe"}, "actions": {"edit": "<PERSON>", "delete": "<PERSON><PERSON>"}, "empty": {"noCategories": "Tidak ada kategori di<PERSON>n", "noCategoriesDescription": "<PERSON><PERSON> dengan membuat kategori pertama untuk mengorganisir item Anda", "noResults": "Tidak ada kategori yang sesuai dengan pencarian", "noResultsDescription": "Coba sesuaikan kata kunci pencarian atau filter Anda"}, "table": {"name": "<PERSON><PERSON>", "type": "Tipe", "description": "<PERSON><PERSON><PERSON><PERSON>", "status": "Status"}, "searchPlaceholder": "<PERSON>i kategori berda<PERSON> nama...", "noCategories": "Tidak ada kategori di<PERSON>n", "noSearchResults": "Tidak ada kategori yang sesuai dengan pencarian", "detail": {"subtitle": "Detail dan informasi kategori", "basicInfo": "Informasi <PERSON>", "hierarchy": "<PERSON><PERSON><PERSON><PERSON>", "usage": "Statistik Penggunaan", "metadata": "<PERSON><PERSON><PERSON>", "noDescription": "Tidak ada <PERSON>", "parentCategory": "<PERSON><PERSON><PERSON>", "childCategories": "<PERSON><PERSON><PERSON>", "noHierarchy": "Kategori ini tidak memiliki kategori induk atau anak", "totalItems": "Total Item", "activeItems": "Item Aktif", "inactiveItems": "Item Tidak Aktif"}, "create": {"title": "Tambah Kategori Baru", "description": "Tambahkan kategori baru untuk mengorganisir item Anda"}, "edit": {"title": "<PERSON>", "description": "Perbarui informasi kategori"}, "delete": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus \"{{name}}\"? Tindakan ini tidak dapat dibatalkan.", "confirm": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus kategori ini? Tindakan ini tidak dapat dibatalkan.", "hasChildren": "<PERSON><PERSON>i ini memiliki {{count}} kategori anak yang juga akan terpengaruh."}, "fields": {"name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "type": "Tipe", "color": "<PERSON><PERSON>", "parent": "<PERSON><PERSON><PERSON>", "children": "<PERSON><PERSON><PERSON>", "itemCount": "<PERSON><PERSON>", "createdAt": "Dibuat", "updatedAt": "<PERSON><PERSON><PERSON><PERSON>"}, "form": {"name": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> nama kategori"}, "description": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> kate<PERSON>i"}, "type": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON> tipe kategori"}, "parent": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON> kategori induk"}, "color": {"label": "<PERSON><PERSON>"}, "sortOrder": {"label": "Uru<PERSON> Sortir", "placeholder": "Masukkan urutan sortir (opsional)"}, "isActive": {"label": "Status Aktif", "description": "Kategori aktif dan terlihat"}, "validation": {"nameRequired": "<PERSON><PERSON> kategori wajib diisi", "typeRequired": "<PERSON><PERSON><PERSON> kategori wajib dipilih", "nameExists": "<PERSON><PERSON><PERSON> dengan nama ini sudah ada"}}, "types": {"ingredient": "Bahan Baku", "product": "Produk", "supplier": "Pemasok", "customer": "Pelanggan"}, "messages": {"createSuccess": "<PERSON><PERSON><PERSON> be<PERSON> di<PERSON>at", "createError": "<PERSON><PERSON> membuat kate<PERSON>i", "updateSuccess": "<PERSON><PERSON><PERSON>", "updateError": "<PERSON><PERSON> kategori", "deleteSuccess": "<PERSON><PERSON><PERSON>", "deleteError": "<PERSON><PERSON><PERSON> ka<PERSON>i", "deleteConfirm": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus kategori ini?", "deleteWarning": "Tindakan ini tidak dapat dibatalkan."}}}