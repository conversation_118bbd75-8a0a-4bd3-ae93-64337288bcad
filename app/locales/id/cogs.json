{"title": "Kalkulator HPP", "description": "Hitung Harga Pokok Penjualan produk Anda dengan presisi", "calculator": {"title": "Ka<PERSON>ulator Harga Pokok Penjualan", "description": "Masukkan detail produk, bahan baku, biaya tenaga kerja, dan overhead untuk menghitung HPP yang akurat dan saran harga"}, "playground": {"title": "Playground HPP", "sandboxBadge": "Sandbox", "description": {"welcome": "Selamat datang di Playground HPP!", "line1": "Ini adalah lingkungan sandbox Anda untuk bereksperimen dengan bahan baku dan produk.", "line2": "<PERSON><PERSON><PERSON> bahan baku se<PERSON>ara, bangun produk dengan resep, dan hitung HPP secara real-time.", "line3": "Ke<PERSON>ka Anda puas dengan per<PERSON>, <PERSON><PERSON> dapat menyimpannya ke database."}, "badges": {"realTime": "<PERSON><PERSON><PERSON>an Real-time", "temporaryIngredients": "Bahan Baku Sementara", "productExperimentation": "Eksperimen Produk"}, "tabs": {"ingredients": "Bahan Baku", "products": "Produk"}, "gettingStarted": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> langkah-langkah sederhana ini untuk mulai menghitung HPP Anda:", "steps": {"createIngredients": {"title": "Buat Bahan Baku", "description": "Tambah<PERSON> bahan baku dengan biaya dan satuan untuk membangun perpustakaan bahan baku Anda."}, "buildProducts": {"title": "<PERSON><PERSON>", "description": "Buat produk dengan menggabungkan bahan baku dengan jumlah penggunaan spesifik per porsi."}, "saveWhenReady": {"title": "Simpan Saat Siap", "description": "<PERSON><PERSON><PERSON>a puas dengan perhitungan, simpan ke database permanen <PERSON>."}}}}, "ingredientList": {"title": "Bahan Baku Sementara", "sandboxBadge": "Sandbox", "description": "Buat dan kelola bahan baku sementara untuk perhitungan HPP", "addIngredient": "Tambah Bahan Baku", "emptyMessage": "Belum ada bahan baku yang dibuat. <PERSON><PERSON> dengan menambahkan bahan baku pertama <PERSON>.", "addFirst": "Tambah Bahan Baku Pertama", "table": {"name": "<PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "baseCost": "<PERSON><PERSON><PERSON>", "baseQuantity": "<PERSON><PERSON><PERSON>", "unitCost": "Biaya per Sa<PERSON>", "unit": "Satuan", "status": "Status", "actions": "<PERSON><PERSON><PERSON>", "active": "Aktif", "inactive": "Tidak Aktif"}}, "ingredientForm": {"addTitle": "Tambah Bahan Baku Baru", "editTitle": "<PERSON>", "addDescription": "<PERSON><PERSON>t bahan baku sementara baru untuk perhitungan HPP", "editDescription": "<PERSON><PERSON><PERSON> <PERSON> bahan baku", "fields": {"name": {"label": "<PERSON><PERSON>", "placeholder": "contoh: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>"}, "baseUnitCost": {"label": "<PERSON><PERSON><PERSON>", "description": "Total biaya untuk jumlah dasar"}, "baseUnitQuantity": {"label": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> untuk biaya dasar"}, "unit": {"label": "Satuan", "placeholder": "<PERSON><PERSON><PERSON> satuan"}, "category": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON> ka<PERSON>i", "none": "<PERSON><PERSON> kategori"}, "supplierInfo": {"label": "Info Supplier", "placeholder": "Nama supplier atau detail", "description": "Informasi supplier opsional"}, "note": {"label": "Catatan", "placeholder": "Catatan tambahan tentang bahan baku ini"}, "isActive": {"label": "Bahan Baku Aktif", "description": "<PERSON>han baku aktif dapat digunakan dalam produk"}}, "buttons": {"add": "Tambah Bahan Baku"}}, "productList": {"title": "Produk Sementara", "sandboxBadge": "Sandbox", "description": "Buat dan kelola produk sementara dengan resep bahan baku", "addProduct": "Tambah Produk", "emptyMessage": "Belum ada produk yang dibuat. <PERSON><PERSON> dengan menambahkan produk pertama Anda.", "addFirst": "Tambah Produk Pertama", "noIngredientsWarning": "<PERSON>a perlu membuat bahan baku aktif sebelum menambahkan produk.", "table": {"productName": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "ingredients": "Bahan Baku", "cogsPerCup": "HPP per Cup", "status": "Status", "actions": "<PERSON><PERSON><PERSON>", "active": "Aktif", "inactive": "Tidak Aktif"}, "summary": {"title": "<PERSON><PERSON><PERSON>", "totalProducts": "Total Produk", "activeProducts": "Produk Aktif", "avgCogsPerCup": "Rata-rata HPP per Cup", "totalIngredientsUsed": "Total Bahan Baku Digunakan"}}, "productForm": {"addTitle": "Tambah Produk Baru", "editTitle": "Edit Produk", "addDescription": "Buat produk sementara baru dengan resep bahan baku", "editDescription": "Perbarui detail produk dan resep", "sections": {"basicInfo": "Informasi <PERSON>", "ingredients": "Bahan Baku & Resep", "ingredientsDescription": "Tambah<PERSON> bahan baku dan tentukan berapa banyak yang digunakan per porsi"}, "fields": {"name": {"label": "<PERSON><PERSON>", "placeholder": "contoh: <PERSON><PERSON>ccino, Latte, Americano"}, "description": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "Deskripsi singkat produk"}, "note": {"label": "Catatan", "placeholder": "Catatan tambahan tentang produk ini"}, "isActive": {"label": "Produk Aktif", "description": "Produk aktif disertakan dalam perhitungan"}}, "addIngredient": {"ingredient": "Bahan Baku", "selectIngredient": "<PERSON><PERSON><PERSON> bahan baku", "usagePerCup": "Penggunaan per Cup", "note": "Catatan", "notePlaceholder": "Catatan opsional", "add": "Tambah"}, "ingredientsList": {"ingredient": "Bahan Baku", "usagePerCup": "Penggunaan per Cup", "costPerCup": "Biaya per Cup", "note": "Catatan", "actions": "<PERSON><PERSON><PERSON>", "empty": "Belum ada bahan baku yang ditambahkan. Tambahkan bahan baku untuk menghitung HPP."}, "summary": {"totalCOGS": "Total HPP per Cup"}, "buttons": {"add": "Tambah Produk"}}, "fields": {"productName": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> nama produk"}, "batchSize": {"label": "Ukuran Bat<PERSON>", "placeholder": "1", "description": "Jumlah unit yang diproduksi dalam satu batch"}, "ingredientName": {"label": "<PERSON><PERSON>", "placeholder": "contoh: <PERSON><PERSON> kopi"}, "quantity": {"label": "<PERSON><PERSON><PERSON>"}, "unit": {"label": "Satuan"}, "costPerUnit": {"label": "<PERSON>rga per <PERSON>"}, "laborCostPerHour": {"label": "Biaya Tenaga Kerja per Jam"}, "laborHours": {"label": "<PERSON>"}, "overheadCosts": {"label": "Biaya Overhead", "description": "<PERSON><PERSON>, <PERSON><PERSON>, de<PERSON><PERSON><PERSON><PERSON>, dll."}, "profitMargin": {"label": "<PERSON><PERSON> (%)", "description": "Persentase margin keuntungan yang di<PERSON>an"}}, "buttons": {"addIngredient": "Tambah Bahan", "calculate": "Hitung HPP"}, "results": {"ingredientsCost": "Biaya Bahan Baku", "laborCost": "Biaya Tenaga Kerja", "totalCOGS": "Total HPP", "suggestedPrice": "<PERSON><PERSON> yang <PERSON>", "costPerUnit": "Biaya per Unit", "profitPerUnit": "Keuntungan per Unit", "profitMargin": "<PERSON><PERSON>"}, "validation": {"name": {"required": "<PERSON><PERSON> wa<PERSON><PERSON> di<PERSON>"}, "baseUnitCost": {"min": "Biaya satuan dasar tidak boleh negatif"}, "baseUnitQuantity": {"min": "<PERSON><PERSON><PERSON> satuan dasar harus lebih dari 0"}, "unit": {"required": "<PERSON><PERSON><PERSON> wajib di<PERSON>"}}}