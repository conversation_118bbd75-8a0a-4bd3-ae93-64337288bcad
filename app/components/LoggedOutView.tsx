import { useTranslation } from "react-i18next"
import { 
  Building2, Coffee, Apple, Croissant, Wheat, Grape, Cherry, <PERSON>ake, <PERSON>ie, IceCream,
  Store, ShoppingBag, Scissors, Wrench, Car, Shirt, Flower, Pizza, Utensils, 
  <PERSON>mb<PERSON>, Heart, Laptop, Camera, Paintbrush, Music, Book, Gamepad2
} from "lucide-react"
import { Button } from "~/components/ui/button"
import { useAuth } from "~/lib/providers/AuthProvider"

interface LoggedOutViewProps {
  className?: string
}

const businessIcons = [
  // Food & Beverage
  { Icon: Coffee, delay: 0 },
  { Icon: Apple, delay: 1 },
  { Icon: Croissant, delay: 2 },
  { Icon: Wheat, delay: 3 },
  { Icon: Grape, delay: 4 },
  { Icon: Cherry, delay: 0.5 },
  { Icon: Cake, delay: 1.5 },
  { Icon: Cookie, delay: 2.5 },
  { Icon: IceCream, delay: 3.5 },
  { Icon: Pizza, delay: 4.5 },
  { Icon: Utensils, delay: 5 },
  
  // Retail & Commerce
  { Icon: Store, delay: 1.2 },
  { Icon: ShoppingBag, delay: 2.8 },
  { Icon: Shirt, delay: 3.8 },
  
  // Services
  { Icon: Scissors, delay: 0.8 },
  { Icon: Wrench, delay: 1.8 },
  { Icon: Car, delay: 2.3 },
  { Icon: Flower, delay: 4.2 },
  { Icon: Dumbbell, delay: 0.3 },
  { Icon: Heart, delay: 1.3 },
  
  // Creative & Tech
  { Icon: Laptop, delay: 2.7 },
  { Icon: Camera, delay: 3.3 },
  { Icon: Paintbrush, delay: 4.7 },
  { Icon: Music, delay: 0.7 },
  { Icon: Book, delay: 1.7 },
  { Icon: Gamepad2, delay: 3.7 },
];

export function LoggedOutView({ className }: LoggedOutViewProps) {
  const { t } = useTranslation(['common', 'dashboard'])
  const { showAuthModal } = useAuth()

  return (
    <div className={`relative min-h-screen overflow-hidden ${className}`}>
      {/* Falling Business Icons Background */}
      <div className="absolute inset-0 pointer-events-none">
        <style>{`
          @keyframes fall {
            0% {
              transform: translateY(-200px) rotate(0deg);
              opacity: 0;
            }
            5% {
              opacity: 0;
            }
            15% {
              opacity: 1;
            }
            85% {
              opacity: 1;
            }
            100% {
              transform: translateY(calc(100vh + 100px)) rotate(360deg);
              opacity: 0;
            }
          }
          .falling-icon {
            animation: fall linear infinite;
            opacity: 0;
          }
        `}</style>
        
        {/* Generate multiple columns of falling icons */}
        {Array.from({ length: 8 }).map((_, columnIndex) => (
          <div
            key={columnIndex}
            className="absolute top-0 w-16"
            style={{
              left: `${(columnIndex * 12.5)}%`,
            }}
          >
            {businessIcons.map(({ Icon, delay }, iconIndex) => (
              <div
                key={`${columnIndex}-${iconIndex}`}
                className="falling-icon absolute text-muted-foreground/40"
                style={{
                  animationDuration: `${10 + Math.random() * 6}s`,
                  animationDelay: `${delay + columnIndex * 1.5 + iconIndex * 2 + 2}s`,
                  left: `${Math.random() * 50}px`,
                }}
              >
                <Icon size={24 + Math.random() * 16} />
              </div>
            ))}
          </div>
        ))}
      </div>

      {/* Overlay with Sign In Prompt */}
      <div className="absolute inset-0 flex items-center justify-center bg-background/80 z-10">
        <div className="text-center space-y-6 p-8 max-w-md">
          <div className="flex items-center justify-center w-20 h-20 mx-auto rounded-full bg-primary/10">
            <img
              src="/kwaci.webp"
              alt="KWACI Logo"
              className="w-12 h-12 object-contain"
            />
          </div>
          
          <div className="space-y-4">
            <h1 className="text-3xl font-bold tracking-tight">
              Welcome to KWACI Grow
            </h1>
            <p className="text-lg text-muted-foreground">
              Sign in to access your business management platform
            </p>
          </div>

          <div className="flex flex-col gap-3">
            <Button 
              size="lg" 
              onClick={() => showAuthModal("login")}
              className="gap-2"
            >
              <Building2 className="w-4 h-4" />
              {t('common:buttons.signIn')}
            </Button>
            <Button 
              variant="outline" 
              size="lg"
              onClick={() => showAuthModal("register")}
            >
              {t('common:buttons.signUp')}
            </Button>
          </div>

          <p className="text-sm text-muted-foreground">
            New to KWACI? Create an account to get started.
          </p>
        </div>
      </div>
    </div>
  )
}
