import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Edit, Trash2, Plus, Beaker } from "lucide-react";
import { TempIngredientForm } from "./TempIngredientForm";
import { formatCurrency } from "~/utils/formatters";
import { calculateUnitCost } from "~/lib/types/cogs";
import { useCurrentBusinessCurrency } from "~/lib/stores/businessStore";
import type { TempIngredient } from "~/lib/types/cogs";

interface TempIngredientListProps {
  ingredients: TempIngredient[];
  onAdd: (ingredient: Omit<TempIngredient, 'id'>) => void;
  onEdit: (id: string, ingredient: Omit<TempIngredient, 'id'>) => void;
  onDelete: (id: string) => void;
}

export function TempIngredientList({ ingredients, onAdd, onEdit, onDelete }: TempIngredientListProps) {
  const { t } = useTranslation(['cogs', 'common']);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingIngredient, setEditingIngredient] = useState<TempIngredient | undefined>();
  const currentCurrency = useCurrentBusinessCurrency();

  const handleAdd = (ingredient: Omit<TempIngredient, 'id'>) => {
    onAdd(ingredient);
    setIsFormOpen(false);
  };

  const handleEdit = (ingredient: Omit<TempIngredient, 'id'>) => {
    if (editingIngredient) {
      onEdit(editingIngredient.id, ingredient);
      setEditingIngredient(undefined);
    }
  };

  const startEdit = (ingredient: TempIngredient) => {
    setEditingIngredient(ingredient);
  };

  const handleFormClose = () => {
    setIsFormOpen(false);
    setEditingIngredient(undefined);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CardTitle className="flex items-center gap-2">
              <Beaker className="h-5 w-5" />
              {t('cogs:ingredientList.title')}
            </CardTitle>
            <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
              {t('cogs:ingredientList.sandboxBadge')}
            </Badge>
          </div>
          <Button onClick={() => setIsFormOpen(true)} size="sm">
            <Plus className="h-4 w-4 mr-2" />
            {t('cogs:ingredientList.addIngredient')}
          </Button>
        </div>
        <p className="text-sm text-muted-foreground">
          {t('cogs:ingredientList.description')}
        </p>
      </CardHeader>
      <CardContent>
        {ingredients.length === 0 ? (
          <div className="text-center py-8">
            <Beaker className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground mb-4">{t('cogs:ingredientList.emptyMessage')}</p>
            <Button onClick={() => setIsFormOpen(true)} variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              {t('cogs:ingredientList.addFirst')}
            </Button>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('cogs:ingredientList.table.name')}</TableHead>
                <TableHead>{t('cogs:ingredientList.table.category')}</TableHead>
                <TableHead>{t('cogs:ingredientList.table.baseCost')}</TableHead>
                <TableHead>{t('cogs:ingredientList.table.baseQuantity')}</TableHead>
                <TableHead>{t('cogs:ingredientList.table.unitCost')}</TableHead>
                <TableHead>{t('cogs:ingredientList.table.unit')}</TableHead>
                <TableHead>{t('cogs:ingredientList.table.status')}</TableHead>
                <TableHead className="w-[100px]">{t('cogs:ingredientList.table.actions')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {ingredients.map((ingredient) => (
                <TableRow key={ingredient.id}>
                  <TableCell className="font-medium">
                    {ingredient.name}
                    {ingredient.note && (
                      <p className="text-xs text-muted-foreground mt-1">
                        {ingredient.note}
                      </p>
                    )}
                  </TableCell>
                  <TableCell>
                    {ingredient.category ? (
                      <Badge variant="outline">{ingredient.category}</Badge>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell>{formatCurrency(ingredient.baseUnitCost, currentCurrency)}</TableCell>
                  <TableCell>{ingredient.baseUnitQuantity}</TableCell>
                  <TableCell>{formatCurrency(calculateUnitCost(ingredient), currentCurrency)}</TableCell>
                  <TableCell>
                    <Badge variant="secondary">{ingredient.unit}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={ingredient.isActive ? "default" : "secondary"}>
                      {ingredient.isActive ? t('cogs:ingredientList.table.active') : t('cogs:ingredientList.table.inactive')}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => startEdit(ingredient)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onDelete(ingredient.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>

      <TempIngredientForm
        isOpen={isFormOpen || !!editingIngredient}
        onClose={handleFormClose}
        onSave={editingIngredient ? handleEdit : handleAdd}
        ingredient={editingIngredient}
      />
    </Card>
  );
}
