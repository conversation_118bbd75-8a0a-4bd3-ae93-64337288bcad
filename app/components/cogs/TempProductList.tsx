import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Edit, Trash2, Plus, Package, Calculator } from "lucide-react";
import { TempProductForm } from "./TempProductForm";
import { formatCurrency } from "~/utils/formatters";
import { calculateProductCOGS } from "~/lib/types/cogs";
import { useCurrentBusinessCurrency } from "~/lib/stores/businessStore";
import type { TempProduct, TempIngredient } from "~/lib/types/cogs";

interface TempProductListProps {
  products: TempProduct[];
  ingredients: TempIngredient[];
  onAdd: (product: Omit<TempProduct, 'id'>) => void;
  onEdit: (id: string, product: Omit<TempProduct, 'id'>) => void;
  onDelete: (id: string) => void;
}

export function TempProductList({ products, ingredients, onAdd, onEdit, onDelete }: TempProductListProps) {
  const { t } = useTranslation(['cogs', 'common']);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<TempProduct | undefined>();
  const currentCurrency = useCurrentBusinessCurrency();

  const handleAdd = (product: Omit<TempProduct, 'id'>) => {
    onAdd(product);
    setIsFormOpen(false);
  };

  const handleEdit = (product: Omit<TempProduct, 'id'>) => {
    if (editingProduct) {
      onEdit(editingProduct.id, product);
      setEditingProduct(undefined);
    }
  };

  const startEdit = (product: TempProduct) => {
    setEditingProduct(product);
  };

  const handleFormClose = () => {
    setIsFormOpen(false);
    setEditingProduct(undefined);
  };

  const getProductIngredientCount = (product: TempProduct) => {
    return product.ingredients.length;
  };

  const hasActiveIngredients = ingredients.some(ing => ing.isActive);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              {t('cogs:productList.title')}
            </CardTitle>
            <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
              {t('cogs:productList.sandboxBadge')}
            </Badge>
          </div>
          <Button 
            onClick={() => setIsFormOpen(true)} 
            size="sm"
            disabled={!hasActiveIngredients}
          >
            <Plus className="h-4 w-4 mr-2" />
            {t('cogs:productList.addProduct')}
          </Button>
        </div>
        <p className="text-sm text-muted-foreground">
          {t('cogs:productList.description')}
        </p>
        {!hasActiveIngredients && (
          <p className="text-sm text-amber-600">
            {t('cogs:productList.noIngredientsWarning')}
          </p>
        )}
      </CardHeader>
      <CardContent>
        {products.length === 0 ? (
          <div className="text-center py-8">
            <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground mb-4">{t('cogs:productList.emptyMessage')}</p>
            {hasActiveIngredients ? (
              <Button onClick={() => setIsFormOpen(true)} variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                {t('cogs:productList.addFirst')}
              </Button>
            ) : (
              <p className="text-sm text-muted-foreground">
                {t('cogs:productList.noIngredientsWarning')}
              </p>
            )}
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('cogs:productList.table.productName')}</TableHead>
                <TableHead>{t('cogs:productList.table.description')}</TableHead>
                <TableHead>{t('cogs:productList.table.ingredients')}</TableHead>
                <TableHead>{t('cogs:productList.table.cogsPerCup')}</TableHead>
                <TableHead>{t('cogs:productList.table.status')}</TableHead>
                <TableHead className="w-[100px]">{t('cogs:productList.table.actions')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {products.map((product) => {
                const cogsPerCup = calculateProductCOGS(product, ingredients);
                const ingredientCount = getProductIngredientCount(product);

                return (
                  <TableRow key={product.id}>
                    <TableCell className="font-medium">
                      <div>
                        <p className="font-medium">{product.name}</p>
                        {product.note && (
                          <p className="text-xs text-muted-foreground mt-1">
                            {product.note}
                          </p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <p className="text-sm text-muted-foreground max-w-[200px] truncate">
                        {product.description || '-'}
                      </p>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">
                          {ingredientCount} ingredient{ingredientCount !== 1 ? 's' : ''}
                        </Badge>
                        {ingredientCount > 0 && (
                          <Calculator className="h-4 w-4 text-muted-foreground" />
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-primary">
                          {formatCurrency(cogsPerCup, currentCurrency)}
                        </span>
                        {ingredientCount === 0 && (
                          <Badge variant="secondary" className="text-xs">
                            No ingredients
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={product.isActive ? "default" : "secondary"}>
                        {product.isActive ? t('cogs:productList.table.active') : t('cogs:productList.table.inactive')}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => startEdit(product)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onDelete(product.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        )}

        {/* Summary Section */}
        {products.length > 0 && (
          <div className="mt-6 p-4 bg-muted/50 rounded-lg">
            <h4 className="font-medium mb-2">{t('cogs:productList.summary.title')}</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <p className="text-muted-foreground">{t('cogs:productList.summary.totalProducts')}</p>
                <p className="font-medium">{products.length}</p>
              </div>
              <div>
                <p className="text-muted-foreground">{t('cogs:productList.summary.activeProducts')}</p>
                <p className="font-medium">{products.filter(p => p.isActive).length}</p>
              </div>
              <div>
                <p className="text-muted-foreground">{t('cogs:productList.summary.avgCogsPerCup')}</p>
                <p className="font-medium">
                  {formatCurrency(
                    products.length > 0
                      ? products.reduce((sum, p) => sum + calculateProductCOGS(p, ingredients), 0) / products.length
                      : 0,
                    currentCurrency
                  )}
                </p>
              </div>
              <div>
                <p className="text-muted-foreground">{t('cogs:productList.summary.totalIngredientsUsed')}</p>
                <p className="font-medium">
                  {new Set(products.flatMap(p => p.ingredients.map(i => i.ingredientId))).size}
                </p>
              </div>
            </div>
          </div>
        )}
      </CardContent>

      <TempProductForm
        isOpen={isFormOpen || !!editingProduct}
        onClose={handleFormClose}
        onSave={editingProduct ? handleEdit : handleAdd}
        product={editingProduct}
        availableIngredients={ingredients}
      />
    </Card>
  );
}
