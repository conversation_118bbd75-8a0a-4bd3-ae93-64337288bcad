import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useTranslation } from "react-i18next";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Textarea } from "~/components/ui/textarea";
import { Switch } from "~/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "~/components/ui/sheet";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Badge } from "~/components/ui/badge";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "~/components/ui/form";
import { Plus, Trash2, Package } from "lucide-react";
import { formatCurrency } from "~/utils/formatters";
import { calculateIngredientCostPerCup } from "~/lib/types/cogs";
import { useCurrentBusinessCurrency } from "~/lib/stores/businessStore";
import type { TempProduct, TempIngredient, TempProductIngredient } from "~/lib/types/cogs";

interface TempProductFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (product: Omit<TempProduct, 'id'>) => void;
  product?: TempProduct;
  availableIngredients: TempIngredient[];
}

export function TempProductForm({ isOpen, onClose, onSave, product, availableIngredients }: TempProductFormProps) {
  const { t } = useTranslation(['cogs', 'common']);
  const isEditing = !!product;
  const currentCurrency = useCurrentBusinessCurrency();

  const productSchema = z.object({
    name: z.string().min(1, t('cogs:validation.name.required')),
    description: z.string().optional(),
    note: z.string().optional(),
    isActive: z.boolean(),
  });

  type FormData = z.infer<typeof productSchema>;

  const form = useForm<FormData>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: "",
      description: "",
      note: "",
      isActive: true,
    },
  });

  const [productIngredients, setProductIngredients] = useState<TempProductIngredient[]>([]);
  const [selectedIngredientId, setSelectedIngredientId] = useState("");
  const [usagePerCup, setUsagePerCup] = useState("");
  const [ingredientNote, setIngredientNote] = useState("");

  // Initialize form data when editing
  useEffect(() => {
    if (product) {
      form.reset({
        name: product.name,
        description: product.description,
        note: product.note,
        isActive: product.isActive,
      });
      setProductIngredients(product.ingredients);
    } else {
      form.reset({
        name: "",
        description: "",
        note: "",
        isActive: true,
      });
      setProductIngredients([]);
    }
  }, [product, form]);

  const handleAddIngredient = () => {
    if (!selectedIngredientId || !usagePerCup) return;

    const usage = parseFloat(usagePerCup);
    if (usage <= 0) return;

    // Check if ingredient already exists
    const existingIndex = productIngredients.findIndex(pi => pi.ingredientId === selectedIngredientId);
    
    if (existingIndex >= 0) {
      // Update existing ingredient
      const updated = [...productIngredients];
      updated[existingIndex] = {
        ingredientId: selectedIngredientId,
        usagePerCup: usage,
        note: ingredientNote,
      };
      setProductIngredients(updated);
    } else {
      // Add new ingredient
      setProductIngredients([...productIngredients, {
        ingredientId: selectedIngredientId,
        usagePerCup: usage,
        note: ingredientNote,
      }]);
    }

    // Reset form
    setSelectedIngredientId("");
    setUsagePerCup("");
    setIngredientNote("");
  };

  const handleRemoveIngredient = (ingredientId: string) => {
    setProductIngredients(productIngredients.filter(pi => pi.ingredientId !== ingredientId));
  };

  const onSubmit = (data: FormData) => {
    const productData: Omit<TempProduct, 'id'> = {
      name: data.name.trim(),
      description: data.description?.trim() || "",
      note: data.note || "",
      isActive: data.isActive,
      ingredients: productIngredients,
    };

    onSave(productData);
    onClose();
  };

  const handleClose = () => {
    form.reset();
    setProductIngredients([]);
    setSelectedIngredientId("");
    setUsagePerCup("");
    setIngredientNote("");
    onClose();
  };

  const activeIngredients = availableIngredients.filter(ing => ing.isActive);
  const availableForSelection = activeIngredients.filter(ing => 
    !productIngredients.some(pi => pi.ingredientId === ing.id)
  );

  const calculateTotalCOGS = () => {
    return productIngredients.reduce((total, pi) => {
      const ingredient = availableIngredients.find(ing => ing.id === pi.ingredientId);
      if (!ingredient) return total;
      return total + calculateIngredientCostPerCup(ingredient, pi.usagePerCup);
    }, 0);
  };

  return (
    <Sheet open={isOpen} onOpenChange={handleClose}>
      <SheetContent className="w-[800px] sm:w-[1000px] lg:w-[1200px] p-0">
        <SheetHeader className="flex-none border-b p-6 text-left">
          <SheetTitle>
            {isEditing ? t('cogs:productForm.editTitle') : t('cogs:productForm.addTitle')}
          </SheetTitle>
          <SheetDescription>
            {isEditing ? t('cogs:productForm.editDescription') : t('cogs:productForm.addDescription')}
          </SheetDescription>
        </SheetHeader>

        <div className="flex-1 overflow-y-auto">
          <div className="space-y-6 p-6">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Product Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">{t('cogs:productForm.sections.basicInfo')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('cogs:productForm.fields.name.label')}</FormLabel>
                      <FormControl>
                        <Input placeholder={t('cogs:productForm.fields.name.placeholder')} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('cogs:productForm.fields.description.label')}</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={t('cogs:productForm.fields.description.placeholder')}
                          className="resize-none"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="note"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('cogs:productForm.fields.note.label')}</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={t('cogs:productForm.fields.note.placeholder')}
                          className="resize-none"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="isActive"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">
                          {t('cogs:productForm.fields.isActive.label')}
                        </FormLabel>
                        <FormDescription>
                          {t('cogs:productForm.fields.isActive.description')}
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Ingredients Section */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">{t('cogs:productForm.sections.ingredients')}</CardTitle>
                <p className="text-sm text-muted-foreground">
                  {t('cogs:productForm.sections.ingredientsDescription')}
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Add Ingredient Form */}
                <div className="space-y-4 p-4 border rounded-lg">
                  <div>
                    <label className="text-sm font-medium">{t('cogs:productForm.addIngredient.ingredient')}</label>
                    <Select value={selectedIngredientId} onValueChange={setSelectedIngredientId}>
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder={t('cogs:productForm.addIngredient.selectIngredient')} />
                      </SelectTrigger>
                      <SelectContent>
                        {availableForSelection.map((ingredient) => (
                          <SelectItem key={ingredient.id} value={ingredient.id}>
                            {ingredient.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="text-sm font-medium">{t('cogs:productForm.addIngredient.usagePerCup')}</label>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="0.00"
                      value={usagePerCup}
                      onChange={(e) => setUsagePerCup(e.target.value)}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">{t('cogs:productForm.addIngredient.note')}</label>
                    <Input
                      placeholder={t('cogs:productForm.addIngredient.notePlaceholder')}
                      value={ingredientNote}
                      onChange={(e) => setIngredientNote(e.target.value)}
                      className="mt-1"
                    />
                  </div>
                  <div className="flex justify-end">
                    <Button
                      type="button"
                      onClick={handleAddIngredient}
                      disabled={!selectedIngredientId || !usagePerCup}
                      size="sm"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      {t('cogs:productForm.addIngredient.add')}
                    </Button>
                  </div>
                </div>

                {/* Ingredients List */}
                {productIngredients.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>{t('cogs:productForm.ingredientsList.ingredient')}</TableHead>
                        <TableHead>{t('cogs:productForm.ingredientsList.usagePerCup')}</TableHead>
                        <TableHead>{t('cogs:productForm.ingredientsList.costPerCup')}</TableHead>
                        <TableHead>{t('cogs:productForm.ingredientsList.note')}</TableHead>
                        <TableHead className="w-[80px]">{t('cogs:productForm.ingredientsList.actions')}</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {productIngredients.map((pi) => {
                        const ingredient = availableIngredients.find(ing => ing.id === pi.ingredientId);
                        if (!ingredient) return null;

                        const costPerCup = calculateIngredientCostPerCup(ingredient, pi.usagePerCup);

                        return (
                          <TableRow key={pi.ingredientId}>
                            <TableCell>
                              <div>
                                <p className="font-medium">{ingredient.name}</p>
                                <p className="text-xs text-muted-foreground">
                                  {ingredient.unit} • {formatCurrency(ingredient.baseUnitCost / ingredient.baseUnitQuantity, currentCurrency)}/unit
                                </p>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline">{pi.usagePerCup} {ingredient.unit}</Badge>
                            </TableCell>
                            <TableCell>
                              <span className="font-medium text-primary">{formatCurrency(costPerCup, currentCurrency)}</span>
                            </TableCell>
                            <TableCell>
                              <span className="text-sm text-muted-foreground">
                                {pi.note || '-'}
                              </span>
                            </TableCell>
                            <TableCell>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => handleRemoveIngredient(pi.ingredientId)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Package className="h-8 w-8 mx-auto mb-2" />
                    <p>{t('cogs:productForm.ingredientsList.empty')}</p>
                  </div>
                )}

                {/* COGS Summary */}
                {productIngredients.length > 0 && (
                  <div className="p-4 bg-muted/50 rounded-lg">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">{t('cogs:productForm.summary.totalCOGS')}</span>
                      <span className="text-lg font-bold text-primary">
                        {formatCurrency(calculateTotalCOGS(), currentCurrency)}
                      </span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

              <div className="flex justify-end space-x-2 pt-4">
                <Button type="button" variant="outline" onClick={handleClose}>
                  {t('common:buttons.cancel')}
                </Button>
                <Button type="submit">
                  {isEditing ? t('common:buttons.save') : t('cogs:productForm.buttons.add')}
                </Button>
              </div>
            </form>
          </Form>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
