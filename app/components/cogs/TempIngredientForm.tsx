import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useTranslation } from "react-i18next";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Textarea } from "~/components/ui/textarea";
import { Switch } from "~/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "~/components/ui/sheet";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "~/components/ui/form";
import { UNIT_OPTIONS, INGREDIENT_CATEGORIES } from "~/lib/types/cogs";
import type { TempIngredient } from "~/lib/types/cogs";

interface TempIngredientFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (ingredient: Omit<TempIngredient, 'id'>) => void;
  ingredient?: TempIngredient;
}

export function TempIngredientForm({ isOpen, onClose, onSave, ingredient }: TempIngredientFormProps) {
  const { t } = useTranslation(['cogs', 'common']);
  const isEditing = !!ingredient;

  const ingredientSchema = z.object({
    name: z.string().min(1, t('cogs:validation.name.required')),
    baseUnitCost: z.number().min(0, t('cogs:validation.baseUnitCost.min')),
    baseUnitQuantity: z.number().min(0.01, t('cogs:validation.baseUnitQuantity.min')),
    unit: z.string().min(1, t('cogs:validation.unit.required')),
    supplierInfo: z.string().optional(),
    category: z.string().optional(),
    note: z.string().optional(),
    isActive: z.boolean(),
  });

  type FormData = z.infer<typeof ingredientSchema>;

  const form = useForm<FormData>({
    resolver: zodResolver(ingredientSchema),
    defaultValues: {
      name: "",
      baseUnitCost: 0,
      baseUnitQuantity: 1,
      unit: "ml",
      supplierInfo: "",
      category: "",
      note: "",
      isActive: true,
    },
  });

  // Initialize form data when editing
  useEffect(() => {
    if (ingredient) {
      form.reset({
        name: ingredient.name,
        baseUnitCost: ingredient.baseUnitCost,
        baseUnitQuantity: ingredient.baseUnitQuantity,
        unit: ingredient.unit,
        supplierInfo: ingredient.supplierInfo || "",
        category: ingredient.category || "none",
        note: ingredient.note,
        isActive: ingredient.isActive,
      });
    } else {
      form.reset({
        name: "",
        baseUnitCost: 0,
        baseUnitQuantity: 1,
        unit: "ml",
        supplierInfo: "",
        category: "none",
        note: "",
        isActive: true,
      });
    }
  }, [ingredient, form]);

  const onSubmit = (data: FormData) => {
    const ingredientData: Omit<TempIngredient, 'id'> = {
      name: data.name.trim(),
      baseUnitCost: data.baseUnitCost,
      baseUnitQuantity: data.baseUnitQuantity,
      unit: data.unit,
      supplierInfo: data.supplierInfo?.trim(),
      category: data.category && data.category !== "none" ? data.category : undefined,
      note: data.note || "",
      isActive: data.isActive,
    };

    onSave(ingredientData);
    onClose();
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  return (
    <Sheet open={isOpen} onOpenChange={handleClose}>
      <SheetContent className="w-[400px] sm:w-[540px] p-0">
        <SheetHeader className="flex-none border-b p-6 text-left">
          <SheetTitle>
            {isEditing ? t('cogs:ingredientForm.editTitle') : t('cogs:ingredientForm.addTitle')}
          </SheetTitle>
          <SheetDescription>
            {isEditing ? t('cogs:ingredientForm.editDescription') : t('cogs:ingredientForm.addDescription')}
          </SheetDescription>
        </SheetHeader>

        <div className="flex-1 overflow-y-auto">
          <div className="space-y-6 p-6">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('cogs:ingredientForm.fields.name.label')}</FormLabel>
                  <FormControl>
                    <Input placeholder={t('cogs:ingredientForm.fields.name.placeholder')} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="baseUnitCost"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('cogs:ingredientForm.fields.baseUnitCost.label')}</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormDescription>
                      {t('cogs:ingredientForm.fields.baseUnitCost.description')}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="baseUnitQuantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('cogs:ingredientForm.fields.baseUnitQuantity.label')}</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="1"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormDescription>
                      {t('cogs:ingredientForm.fields.baseUnitQuantity.description')}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="unit"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('cogs:ingredientForm.fields.unit.label')}</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={t('cogs:ingredientForm.fields.unit.placeholder')} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {UNIT_OPTIONS.map((unit) => (
                        <SelectItem key={unit.value} value={unit.value}>
                          {unit.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('cogs:ingredientForm.fields.category.label')}</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={t('cogs:ingredientForm.fields.category.placeholder')} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="none">{t('cogs:ingredientForm.fields.category.none')}</SelectItem>
                      {INGREDIENT_CATEGORIES.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="supplierInfo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('cogs:ingredientForm.fields.supplierInfo.label')}</FormLabel>
                  <FormControl>
                    <Input placeholder={t('cogs:ingredientForm.fields.supplierInfo.placeholder')} {...field} />
                  </FormControl>
                  <FormDescription>
                    {t('cogs:ingredientForm.fields.supplierInfo.description')}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="note"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('cogs:ingredientForm.fields.note.label')}</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={t('cogs:ingredientForm.fields.note.placeholder')}
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isActive"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">
                      {t('cogs:ingredientForm.fields.isActive.label')}
                    </FormLabel>
                    <FormDescription>
                      {t('cogs:ingredientForm.fields.isActive.description')}
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

                <div className="flex justify-end space-x-2 pt-4">
                  <Button type="button" variant="outline" onClick={handleClose}>
                    {t('common:buttons.cancel')}
                  </Button>
                  <Button type="submit">
                    {isEditing ? t('common:buttons.save') : t('cogs:ingredientForm.buttons.add')}
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
