"use client"

import * as React from "react"
import {
  Frame,
  Map,
  PieChart,
  Package,
  Calculator,
  Users,
  Cog,
  TrendingUp,
  UserCog,
  Building2,
  Bell,
} from "lucide-react"
import { useTranslation } from "react-i18next"

import { NavMain } from "~/components/nav-main"
import { NavProjects } from "~/components/nav-projects"
import { NavUser } from "~/components/nav-user"
import { BusinessSwitcher } from "~/components/BusinessSwitcher"
import { useAuth } from "~/lib/providers/AuthProvider"
import { useCurrentBusiness } from "~/lib/stores/businessStore"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "~/components/ui/sidebar"



export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { t } = useTranslation('navigation');
  const { isAuthenticated, user } = useAuth();
  const currentBusiness = useCurrentBusiness();

  // Helper function to build URLs with business context
  const buildBusinessUrl = (basePath: string) => {
    if (!currentBusiness?.id) return basePath;
    return `${basePath}?businessId=${currentBusiness.id}`;
  };

  const navMain = [
    {
      title: t('sections.inventorySupply'),
      url: "#",
      icon: Package,
      isActive: true,
      items: [
        {
          title: t('inventory.ingredient'),
          url: buildBusinessUrl("/inventory/ingredients"),
        },
        {
          title: t('inventory.product'),
          url: buildBusinessUrl("/inventory/products"),
        },
        {
          title: t('inventory.categories'),
          url: buildBusinessUrl("/categories"),
        },
        {
          title: t('inventory.warehouse'),
          url: "#",
        },
      ],
    },
    {
      title: t('sections.financials'),
      url: "#",
      icon: Calculator,
      items: [
        {
          title: t('financials.cogsCalculator'),
          url: buildBusinessUrl("/cogs"),
        },
        {
          title: t('financials.accounting'),
          url: "#",
        },
      ],
    },
    {
      title: t('sections.peopleOrganization'),
      url: "#",
      icon: Users,
      items: [
        {
          title: t('people.people'),
          url: "#",
        },
        {
          title: t('people.branch'),
          url: "#",
        },
        {
          title: t('people.business'),
          url: "#",
        },
      ],
    },
    {
      title: t('sections.operations'),
      url: "#",
      icon: Cog,
      items: [
        {
          title: t('operations.operation'),
          url: "#",
        },
      ],
    },
    {
      title: t('sections.salesMarketing'),
      url: "#",
      icon: TrendingUp,
      items: [
        {
          title: t('sales.sale'),
          url: "#",
        },
        {
          title: t('sales.marketing'),
          url: "#",
        },
      ],
    },
  ];

  // General settings navigation - always available
  const generalSettingsNav = [
    {
      title: t('sections.settings'),
      url: "#",
      icon: UserCog,
      items: [
        {
          title: "Notifications",
          url: "/notifications",
        },
        {
          title: "User Settings",
          url: "/settings/user",
        },
      ],
    },
  ];

  // Business-specific settings navigation - only show if business is selected
  const businessSettingsNav = currentBusiness ? [
    {
      title: "Business Settings",
      url: "#",
      icon: Building2,
      items: [
        {
          title: t('settings.rbac'),
          url: `/business/${currentBusiness.id}/settings/rbac`,
        },
      ],
    },
  ] : [];

  const projects = [
    {
      name: t('projects.designEngineering'),
      url: "#",
      icon: Frame,
    },
    {
      name: t('projects.salesMarketing'),
      url: "#",
      icon: PieChart,
    },
    {
      name: t('projects.travel'),
      url: "#",
      icon: Map,
    },
  ];

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <BusinessSwitcher />
      </SidebarHeader>
      <SidebarContent>
        {isAuthenticated ? (
          <>
            <NavMain items={navMain} label="Business Operations" />
            <NavMain items={generalSettingsNav} label="Account" />
            {businessSettingsNav.length > 0 && <NavMain items={businessSettingsNav} label="Business Settings" />}
            <NavProjects projects={projects} />
          </>
        ) : (
          <div className="flex flex-col items-center justify-center p-4 text-center text-sm text-muted-foreground">
            <img
              src="/kwaci.webp"
              alt="KWACI Logo"
              className="mb-2 h-12 w-12 object-contain"
            />
            <p className="font-semibold">KWACI Grow</p>
            <p className="text-xs">{t('auth.loginToAccess')}</p>
          </div>
        )}
      </SidebarContent>
      <SidebarFooter>
        {isAuthenticated && user ? (
          <NavUser user={{
            name: user.name,
            email: user.email,
            avatar: user.image || "/avatars/default.jpg"
          }} />
        ) : (
          <div className="p-2 text-center text-xs text-muted-foreground">
            {t('auth.notLoggedIn')}
          </div>
        )}
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}