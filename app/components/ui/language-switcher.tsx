import * as React from "react"
import { Languages } from "lucide-react"
import { useTranslation } from "react-i18next"

import { Button } from "~/components/ui/button"

// Language switcher component with react-i18next integration
export function LanguageSwitcher() {
  const { i18n, t } = useTranslation('common')
  const [mounted, setMounted] = React.useState(false)

  const toggleLanguage = () => {
    const newLanguage = i18n.language === 'en' ? 'id' : 'en'
    i18n.changeLanguage(newLanguage)
  }

  React.useEffect(() => {
    setMounted(true)
  }, [])

  // Don't render until mounted to avoid hydration mismatch
  if (!mounted) {
    return (
      <Button
        variant="ghost"
        size="sm"
        className="h-9 px-3 gap-2"
        title={t('navigation.switchLanguage')}
      >
        <Languages className="h-4 w-4" />
        <span className="text-xs font-medium uppercase">
          en
        </span>
        <span className="sr-only">{t('navigation.toggleLanguage')}</span>
      </Button>
    )
  }

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleLanguage}
      className="h-9 px-3 gap-2"
      title={i18n.language === 'en' ? t('navigation.switchToIndonesian') : t('navigation.switchToEnglish')}
    >
      <Languages className="h-4 w-4" />
      <span className="text-xs font-medium uppercase">
        {i18n.language}
      </span>
      <span className="sr-only">{t('navigation.toggleLanguage')}</span>
    </Button>
  )
}