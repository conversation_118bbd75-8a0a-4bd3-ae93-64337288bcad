import React, { useState, useEffect } from 'react';
import { Input } from './input';
import { cn } from '~/lib/utils';

interface CurrencyInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  currency?: string;
  locale?: string;
  disabled?: boolean;
  min?: number;
  step?: number;
}

export function CurrencyInput({
  value,
  onChange,
  placeholder = "0",
  className,
  currency = 'IDR',
  locale = 'id-ID',
  disabled = false,
  min = 0,
  step = 0.01,
  ...props
}: CurrencyInputProps) {
  const [displayValue, setDisplayValue] = useState('');
  const [isFocused, setIsFocused] = useState(false);

  // Format number for display
  const formatForDisplay = (num: number) => {
    if (isNaN(num)) return '';
    return new Intl.NumberFormat(locale, {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(num);
  };

  // Parse display value to number
  const parseDisplayValue = (displayVal: string) => {
    // Remove all non-digit characters except decimal point
    const cleanValue = displayVal.replace(/[^\d.,]/g, '');
    // Handle Indonesian number format (comma as thousand separator, period as decimal)
    const normalizedValue = cleanValue.replace(/\./g, '').replace(',', '.');
    return parseFloat(normalizedValue) || 0;
  };

  // Update display value when value prop changes
  useEffect(() => {
    if (!isFocused) {
      const numValue = parseFloat(value) || 0;
      setDisplayValue(formatForDisplay(numValue));
    }
  }, [value, isFocused, locale]);

  // Initialize display value on mount
  useEffect(() => {
    const numValue = parseFloat(value) || 0;
    setDisplayValue(formatForDisplay(numValue));
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    
    // Allow only numbers, commas, and periods
    const validChars = /^[\d.,]*$/;
    if (!validChars.test(inputValue)) {
      return;
    }

    setDisplayValue(inputValue);
    
    // Convert to numeric value and call onChange
    const numericValue = parseDisplayValue(inputValue);
    onChange(numericValue.toString());
  };

  const handleFocus = () => {
    setIsFocused(true);
    // Show raw numeric value when focused for easier editing
    const numValue = parseFloat(value) || 0;
    setDisplayValue(numValue.toString());
  };

  const handleBlur = () => {
    setIsFocused(false);
    // Format for display when not focused
    const numValue = parseFloat(value) || 0;
    setDisplayValue(formatForDisplay(numValue));
  };

  const getCurrencySymbol = () => {
    switch (currency) {
      case 'IDR':
        return 'Rp';
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      default:
        return currency;
    }
  };

  return (
    <div className="relative">
      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground text-sm pointer-events-none">
        {getCurrencySymbol()}
      </div>
      <Input
        type="text"
        value={displayValue}
        onChange={handleInputChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        placeholder={placeholder}
        className={cn("pl-12", className)}
        disabled={disabled}
        {...props}
      />
    </div>
  );
}
