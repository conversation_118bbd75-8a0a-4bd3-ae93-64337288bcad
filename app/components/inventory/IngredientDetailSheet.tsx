import { useTranslation } from 'react-i18next';
import { Package, ExternalLink, Edit, Trash2 } from 'lucide-react';
import { memo, useCallback, useEffect, useState } from 'react';
import { useRBAC } from '~/lib/hooks/useRBAC';
import { <PERSON><PERSON> } from "~/components/ui/button";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "~/components/ui/sheet";
import { Badge } from "~/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import type { Ingredient, IngredientWithCategory } from "~/lib/types/inventory";

interface ProductUsage {
  id: string;
  productId: string;
  ingredientId: string;
  quantityNeeded: string;
  createdAt: Date;
  costPerCup: number;
  product: {
    id: string;
    name: string;
    description: string | null;
    note: string | null;
    categoryId: string | null;
    isActive: boolean;
    cogsPerCup: string | null;
    businessId: string;
    createdAt: Date;
    updatedAt: Date;
  };
}

interface IngredientUsageData {
  ingredient: IngredientWithCategory;
  productUsage: ProductUsage[];
  statistics: {
    totalProducts: number;
    averageUsage: number;
    averageCost: number;
  };
}

interface IngredientDetailSheetProps {
  ingredient: Ingredient | null;
  businessId: string;
  isOpen: boolean;
  onClose: () => void;
  onEdit: (ingredient: Ingredient) => void;
  onDelete: (ingredient: Ingredient) => void;
}

export const IngredientDetailSheet = memo(function IngredientDetailSheet({
  ingredient,
  businessId,
  isOpen,
  onClose,
  onEdit,
  onDelete,
}: IngredientDetailSheetProps) {
  const { t } = useTranslation('inventory');
  const { hasPermission } = useRBAC();
  const [usageData, setUsageData] = useState<IngredientUsageData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Redirect to unauthorized page if user doesn't have ingredients.read permission
  useEffect(() => {
    if (isOpen && !hasPermission('ingredients.read')) {
      window.location.href = '/unauthorized';
    }
  }, [isOpen, hasPermission]);

  const formatCurrency = useCallback((amount: string | number) => {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount;
    if (isNaN(num)) return 'Rp 0';
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(num);
  }, []);

  const formatNumber = useCallback((num: number, decimals: number = 2) => {
    return new Intl.NumberFormat('id-ID', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    }).format(num);
  }, []);

  // Fetch usage data when ingredient changes
  useEffect(() => {
    if (!ingredient || !businessId || !isOpen) {
      setUsageData(null);
      return;
    }

    const fetchUsageData = async () => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/ingredients/${ingredient.id}/usage?businessId=${businessId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch ingredient usage data');
        }

        const data = await response.json();
        setUsageData(data);
      } catch (err) {
        console.error('Error fetching ingredient usage:', err);
        setError(err instanceof Error ? err.message : 'Failed to load ingredient usage');
      } finally {
        setLoading(false);
      }
    };

    fetchUsageData();
  }, [ingredient, businessId, isOpen]);

  // Show loading state if sheet is open but no ingredient is found yet
  if (isOpen && !ingredient) {
    return (
      <Sheet open={isOpen} onOpenChange={onClose}>
        <SheetContent side="right" className="w-full sm:max-w-4xl p-0">
          <div className="p-6">
            <SheetHeader>
              <SheetTitle>Loading...</SheetTitle>
              <SheetDescription>
                {t('common.loading')}
              </SheetDescription>
            </SheetHeader>
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  if (!ingredient) return null;

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full sm:max-w-4xl overflow-y-auto p-0">
        <div className="p-6">
          <SheetHeader className="space-y-2 pb-6">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <SheetTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Ingredient Usage
                </SheetTitle>
                <SheetDescription className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  View which products use this ingredient
                </SheetDescription>
              </div>
              <div className="flex items-center gap-2">
                {hasPermission('ingredients.update') && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onEdit(ingredient)}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    {t('common.edit')}
                  </Button>
                )}
                {hasPermission('ingredients.delete') && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onDelete(ingredient)}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    {t('common.delete')}
                  </Button>
                )}
              </div>
            </div>
          </SheetHeader>

          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-600 mb-4">{error}</p>
            </div>
          ) : usageData ? (
            <div className="space-y-6">
              {/* Ingredient Details Section */}
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <div className="flex items-start gap-4">
                  <div className="p-3 bg-gray-200 dark:bg-gray-700 rounded-lg">
                    <Package className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                  </div>
                  <div className="flex-1">
                    <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                      {usageData.ingredient.name}
                    </h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <div className="space-y-4">
                          {hasPermission('categories.read') ? (
                            <div>
                              <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">Category</p>
                              <div className="flex items-center gap-2">
                                {usageData.ingredient.categoryColor && (
                                  <div
                                    className="w-3 h-3 rounded-full"
                                    style={{ backgroundColor: usageData.ingredient.categoryColor }}
                                  />
                                )}
                                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                  {usageData.ingredient.categoryName || 'Uncategorized'}
                                </p>
                              </div>
                            </div>
                          ) : (
                            <div>
                              <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">Category</p>
                              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">-</p>
                            </div>
                          )}

                          <div>
                            <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">Base Unit Cost</p>
                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {formatCurrency(usageData.ingredient.baseUnitCost)}
                            </p>
                          </div>

                          <div>
                            <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">Cost per Unit</p>
                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {formatCurrency(
                                parseFloat(usageData.ingredient.baseUnitCost) / parseFloat(usageData.ingredient.baseUnitQuantity)
                              )} per {usageData.ingredient.unit}
                            </p>
                          </div>
                        </div>
                      </div>

                      <div>
                        <div className="space-y-4">
                          <div>
                            <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">Unit</p>
                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {usageData.ingredient.unit}
                            </p>
                          </div>

                          <div>
                            <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">Base Unit Quantity</p>
                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {usageData.ingredient.baseUnitQuantity} {usageData.ingredient.unit}
                            </p>
                          </div>

                          <div>
                            <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">Supplier</p>
                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {usageData.ingredient.supplierInfo || '-'}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {usageData.ingredient.notes && (
                      <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                        <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">Notes</p>
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {usageData.ingredient.notes}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Product Usage Section */}
              {hasPermission('products.read') ? (
                <div className="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                  <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                      <Package className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                      Product Usage ({usageData.statistics.totalProducts})
                    </h3>
                  </div>

                  {usageData.productUsage.length > 0 ? (
                    <div className="overflow-hidden">
                      <Table>
                        <TableHeader>
                          <TableRow className="border-b border-gray-200 dark:border-gray-700">
                            <TableHead className="text-sm font-medium text-gray-500 dark:text-gray-400 px-6 py-3">Product</TableHead>
                            <TableHead className="text-sm font-medium text-gray-500 dark:text-gray-400 px-6 py-3">Usage per Cup</TableHead>
                            <TableHead className="text-sm font-medium text-gray-500 dark:text-gray-400 px-6 py-3">Cost per Cup</TableHead>
                            <TableHead className="text-sm font-medium text-gray-500 dark:text-gray-400 px-6 py-3">Note</TableHead>
                            <TableHead className="text-sm font-medium text-gray-500 dark:text-gray-400 px-6 py-3">Status</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {usageData.productUsage.map((usage) => (
                            <TableRow key={usage.id} className="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800">
                              <TableCell className="px-6 py-4">
                                <div>
                                  <div className="flex items-center gap-2">
                                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                      {usage.product.name}
                                    </span>
                                    <ExternalLink className="h-3 w-3 text-gray-400 dark:text-gray-500" />
                                  </div>
                                  {usage.product.description && (
                                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                      {usage.product.description}
                                    </p>
                                  )}
                                </div>
                              </TableCell>
                              <TableCell className="px-6 py-4">
                                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                  {formatNumber(parseFloat(usage.quantityNeeded), 2)} {usageData.ingredient.unit}
                                </span>
                              </TableCell>
                              <TableCell className="px-6 py-4">
                                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                  {formatCurrency(usage.costPerCup)}
                                </span>
                              </TableCell>
                              <TableCell className="px-6 py-4">
                                <span className="text-sm text-gray-500 dark:text-gray-400">
                                  Serving cup
                                </span>
                              </TableCell>
                              <TableCell className="px-6 py-4">
                                <Badge
                                  variant={usage.product.isActive ? 'default' : 'secondary'}
                                  className="text-sm"
                                >
                                  {usage.product.isActive ? 'Active' : 'Inactive'}
                                </Badge>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <div className="p-8 text-center">
                      <p className="text-sm text-gray-500 dark:text-gray-400">No products are currently using this ingredient.</p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                  <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                      <Package className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                      Product Usage
                    </h3>
                  </div>
                  <div className="p-8 text-center">
                    <p className="text-sm text-gray-500 dark:text-gray-400">You don&apos;t have permission to view product usage information.</p>
                  </div>
                </div>
              )}

              {/* Cost Analysis Section */}
              {hasPermission('cogs.read') ? (
                <div className="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                  <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                      <Package className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                      Cost Analysis
                    </h3>
                  </div>

                  <div className="p-6 bg-white dark:bg-gray-900">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                      <div className="text-left">
                        <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">Total Products Using</p>
                        <p className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                          {usageData.statistics.totalProducts}
                        </p>
                      </div>

                      <div className="text-left">
                        <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">Average Usage per Cup</p>
                        <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                          {formatNumber(usageData.statistics.averageUsage, 2)} {usageData.ingredient.unit}
                        </p>
                      </div>

                      <div className="text-left">
                        <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">Average Cost per Cup</p>
                        <p className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                          {formatCurrency(usageData.statistics.averageCost)}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                  <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                      <Package className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                      Cost Analysis
                    </h3>
                  </div>
                  <div className="p-8 text-center">
                    <p className="text-sm text-gray-500 dark:text-gray-400">You don&apos;t have permission to view cost analysis information.</p>
                  </div>
                </div>
              )}
            </div>
          ) : null}
        </div>
      </SheetContent>
    </Sheet>
  );
});
