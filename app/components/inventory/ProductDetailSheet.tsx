import { useTranslation } from 'react-i18next';
import { Edit, Trash2, X, Plus, Calculator, Info, Package, Shield } from 'lucide-react';
import { memo, useCallback, useState, useEffect } from 'react';
import { useRBAC } from '~/lib/hooks/useRBAC';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "~/components/ui/sheet";
import { Button } from "~/components/ui/button";
import { Separator } from "~/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "~/components/ui/tooltip";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Badge } from "~/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { PermissionsSheet } from "~/components/inventory/PermissionsSheet";
import { getPermissionConfig } from "~/lib/utils/permissionConfigs";
import { CogsService } from "~/lib/services/cogsService";
import { ProductService } from "~/lib/services/productService";
import { IngredientService } from "~/lib/services/ingredientService";
import type { Product, ProductCogs, CogsBreakdown, ProductWithIngredients, Ingredient } from "~/lib/types/inventory";

interface ProductDetailSheetProps {
  product: Product | null;
  businessId: string;
  isOpen: boolean;
  onClose: () => void;
  onDelete: (product: Product) => void;
}

export const ProductDetailSheet = memo(function ProductDetailSheet({
  product,
  businessId,
  isOpen,
  onClose,
  onDelete,
}: ProductDetailSheetProps) {
  const { t } = useTranslation('inventory');
  const { t: tCommon } = useTranslation('common');
  const { products: productsPermissions, cogs: cogsPermissions, ingredients: ingredientsPermissions } = useRBAC();
  const [productCogs, setProductCogs] = useState<ProductCogs | null>(null);
  const [isLoadingCogs, setIsLoadingCogs] = useState(false);
  const [cogsBreakdown, setCogsBreakdown] = useState<CogsBreakdown[]>([]);
  const [isLoadingBreakdown, setIsLoadingBreakdown] = useState(false);
  const [productWithIngredients, setProductWithIngredients] = useState<ProductWithIngredients | null>(null);
  const [isLoadingProduct, setIsLoadingProduct] = useState(false);
  const [availableIngredients, setAvailableIngredients] = useState<Ingredient[]>([]);
  const [isLoadingIngredients, setIsLoadingIngredients] = useState(false);
  const [showAddIngredient, setShowAddIngredient] = useState(false);
  const [selectedIngredientId, setSelectedIngredientId] = useState('');
  const [quantityNeeded, setQuantityNeeded] = useState('');
  const [isAddingIngredient, setIsAddingIngredient] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Edit ingredient state
  const [editingIngredientId, setEditingIngredientId] = useState<string | null>(null);
  const [editQuantity, setEditQuantity] = useState<string>('');
  
  // Permissions sheet state
  const [showPermissions, setShowPermissions] = useState(false);
  const [isUpdatingIngredient, setIsUpdatingIngredient] = useState(false);


  const formatCurrency = useCallback((amount: string | number) => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    if (isNaN(numAmount)) return 'Rp 0';
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(numAmount);
  }, []);



  // Memoized function to load all COGS data
  const loadCogsData = useCallback(async () => {
    if (!product || !businessId) return;

    // Load ingredient breakdown first
    setIsLoadingBreakdown(true);
    setIsLoadingCogs(true);
    try {
      const breakdown = await CogsService.getCogsBreakdown(product.id, businessId);
      setCogsBreakdown(breakdown);

      // Calculate total COGS from breakdown
      const totalIngredientCosts = breakdown.reduce((sum, item) => {
        const cost = parseFloat(item.costPerCup || '0');
        return sum + (isNaN(cost) ? 0 : cost);
      }, 0);

      // Create ProductCogs object with calculated total
      const calculatedCogs: ProductCogs = {
        id: `calculated-${product.id}`,
        productId: product.id,
        businessId: businessId,
        ingredientCosts: totalIngredientCosts.toString(),
        laborCosts: '0',
        overheadCosts: '0',
        totalCogs: totalIngredientCosts.toString(),
        calculationMethod: 'auto',
        calculationDate: new Date(),
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      setProductCogs(calculatedCogs);
    } catch (error) {
      console.error('Failed to load ingredient breakdown:', error);
      setCogsBreakdown([]);
      setProductCogs(null);
    } finally {
      setIsLoadingBreakdown(false);
      setIsLoadingCogs(false);
    }


  }, [product, businessId]);

  // Load product with ingredients
  const loadProductWithIngredients = useCallback(async () => {
    if (!product || !businessId) return;

    setIsLoadingProduct(true);
    try {
      const productData = await ProductService.getByIdWithIngredients(product.id, businessId);
      setProductWithIngredients(productData || null);
    } catch (error) {
      console.error('Failed to load product with ingredients:', error);
      setProductWithIngredients(null);
    } finally {
      setIsLoadingProduct(false);
    }
  }, [product, businessId]);

  // Load available ingredients
  const loadAvailableIngredients = useCallback(async () => {
    if (!businessId) return;

    setIsLoadingIngredients(true);
    try {
      const ingredients = await IngredientService.getAllByBusiness(businessId);
      setAvailableIngredients(ingredients);
    } catch (error) {
      console.error('Failed to load ingredients:', error);
      setAvailableIngredients([]);
    } finally {
      setIsLoadingIngredients(false);
    }
  }, [businessId]);

  // Handle adding ingredient
  const handleAddIngredient = async () => {
    if (!product || !selectedIngredientId || !quantityNeeded) {
      setError('Please select an ingredient and enter quantity');
      return;
    }

    const quantity = parseFloat(quantityNeeded);
    if (isNaN(quantity) || quantity <= 0) {
      setError('Please enter a valid quantity');
      return;
    }

    setIsAddingIngredient(true);
    setError(null);
    try {
      await ProductService.addIngredientToProduct(
        product.id,
        selectedIngredientId,
        quantity,
        businessId
      );

      // Reset form
      setSelectedIngredientId('');
      setQuantityNeeded('');
      setShowAddIngredient(false);

      // Reload data
      await Promise.all([
        loadProductWithIngredients(),
        loadCogsData()
      ]);
    } catch (error) {
      console.error('Failed to add ingredient:', error);
      setError(error instanceof Error ? error.message : 'Failed to add ingredient');
    } finally {
      setIsAddingIngredient(false);
    }
  };

  // Handle removing ingredient
  const handleRemoveIngredient = async (ingredientId: string) => {
    if (!product) return;

    try {
      await ProductService.removeIngredientFromProduct(businessId, product.id, ingredientId);

      // Reload data
      await Promise.all([
        loadProductWithIngredients(),
        loadCogsData()
      ]);
    } catch (error) {
      console.error('Failed to remove ingredient:', error);
      setError(error instanceof Error ? error.message : 'Failed to remove ingredient');
    }
  };

  // Inline editing functions
  const startEditIngredient = useCallback((ingredientId: string, currentQuantity: string) => {
    setEditingIngredientId(ingredientId);
    setEditQuantity(currentQuantity);
  }, []);

  const cancelEditIngredient = useCallback(() => {
    setEditingIngredientId(null);
    setEditQuantity('');
  }, []);

  const saveEditIngredient = useCallback(async () => {
    if (!product || !editingIngredientId) return;

    const quantity = parseFloat(editQuantity);
    if (isNaN(quantity) || quantity <= 0) {
      setError('Please enter a valid quantity');
      return;
    }

    setIsUpdatingIngredient(true);
    try {
      await ProductService.updateProductIngredient(product.id, editingIngredientId, quantity, businessId);
      // Refresh the product data
      await Promise.all([
        loadProductWithIngredients(),
        loadCogsData()
      ]);
      setEditingIngredientId(null);
      setEditQuantity('');
      setError(null);
    } catch (error) {
      console.error('Failed to update ingredient:', error);
      setError('Failed to update ingredient');
    } finally {
      setIsUpdatingIngredient(false);
    }
  }, [product, businessId, editingIngredientId, editQuantity, loadProductWithIngredients, loadCogsData]);

  // Load COGS data when product changes
  useEffect(() => {
    if (isOpen) {
      loadCogsData();
      loadProductWithIngredients();
      loadAvailableIngredients();
    }
  }, [product, businessId, isOpen, loadCogsData, loadProductWithIngredients, loadAvailableIngredients]);

  // Show loading state if sheet is open but no product is found yet
  if (isOpen && !product) {
    return (
      <Sheet open={isOpen} onOpenChange={onClose}>
        <SheetContent side="right" className="w-full sm:max-w-4xl p-0 overflow-y-auto">
          <SheetHeader className="px-6 py-4 border-b">
            <SheetTitle>Loading...</SheetTitle>
            <SheetDescription>
              {t('common.loading')}
            </SheetDescription>
          </SheetHeader>
          <div className="px-6 py-8 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  if (!product) return null;

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full sm:max-w-4xl p-0 overflow-y-auto">
        <SheetHeader className="px-6 py-4 border-b">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <SheetTitle className="text-xl font-semibold">
                {product.name}
              </SheetTitle>
              <SheetDescription className="mt-1">
                 {t('products.detail.subtitle')}
               </SheetDescription>
             </div>
            <div className="flex items-center gap-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-8 w-8" 
                      onClick={() => setShowPermissions(true)}
                    >
                      <Shield className="h-4 w-4" />
                      <span className="sr-only">View Permissions</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{t('common.permissions.view')}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <Button variant="ghost" size="icon" className="h-8 w-8" onClick={onClose}>
                <X className="h-4 w-4" />
                <span className="sr-only">Close</span>
              </Button>
            </div>
          </div>
        </SheetHeader>

        <div className="px-6 py-4 space-y-6">
          {/* Product Header */}
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Package className="h-5 w-5 text-muted-foreground" />
                <h2 className="text-lg font-semibold">{product.name}</h2>
              </div>
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Description</p>
                <p className="text-sm">{product.description || 'Espresso with hot water'}</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-muted-foreground">Total COGS per Cup</p>
              <p className="text-lg font-semibold">
                {isLoadingCogs ? (
                  <span className="animate-pulse">Loading...</span>
                ) : productCogs ? (
                  formatCurrency(productCogs.totalCogs)
                ) : (
                  'Rp 0'
                )}
              </p>
            </div>
          </div>

          <Separator />

          {/* Ingredients Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-base font-semibold">Ingredients</h3>
              {productsPermissions.canUpdate && (
                <Button
                  size="sm"
                  className="gap-2"
                  onClick={() => setShowAddIngredient(!showAddIngredient)}
                  disabled={isLoadingIngredients || availableIngredients.length === 0}
                >
                  <Plus className="h-4 w-4" />
                  Add Ingredient
                </Button>
              )}
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-3">
                <p className="text-sm text-destructive">{error}</p>
              </div>
            )}

            {/* Add Ingredient Form */}
            {showAddIngredient && productsPermissions.canUpdate && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Add Ingredient</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="ingredient-select">Ingredient</Label>
                      <Select
                        value={selectedIngredientId}
                        onValueChange={setSelectedIngredientId}
                        disabled={isLoadingIngredients}
                      >
                        <SelectTrigger id="ingredient-select">
                          <SelectValue placeholder="Select ingredient" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableIngredients
                            .filter(ingredient =>
                              !productWithIngredients?.productIngredients.some(
                                pi => pi.ingredientId === ingredient.id
                              )
                            )
                            .map((ingredient) => (
                              <SelectItem key={ingredient.id} value={ingredient.id}>
                                <div className="flex items-center gap-2">
                                  <span>{ingredient.name}</span>
                                  <Badge variant="secondary" className="text-xs">
                                    {ingredient.unit}
                                  </Badge>
                                </div>
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="quantity-input">Usage per Cup</Label>
                      <Input
                        id="quantity-input"
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        value={quantityNeeded}
                        onChange={(e) => setQuantityNeeded(e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      onClick={handleAddIngredient}
                      disabled={isAddingIngredient || !selectedIngredientId || !quantityNeeded}
                      size="sm"
                    >
                      {isAddingIngredient ? 'Adding...' : 'Add'}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setShowAddIngredient(false);
                        setSelectedIngredientId('');
                        setQuantityNeeded('');
                        setError(null);
                      }}
                      size="sm"
                    >
                      Cancel
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Ingredients Table */}
            {ingredientsPermissions.canRead ? (
              <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Ingredient</TableHead>
                    <TableHead>Usage per Cup</TableHead>
                    {cogsPermissions.canRead && <TableHead>Cost per Cup</TableHead>}
                    {cogsPermissions.canRead && <TableHead>Unit Cost</TableHead>}
                    <TableHead>Note</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoadingProduct || isLoadingBreakdown ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
                      </TableCell>
                    </TableRow>
                  ) : productWithIngredients?.productIngredients.length ? (
                    productWithIngredients.productIngredients.map((pi) => {
                      const breakdown = cogsBreakdown.find(cb => cb.ingredientId === pi.ingredientId);
                      const isEditing = editingIngredientId === pi.ingredientId;

                      return (
                        <TableRow key={pi.id}>
                          <TableCell>
                            <div className="font-medium">{pi.ingredient.name}</div>
                            <Badge variant="secondary" className="text-xs mt-1">
                              {pi.ingredient.unit}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {isEditing ? (
                              <div className="flex items-center gap-2">
                                <Input
                                  type="number"
                                  step="0.01"
                                  min="0"
                                  value={editQuantity}
                                  onChange={(e) => setEditQuantity(e.target.value)}
                                  className="w-20"
                                  disabled={isUpdatingIngredient}
                                />
                                <span className="text-sm text-muted-foreground">
                                  {pi.ingredient.unit}
                                </span>
                              </div>
                            ) : (
                              `${pi.quantityNeeded} ${pi.ingredient.unit}`
                            )}
                          </TableCell>
                          {cogsPermissions.canRead && (
                            <TableCell>
                              {breakdown ? formatCurrency(breakdown.costPerCup) : 'N/A'}
                            </TableCell>
                          )}
                          {cogsPermissions.canRead && (
                            <TableCell>
                              {breakdown ? formatCurrency(breakdown.unitCost) : 'N/A'}
                            </TableCell>
                          )}
                          <TableCell className="text-sm text-muted-foreground">
                            {pi.ingredient.notes || '-'}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              {isEditing ? (
                                <>
                                  <Button
                                    variant="default"
                                    size="sm"
                                    onClick={saveEditIngredient}
                                    disabled={isUpdatingIngredient}
                                    className="h-8 px-3"
                                  >
                                    {isUpdatingIngredient ? (
                                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></div>
                                    ) : (
                                      t('products.form.actions.save')
                                    )}
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={cancelEditIngredient}
                                    disabled={isUpdatingIngredient}
                                    className="h-8 px-3"
                                  >
                                    {t('products.form.actions.cancel')}
                                  </Button>
                                </>
                              ) : (
                                <>
                                  {productsPermissions.canUpdate && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="h-8 w-8 p-0"
                                      onClick={() => startEditIngredient(pi.ingredientId, pi.quantityNeeded)}
                                      title={t('products.form.actions.edit')}
                                    >
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                  )}
                                  {productsPermissions.canUpdate && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="h-8 w-8 p-0"
                                      onClick={() => handleRemoveIngredient(pi.ingredientId)}
                                      title={t('common.delete')}
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  )}
                                </>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                        No ingredients added yet
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                You don&apos;t have permission to view ingredients
              </div>
            )}

          <Separator />

          {/* COGS Calculation Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Calculator className="h-5 w-5 text-muted-foreground" />
              <h3 className="text-base font-semibold">COGS Calculation for {product.name}</h3>
            </div>

            {cogsPermissions.canRead ? (
              <>
                {/* What is COGS? Info */}
                <div className="flex items-start gap-2 p-3 bg-muted/50 rounded-lg">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-muted-foreground mt-0.5" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Cost of Goods Sold information</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <div className="text-sm">
                    <p className="font-medium text-muted-foreground mb-1">What is COGS?</p>
                    <p className="text-muted-foreground">
                      COGS (Cost of Goods Sold) represents the total cost of ingredients needed to make one cup of this product.
                      It&apos;s calculated by multiplying each ingredient&apos;s cost per unit by the amount used per cup.
                    </p>
                  </div>
                </div>

                {/* Total COGS */}
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">Total COGS per Cup</p>
                  <div className="flex items-baseline gap-2">
                    <p className="text-2xl font-bold">
                      {isLoadingCogs ? (
                        <span className="animate-pulse">Loading...</span>
                      ) : productCogs ? (
                        formatCurrency(productCogs.totalCogs)
                      ) : (
                        'Rp 0'
                      )}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {cogsBreakdown.length} ingredient{cogsBreakdown.length !== 1 ? 's' : ''} calculated
                    </p>
                  </div>
                </div>

                {/* Breakdown Table */}
                <div className="space-y-3">
                  <p className="text-sm font-medium text-muted-foreground">Ingredient Breakdown</p>

                  <div className="space-y-2">
                    {isLoadingBreakdown ? (
                      <div className="flex items-center justify-center py-8">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                      </div>
                    ) : cogsBreakdown.length > 0 ? (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Ingredient</TableHead>
                            <TableHead>Usage per Cup</TableHead>
                            <TableHead>Cost per Cup</TableHead>
                            <TableHead>% of Total</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {cogsBreakdown.map((ingredient, index) => {
                            const percentage = productCogs ? (parseFloat(ingredient.costPerCup) / parseFloat(productCogs.totalCogs)) * 100 : 0;
                            return (
                              <TableRow key={index}>
                                <TableCell className="font-medium">{ingredient.ingredientName}</TableCell>
                                <TableCell>{ingredient.usagePerCup} {ingredient.unit}</TableCell>
                                <TableCell>{formatCurrency(ingredient.costPerCup)}</TableCell>
                                <TableCell>{percentage.toFixed(1)}%</TableCell>
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      </Table>
                    ) : (
                      <p className="text-sm text-muted-foreground text-center py-4">
                        No ingredients added yet
                      </p>
                    )}
                  </div>

                  {/* Formula Explanation */}
                  <div className="mt-4 p-3 bg-muted/30 rounded-lg">
                    <p className="text-xs font-medium text-muted-foreground mb-1">Calculation Formula:</p>
                    <p className="text-xs text-muted-foreground">
                      Cost per Cup = (Base Unit Cost ÷ Base Unit Quantity) × Usage per Cup
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Example: If milk costs 20,000 per 1000ml and you use 100ml per cup, then cost per cup = (20,000 ÷ 1000) × 100 = 2,000
                    </p>
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                You don&apos;t have permission to view COGS information
              </div>
            )}
          </div>

        </div>
        </div>

        {/* Action Buttons */}
        {productsPermissions.canDelete && (
          <div className="px-6 py-4 border-t bg-background flex gap-2">
            <Button
              onClick={() => onDelete(product)}
              variant="destructive"
              className="flex-1"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {tCommon('buttons.delete')}
            </Button>
          </div>
        )}


      </SheetContent>
      
      {/* Permissions Sheet */}
       <PermissionsSheet
           open={showPermissions}
           onOpenChange={setShowPermissions}
           routeName="products"
           permissions={getPermissionConfig('products').permissions}
           title={t('common.permissions.title')}
         />
    </Sheet>
  );
});
