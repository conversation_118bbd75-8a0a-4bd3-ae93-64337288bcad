import { useState, useC<PERSON>back, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useTranslation } from "react-i18next";

import { <PERSON><PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Textarea } from "~/components/ui/textarea";
import { CurrencyInput } from "~/components/ui/currency-input";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "~/components/ui/sheet";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, <PERSON>ert<PERSON>ialog<PERSON><PERSON>er, <PERSON><PERSON>Dialog<PERSON>eader, AlertDialogTitle } from "~/components/ui/alert-dialog";
import { CategorySelector } from "./CategorySelector";
import { INGREDIENT_UNITS } from "~/lib/types/inventory";
import { IngredientService } from "~/lib/services/ingredientService";
import type { Ingredient, CategoryWithChildren } from "~/lib/types/inventory";

type IngredientFormValues = z.infer<typeof ingredientSchema>;

const ingredientSchema = z.object({
  name: z.string().min(1, "Name is required"),
  baseUnitCost: z.string().min(1, "Base unit cost is required").refine(
    (val) => !isNaN(parseFloat(val)) && parseFloat(val) > 0,
    "Cost must be a positive number"
  ),
  baseUnitQuantity: z.string().min(1, "Base unit quantity is required").refine(
    (val) => !isNaN(parseFloat(val)) && parseFloat(val) > 0,
    "Quantity must be a positive number"
  ),
  unit: z.string().min(1, "Unit is required"),
  categoryId: z.string().optional(),
  supplierInfo: z.string().optional(),
  notes: z.string().optional(),
  isActive: z.boolean(),
});

interface IngredientManagementSheetProps {
  mode?: 'create' | 'manage' | 'edit';
  businessId: string;
  onIngredientChange: () => void;
  children: React.ReactNode;
  editingIngredient?: Ingredient;
  onEditComplete?: () => void;
  isOpen?: boolean;
  onClose?: () => void;
  allCategories?: CategoryWithChildren[];
}

export function IngredientManagementSheet({
  mode = 'manage',
  businessId,
  onIngredientChange,
  children,
  editingIngredient: externalEditingIngredient,
  onEditComplete,
  isOpen,
  onClose,
  allCategories = []
}: IngredientManagementSheetProps) {
  const { t } = useTranslation('inventory');
  const [editingIngredient, setEditingIngredient] = useState<Ingredient | null>(null);
  const [deletingIngredient, setDeletingIngredient] = useState<Ingredient | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle external editing ingredient prop
  useEffect(() => {
    if (externalEditingIngredient) {
      setEditingIngredient(externalEditingIngredient);
      form.reset({
        name: externalEditingIngredient.name,
        baseUnitCost: externalEditingIngredient.baseUnitCost,
        baseUnitQuantity: externalEditingIngredient.baseUnitQuantity,
        unit: externalEditingIngredient.unit,
        categoryId: externalEditingIngredient.categoryId || "",
        supplierInfo: externalEditingIngredient.supplierInfo || "",
        notes: externalEditingIngredient.notes || "",
        isActive: externalEditingIngredient.isActive,
      });
    }
  }, [externalEditingIngredient]);

  const form = useForm<IngredientFormValues>({
    resolver: zodResolver(ingredientSchema),
    defaultValues: {
      name: "",
      baseUnitCost: "",
      baseUnitQuantity: "1",
      unit: "",
      categoryId: "",
      supplierInfo: "",
      notes: "",
      isActive: true,
    },
  });



  const handleDelete = useCallback(async (ingredient: Ingredient) => {
    setIsSubmitting(true);
    try {
      await IngredientService.delete(ingredient.id, businessId);
      onIngredientChange();
      setDeletingIngredient(null);
    } catch (error) {
      console.error("Failed to delete ingredient:", error);
      alert("Failed to delete ingredient. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  }, [businessId, onIngredientChange]);

  const onSubmit = useCallback(async (data: IngredientFormValues) => {
    setIsSubmitting(true);
    try {
      if (editingIngredient) {
        // Update existing ingredient
        await IngredientService.update(editingIngredient.id, businessId, data);
      } else {
        // Create new ingredient
        await IngredientService.create(businessId, data);
      }

      form.reset();
      setEditingIngredient(null);
      onIngredientChange();
      onEditComplete?.();
    } catch (error) {
      console.error("Failed to save ingredient:", error);
      alert("Failed to save ingredient. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  }, [editingIngredient, businessId, form, onIngredientChange]);



  return (
    <>
      <Sheet open={isOpen} onOpenChange={onClose}>
        <SheetTrigger asChild>
          {children}
        </SheetTrigger>
        <SheetContent className="w-[400px] sm:w-[540px] p-0">
          <SheetHeader className="flex-none border-b p-6 text-left">
            <SheetTitle>
              {editingIngredient
                ? t('ingredients.form.title.edit')
                : mode === 'create'
                  ? t('ingredients.form.title.create')
                  : t('ingredients.title')
              }
            </SheetTitle>
            <SheetDescription>
              {editingIngredient
                ? t('ingredients.form.description.edit')
                : mode === 'create'
                  ? t('ingredients.form.description.create')
                  : t('ingredients.subtitle', { businessName: 'Current Business' })
              }
            </SheetDescription>
          </SheetHeader>

          <div className="flex-1 overflow-y-auto">
            <div className="space-y-6 p-6">
              {/* Ingredient Form */}
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('ingredients.form.fields.name')}</FormLabel>
                        <FormControl>
                          <Input placeholder={t('ingredients.form.placeholders.name')} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="categoryId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('ingredients.form.fields.category')}</FormLabel>
                        <FormControl>
                          <CategorySelector
                            type="ingredient"
                            value={field.value}
                            onValueChange={field.onChange}
                            placeholder={t('ingredients.form.placeholders.selectCategory')}
                            categories={allCategories}
                          />
                        </FormControl>
                        <FormDescription>
                          {t('common.optional')}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="baseUnitCost"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('ingredients.form.fields.baseUnitCost')}</FormLabel>
                        <FormControl>
                          <CurrencyInput
                            value={field.value}
                            onChange={field.onChange}
                            placeholder={t('ingredients.form.placeholders.baseUnitCost')}
                            currency="IDR"
                            locale="id-ID"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="baseUnitQuantity"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('ingredients.form.fields.baseUnitQuantity')}</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.0001"
                            min="0"
                            placeholder={t('ingredients.form.placeholders.baseUnitQuantity')}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="unit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('ingredients.form.fields.unit')}</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t('ingredients.form.placeholders.unit')} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {INGREDIENT_UNITS.map((unit) => (
                              <SelectItem key={unit} value={unit}>
                                {t(`ingredients.form.units.${unit}`)}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="supplierInfo"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('ingredients.form.fields.supplierInfo')}</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder={t('ingredients.form.placeholders.supplierInfo')}
                            className="min-h-[80px]"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          {t('common.optional')}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="notes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('ingredients.form.fields.notes')}</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder={t('ingredients.form.placeholders.notes')}
                            className="min-h-[80px]"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          {t('common.optional')}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="isActive"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            {t('ingredients.form.fields.isActive')}
                          </FormLabel>
                          <FormDescription>
                            {t('ingredients.form.descriptions.isActive')}
                          </FormDescription>
                        </div>
                        <FormControl>
                          <input
                            type="checkbox"
                            checked={field.value}
                            onChange={field.onChange}
                            className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <div className="flex gap-2">
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? t('common.loading') : editingIngredient ? t('common.update') : t('common.create')}
                    </Button>
                    {editingIngredient && (
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          setEditingIngredient(null);
                          form.reset({
                            name: "",
                            baseUnitCost: "",
                            baseUnitQuantity: "1",
                            unit: "",
                            categoryId: "",
                            supplierInfo: "",
                            notes: "",
                            isActive: true,
                          });
                          onEditComplete?.();
                        }}
                      >
                        {t('common.cancel')}
                      </Button>
                    )}
                  </div>
                </form>
              </Form>


            </div>
          </div>
        </SheetContent>
      </Sheet>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingIngredient} onOpenChange={() => setDeletingIngredient(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('ingredients.messages.deleteConfirm')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('ingredients.messages.deleteWarning')}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('common.cancel')}</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deletingIngredient && handleDelete(deletingIngredient)}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {t('common.delete')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
