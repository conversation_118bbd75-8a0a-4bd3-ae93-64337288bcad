import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { ProductManagementSheet } from '../ProductManagementSheet';
import { CategoryService } from '~/lib/services/categoryService';

// Mock the services
vi.mock('~/lib/services/categoryService');

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'products.form.placeholders.noCategory': 'No category',
        'products.form.placeholders.category': 'Select category',
        'products.form.fields.category': 'Category',
        'common.optional': 'Optional',
        'common.loading': 'Loading...',
      };
      return translations[key] || key;
    },
  }),
}));

// Mock data
const mockBusinessId = 'test-business-id';
const mockProducts: any[] = [];
const mockIngredients: any[] = [];
const mockCategories = [
  {
    id: 'category-1',
    name: 'Test Category',
    type: 'product',
    businessId: mockBusinessId,
    children: [],
  },
];

describe('ProductManagementSheet - Radix UI Select Fix', () => {
  const mockOnProductChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(CategoryService.getHierarchicalCategories).mockResolvedValue(mockCategories);
  });

  it('should not have empty string values in SelectItem components', async () => {
    const { container } = render(
      <ProductManagementSheet
        businessId={mockBusinessId}
        products={mockProducts}
        ingredients={mockIngredients}
        onProductChange={mockOnProductChange}
      >
        <button>Add Product</button>
      </ProductManagementSheet>
    );

    // Click add product button to open the form
    fireEvent.click(screen.getByText('Add Product'));

    // Wait for categories to load and form to render
    await screen.findByText('Select category');

    // Check that no SelectItem has an empty string value
    const selectItems = container.querySelectorAll('[data-radix-select-item]');
    selectItems.forEach((item) => {
      const value = item.getAttribute('data-value');
      expect(value).not.toBe('');
      expect(value).not.toBe(null);
      expect(value).toBeTruthy();
    });
  });

  it('should use "none" as placeholder value for no category option', async () => {
    const { container } = render(
      <ProductManagementSheet
        businessId={mockBusinessId}
        products={mockProducts}
        ingredients={mockIngredients}
        onProductChange={mockOnProductChange}
      >
        <button>Add Product</button>
      </ProductManagementSheet>
    );

    // Click add product button to open the form
    fireEvent.click(screen.getByText('Add Product'));

    // Wait for categories to load and form to render
    await screen.findByText('Select category');

    // Open the category dropdown
    const categoryTrigger = screen.getByText('Select category');
    fireEvent.click(categoryTrigger);

    // Check that the "No category" option has value "none"
    const noCategoryOption = screen.getByText('No category');
    expect(noCategoryOption).toBeInTheDocument();
    
    // The parent SelectItem should have data-value="none"
    const selectItem = noCategoryOption.closest('[data-radix-select-item]');
    expect(selectItem).toHaveAttribute('data-value', 'none');
  });

  it('should handle form submission with "none" category value correctly', async () => {
    const { container } = render(
      <ProductManagementSheet
        businessId={mockBusinessId}
        products={mockProducts}
        ingredients={mockIngredients}
        onProductChange={mockOnProductChange}
      >
        <button>Add Product</button>
      </ProductManagementSheet>
    );

    // Click add product button to open the form
    fireEvent.click(screen.getByText('Add Product'));

    // Wait for form to render
    await screen.findByText('Select category');

    // The form should initialize with categoryId: "none"
    // This test verifies that the form can be rendered without Radix UI errors
    expect(screen.getByText('Select category')).toBeInTheDocument();
  });
});
