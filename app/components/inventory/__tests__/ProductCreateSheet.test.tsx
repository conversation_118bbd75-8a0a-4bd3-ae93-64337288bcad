import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { ProductCreateSheet } from '../ProductCreateSheet';
import { ProductService } from '~/lib/services/productService';

// Mock the ProductService
vi.mock('~/lib/services/productService');

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'products.create.title': 'Create New Product',
        'products.create.subtitle': 'Add a new product to your catalog',
        'products.create.nextSteps': 'Next steps:',
        'products.create.step1': 'After creating the product, you can add ingredients',
        'products.create.step2': 'Set usage amounts per cup for each ingredient',
        'products.create.step3': 'Use the product in COGS calculations and production',
        'products.form.fields.name': 'Product Name',
        'products.form.fields.description': 'Description',
        'products.form.fields.notes': 'Notes',
        'products.form.fields.status': 'Active Status',
        'products.form.placeholders.name': 'Enter product name',
        'products.form.placeholders.description': 'Enter product description',
        'products.form.placeholders.notes': 'Additional notes about this product',
        'products.form.statusDescription': 'Product is active',
        'products.form.actions.create': 'Create Product',
        'common.loading': 'Loading...',
        'common.cancel': 'Cancel',
      };
      return translations[key] || key;
    },
  }),
}));

describe('ProductCreateSheet', () => {
  const mockBusinessId = 'test-business-id';
  const mockOnProductChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render the create product form when opened', async () => {
    render(
      <ProductCreateSheet
        businessId={mockBusinessId}
        onProductChange={mockOnProductChange}
      >
        <button>Add Product</button>
      </ProductCreateSheet>
    );

    // Click the trigger button
    fireEvent.click(screen.getByText('Add Product'));

    // Check that the form is rendered
    expect(screen.getByText('Create New Product')).toBeInTheDocument();
    expect(screen.getByText('Add a new product to your catalog')).toBeInTheDocument();
    expect(screen.getByLabelText(/Product Name/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Description/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Notes/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Active Status/)).toBeInTheDocument();
  });

  it('should show next steps information', async () => {
    render(
      <ProductCreateSheet
        businessId={mockBusinessId}
        onProductChange={mockOnProductChange}
      >
        <button>Add Product</button>
      </ProductCreateSheet>
    );

    // Click the trigger button
    fireEvent.click(screen.getByText('Add Product'));

    // Check next steps
    expect(screen.getByText('Next steps:')).toBeInTheDocument();
    expect(screen.getByText('After creating the product, you can add ingredients')).toBeInTheDocument();
    expect(screen.getByText('Set usage amounts per cup for each ingredient')).toBeInTheDocument();
    expect(screen.getByText('Use the product in COGS calculations and production')).toBeInTheDocument();
  });

  it('should submit form with correct data', async () => {
    const mockCreate = vi.mocked(ProductService.create).mockResolvedValue({} as any);

    render(
      <ProductCreateSheet
        businessId={mockBusinessId}
        onProductChange={mockOnProductChange}
      >
        <button>Add Product</button>
      </ProductCreateSheet>
    );

    // Click the trigger button
    fireEvent.click(screen.getByText('Add Product'));

    // Fill out the form
    fireEvent.change(screen.getByLabelText(/Product Name/), {
      target: { value: 'Test Product' }
    });
    fireEvent.change(screen.getByLabelText(/Description/), {
      target: { value: 'Test Description' }
    });
    fireEvent.change(screen.getByLabelText(/Notes/), {
      target: { value: 'Test Notes' }
    });

    // Submit the form
    fireEvent.click(screen.getByText('Create Product'));

    // Wait for the service to be called
    await waitFor(() => {
      expect(mockCreate).toHaveBeenCalledWith(mockBusinessId, {
        name: 'Test Product',
        description: 'Test Description',
        notes: 'Test Notes',
        status: 'active',
        sellingPrice: '0',
      });
    });

    // Check that onProductChange was called
    expect(mockOnProductChange).toHaveBeenCalled();
  });

  it('should handle form cancellation', async () => {
    render(
      <ProductCreateSheet
        businessId={mockBusinessId}
        onProductChange={mockOnProductChange}
      >
        <button>Add Product</button>
      </ProductCreateSheet>
    );

    // Click the trigger button
    fireEvent.click(screen.getByText('Add Product'));

    // Fill out some data
    fireEvent.change(screen.getByLabelText(/Product Name/), {
      target: { value: 'Test Product' }
    });

    // Click cancel
    fireEvent.click(screen.getByText('Cancel'));

    // Form should be closed (we can't easily test this without more complex setup)
    // But we can verify the form was rendered initially
    expect(screen.queryByText('Create New Product')).not.toBeInTheDocument();
  });

  it('should validate required fields', async () => {
    render(
      <ProductCreateSheet
        businessId={mockBusinessId}
        onProductChange={mockOnProductChange}
      >
        <button>Add Product</button>
      </ProductCreateSheet>
    );

    // Click the trigger button
    fireEvent.click(screen.getByText('Add Product'));

    // Try to submit without filling required fields
    fireEvent.click(screen.getByText('Create Product'));

    // Should show validation error (this depends on the form validation setup)
    await waitFor(() => {
      // The form should not submit without a product name
      expect(ProductService.create).not.toHaveBeenCalled();
    });
  });
});
