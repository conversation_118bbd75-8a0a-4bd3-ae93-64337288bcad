import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { ProductManagementSheet } from '../ProductManagementSheet';
import { ProductDetailSheet } from '../ProductDetailSheet';
import { ProductService } from '~/lib/services/productService';
import { CategoryService } from '~/lib/services/categoryService';
import { CogsService } from '~/lib/services/cogsService';

// Mock the services
vi.mock('~/lib/services/productService');
vi.mock('~/lib/services/categoryService');
vi.mock('~/lib/services/cogsService');

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

// Mock data
const mockBusinessId = 'test-business-id';
const mockProducts = [
  {
    id: 'product-1',
    name: 'Test Product',
    description: 'Test Description',
    sellingPrice: '25000',
    categoryId: 'category-1',
    status: 'active',
    businessId: mockBusinessId,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

const mockIngredients = [
  {
    id: 'ingredient-1',
    name: 'Test Ingredient',
    baseUnitCost: '10000',
    baseUnitQuantity: '1',
    unit: 'kg',
    businessId: mockBusinessId,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

const mockCategories = [
  {
    id: 'category-1',
    name: 'Test Category',
    type: 'product',
    businessId: mockBusinessId,
    children: [],
  },
];

const mockProductCogs = {
  id: 'cogs-1',
  productId: 'product-1',
  totalCogs: '15000',
  ingredientCosts: '12000',
  laborCosts: '2000',
  overheadCosts: '1000',
  businessId: mockBusinessId,
  createdAt: new Date(),
  updatedAt: new Date(),
};

describe('ProductManagementSheet', () => {
  const mockOnProductChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(CategoryService.getHierarchicalCategories).mockResolvedValue(mockCategories);
  });

  it('renders product management sheet correctly in create mode', async () => {
    render(
      <ProductManagementSheet
        mode="create"
        businessId={mockBusinessId}
        onProductChange={mockOnProductChange}
      >
        <div>Test Content</div>
      </ProductManagementSheet>
    );

    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('renders product management sheet correctly in edit mode', async () => {
    const mockProduct = mockProducts[0];
    vi.mocked(ProductService.getByIdWithIngredients).mockResolvedValue({
      ...mockProduct,
      productIngredients: []
    });

    render(
      <ProductManagementSheet
        mode="edit"
        businessId={mockBusinessId}
        productId={mockProduct.id}
        onProductChange={mockOnProductChange}
      >
        <div>Test Content</div>
      </ProductManagementSheet>
    );

    expect(screen.getByText('Test Content')).toBeInTheDocument();

    await waitFor(() => {
      expect(ProductService.getByIdWithIngredients).toHaveBeenCalledWith(
        mockProduct.id,
        mockBusinessId
      );
    });
  });

  it('handles form submission correctly in create mode', async () => {
    vi.mocked(ProductService.create).mockResolvedValue();

    render(
      <ProductManagementSheet
        mode="create"
        businessId={mockBusinessId}
        onProductChange={mockOnProductChange}
      >
        <button>Add Product</button>
      </ProductManagementSheet>
    );

    // Click add product button
    fireEvent.click(screen.getByText('Add Product'));

    // Fill form fields
    const nameInput = screen.getByPlaceholderText('products.form.placeholders.name');
    const priceInput = screen.getByPlaceholderText('products.form.placeholders.sellingPrice');

    fireEvent.change(nameInput, { target: { value: 'New Product' } });
    fireEvent.change(priceInput, { target: { value: '30000' } });

    // Submit form
    const submitButton = screen.getByText('common.create');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(ProductService.create).toHaveBeenCalled();
    });
  });

  it('validates required fields', async () => {
    render(
      <ProductManagementSheet
        businessId={mockBusinessId}
        products={mockProducts}
        ingredients={mockIngredients}
        onProductChange={mockOnProductChange}
      >
        <button>Add Product</button>
      </ProductManagementSheet>
    );

    // Click add product button
    fireEvent.click(screen.getByText('Add Product'));

    // Submit form without filling required fields
    const submitButton = screen.getByText('common.create');
    fireEvent.click(submitButton);

    // Should show validation errors
    await waitFor(() => {
      expect(screen.getByText('Name is required')).toBeInTheDocument();
    });
  });

  it('calculates ingredient costs in real-time', async () => {
    render(
      <ProductManagementSheet
        businessId={mockBusinessId}
        products={mockProducts}
        ingredients={mockIngredients}
        onProductChange={mockOnProductChange}
      >
        <button>Add Product</button>
      </ProductManagementSheet>
    );

    // Click add product button
    fireEvent.click(screen.getByText('Add Product'));

    // Select ingredient
    const ingredientSelect = screen.getByText('products.form.ingredients.selectIngredient');
    fireEvent.click(ingredientSelect);
    fireEvent.click(screen.getByText('Test Ingredient'));

    // Enter quantity
    const quantityInput = screen.getByPlaceholderText('products.form.ingredients.quantityPlaceholder');
    fireEvent.change(quantityInput, { target: { value: '2' } });

    // Should show estimated cost
    await waitFor(() => {
      expect(screen.getByText(/products.form.ingredients.estimatedCost/)).toBeInTheDocument();
    });
  });
});

describe('ProductDetailSheet', () => {
  const mockOnClose = vi.fn();
  const mockOnEdit = vi.fn();
  const mockOnDelete = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(CogsService.getProductCogs).mockResolvedValue(mockProductCogs);
    vi.mocked(CogsService.getCogsBreakdown).mockResolvedValue([]);
  });

  it('renders product detail sheet correctly', () => {
    render(
      <ProductDetailSheet
        product={mockProducts[0]}
        businessId={mockBusinessId}
        isOpen={true}
        onClose={mockOnClose}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    expect(screen.getByText('Test Product')).toBeInTheDocument();
  });

  it('loads COGS data when opened', async () => {
    render(
      <ProductDetailSheet
        product={mockProducts[0]}
        businessId={mockBusinessId}
        isOpen={true}
        onClose={mockOnClose}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    await waitFor(() => {
      expect(CogsService.getProductCogs).toHaveBeenCalledWith(
        'product-1',
        mockBusinessId
      );
    });
  });

  it('displays COGS information correctly', async () => {
    render(
      <ProductDetailSheet
        product={mockProducts[0]}
        businessId={mockBusinessId}
        isOpen={true}
        onClose={mockOnClose}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('COGS Calculation for')).toBeInTheDocument();
    });
  });

  it('displays ingredients table correctly', async () => {
    render(
      <ProductDetailSheet
        product={mockProducts[0]}
        businessId={mockBusinessId}
        isOpen={true}
        onClose={mockOnClose}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Ingredients')).toBeInTheDocument();
      expect(screen.getByText('Add Ingredient')).toBeInTheDocument();
    });
  });

  it('displays COGS calculation section correctly', async () => {
    render(
      <ProductDetailSheet
        product={mockProducts[0]}
        businessId={mockBusinessId}
        isOpen={true}
        onClose={mockOnClose}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('COGS Calculation for Test Product')).toBeInTheDocument();
      expect(screen.getByText('What is COGS?')).toBeInTheDocument();
    });
  });
});
