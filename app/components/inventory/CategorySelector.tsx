import { useMemo } from 'react';
import { cn } from '~/lib/utils';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import type { Category, CategoryWithChildren } from '~/lib/types/inventory';

interface CategorySelectorProps {
  type?: 'ingredient' | 'product' | 'supplier' | 'customer'; // Category type
  value?: string; // categoryId
  onValueChange: (categoryId: string | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  categories?: CategoryWithChildren[]; // Pre-loaded categories
}

export function CategorySelector({
  type = 'ingredient',
  value,
  onValueChange,
  placeholder = "Select category...",
  disabled = false,
  className,
  categories: providedCategories = [],
}: CategorySelectorProps) {

  // Filter categories by type and flatten the hierarchy
  const filteredCategories = useMemo(() => {
    const flattenCategories = (cats: CategoryWithChildren[]): Category[] => {
      const result: Category[] = [];
      for (const cat of cats) {
        if (cat.type === type) {
          result.push(cat);
        }
        if (cat.children && cat.children.length > 0) {
          result.push(...flattenCategories(cat.children));
        }
      }
      return result;
    };
    return flattenCategories(providedCategories);
  }, [providedCategories, type]);

  const handleValueChange = (newValue: string) => {
    if (newValue === "none" || newValue === "") {
      onValueChange(undefined);
    } else {
      onValueChange(newValue);
    }
  };

  // Find selected category for display
  const selectedCategory = filteredCategories.find(cat => cat.id === value);

  // Handle invalid category IDs by logging warning
  if (value && value !== "none" && filteredCategories.length > 0 && !selectedCategory) {
    console.warn(`⚠️ CategorySelector: Invalid categoryId "${value}" for type "${type}". This product may have an old/invalid category ID.`);
  }

  return (
    <Select
      value={value || "none"}
      onValueChange={handleValueChange}
      disabled={disabled}
    >
      <SelectTrigger className={cn("w-full", className)}>
        <SelectValue>
          {selectedCategory ? (
            <div className="flex items-center gap-2">
              {selectedCategory.color && (
                <div
                  className="w-3 h-3 rounded-full flex-shrink-0"
                  style={{ backgroundColor: selectedCategory.color }}
                />
              )}
              <span>{selectedCategory.name}</span>
            </div>
          ) : (
            <span className="text-muted-foreground">{placeholder}</span>
          )}
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        {/* No category option */}
        <SelectItem value="none">
          <span className="text-muted-foreground">No category</span>
        </SelectItem>

        {/* Category options */}
        {filteredCategories.map((category) => (
          <SelectItem key={category.id} value={category.id}>
            <div className="flex items-center gap-2">
              {category.color && (
                <div
                  className="w-3 h-3 rounded-full flex-shrink-0"
                  style={{ backgroundColor: category.color }}
                />
              )}
              <span>{category.name}</span>
              {category.description && (
                <span className="text-xs text-muted-foreground ml-2">
                  {category.description}
                </span>
              )}
            </div>
          </SelectItem>
        ))}

        {/* Empty state */}
        {filteredCategories.length === 0 && (
          <SelectItem value="empty" disabled>
            <span className="text-muted-foreground">No categories available</span>
          </SelectItem>
        )}
      </SelectContent>
    </Select>
  );
}
