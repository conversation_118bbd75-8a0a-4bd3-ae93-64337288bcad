import { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Calculator, TrendingUp, TrendingDown, AlertTriangle, Info } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Badge } from "~/components/ui/badge";
import { Separator } from "~/components/ui/separator";
import { Alert, AlertDescription } from "~/components/ui/alert";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import { CogsService } from "~/lib/services/cogsService";
import type { Product, CogsCalculationResult, CogsBreakdown, ProductCogs } from "~/lib/types/inventory";

interface CogsCalculationProps {
  product: Product;
  businessId: string;
  onCogsUpdate?: (cogs: ProductCogs) => void;
}

export function CogsCalculation({ product, businessId, onCogsUpdate }: CogsCalculationProps) {
  const { t } = useTranslation('inventory');
  const [laborCosts, setLaborCosts] = useState('0');
  const [overheadCosts, setOverheadCosts] = useState('0');
  const [calculation, setCalculation] = useState<CogsCalculationResult | null>(null);
  const [breakdown, setBreakdown] = useState<CogsBreakdown[]>([]);
  const [existingCogs, setExistingCogs] = useState<ProductCogs | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load existing COGS data
  useEffect(() => {
    const loadExistingCogs = async () => {
      try {
        const cogs = await CogsService.getProductCogs(product.id, businessId);
        if (cogs) {
          setExistingCogs(cogs);
          setLaborCosts(cogs.laborCosts);
          setOverheadCosts(cogs.overheadCosts);
        }
      } catch (error) {
        console.error('Failed to load existing COGS:', error);
      }
    };

    loadExistingCogs();
  }, [product.id, businessId]);

  // Load COGS breakdown
  useEffect(() => {
    const loadBreakdown = async () => {
      try {
        const breakdownData = await CogsService.getCogsBreakdown(product.id, businessId);
        setBreakdown(breakdownData);
      } catch (error) {
        console.error('Failed to load COGS breakdown:', error);
      }
    };

    loadBreakdown();
  }, [product.id, businessId]);

  const handleCalculate = useCallback(async () => {
    setIsCalculating(true);
    setError(null);

    try {
      const laborCostsNum = parseFloat(laborCosts) || 0;
      const overheadCostsNum = parseFloat(overheadCosts) || 0;

      const result = await CogsService.calculateProductCogs(
        product.id,
        businessId,
        laborCostsNum,
        overheadCostsNum
      );

      setCalculation(result);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to calculate COGS');
    } finally {
      setIsCalculating(false);
    }
  }, [product.id, businessId, laborCosts, overheadCosts]);

  const handleSave = useCallback(async () => {
    if (!calculation) return;

    setIsSaving(true);
    setError(null);

    try {
      const cogs = await CogsService.saveProductCogs(product.id, businessId, {
        laborCosts,
        overheadCosts,
        calculationMethod: 'manual',
      });

      setExistingCogs(cogs);
      onCogsUpdate?.(cogs);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to save COGS');
    } finally {
      setIsSaving(false);
    }
  }, [calculation, product.id, businessId, laborCosts, overheadCosts, onCogsUpdate]);

  const formatCurrency = useCallback((amount: number | string) => {
    return CogsService.formatCurrency(amount);
  }, []);

  const formatPercentage = useCallback((percentage: number) => {
    return CogsService.formatPercentage(percentage);
  }, []);

  const getProfitMarginColor = useCallback((profitMargin: number) => {
    return CogsService.getProfitMarginColor(profitMargin);
  }, []);

  const getProfitMarginStatus = useCallback((profitMargin: number) => {
    return CogsService.getProfitMarginStatus(profitMargin);
  }, []);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <Calculator className="h-5 w-5" />
          <CardTitle>{t('products.cogs.title')}</CardTitle>
        </div>
        <CardDescription>
          {t('products.cogs.description')}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Input Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="laborCosts">{t('products.cogs.laborCosts')}</Label>
            <Input
              id="laborCosts"
              type="number"
              min="0"
              step="0.01"
              value={laborCosts}
              onChange={(e) => setLaborCosts(e.target.value)}
              placeholder="0.00"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="overheadCosts">{t('products.cogs.overheadCosts')}</Label>
            <Input
              id="overheadCosts"
              type="number"
              min="0"
              step="0.01"
              value={overheadCosts}
              onChange={(e) => setOverheadCosts(e.target.value)}
              placeholder="0.00"
            />
          </div>
        </div>

        <div className="flex gap-2">
          <Button 
            onClick={handleCalculate} 
            disabled={isCalculating}
            variant="outline"
          >
            {isCalculating ? t('common.calculating') : t('products.cogs.calculate')}
          </Button>
          {calculation && (
            <Button 
              onClick={handleSave} 
              disabled={isSaving}
            >
              {isSaving ? t('common.saving') : t('products.cogs.save')}
            </Button>
          )}
        </div>

        {/* Calculation Results */}
        {calculation && (
          <>
            <Separator />
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">{t('products.cogs.results')}</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">{t('products.cogs.ingredientCosts')}</p>
                  <p className="text-lg font-semibold">{formatCurrency(calculation.ingredientCosts)}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">{t('products.cogs.laborCosts')}</p>
                  <p className="text-lg font-semibold">{formatCurrency(calculation.laborCosts)}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">{t('products.cogs.overheadCosts')}</p>
                  <p className="text-lg font-semibold">{formatCurrency(calculation.overheadCosts)}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">{t('products.cogs.totalCogs')}</p>
                  <p className="text-xl font-bold">{formatCurrency(calculation.totalCogs)}</p>
                </div>
              </div>

              {/* Profit Analysis */}
              <div className="bg-muted/50 p-4 rounded-lg">
                <h4 className="font-semibold mb-3">{t('products.cogs.profitAnalysis')}</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">{t('products.fields.sellingPrice')}</p>
                    <p className="text-lg font-semibold">{formatCurrency(product.sellingPrice)}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">{t('products.cogs.profitAmount')}</p>
                    <div className="flex items-center gap-2">
                      <p className={`text-lg font-semibold ${getProfitMarginColor(calculation.profitMargin)}`}>
                        {formatCurrency(calculation.profitAmount)}
                      </p>
                      {calculation.profitAmount >= 0 ? (
                        <TrendingUp className="h-4 w-4 text-green-600" />
                      ) : (
                        <TrendingDown className="h-4 w-4 text-red-600" />
                      )}
                    </div>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">{t('products.cogs.profitMargin')}</p>
                    <div className="flex items-center gap-2">
                      <p className={`text-lg font-semibold ${getProfitMarginColor(calculation.profitMargin)}`}>
                        {formatPercentage(calculation.profitMargin)}
                      </p>
                      <Badge variant={calculation.profitMargin >= 20 ? 'default' : calculation.profitMargin >= 10 ? 'secondary' : 'destructive'}>
                        {getProfitMarginStatus(calculation.profitMargin)}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Ingredient Breakdown */}
        {breakdown.length > 0 && (
          <>
            <Separator />
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">{t('products.cogs.ingredientBreakdown')}</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('ingredients.fields.name')}</TableHead>
                    <TableHead className="text-right">{t('products.cogs.quantityNeeded')}</TableHead>
                    <TableHead className="text-right">{t('products.cogs.unitCost')}</TableHead>
                    <TableHead className="text-right">{t('products.cogs.totalCost')}</TableHead>
                    <TableHead className="text-right">{t('products.cogs.percentage')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {breakdown.map((item) => (
                    <TableRow key={item.ingredient.id}>
                      <TableCell className="font-medium">{item.ingredient.name}</TableCell>
                      <TableCell className="text-right">
                        {item.quantityNeeded} {item.ingredient.unit}
                      </TableCell>
                      <TableCell className="text-right">
                        {formatCurrency(item.unitCost)}
                      </TableCell>
                      <TableCell className="text-right font-semibold">
                        {formatCurrency(item.totalCost)}
                      </TableCell>
                      <TableCell className="text-right">
                        {formatPercentage(item.percentageOfTotal)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </>
        )}

        {/* Info Alert */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            {t('products.cogs.info')}
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
}
