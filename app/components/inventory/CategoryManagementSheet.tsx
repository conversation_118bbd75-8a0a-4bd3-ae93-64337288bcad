import { useState, useCallback, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useTranslation } from "react-i18next";
import { Trash2, <PERSON><PERSON> } from "lucide-react";

import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Textarea } from "~/components/ui/textarea";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "~/components/ui/sheet";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Switch } from "~/components/ui/switch";
import { CategorySelector } from "~/components/inventory/CategorySelector";
import { CategoryService } from "~/lib/services/categoryService";
import type {
  Category,
  CategoryFormData,
  CategoryType,
  CategoryWithChildren,
} from "~/lib/types/inventory";

interface CategoryManagementSheetProps {
  mode?: "create" | "edit";
  businessId: string;
  onCategoryChange: () => void;
  children: React.ReactNode;
  editingCategory?: Category;
  onEditComplete?: () => void;
  isOpen?: boolean;
  onClose?: () => void;
  allCategories?: CategoryWithChildren[];
}

const categorySchema = z.object({
  name: z
    .string()
    .min(1, "Category name is required")
    .max(100, "Name must be less than 100 characters"),
  description: z
    .string()
    .max(500, "Description must be less than 500 characters")
    .optional(),
  type: z.enum(["ingredient", "product", "supplier", "customer"]),
  parentId: z.string().optional(),
  color: z
    .string()
    .regex(/^#[0-9A-F]{6}$/i, "Color must be a valid hex color")
    .optional(),
  sortOrder: z.number().min(0, "Sort order must be positive").optional(),
  isActive: z.boolean(),
});

type CategoryFormValues = z.infer<typeof categorySchema>;

export function CategoryManagementSheet({
  businessId,
  onCategoryChange,
  children,
  editingCategory: externalEditingCategory,
  onEditComplete,
  isOpen,
  onClose,
  allCategories = [],
}: CategoryManagementSheetProps) {
  const { t } = useTranslation("inventory");
  const { t: tCommon } = useTranslation("common");
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [deletingCategory, setDeletingCategory] = useState<Category | null>(
    null
  );
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [sheetOpen, setSheetOpen] = useState(false);

  // Handle external editing category prop
  useEffect(() => {
    if (externalEditingCategory) {
      setEditingCategory(externalEditingCategory);
      form.reset({
        name: externalEditingCategory.name,
        description: externalEditingCategory.description || "",
        type: externalEditingCategory.type as CategoryType,
        parentId: externalEditingCategory.parentId || "",
        color:
          externalEditingCategory.color ||
          CategoryService.generateRandomColor(),
        sortOrder: externalEditingCategory.sortOrder
          ? parseFloat(externalEditingCategory.sortOrder)
          : 0,
        isActive: externalEditingCategory.isActive,
      });
      setSheetOpen(true);
    }
  }, [externalEditingCategory]);

  // Handle external isOpen prop
  useEffect(() => {
    if (isOpen !== undefined) {
      setSheetOpen(isOpen);
    }
  }, [isOpen]);

  const form = useForm<CategoryFormValues>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: "",
      description: "",
      type: "ingredient",
      parentId: "",
      color: CategoryService.generateRandomColor(),
      sortOrder: 0,
      isActive: true,
    },
  });

  const selectedType = form.watch("type");

  const resetForm = useCallback(() => {
    form.reset({
      name: "",
      description: "",
      type: "ingredient",
      parentId: "",
      color: CategoryService.generateRandomColor(),
      sortOrder: 0,
      isActive: true,
    });
    setEditingCategory(null);
  }, [form]);

  const handleSheetOpenChange = useCallback(
    (open: boolean) => {
      setSheetOpen(open);
      if (!open) {
        resetForm();
        if (onClose) {
          onClose();
        }
        if (onEditComplete) {
          onEditComplete();
        }
      }
    },
    [resetForm, onClose, onEditComplete]
  );

  const handleSubmit = async (data: CategoryFormValues) => {
    setIsSubmitting(true);
    try {
      const categoryData: CategoryFormData = {
        name: data.name,
        description: data.description || undefined,
        type: data.type,
        parentId: data.parentId || undefined,
        color: data.color || undefined,
        sortOrder: data.sortOrder || undefined,
        isActive: data.isActive,
      };

      if (editingCategory) {
        await CategoryService.update(
          editingCategory.id,
          businessId,
          categoryData
        );
      } else {
        await CategoryService.create(businessId, categoryData);
      }

      onCategoryChange();
      handleSheetOpenChange(false);
    } catch (error) {
      console.error("Failed to save category:", error);
      // You might want to show an error toast here
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async (category: Category) => {
    try {
      await CategoryService.delete(category.id, businessId);
      onCategoryChange();
      setDeletingCategory(null);
      handleSheetOpenChange(false);
    } catch (error) {
      console.error("Failed to delete category:", error);
      // You might want to show an error toast here
    }
  };

  const defaultColors = CategoryService.getDefaultColors();

  return (
    <Sheet open={sheetOpen} onOpenChange={handleSheetOpenChange}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className="w-[400px] sm:w-[540px] overflow-y-auto p-6">
        <SheetHeader>
          <SheetTitle>
            {editingCategory
              ? t("categories.edit.title")
              : t("categories.create.title")}
          </SheetTitle>
          <SheetDescription>
            {editingCategory
              ? t("categories.edit.description")
              : t("categories.create.description")}
          </SheetDescription>
        </SheetHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6 mt-6"
          >
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("categories.form.name.label")}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t("categories.form.name.placeholder")}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t("categories.form.description.label")}
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={t("categories.form.description.placeholder")}
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("categories.form.type.label")}</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={t("categories.form.type.placeholder")}
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="ingredient">
                        {t("categories.types.ingredient")}
                      </SelectItem>
                      <SelectItem value="product">
                        {t("categories.types.product")}
                      </SelectItem>
                      <SelectItem value="supplier">
                        {t("categories.types.supplier")}
                      </SelectItem>
                      <SelectItem value="customer">
                        {t("categories.types.customer")}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="parentId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("categories.form.parent.label")}</FormLabel>
                  <CategorySelector
                    type={selectedType}
                    value={field.value || ""}
                    onValueChange={field.onChange}
                    placeholder={t("categories.form.parent.placeholder")}
                    categories={allCategories}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="color"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <Palette className="h-4 w-4" />
                    {t("categories.form.color.label")}
                  </FormLabel>
                  <div className="space-y-3">
                    <FormControl>
                      <Input
                        type="color"
                        {...field}
                        className="w-20 h-10 p-1 border rounded"
                      />
                    </FormControl>
                    <div className="grid grid-cols-5 gap-2">
                      {defaultColors.map((color) => (
                        <button
                          key={color}
                          type="button"
                          className={`w-8 h-8 rounded border-2 ${
                            field.value === color
                              ? "border-gray-900"
                              : "border-gray-300"
                          }`}
                          style={{ backgroundColor: color }}
                          onClick={() => field.onChange(color)}
                        />
                      ))}
                    </div>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="sortOrder"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("categories.form.sortOrder.label")}</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="0"
                      step="1"
                      placeholder={t("categories.form.sortOrder.placeholder")}
                      {...field}
                      onChange={(e) =>
                        field.onChange(
                          e.target.value
                            ? parseFloat(e.target.value)
                            : undefined
                        )
                      }
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isActive"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">
                      {t("categories.form.isActive.label")}
                    </FormLabel>
                    <div className="text-sm text-muted-foreground">
                      {t("categories.form.isActive.description")}
                    </div>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <div className="pt-6 space-y-3">
              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="flex-1"
                >
                  {isSubmitting
                    ? tCommon("buttons.saving")
                    : editingCategory
                    ? tCommon("buttons.update")
                    : tCommon("buttons.create")}
                </Button>
                {editingCategory && (
                  <div className="flex justify-center">
                    <Button
                      type="button"
                      variant="destructive"
                      onClick={() => setDeletingCategory(editingCategory)}
                      className="w-full sm:w-auto"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      {t("categories.actions.delete")}
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </form>
        </Form>

        {/* Delete Confirmation Dialog */}
        {deletingCategory && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold mb-2">
                {t("categories.delete.title")}
              </h3>
              <p className="text-muted-foreground mb-4">
                {t("categories.delete.description", {
                  name: deletingCategory.name,
                })}
              </p>
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setDeletingCategory(null)}
                >
                  {tCommon("buttons.cancel")}
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => handleDelete(deletingCategory)}
                >
                  {tCommon("buttons.delete")}
                </Button>
              </div>
            </div>
          </div>
        )}
      </SheetContent>
    </Sheet>
  );
}
