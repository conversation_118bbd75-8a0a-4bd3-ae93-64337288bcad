import { useState, useEffect, memo } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useTranslation } from "react-i18next";


import { <PERSON><PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Textarea } from "~/components/ui/textarea";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "~/components/ui/sheet";


// PRODUCT_STATUSES removed - now using isActive boolean
import { ProductService } from "~/lib/services/productService";

import { CategorySelector } from "~/components/inventory/CategorySelector";
import type { ProductWithIngredients, BasicProductFormData, CategoryWithChildren } from "~/lib/types/inventory";

const productSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  note: z.string().optional(),
  categoryId: z.string().optional(),
  isActive: z.boolean(),
  // cogsPerCup removed - will be calculated automatically based on ingredients
});

type ProductFormValues = z.infer<typeof productSchema>;

interface ProductManagementSheetProps {
  mode?: 'create' | 'edit';
  businessId: string;
  productId?: string; // Product ID for edit mode
  onProductChange: () => void;
  children: React.ReactNode;
  allCategories?: CategoryWithChildren[];
}

const ProductManagementSheet = memo(function ProductManagementSheet({
  mode = 'create',
  businessId,
  productId,
  onProductChange,
  children,
  allCategories = []
}: ProductManagementSheetProps) {
  const { t } = useTranslation('inventory');
  const [editingProduct, setEditingProduct] = useState<ProductWithIngredients | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Determine if we're in edit mode
  const isEditMode = mode === 'edit' && productId;

  const form = useForm<ProductFormValues>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: "",
      description: "",
      note: "",
      categoryId: "none",
      isActive: true,
    },
  });

  // Load product data when in edit mode
  useEffect(() => {
    const loadProductData = async () => {
      if (!isEditMode || !productId) return;

      setIsLoading(true);
      setError(null);
      try {
        const product = await ProductService.getByIdWithIngredients(productId, businessId);
        if (product) {
          setEditingProduct(product);
          // Pre-fill form with existing product data
          // Handle category mapping properly
          const categoryIdValue = product.categoryId && product.categoryId !== "" ? product.categoryId : "none";

          form.reset({
            name: product.name,
            description: product.description || "",
            note: product.note || "",
            categoryId: categoryIdValue,
            isActive: product.isActive,
          });
        }
      } catch (error) {
        console.error("Failed to load product data:", error);
        setError(t('products.form.errors.loadFailed'));
      } finally {
        setIsLoading(false);
      }
    };

    loadProductData();
  }, [isEditMode, productId, businessId, form, t]);







  const onSubmit = async (data: ProductFormValues) => {
    setIsSubmitting(true);
    setError(null);
    try {
      const formData: BasicProductFormData = {
        name: data.name,
        description: data.description,
        note: data.note,
        categoryId: data.categoryId === "none" ? undefined : data.categoryId,
        isActive: data.isActive,
        // cogsPerCup will be calculated automatically based on ingredients
      };

      if (editingProduct) {
        // Update existing product
        await ProductService.update(editingProduct.id, businessId, formData);
      } else {
        // Create new product
        await ProductService.create(businessId, formData);
      }

      form.reset();
      setEditingProduct(null);
      onProductChange();
    } catch (error) {
      console.error("Failed to save product:", error);
      setError(
        editingProduct
          ? t('products.form.errors.updateFailed')
          : t('products.form.errors.createFailed')
      );
    } finally {
      setIsSubmitting(false);
    }
  };



  return (
    <>
      <Sheet>
        <SheetTrigger asChild>
          {children}
        </SheetTrigger>
        <SheetContent className="w-[400px] sm:w-[600px] p-0">
          <SheetHeader className="flex-none border-b p-6 text-left">
            <SheetTitle>
              {isEditMode || editingProduct
                ? t('products.form.title.edit')
                : t('products.form.title.create')
              }
            </SheetTitle>
            <SheetDescription>
              {isEditMode || editingProduct
                ? t('products.form.description.edit')
                : t('products.form.description.create')
              }
            </SheetDescription>
          </SheetHeader>

          <div className="flex-1 overflow-y-auto">
            <div className="space-y-6 p-6">
              {/* Error Display */}
              {error && (
                <div className="bg-destructive/15 text-destructive px-4 py-3 rounded-md text-sm">
                  {error}
                </div>
              )}

              {/* Loading Display */}
              {isLoading && (
                <div className="flex items-center justify-center py-8">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                    <p className="text-sm text-muted-foreground">
                      {t('products.form.loading')}
                    </p>
                  </div>
                </div>
              )}

              {/* Product Form */}
              {!isLoading && (
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  {/* Product Name - Full Width */}
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel>{t('products.form.fields.name')}</FormLabel>
                        <FormControl>
                          <Input placeholder={t('products.form.placeholders.name')} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Description - Full Width */}
                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel>{t('products.form.fields.description')}</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder={t('products.form.placeholders.description')}
                            className="min-h-[80px]"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          {t('common.optional')}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Note - Full Width */}
                  <FormField
                    control={form.control}
                    name="note"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel>{t('products.form.fields.note')}</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder={t('products.form.placeholders.note')}
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          {t('common.optional')}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Active Status - Full Width */}
                  <FormField
                    control={form.control}
                    name="isActive"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel>{t('products.form.fields.isActive')}</FormLabel>
                        <Select
                          onValueChange={(value) => field.onChange(value === "true")}
                          value={field.value ? "true" : "false"}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="true">
                              {t('products.status.active')}
                            </SelectItem>
                            <SelectItem value="false">
                              {t('products.status.inactive')}
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Category - Full Width with Color Indicators */}
                  <FormField
                    control={form.control}
                    name="categoryId"
                    render={({ field }) => {
                      const selectorValue = field.value && field.value !== "none" ? field.value : undefined;

                      return (
                        <FormItem className="w-full">
                          <FormLabel>{t('products.form.fields.category')}</FormLabel>
                          <FormControl>
                            <CategorySelector
                              type="product"
                              value={selectorValue}
                              onValueChange={(value) => field.onChange(value || "none")}
                              placeholder={t('products.form.placeholders.category')}
                              categories={allCategories}
                            />
                          </FormControl>
                          <FormDescription>
                            {t('common.optional')}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />




                  <div className="flex gap-2">
                    <Button type="submit" disabled={isSubmitting || isLoading}>
                      {isSubmitting
                        ? t('common.loading')
                        : (isEditMode || editingProduct)
                          ? t('common.update')
                          : t('common.create')
                      }
                    </Button>
                    {(isEditMode || editingProduct) && (
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          setEditingProduct(null);
                          form.reset({
                            name: "",
                            description: "",
                            note: "",
                            categoryId: "none",
                            isActive: true,
                          });
                        }}
                        disabled={isSubmitting || isLoading}
                      >
                        {t('common.cancel')}
                      </Button>
                    )}
                  </div>
                  </form>
                </Form>
              )}

            </div>
          </div>
        </SheetContent>
      </Sheet>


    </>
  );
});

export { ProductManagementSheet };
