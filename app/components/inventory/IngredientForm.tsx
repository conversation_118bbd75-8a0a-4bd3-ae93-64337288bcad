import { useState, useEffect } from 'react';
import { Form, useActionData, useNavigation } from '@remix-run/react';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Textarea } from '~/components/ui/textarea';
import { Switch } from '~/components/ui/switch';
import { CategorySelector } from './CategorySelector';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import type { Ingredient, CategoryWithChildren } from '~/lib/types/inventory';

interface IngredientFormProps {
  ingredient?: Ingredient;
  businessId: string;
  onSuccess?: () => void;
  onCancel?: () => void;
  allCategories?: CategoryWithChildren[];
}

const UNITS = [
  'kg', 'g', 'mg',
  'l', 'ml',
  'pcs', 'pack', 'box',
  'cup', 'tbsp', 'tsp',
  'oz', 'lb'
];

export function IngredientForm({ 
  ingredient, 
  businessId, 
  onSuccess, 
  onCancel,
  allCategories = []
}: IngredientFormProps) {
  const navigation = useNavigation();
  const actionData = useActionData<{ ingredient?: Ingredient; error?: string }>();
  
  const [formData, setFormData] = useState({
    name: ingredient?.name || '',
    baseUnitCost: ingredient?.baseUnitCost || '',
    baseUnitQuantity: ingredient?.baseUnitQuantity || '1',
    unit: ingredient?.unit || '',
    categoryId: ingredient?.categoryId || '',
    supplierInfo: ingredient?.supplierInfo || '',
    notes: ingredient?.notes || '',
    isActive: ingredient?.isActive ?? true,
  });

  const isSubmitting = navigation.state === 'submitting';
  const isEditing = !!ingredient;

  // Handle successful submission
  useEffect(() => {
    if (actionData?.ingredient && !actionData?.error) {
      onSuccess?.();
    }
  }, [actionData, onSuccess]);

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const formatCurrency = (amount: string | number) => {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount;
    if (isNaN(num)) return 'Rp 0';
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(num);
  };

  // Calculate unit cost for display
  const unitCost = (() => {
    const cost = parseFloat(formData.baseUnitCost) || 0;
    const quantity = parseFloat(formData.baseUnitQuantity) || 1;
    return quantity > 0 ? cost / quantity : 0;
  })();

  return (
    <div className="space-y-6">
      <Form method="post" className="space-y-6">
        {/* Hidden fields */}
        <input type="hidden" name="_action" value={isEditing ? "update" : "create"} />
        <input type="hidden" name="businessId" value={businessId} />
        {isEditing && <input type="hidden" name="id" value={ingredient.id} />}

        {/* Error display */}
        {actionData?.error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-sm text-red-600">{actionData.error}</p>
          </div>
        )}

        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="name">Ingredient Name *</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="e.g., Arabica Coffee Beans"
              required
              disabled={isSubmitting}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="categoryId">Category</Label>
            <CategorySelector
              type="ingredient"
              value={formData.categoryId}
              onValueChange={(categoryId) => handleInputChange('categoryId', categoryId || '')}
              placeholder="Select ingredient category"
              disabled={isSubmitting}
              categories={allCategories}
            />
            <input type="hidden" name="categoryId" value={formData.categoryId} />
          </div>
        </div>

        {/* Cost and Quantity */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="baseUnitCost">Base Unit Cost (IDR) *</Label>
            <Input
              id="baseUnitCost"
              name="baseUnitCost"
              type="number"
              step="0.01"
              min="0"
              value={formData.baseUnitCost}
              onChange={(e) => handleInputChange('baseUnitCost', e.target.value)}
              placeholder="25000"
              required
              disabled={isSubmitting}
            />
            <p className="text-xs text-muted-foreground">
              Total cost for the base quantity
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="baseUnitQuantity">Base Quantity *</Label>
            <Input
              id="baseUnitQuantity"
              name="baseUnitQuantity"
              type="number"
              step="0.01"
              min="0.01"
              value={formData.baseUnitQuantity}
              onChange={(e) => handleInputChange('baseUnitQuantity', e.target.value)}
              placeholder="1"
              required
              disabled={isSubmitting}
            />
            <p className="text-xs text-muted-foreground">
              Quantity for the base cost
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="unit">Unit *</Label>
            <Select
              name="unit"
              value={formData.unit}
              onValueChange={(value) => handleInputChange('unit', value)}
              disabled={isSubmitting}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select unit" />
              </SelectTrigger>
              <SelectContent>
                {UNITS.map((unit) => (
                  <SelectItem key={unit} value={unit}>
                    {unit}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Unit Cost Display */}
        {formData.baseUnitCost && formData.baseUnitQuantity && formData.unit && (
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <p className="text-sm text-blue-800">
              <strong>Unit Cost:</strong> {formatCurrency(unitCost)} per {formData.unit}
            </p>
          </div>
        )}

        {/* Supplier Information */}
        <div className="space-y-2">
          <Label htmlFor="supplierInfo">Supplier Information</Label>
          <Input
            id="supplierInfo"
            name="supplierInfo"
            value={formData.supplierInfo}
            onChange={(e) => handleInputChange('supplierInfo', e.target.value)}
            placeholder="e.g., Local Coffee Farm, Jakarta"
            disabled={isSubmitting}
          />
        </div>

        {/* Notes */}
        <div className="space-y-2">
          <Label htmlFor="notes">Notes</Label>
          <Textarea
            id="notes"
            name="notes"
            value={formData.notes}
            onChange={(e) => handleInputChange('notes', e.target.value)}
            placeholder="Additional notes about this ingredient..."
            rows={3}
            disabled={isSubmitting}
          />
        </div>

        {/* Active Status */}
        <div className="flex items-center space-x-2">
          <Switch
            id="isActive"
            name="isActive"
            checked={formData.isActive}
            onCheckedChange={(checked) => handleInputChange('isActive', checked)}
            disabled={isSubmitting}
          />
          <Label htmlFor="isActive" className="text-sm font-medium">
            Active Ingredient
          </Label>
          <span className="text-sm text-muted-foreground">
            {formData.isActive ? 'Available for use' : 'Inactive/Discontinued'}
          </span>
          <input type="hidden" name="isActive" value={formData.isActive.toString()} />
        </div>

        {/* Form Actions */}
        <div className="flex items-center justify-end space-x-2 pt-4 border-t">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
          )}
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting 
              ? (isEditing ? 'Updating...' : 'Creating...') 
              : (isEditing ? 'Update Ingredient' : 'Create Ingredient')
            }
          </Button>
        </div>
      </Form>
    </div>
  );
}
