import { useTranslation } from 'react-i18next';
import { PermissionsInfoSection } from '~/components/rbac/PermissionsInfoSection';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '~/components/ui/sheet';

interface PermissionInfo {
  name: string;
  purpose: string;
  icon: React.ReactNode;
  category: 'primary' | 'action' | 'related';
}

interface PermissionsSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  routeName: string;
  title?: string;
  description?: string;
  permissions: PermissionInfo[];
}

export type { PermissionInfo };

export function PermissionsSheet({ 
  open, 
  onOpenChange, 
  routeName, 
  title, 
  description, 
  permissions 
}: PermissionsSheetProps) {
  const { t } = useTranslation();

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="w-[600px] sm:w-[800px] overflow-y-auto">
        <SheetHeader>
          <SheetTitle>
            {title || t('permissions.title', 'Permissions Information')}
          </SheetTitle>
          <SheetDescription>
            {description || t('permissions.description', 'View all permissions used in this module and their purposes.')}
          </SheetDescription>
        </SheetHeader>
        
        <div className="mt-6">
          <PermissionsInfoSection
            routeName={routeName}
            permissions={permissions}
          />
        </div>
      </SheetContent>
    </Sheet>
  );
}