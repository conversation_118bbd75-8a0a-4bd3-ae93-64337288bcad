import { useState, memo } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useTranslation } from "react-i18next";
import { X } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON>etContent,
  <PERSON>etHeader,
  <PERSON>etTitle,
  SheetTrigger,
} from "~/components/ui/sheet";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { Textarea } from "~/components/ui/textarea";
import { Button } from "~/components/ui/button";
import { Switch } from "~/components/ui/switch";
import { ProductService } from "~/lib/services/productService";

// Schema matching the UI design
const productSchema = z.object({
  name: z.string().min(1, "Product name is required"),
  description: z.string().optional(),
  notes: z.string().optional(),
  isActive: z.boolean().default(true),
});

type ProductFormValues = z.infer<typeof productSchema>;

interface ProductCreateSheetProps {
  businessId: string;
  onProductChange: () => void;
  children: React.ReactNode;
}

const ProductCreateSheet = memo(function ProductCreateSheet({
  businessId,
  onProductChange,
  children,
}: ProductCreateSheetProps) {
  const { t } = useTranslation('inventory');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  const form = useForm<ProductFormValues>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: "",
      description: "",
      notes: "",
      isActive: true,
    },
  });

  const onSubmit = async (data: ProductFormValues) => {
    setIsSubmitting(true);
    try {
      // Convert form data to match API expectations
      const formData = {
        name: data.name,
        description: data.description || "",
        notes: data.notes || "",
        status: data.isActive ? "active" : "inactive",
        sellingPrice: "0", // Default price, can be set later
      };

      await ProductService.create(businessId, formData);
      
      // Reset form and close sheet
      form.reset();
      setIsOpen(false);
      onProductChange();
    } catch (error) {
      console.error("Failed to create product:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    form.reset();
    setIsOpen(false);
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        {children}
      </SheetTrigger>
      <SheetContent className="w-full max-w-md p-0 overflow-y-auto">
        {/* Header */}
        <SheetHeader className="px-6 py-4 border-b">
          <div className="flex items-center justify-between">
            <div>
              <SheetTitle className="text-lg font-semibold text-left">
                {t('products.create.title')}
              </SheetTitle>
              <p className="text-sm text-muted-foreground mt-1">
                {t('products.create.subtitle')}
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCancel}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </SheetHeader>

        {/* Form */}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col h-full">
            <div className="flex-1 px-6 py-6 space-y-6">
              {/* Product Name */}
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">
                      {t('products.form.fields.name')} <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t('products.form.placeholders.name')}
                        className="mt-1"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Description */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">
                      {t('products.form.fields.description')}
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={t('products.form.placeholders.description')}
                        className="mt-1 min-h-[80px] resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Notes */}
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">
                      {t('products.form.fields.notes')}
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={t('products.form.placeholders.notes')}
                        className="mt-1 min-h-[80px] resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Active Status */}
              <FormField
                control={form.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex items-center justify-between">
                    <div>
                      <FormLabel className="text-sm font-medium">
                        {t('products.form.fields.status')}
                      </FormLabel>
                      <p className="text-sm text-muted-foreground">
                        {t('products.form.statusDescription')}
                      </p>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Footer */}
            <div className="border-t px-6 py-4 space-y-4">
              {/* Action Buttons */}
              <div className="flex gap-3">
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="flex-1 bg-gray-900 hover:bg-gray-800 text-white"
                >
                  {isSubmitting ? t('common.loading') : t('products.form.actions.create')}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                  disabled={isSubmitting}
                >
                  {t('common.cancel')}
                </Button>
              </div>

              {/* Next Steps */}
              <div className="text-sm text-muted-foreground">
                <p className="font-medium mb-2">{t('products.create.nextSteps')}</p>
                <ul className="space-y-1 text-xs">
                  <li>• {t('products.create.step1')}</li>
                  <li>• {t('products.create.step2')}</li>
                  <li>• {t('products.create.step3')}</li>
                </ul>
              </div>
            </div>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  );
});

export { ProductCreateSheet };
