import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Edit, Trash2, Tag, Palette, FolderTree, Calendar, ToggleLeft, ToggleRight } from "lucide-react";
import { Protected } from "~/lib/hooks/useRBAC";

import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "~/components/ui/sheet";
import { Separator } from "~/components/ui/separator";
import { CategoryService } from "~/lib/services/categoryService";
import type { Category, CategoryWithChildren } from "~/lib/types/inventory";

interface CategoryDetailSheetProps {
  category: Category;
  businessId: string;
  isOpen: boolean;
  onClose: () => void;
  onCategoryChange: () => void;
  onEdit: (category: Category) => void;
  allCategories: CategoryWithChildren[];
}

interface CategoryStats {
  totalItems: number;
  activeItems: number;
  inactiveItems: number;
}

export function CategoryDetailSheet({ 
  category, 
  businessId, 
  isOpen, 
  onClose, 
  onCategoryChange, 
  onEdit,
  allCategories 
}: CategoryDetailSheetProps) {
  const { t } = useTranslation('inventory');
  const { t: tCommon } = useTranslation('common');
  const [stats, setStats] = useState<CategoryStats | null>(null);
  const [deletingCategory, setDeletingCategory] = useState<Category | null>(null);
  const [parentCategory, setParentCategory] = useState<Category | null>(null);
  const [childCategories, setChildCategories] = useState<Category[]>([]);

  // Load category details when category changes
  useEffect(() => {
    if (category && isOpen) {
      loadCategoryDetails();
    }
  }, [category, isOpen]);

  const loadCategoryDetails = () => {
    try {
      // Find parent category from allCategories if exists
      if (category.parentId) {
        const findCategory = (cats: CategoryWithChildren[]): Category | null => {
          for (const cat of cats) {
            if (cat.id === category.parentId) return cat;
            if (cat.children && cat.children.length > 0) {
              const found = findCategory(cat.children);
              if (found) return found;
            }
          }
          return null;
        };
        const parent = findCategory(allCategories);
        setParentCategory(parent);
      } else {
        setParentCategory(null);
      }

      // Find child categories from allCategories
      const findChildren = (cats: CategoryWithChildren[]): Category[] => {
        const children: Category[] = [];
        for (const cat of cats) {
          if (cat.parentId === category.id) {
            children.push(cat);
          }
          if (cat.children && cat.children.length > 0) {
            children.push(...findChildren(cat.children));
          }
        }
        return children;
      };
      const children = findChildren(allCategories);
      setChildCategories(children);

      // Load usage statistics (mock for now - you can implement actual stats)
      setStats({
        totalItems: 0,
        activeItems: 0,
        inactiveItems: 0,
      });
    } catch (error) {
      console.error('Failed to load category details:', error);
    }
  };

  const handleDelete = async (categoryToDelete: Category) => {
    try {
      await CategoryService.delete(categoryToDelete.id, businessId);
      onCategoryChange();
      setDeletingCategory(null);
      onClose();
    } catch (error) {
      console.error('Failed to delete category:', error);
      // You might want to show an error toast here
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'ingredient': return 'bg-blue-100 text-blue-800';
      case 'product': return 'bg-green-100 text-green-800';
      case 'supplier': return 'bg-purple-100 text-purple-800';
      case 'customer': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'ingredient': return t('categories.types.ingredient');
      case 'product': return t('categories.types.product');
      case 'supplier': return t('categories.types.supplier');
      case 'customer': return t('categories.types.customer');
      default: return type;
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(date));
  };

  return (
    <>
      <Sheet open={isOpen} onOpenChange={onClose}>
        <SheetContent className="w-[400px] sm:w-[540px] overflow-y-auto p-6">
          <SheetHeader>
            <div className="flex items-center gap-3">
              {category.color && (
                <div
                  className="w-6 h-6 rounded-full border border-gray-300 flex-shrink-0"
                  style={{ backgroundColor: category.color }}
                />
              )}
              <div className="flex-1">
                <SheetTitle className="text-xl">{category.name}</SheetTitle>
                <SheetDescription>
                  {category.description || t('categories.detail.noDescription')}
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          <div className="space-y-6 mt-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Tag className="h-5 w-5" />
                {t('categories.detail.basicInfo')}
              </h3>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t('categories.form.type.label')}
                  </label>
                  <div className="mt-1">
                    <Badge variant="secondary" className={getTypeColor(category.type)}>
                      {getTypeLabel(category.type)}
                    </Badge>
                  </div>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t('categories.form.isActive.label')}
                  </label>
                  <div className="mt-1 flex items-center gap-2">
                    {category.isActive ? (
                      <>
                        <ToggleRight className="h-5 w-5 text-green-600" />
                        <Badge variant="default">{tCommon('status.active')}</Badge>
                      </>
                    ) : (
                      <>
                        <ToggleLeft className="h-5 w-5 text-gray-400" />
                        <Badge variant="secondary">{tCommon('status.inactive')}</Badge>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {category.color && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                    <Palette className="h-4 w-4" />
                    {t('categories.form.color.label')}
                  </label>
                  <div className="mt-1 flex items-center gap-2">
                    <div
                      className="w-6 h-6 rounded border border-gray-300"
                      style={{ backgroundColor: category.color }}
                    />
                    <span className="text-sm font-mono">{category.color}</span>
                  </div>
                </div>
              )}

              {category.sortOrder && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t('categories.form.sortOrder.label')}
                  </label>
                  <div className="mt-1">
                    <span className="text-sm">{category.sortOrder}</span>
                  </div>
                </div>
              )}
            </div>

            <Separator />

            {/* Hierarchy Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <FolderTree className="h-5 w-5" />
                {t('categories.detail.hierarchy')}
              </h3>
              
              {parentCategory && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t('categories.detail.parentCategory')}
                  </label>
                  <div className="mt-1 flex items-center gap-2">
                    {parentCategory.color && (
                      <div
                        className="w-4 h-4 rounded-full border border-gray-300"
                        style={{ backgroundColor: parentCategory.color }}
                      />
                    )}
                    <span>{parentCategory.name}</span>
                  </div>
                </div>
              )}

              {childCategories.length > 0 && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t('categories.detail.childCategories')} ({childCategories.length})
                  </label>
                  <div className="mt-2 space-y-2">
                    {childCategories.map((child) => (
                      <div key={child.id} className="flex items-center gap-2 p-2 bg-muted/50 rounded">
                        {child.color && (
                          <div
                            className="w-3 h-3 rounded-full border border-gray-300"
                            style={{ backgroundColor: child.color }}
                          />
                        )}
                        <span className="text-sm">{child.name}</span>
                        <Badge variant={child.isActive ? "default" : "secondary"} className="ml-auto text-xs">
                          {child.isActive ? tCommon('status.active') : tCommon('status.inactive')}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {!parentCategory && childCategories.length === 0 && (
                <p className="text-sm text-muted-foreground">
                  {t('categories.detail.noHierarchy')}
                </p>
              )}
            </div>

            <Separator />

            {/* Usage Statistics */}
            {stats && (
              <>
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">
                    {t('categories.detail.usage')}
                  </h3>
                  
                  <div className="grid grid-cols-3 gap-4">
                    <div className="text-center p-3 bg-muted/50 rounded">
                      <div className="text-2xl font-bold">{stats.totalItems}</div>
                      <div className="text-sm text-muted-foreground">
                        {t('categories.detail.totalItems')}
                      </div>
                    </div>
                    <div className="text-center p-3 bg-muted/50 rounded">
                      <div className="text-2xl font-bold text-green-600">{stats.activeItems}</div>
                      <div className="text-sm text-muted-foreground">
                        {t('categories.detail.activeItems')}
                      </div>
                    </div>
                    <div className="text-center p-3 bg-muted/50 rounded">
                      <div className="text-2xl font-bold text-gray-500">{stats.inactiveItems}</div>
                      <div className="text-sm text-muted-foreground">
                        {t('categories.detail.inactiveItems')}
                      </div>
                    </div>
                  </div>
                </div>
                <Separator />
              </>
            )}

            {/* Metadata */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                {t('categories.detail.metadata')}
              </h3>
              
              <div className="grid grid-cols-1 gap-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{tCommon('fields.createdAt')}:</span>
                  <span>{formatDate(category.createdAt)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{tCommon('fields.updatedAt')}:</span>
                  <span>{formatDate(category.updatedAt)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{tCommon('fields.id')}:</span>
                  <span className="font-mono text-xs">{category.id}</span>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="pt-6 border-t space-y-3">
              <div className="flex flex-col sm:flex-row gap-3">
                <Protected permissions={["categories.edit"]}>
                  <Button
                    className="flex-1"
                    onClick={() => onEdit(category)}
                  >
                    <Edit className="mr-2 h-4 w-4" />
                    {t('categories.actions.edit')}
                  </Button>
                </Protected>
                <Protected permissions={["categories.delete"]}>
                  <Button
                    className="flex-1"
                    variant="destructive"
                    onClick={() => setDeletingCategory(category)}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    {t('categories.actions.delete')}
                  </Button>
                </Protected>
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>

      {/* Delete Confirmation Dialog */}
      {deletingCategory && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-2">
              {t('categories.delete.title')}
            </h3>
            <p className="text-muted-foreground mb-4">
              {t('categories.delete.description', { name: deletingCategory.name })}
            </p>
            {childCategories.length > 0 && (
              <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
                <p className="text-sm text-yellow-800">
                  {t('categories.delete.hasChildren', { count: childCategories.length })}
                </p>
              </div>
            )}
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setDeletingCategory(null)}
              >
                {tCommon('buttons.cancel')}
              </Button>
              <Button
                variant="destructive"
                onClick={() => handleDelete(deletingCategory)}
              >
                {tCommon('buttons.delete')}
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}