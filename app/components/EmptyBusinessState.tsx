import React from "react"
import { useTranslation } from "react-i18next"
import { Building2, Plus } from "lucide-react"
import { Button } from "~/components/ui/button"
import { BusinessManagementSheet } from "~/components/BusinessManagementSheet"

interface EmptyBusinessStateProps {
  className?: string
}

export function EmptyBusinessState({ className }: EmptyBusinessStateProps) {
  const { t } = useTranslation('business')

  return (
    <div className={`flex flex-col items-center justify-center min-h-[400px] p-8 text-center ${className}`}>
      <div className="flex flex-col items-center gap-6 max-w-md">
        {/* Icon */}
        <div className="flex items-center justify-center w-20 h-20 rounded-full bg-muted">
          <img
            src="/kwaci.webp"
            alt="KWACI Logo"
            className="w-12 h-12 object-contain"
          />
        </div>

        {/* Content */}
        <div className="space-y-2">
          <h2 className="text-2xl font-semibold tracking-tight">
            {t('switcher.createFirstBusiness')}
          </h2>
          <p className="text-muted-foreground">
            {t('switcher.getStarted')}
          </p>
        </div>

        {/* Action */}
        <BusinessManagementSheet mode="create">
          <Button size="lg" className="gap-2">
            <Plus className="w-4 h-4" />
            {t('switcher.addBusiness')}
          </Button>
        </BusinessManagementSheet>

        {/* Additional help text */}
        <div className="text-sm text-muted-foreground max-w-sm">
          <p>
            Create your first business to start managing your coffee operations, 
            track inventory, and monitor your business performance.
          </p>
        </div>
      </div>
    </div>
  )
}
