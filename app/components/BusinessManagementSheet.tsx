import React, { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { useTranslation } from "react-i18next"
import { Pencil, Trash2 } from "lucide-react"

import { But<PERSON> } from "~/components/ui/button"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "~/components/ui/sheet"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "~/components/ui/alert-dialog"
import { Input } from "~/components/ui/input"
import { Textarea } from "~/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select"
import { useBusinessStore, useBusinesses, useCurrentBusiness } from "~/lib/stores/businessStore"
import { useCreateBusiness, useUpdateBusiness, useDeleteBusiness } from "~/lib/query/hooks"
import { getCurrencyOptions, DEFAULT_CURRENCY } from "~/lib/utils/currencyUtils"
import { useAuth } from "~/lib/providers/AuthProvider"
import type { Business } from "~/lib/types/business"

type BusinessFormData = {
  name: string;
  description?: string;
  note?: string;
  currency: string;
  logo?: string;
};

interface BusinessManagementSheetProps {
  mode?: 'create' | 'manage'
  children: React.ReactNode
}

export function BusinessManagementSheet({
  mode = 'manage',
  children
}: BusinessManagementSheetProps) {
  const { t } = useTranslation('business')
  const { user } = useAuth()
  const [editingBusiness, setEditingBusiness] = useState<Business | null>(null)
  const [deletingBusiness, setDeletingBusiness] = useState<Business | null>(null)

  const businessSchema = z.object({
    name: z.string().min(1, t('management.validation.name.required')).max(100, t('management.validation.name.maxLength')),
    description: z.string().max(500, t('management.validation.description.maxLength')).optional(),
    note: z.string().max(1000, t('management.validation.note.maxLength')).optional(),
    currency: z.string().min(1, t('management.validation.currency.required')),
    logo: z.string().optional(),
  })

  const businesses = useBusinesses()
  const currentBusiness = useCurrentBusiness()
  const { addBusiness, updateBusiness, removeBusiness, setCurrentBusiness } = useBusinessStore()

  const form = useForm<BusinessFormData>({
    resolver: zodResolver(businessSchema),
    defaultValues: {
      name: "",
      description: "",
      note: "",
      currency: DEFAULT_CURRENCY,
      logo: undefined,
    },
  })

  const handleEdit = (business: Business) => {
    setEditingBusiness(business)
    form.reset({
      name: business.name,
      description: business.description || "",
      note: business.note || "",
      currency: business.currency || DEFAULT_CURRENCY,
      logo: business.logo || undefined,
    })
  }

  // Use React Query mutation for deleting a business
  const deleteMutation = useDeleteBusiness(user?.id)

  const handleDelete = async (business: Business) => {
    if (!user?.id) {
      alert('User not authenticated')
      return
    }

    try {
      // Use the mutation to delete the business
      await deleteMutation.mutateAsync(business.id)

      // Update the Zustand store for UI state
      removeBusiness(business.id)

      // If we deleted the current business, clear it or switch to another one
      if (currentBusiness?.id === business.id) {
        const remainingBusiness = businesses.find(b => b.id !== business.id)
        setCurrentBusiness(remainingBusiness || null)
      }

      setDeletingBusiness(null)
    } catch (error) {
      console.error("Failed to delete business:", error)
      // Error handling is already done in the mutation
    }
  }

  // Use React Query mutations for creating and updating businesses
  const createMutation = useCreateBusiness(user?.id)
  const updateMutation = useUpdateBusiness(
    editingBusiness?.id,
    user?.id
  )

  const onSubmit = async (data: BusinessFormData) => {
    if (!user?.id) {
      alert('User not authenticated')
      return
    }

    try {
      if (editingBusiness) {
        // Update existing business using React Query mutation
        await updateMutation.mutateAsync(data)

        // Update the Zustand store for UI state
        updateBusiness(editingBusiness.id, data)
      } else {
        // Create new business using React Query mutation
        const newBusiness = await createMutation.mutateAsync(data)

        // Update the Zustand store for UI state
        addBusiness(newBusiness)
      }

      form.reset()
      setEditingBusiness(null)
    } catch (error) {
      console.error("Failed to save business:", error)
      // Error handling is already done in the mutations
    }
  }

  // Determine if any mutation is loading
  const isSubmitting = createMutation.isPending || updateMutation.isPending || deleteMutation.isPending

  return (
    <>
      <Sheet>
        <SheetTrigger asChild>
          {children}
        </SheetTrigger>
        <SheetContent className="w-[400px] sm:w-[540px] p-0">
          <SheetHeader className="flex-none border-b p-6 text-left">
            <SheetTitle>
              {editingBusiness
                ? t('management.titles.edit')
                : mode === 'create'
                  ? t('management.titles.create')
                  : t('management.titles.manage')
              }
            </SheetTitle>
            <SheetDescription>
              {editingBusiness
                ? t('management.descriptions.edit')
                : mode === 'create'
                  ? t('management.descriptions.create')
                  : t('management.descriptions.manage')
              }
            </SheetDescription>
          </SheetHeader>

          <div className="flex-1 overflow-y-auto">
            <div className="space-y-6 p-6">
            {/* Business Form */}
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('management.form.fields.name.label')}</FormLabel>
                      <FormControl>
                        <Input placeholder={t('management.form.fields.name.placeholder')} {...field} />
                      </FormControl>
                      <FormDescription>
                        {t('management.form.fields.name.description')}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="currency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className={editingBusiness ? "text-muted-foreground" : ""}>
                        {editingBusiness ? t('management.form.fields.currency.labelReadonly') : t('management.form.fields.currency.label')}
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        disabled={!!editingBusiness} // Disable when editing existing business
                      >
                        <FormControl>
                          <SelectTrigger className={editingBusiness ? "opacity-60 cursor-not-allowed" : ""}>
                            <SelectValue placeholder={t('management.form.fields.currency.placeholder')} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {getCurrencyOptions().map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        {editingBusiness
                          ? t('management.form.fields.currency.descriptionReadonly')
                          : t('management.form.fields.currency.description')
                        }
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('management.form.fields.description.label')}</FormLabel>
                      <FormControl>
                        <Input placeholder={t('management.form.fields.description.placeholder')} {...field} />
                      </FormControl>
                      <FormDescription>
                        {t('management.form.fields.description.description')}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="note"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('management.form.fields.note.label')}</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={t('management.form.fields.note.placeholder')}
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        {t('management.form.fields.note.description')}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex gap-2">
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? t('management.form.buttons.saving') : editingBusiness ? t('management.form.buttons.update') : t('management.form.buttons.create')}
                  </Button>
                  {editingBusiness && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        setEditingBusiness(null)
                        // Reset form to clean default values
                        form.reset({
                          name: "",
                          description: "",
                          note: "",
                          currency: DEFAULT_CURRENCY,
                          logo: undefined,
                        })
                      }}
                    >
                      {t('management.form.buttons.cancel')}
                    </Button>
                  )}
                </div>
              </form>
            </Form>

            {/* Existing Businesses List - only show in manage mode */}
            {!editingBusiness && mode === 'manage' && businesses.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">{t('management.existingBusinesses.title')}</h3>
                <div className="space-y-2">
                  {businesses.map((business) => (
                    <div
                      key={business.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <img
                          src="/kwaci.webp"
                          alt="KWACI Logo"
                          className="size-4 object-contain opacity-70"
                        />
                        <div>
                          <div className="font-medium">{business.name}</div>
                          {business.description && (
                            <div className="text-sm text-muted-foreground">
                              {business.description}
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex gap-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleEdit(business)}
                        >
                          <Pencil className="size-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => setDeletingBusiness(business)}
                          disabled={businesses.length <= 1}
                        >
                          <Trash2 className="size-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
            </div>
          </div>
        </SheetContent>
      </Sheet>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingBusiness} onOpenChange={() => setDeletingBusiness(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('management.deleteDialog.title')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('management.deleteDialog.description', { businessName: deletingBusiness?.name })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('management.deleteDialog.buttons.cancel')}</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deletingBusiness && handleDelete(deletingBusiness)}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {t('management.deleteDialog.buttons.delete')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
