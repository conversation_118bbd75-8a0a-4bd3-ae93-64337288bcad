/**
 * Session diagnostic component for debugging session issues
 * Only shows in development mode
 */

import React, { useEffect, useState } from 'react';
import { useAuth } from '~/lib/providers/AuthProvider';
import { useClientSession } from '~/lib/hooks/useClientSession';
import { convertBetterAuthSession } from '~/lib/session/sessionMonitor';

export function SessionDiagnostic() {
  const { data: rawSession } = useClientSession();
  const { 
    isAuthenticated, 
    sessionTimeRemaining, 
    isSessionMonitoring 
  } = useAuth();
  
  const [diagnosticData, setDiagnosticData] = useState<any>(null);

  useEffect(() => {
    if (rawSession) {
      console.log('🔍 [SessionDiagnostic] Raw session data:', rawSession);
      
      // Test session conversion
      const convertedSession = convertBetterAuthSession(rawSession);
      console.log('🔄 [SessionDiagnostic] Converted session:', convertedSession);
      
      setDiagnosticData({
        rawSession,
        convertedSession,
        isAuthenticated,
        sessionTimeRemaining,
        isSessionMonitoring,
        timestamp: new Date().toISOString()
      });
    }
  }, [rawSession, isAuthenticated, sessionTimeRemaining, isSessionMonitoring]);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: '#1a1a1a',
      color: '#fff',
      padding: '10px',
      borderRadius: '5px',
      fontSize: '12px',
      maxWidth: '400px',
      zIndex: 9999,
      fontFamily: 'monospace'
    }}>
      <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>
        🔍 Session Diagnostic
      </div>
      
      <div>
        <strong>Auth Status:</strong> {isAuthenticated ? '✅ Authenticated' : '❌ Not Authenticated'}
      </div>
      
      <div>
        <strong>Monitoring:</strong> {isSessionMonitoring ? '✅ Active' : '❌ Inactive'}
      </div>

      <div>
        <strong>Session ID:</strong> {rawSession?.session?.token ? `${rawSession.session.token.substring(0, 8)}...` : 'N/A'}
      </div>
      
      <div>
        <strong>Time Remaining:</strong> {sessionTimeRemaining > 0 ? `${Math.floor(sessionTimeRemaining / (1000 * 60))}m` : 'N/A'}
      </div>
      
      {rawSession && (
        <div style={{ marginTop: '10px' }}>
          <strong>Session Structure:</strong>
          <div style={{ fontSize: '10px', marginTop: '5px' }}>
            {rawSession.session ? '✅ session' : '❌ session'}<br/>
            {rawSession.user ? '✅ user' : '❌ user'}<br/>
            {rawSession.session?.id ? `✅ session.id: ${rawSession.session.id.substring(0, 8)}...` : '❌ session.id'}<br/>
            {rawSession.session?.expiresAt ? `✅ session.expiresAt: ${new Date(rawSession.session.expiresAt).toLocaleTimeString()}` : '❌ session.expiresAt'}
          </div>
        </div>
      )}
      
      {diagnosticData && (
        <details style={{ marginTop: '10px', fontSize: '10px' }}>
          <summary style={{ cursor: 'pointer' }}>Raw Data</summary>
          <pre style={{ 
            background: '#2a2a2a', 
            padding: '5px', 
            borderRadius: '3px', 
            overflow: 'auto',
            maxHeight: '200px',
            marginTop: '5px'
          }}>
            {JSON.stringify(diagnosticData, null, 2)}
          </pre>
        </details>
      )}
    </div>
  );
}
