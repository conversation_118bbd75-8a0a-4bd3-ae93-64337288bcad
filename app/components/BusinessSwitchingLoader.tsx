import { useState, useEffect } from "react"
import { Building2, CheckCircle } from "lucide-react"
import { useIsBusinessSwitching, useCurrentBusiness } from "~/lib/stores/businessStore"

export function BusinessSwitchingLoader() {
  const isBusinessSwitching = useIsBusinessSwitching()
  const currentBusiness = useCurrentBusiness()
  const [showCompletion, setShowCompletion] = useState(false)
  const [completionBusinessName, setCompletionBusinessName] = useState<string | null>(null)
  const [wasSwitching, setWasSwitching] = useState(false)

  useEffect(() => {
    if (isBusinessSwitching) {
      setWasSwitching(true)
      setShowCompletion(false)
      setCompletionBusinessName(null)
    } else if (wasSwitching && !isBusinessSwitching && currentBusiness) {
      // Only show completion if we were actually switching (not on initial load)
      setCompletionBusinessName(currentBusiness.name)
      setShowCompletion(true)
      
      // Hide completion after a short delay
      const timer = setTimeout(() => {
        setShowCompletion(false)
        setCompletionBusinessName(null)
        setWasSwitching(false) // Reset wasSwitching after completion
      }, 1000)

      return () => clearTimeout(timer)
    }
  }, [isBusinessSwitching, currentBusiness, wasSwitching])

  if (!isBusinessSwitching && !showCompletion) {
    return null
  }

  return (
    <div className="fixed inset-0 z-200 flex items-center justify-center bg-background/80 backdrop-blur-sm">
      <div className="flex flex-col items-center space-y-4 p-8 rounded-lg bg-card border shadow-lg">
        {showCompletion ? (
          <>
            <div className="relative">
              <CheckCircle className="h-12 w-12 text-green-500 animate-in zoom-in-50 duration-300" />
            </div>
            <div className="text-center space-y-2">
              <h3 className="text-lg font-semibold">Switched to {completionBusinessName}</h3>
              <p className="text-sm text-muted-foreground">Business context updated successfully</p>
            </div>
          </>
        ) : (
          <>
            <div className="relative">
              <Building2 className="h-12 w-12 text-primary animate-pulse" />
              <div className="absolute inset-0 rounded-full border-2 border-primary border-t-transparent animate-spin" />
            </div>
            <div className="text-center space-y-2">
              <h3 className="text-lg font-semibold">Switching Business</h3>
              <p className="text-sm text-muted-foreground">Please wait while we update your business context...</p>
            </div>
          </>
        )}
      </div>
    </div>
  )
}
