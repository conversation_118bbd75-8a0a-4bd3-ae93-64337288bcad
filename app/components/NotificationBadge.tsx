import { useEffect, useState } from 'react';
import { Link } from '@remix-run/react';
import { Bell } from 'lucide-react';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { useAuth } from '~/lib/providers/AuthProvider';

interface NotificationBadgeProps {
  className?: string;
}

export function NotificationBadge({ className }: NotificationBadgeProps) {
  const { user, isAuthenticated } = useAuth();
  const [pendingCount, setPendingCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!isAuthenticated || !user?.email) {
      setPendingCount(0);
      return;
    }

    const fetchNotificationCount = async () => {
      setIsLoading(true);
      try {
        const response = await fetch('/api/notifications/count');
        if (response.ok) {
          const data = await response.json();
          setPendingCount(data.count || 0);
        }
      } catch (error) {
        console.error('Failed to fetch notification count:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchNotificationCount();

    // Refresh count every 5 minutes
    const interval = setInterval(fetchNotificationCount, 5 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, [isAuthenticated, user?.email]);

  if (!isAuthenticated) {
    return null;
  }

  return (
    <Button
      asChild
      variant="ghost"
      size="sm"
      className={`relative ${className}`}
    >
      <Link to="/notifications">
        <Bell className="h-5 w-5" />
        {pendingCount > 0 && (
          <Badge 
            variant="destructive" 
            className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
          >
            {pendingCount > 99 ? '99+' : pendingCount}
          </Badge>
        )}
        <span className="sr-only">
          {pendingCount > 0 
            ? `${pendingCount} pending notifications` 
            : 'No pending notifications'
          }
        </span>
      </Link>
    </Button>
  );
}
