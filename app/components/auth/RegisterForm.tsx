import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useTranslation } from "react-i18next";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Alert, AlertDescription } from "~/components/ui/alert";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import { authClient } from "~/lib/auth.client";
import { Loader2, AlertCircle } from "lucide-react";
import { handleAuthError } from "~/lib/utils/authErrorHandler";

type RegisterFormValues = {
  firstName: string;
  lastName?: string;
  email: string;
  password: string;
  confirmPassword: string;
};

interface RegisterFormProps {
  onSuccess?: () => void;
  onSwitchToLogin?: () => void;
}

export function RegisterForm({ onSuccess, onSwitchToLogin }: RegisterFormProps) {
  const { t } = useTranslation('auth');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form validation schema with i18n
  const registerFormSchema = z.object({
    firstName: z.string().min(1, t('forms.validation.firstName.required')),
    lastName: z.string().optional(),
    email: z.string().min(1, t('forms.validation.email.required')).email(t('forms.validation.email.invalid')),
    password: z.string().min(8, t('forms.validation.password.minLength')),
    confirmPassword: z.string().min(1, t('forms.validation.confirmPassword.required')),
  }).refine((data) => data.password === data.confirmPassword, {
    message: t('forms.validation.confirmPassword.mismatch'),
    path: ["confirmPassword"],
  });

  const form = useForm<RegisterFormValues>({
    resolver: zodResolver(registerFormSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
  });

  // Clear error when user starts typing
  useEffect(() => {
    const subscription = form.watch(() => {
      if (error) {
        setError(null);
      }
    });
    return () => subscription.unsubscribe();
  }, [form, error]);

  const handleSubmit = async (values: RegisterFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await authClient.signUp.email({
        email: values.email,
        password: values.password,
        name: `${values.firstName} ${values.lastName || ""}`.trim(),
      });

      // Check if there was an error in the result
      if (result.error) {
        const errorMessage = handleAuthError(result.error, 'register', t);
        setError(errorMessage);
        return;
      }

      // Success - the session will be automatically updated
      onSuccess?.();
    } catch (err: any) {
      // Handle network errors and other exceptions
      const errorMessage = handleAuthError(err, 'register', t);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-bold text-center">{t('forms.register.title')}</CardTitle>
        <CardDescription className="text-center">
          {t('forms.register.description')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('forms.register.fields.firstName.label')}</FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t('forms.register.fields.firstName.placeholder')}
                        disabled={isLoading}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="lastName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('forms.register.fields.lastName.label')}</FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t('forms.register.fields.lastName.placeholder')}
                        disabled={isLoading}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('forms.register.fields.email.label')}</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder={t('forms.register.fields.email.placeholder')}
                      disabled={isLoading}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('forms.register.fields.password.label')}</FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder={t('forms.register.fields.password.placeholder')}
                      disabled={isLoading}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('forms.register.fields.confirmPassword.label')}</FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder={t('forms.register.fields.confirmPassword.placeholder')}
                      disabled={isLoading}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('forms.register.buttons.submitting')}
                </>
              ) : (
                t('forms.register.buttons.submit')
              )}
            </Button>
          </form>
        </Form>
        
        {onSwitchToLogin && (
          <div className="mt-4 text-center text-sm">
            {t('forms.register.messages.switchPrompt')}{" "}
            <button
              type="button"
              onClick={onSwitchToLogin}
              className="text-primary hover:underline font-medium"
            >
              {t('forms.register.buttons.switchToLogin')}
            </button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
