import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useTranslation } from "react-i18next";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Alert, AlertDescription } from "~/components/ui/alert";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import { authClient } from "~/lib/auth.client";
import { Loader2, AlertCircle } from "lucide-react";
import { handleAuthError } from "~/lib/utils/authErrorHandler";

type LoginFormValues = {
  email: string;
  password: string;
};

interface LoginFormProps {
  onSuccess?: () => void;
  onSwitchToRegister?: () => void;
}

export function LoginForm({ onSuccess, onSwitchToRegister }: LoginFormProps) {
  const { t } = useTranslation('auth');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form validation schema with i18n
  const loginFormSchema = z.object({
    email: z.string().min(1, t('forms.validation.email.required')).email(t('forms.validation.email.invalid')),
    password: z.string().min(1, t('forms.validation.password.required')),
  });

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginFormSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  // Clear error when user starts typing
  useEffect(() => {
    const subscription = form.watch(() => {
      if (error) {
        setError(null);
      }
    });
    return () => subscription.unsubscribe();
  }, [form, error]);

  const handleSubmit = async (values: LoginFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await authClient.signIn.email({
        email: values.email,
        password: values.password,
      });

      // Check if there was an error in the result
      if (result.error) {
        const errorMessage = handleAuthError(result.error, 'login', t);
        setError(errorMessage);
        return;
      }

      // Success - the session will be automatically updated
      onSuccess?.();
    } catch (err: any) {
      // Handle network errors and other exceptions
      const errorMessage = handleAuthError(err, 'login', t);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-bold text-center">{t('forms.login.title')}</CardTitle>
        <CardDescription className="text-center">
          {t('forms.login.description')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('forms.login.fields.email.label')}</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder={t('forms.login.fields.email.placeholder')}
                      disabled={isLoading}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('forms.login.fields.password.label')}</FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder={t('forms.login.fields.password.placeholder')}
                      disabled={isLoading}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('forms.login.buttons.submitting')}
                </>
              ) : (
                t('forms.login.buttons.submit')
              )}
            </Button>
          </form>
        </Form>
        
        {onSwitchToRegister && (
          <div className="mt-4 text-center text-sm">
            {t('forms.login.messages.switchPrompt')}{" "}
            <button
              type="button"
              onClick={onSwitchToRegister}
              className="text-primary hover:underline font-medium"
            >
              {t('forms.login.buttons.switchToRegister')}
            </button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
