/**
 * Logout Confirmation Dialog Component
 * 
 * Provides a confirmation dialog for logout actions with options for:
 * - Standard logout (current session only)
 * - Logout from all devices
 * - Proper accessibility and internationalization
 */

import { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "~/components/ui/alert-dialog";
import { Button } from "~/components/ui/button";
import { Checkbox } from "~/components/ui/checkbox";
import { LogOut, Smartphone } from "lucide-react";
import { handleLogout } from "~/lib/utils/logoutHandler";

interface LogoutConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  /** Whether to show the "logout from all devices" option */
  showAllDevicesOption?: boolean;
  /** Custom title for the dialog */
  title?: string;
  /** Custom description for the dialog */
  description?: string;
  /** Callback when logout is initiated */
  onLogoutStart?: () => void;
  /** Callback when logout completes successfully */
  onLogoutSuccess?: () => void;
  /** Callback when logout fails */
  onLogoutError?: (error: any) => void;
}

export function LogoutConfirmDialog({
  isOpen,
  onClose,
  showAllDevicesOption = true,
  title,
  description,
  onLogoutStart,
  onLogoutSuccess,
  onLogoutError,
}: LogoutConfirmDialogProps) {
  const { t } = useTranslation('auth');
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [logoutFromAllDevices, setLogoutFromAllDevices] = useState(false);

  const handleConfirmLogout = async () => {
    if (isLoggingOut) return;

    setIsLoggingOut(true);
    
    try {
      if (onLogoutStart) {
        onLogoutStart();
      }

      const result = await handleLogout(t, {
        revokeAllSessions: logoutFromAllDevices,
        showToasts: true,
        forceLogout: true,
        onSuccess: () => {
          if (onLogoutSuccess) {
            onLogoutSuccess();
          }
        },
        onError: (error) => {
          if (onLogoutError) {
            onLogoutError(error);
          }
        },
      });

      if (result.success) {
        onClose();
      }

    } catch (error) {
      console.error('Logout confirmation error:', error);
      if (onLogoutError) {
        onLogoutError(error);
      }
    } finally {
      setIsLoggingOut(false);
    }
  };

  const handleCancel = () => {
    if (!isLoggingOut) {
      onClose();
    }
  };

  const dialogTitle = title || t('userMenu.confirmSignOut');
  const dialogDescription = description || (
    logoutFromAllDevices 
      ? t('userMenu.confirmSignOutAllMessage')
      : t('userMenu.confirmSignOutMessage')
  );

  return (
    <AlertDialog open={isOpen} onOpenChange={handleCancel}>
      <AlertDialogContent className="sm:max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <LogOut className="h-5 w-5 text-muted-foreground" />
            {dialogTitle}
          </AlertDialogTitle>
          <AlertDialogDescription className="text-left">
            {dialogDescription}
          </AlertDialogDescription>
        </AlertDialogHeader>

        {showAllDevicesOption && (
          <div className="flex items-center space-x-2 py-4">
            <Checkbox
              id="logout-all-devices"
              checked={logoutFromAllDevices}
              onCheckedChange={(checked) => setLogoutFromAllDevices(checked === true)}
              disabled={isLoggingOut}
            />
            <label
              htmlFor="logout-all-devices"
              className="flex items-center gap-2 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
            >
              <Smartphone className="h-4 w-4 text-muted-foreground" />
              {t('userMenu.signOutAllDevices')}
            </label>
          </div>
        )}

        <AlertDialogFooter className="flex-col sm:flex-row gap-2">
          <AlertDialogCancel 
            onClick={handleCancel}
            disabled={isLoggingOut}
            className="w-full sm:w-auto"
          >
            {t('common:buttons.cancel')}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirmLogout}
            disabled={isLoggingOut}
            className="w-full sm:w-auto bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isLoggingOut ? (
              <div className="flex items-center gap-2">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                {logoutFromAllDevices ? t('userMenu.signingOut') : t('userMenu.signingOut')}
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <LogOut className="h-4 w-4" />
                {logoutFromAllDevices ? t('userMenu.signOutAllDevices') : t('userMenu.signOut')}
              </div>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

/**
 * Hook for managing logout confirmation dialog state
 */
export function useLogoutConfirmDialog() {
  const [isOpen, setIsOpen] = useState(false);

  const openDialog = () => setIsOpen(true);
  const closeDialog = () => setIsOpen(false);

  return {
    isOpen,
    openDialog,
    closeDialog,
  };
}
