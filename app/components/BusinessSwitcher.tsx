import { ChevronsUpDown, Plus } from "lucide-react"
import { useTranslation } from "react-i18next"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "~/components/ui/sidebar"
import { useBusinessStore, useCurrentBusiness, useBusinesses } from "~/lib/stores/businessStore"
import { BusinessManagementSheet } from "~/components/BusinessManagementSheet"
import { useAuth } from "~/lib/providers/AuthProvider"
import { useBusinessPermissions } from "~/lib/hooks/useBusinessPermissions"
import type { Business } from "~/lib/types/business"

export function BusinessSwitcher() {
  const { t } = useTranslation('business')
  const { isMobile } = useSidebar()
  const { isAuthenticated, showAuthModal } = useAuth()
  const currentBusiness = useCurrentBusiness()
  const businesses = useBusinesses()
  const { switchBusiness } = useBusinessStore()
  const { canCreateBusiness, canManageBusiness } = useBusinessPermissions()

  const handleBusinessSelect = async (business: Business) => {
    await switchBusiness(business)
  }

  // Show logged-out state when user is not authenticated
  if (!isAuthenticated) {
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton
            size="lg"
            onClick={() => showAuthModal("login")}
            className="cursor-pointer hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
          >
            <div className="flex aspect-square size-8 items-center justify-center rounded-lg border bg-muted">
              <img
                src="/kwaci.webp"
                alt="KWACI Logo"
                className="size-6 shrink-0 object-contain"
              />
            </div>
            <div className="grid flex-1 text-left text-sm leading-tight group-data-[collapsible=icon]:hidden">
              <span className="truncate font-semibold text-muted-foreground">
                KWACI Grow
              </span>
              <span className="truncate text-xs text-muted-foreground">
                {t('switcher.loginToAccess')}
              </span>
            </div>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    )
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div className="flex aspect-square size-8 items-center justify-center rounded-lg border overflow-hidden">
                {currentBusiness?.logo ? (
                  <span className="text-xl">{currentBusiness.logo}</span>
                ) : (
                  <img
                    src="/kwaci.webp"
                    alt="KWACI Logo"
                    className="size-5 object-contain"
                  />
                )}
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight group-data-[collapsible=icon]:hidden">
                <span className="truncate font-semibold">
                  {currentBusiness?.name || (businesses.length === 0 ? t('switcher.createFirstBusiness') : t('switcher.selectBusiness'))}
                </span>
                <span className="truncate text-xs">
                  {currentBusiness ? (currentBusiness.description || '') : (businesses.length === 0 ? t('switcher.getStarted') : t('switcher.noBusinessSelected'))}
                </span>
              </div>
              <ChevronsUpDown className="ml-auto group-data-[collapsible=icon]:hidden" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
            align="start"
            side={isMobile ? "bottom" : "right"}
            sideOffset={4}
          >
            <DropdownMenuLabel className="text-xs text-muted-foreground">
              {t('switcher.label')}
            </DropdownMenuLabel>
            {businesses.length === 0 ? (
              <div className="px-2 py-4 text-center">
                <div className="flex flex-col items-center gap-2">
                  <img
                    src="/kwaci.webp"
                    alt="KWACI Logo"
                    className="size-8 object-contain opacity-50"
                  />
                  <div className="text-sm text-muted-foreground">
                    {t('switcher.noBusinesses')}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {t('switcher.createFirstBusiness')}
                  </div>
                </div>
              </div>
            ) : (
              businesses.map((business, index) => (
                <DropdownMenuItem
                  key={business.id}
                  onClick={() => handleBusinessSelect(business)}
                  className="gap-2 p-2"
                >
                  <div className="flex size-6 items-center justify-center rounded-sm border">
                    {business.logo ? (
                      <span className="text-sm">{business.logo}</span>
                    ) : (
                      <img
                        src="/kwaci.webp"
                        alt="KWACI Logo"
                        className="size-4 object-contain"
                      />
                    )}
                  </div>
                  <div className="flex flex-col">
                    <span className="font-medium">{business.name}</span>
                    {business.description && (
                      <span className="text-xs text-muted-foreground">
                        {business.description}
                      </span>
                    )}
                  </div>
                  <DropdownMenuShortcut>⌘{index + 1}</DropdownMenuShortcut>
                </DropdownMenuItem>
              ))
            )}
            {(businesses.length > 0 || canCreateBusiness || canManageBusiness) && <DropdownMenuSeparator />}
            {canCreateBusiness && (
              <BusinessManagementSheet mode="create">
                <DropdownMenuItem 
                  className="gap-2 p-2"
                  onSelect={(e) => e.preventDefault()}
                >
                  <div className="flex size-6 items-center justify-center rounded-md border border-dashed">
                    <Plus className="size-4" />
                  </div>
                  <div className="font-medium text-muted-foreground">{t('switcher.addBusiness')}</div>
                </DropdownMenuItem>
              </BusinessManagementSheet>
            )}
            {canManageBusiness && businesses.length > 0 && (
              <BusinessManagementSheet mode="manage">
                <DropdownMenuItem 
                  className="gap-2 p-2"
                  onSelect={(e) => e.preventDefault()}
                >
                  <div className="flex size-6 items-center justify-center rounded-md border">
                    <img
                      src="/kwaci.webp"
                      alt="KWACI Logo"
                      className="size-4 object-contain"
                    />
                  </div>
                  <div className="font-medium text-muted-foreground">{t('switcher.manageBusinesses')}</div>
                </DropdownMenuItem>
              </BusinessManagementSheet>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
