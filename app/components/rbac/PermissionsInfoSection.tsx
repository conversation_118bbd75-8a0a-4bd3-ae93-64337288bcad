import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Separator } from '~/components/ui/separator';
import { Shield, Eye, Plus, Edit, Trash2, Users, Building } from 'lucide-react';

interface PermissionInfo {
  name: string;
  purpose: string;
  icon: React.ReactNode;
  category: 'primary' | 'action' | 'related';
}

interface PermissionsInfoSectionProps {
  routeName: string;
  permissions: PermissionInfo[];
}

export function PermissionsInfoSection({ routeName, permissions }: PermissionsInfoSectionProps) {
  const { t } = useTranslation();

  const primaryPermissions = permissions.filter(p => p.category === 'primary');
  const actionPermissions = permissions.filter(p => p.category === 'action');
  const relatedPermissions = permissions.filter(p => p.category === 'related');

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'primary':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'action':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'related':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const PermissionItem = ({ permission }: { permission: PermissionInfo }) => (
    <div className="flex items-start gap-3 p-3 rounded-lg border bg-card">
      <div className="flex-shrink-0 mt-0.5">
        {permission.icon}
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <code className="text-sm font-mono bg-muted px-2 py-1 rounded">
            {permission.name}
          </code>
          <Badge 
            variant="outline" 
            className={getCategoryColor(permission.category)}
          >
            {permission.category}
          </Badge>
        </div>
        <p className="text-sm text-muted-foreground">
          {permission.purpose}
        </p>
      </div>
    </div>
  );

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center gap-2">
          <Shield className="h-5 w-5 text-blue-600" />
          <CardTitle className="text-lg">
            {t('rbac.permissionsInfo.title', { route: routeName })}
          </CardTitle>
        </div>
        <CardDescription>
          {t('rbac.permissionsInfo.description')}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {primaryPermissions.length > 0 && (
          <div>
            <h4 className="font-medium text-sm text-muted-foreground mb-3 uppercase tracking-wide">
              {t('rbac.permissionsInfo.primaryAccess')}
            </h4>
            <div className="space-y-2">
              {primaryPermissions.map((permission, index) => (
                <PermissionItem key={index} permission={permission} />
              ))}
            </div>
          </div>
        )}

        {actionPermissions.length > 0 && (
          <>
            <Separator />
            <div>
              <h4 className="font-medium text-sm text-muted-foreground mb-3 uppercase tracking-wide">
                {t('rbac.permissionsInfo.actionPermissions')}
              </h4>
              <div className="space-y-2">
                {actionPermissions.map((permission, index) => (
                  <PermissionItem key={index} permission={permission} />
                ))}
              </div>
            </div>
          </>
        )}

        {relatedPermissions.length > 0 && (
          <>
            <Separator />
            <div>
              <h4 className="font-medium text-sm text-muted-foreground mb-3 uppercase tracking-wide">
                {t('rbac.permissionsInfo.relatedPermissions')}
              </h4>
              <div className="space-y-2">
                {relatedPermissions.map((permission, index) => (
                  <PermissionItem key={index} permission={permission} />
                ))}
              </div>
            </div>
          </>
        )}

        <Separator />
        <div className="text-xs text-muted-foreground">
          <p>
            {t('rbac.permissionsInfo.note')}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

// Helper function to create permission info with icons
export const createPermissionInfo = {
  primary: (name: string, purpose: string): PermissionInfo => ({
    name,
    purpose,
    icon: <Eye className="h-4 w-4 text-blue-600" />,
    category: 'primary'
  }),
  create: (name: string, purpose: string): PermissionInfo => ({
    name,
    purpose,
    icon: <Plus className="h-4 w-4 text-green-600" />,
    category: 'action'
  }),
  update: (name: string, purpose: string): PermissionInfo => ({
    name,
    purpose,
    icon: <Edit className="h-4 w-4 text-orange-600" />,
    category: 'action'
  }),
  delete: (name: string, purpose: string): PermissionInfo => ({
    name,
    purpose,
    icon: <Trash2 className="h-4 w-4 text-red-600" />,
    category: 'action'
  }),
  related: (name: string, purpose: string): PermissionInfo => ({
    name,
    purpose,
    icon: <Users className="h-4 w-4 text-purple-600" />,
    category: 'related'
  }),
  business: (name: string, purpose: string): PermissionInfo => ({
    name,
    purpose,
    icon: <Building className="h-4 w-4 text-indigo-600" />,
    category: 'related'
  })
};