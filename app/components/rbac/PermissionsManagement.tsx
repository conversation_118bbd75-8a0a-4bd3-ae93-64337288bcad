import React, { useState, useEffect } from 'react';
import { useFetcher } from '@remix-run/react';
import { But<PERSON> } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Textarea } from '~/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '~/components/ui/dialog';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '~/components/ui/card';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '~/components/ui/accordion';
import { Badge } from '~/components/ui/badge';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '~/components/ui/alert-dialog';
import { Plus, Edit, Trash2, RefreshCw, Database, Shield } from 'lucide-react';
import { toast } from 'sonner';

interface Permission {
  id: string;
  name: string;
  displayName: string;
  description: string;
  resource: string;
  action: string;
  isSystemPermission: boolean;
}

interface EndpointOperation {
  endpoint: string;
  resource: string;
  operations: {
    action: string;
    method: string;
    description: string;
  }[];
}

interface PermissionsData {
  permissions: Record<string, Permission[]>;
  endpoints: Record<string, EndpointOperation[]>;
}

interface PermissionsManagementProps {
  businessId: string;
}

export function PermissionsManagement({ businessId }: PermissionsManagementProps) {
  const [permissionsData, setPermissionsData] = useState<PermissionsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingPermission, setEditingPermission] = useState<Permission | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedResource, setSelectedResource] = useState<string>('');
  const [selectedAction, setSelectedAction] = useState<string>('');
  
  const fetcher = useFetcher();
  const seedFetcher = useFetcher();

  // Load permissions data
  const loadPermissions = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/rbac/permissions?businessId=${businessId}`);
      const result = await response.json();
      
      if (result.success) {
        setPermissionsData(result.data);
      } else {
        toast.error('Failed to load permissions');
      }
    } catch (error) {
      toast.error('Error loading permissions');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadPermissions();
  }, [businessId]);

  // Handle form submissions
  useEffect(() => {
    if (fetcher.data && typeof fetcher.data === 'object') {
      const data = fetcher.data as { success?: boolean; error?: string; message?: string };
      if (data.success) {
        toast.success(data.message || 'Operation successful');
        setIsCreateDialogOpen(false);
        setIsEditDialogOpen(false);
        setEditingPermission(null);
        setSelectedResource('');
        setSelectedAction('');
        loadPermissions();
      } else {
        toast.error(data.error || 'Operation failed');
      }
    }
  }, [fetcher.data]);

  useEffect(() => {
    if (seedFetcher.data && typeof seedFetcher.data === 'object') {
      const data = seedFetcher.data as { success?: boolean; error?: string; message?: string };
      if (data.success) {
        toast.success(data.message || 'Seeding successful');
        loadPermissions();
      } else {
        toast.error(data.error || 'Seeding failed');
      }
    }
  }, [seedFetcher.data]);

  const handleCreatePermission = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const formData = new FormData(event.currentTarget);
    
    // Handle custom resource and action
    const resource = selectedResource === 'custom' ? formData.get('customResource') as string : selectedResource;
    const action = selectedAction === 'custom_action' ? formData.get('customAction') as string : selectedAction;
    
    // Generate the permission name
    const permissionName = `${resource}.${action}`;
    
    // Set the final values
    formData.set('resource', resource);
    formData.set('action', action);
    formData.set('name', permissionName);
    
    formData.append('intent', 'create');
    formData.append('businessId', businessId);
    fetcher.submit(formData, { method: 'post', action: '/api/rbac/permissions' });
  };

  const handleUpdatePermission = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const formData = new FormData(event.currentTarget);
    formData.append('intent', 'update');
    formData.append('businessId', businessId);
    formData.append('permissionId', editingPermission!.id);
    fetcher.submit(formData, { method: 'post', action: '/api/rbac/permissions' });
  };

  const handleDeletePermission = (permission: Permission) => {
    const formData = new FormData();
    formData.append('intent', 'delete');
    formData.append('businessId', businessId);
    formData.append('permissionId', permission.id);
    fetcher.submit(formData, { method: 'post', action: '/api/rbac/permissions' });
  };

  const handleSeedEndpointPermissions = () => {
    const formData = new FormData();
    formData.append('intent', 'seed-endpoint-permissions');
    formData.append('businessId', businessId);
    seedFetcher.submit(formData, { method: 'post', action: '/api/rbac/permissions' });
  };

  const openEditDialog = (permission: Permission) => {
    setEditingPermission(permission);
    setIsEditDialogOpen(true);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin" />
        <span className="ml-2">Loading permissions...</span>
      </div>
    );
  }

  if (!permissionsData) {
    return (
      <div className="text-center p-8">
        <p className="text-muted-foreground">Failed to load permissions data.</p>
        <Button onClick={loadPermissions} className="mt-4">
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Permissions Management</h2>
          <p className="text-muted-foreground">
            Manage permissions for API endpoints and system operations
          </p>
        </div>
      </div>

      {/* Documentation Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            System Permissions Overview
          </CardTitle>
          <CardDescription>
            Understanding the different types of permissions in the system.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">System Permissions</h4>
              <p className="text-sm text-muted-foreground mb-3">
                System permissions are automatically generated from API endpoints and cannot be manually edited or deleted. 
                They follow a structured naming convention: <code className="bg-muted px-1 rounded">resource.action</code>
              </p>
              <div className="grid gap-3">
                <div className="border rounded-lg p-3">
                  <h5 className="font-medium text-sm mb-1">CRUD Operations</h5>
                  <p className="text-xs text-muted-foreground mb-2">Standard create, read, update, delete operations for resources</p>
                  <div className="flex flex-wrap gap-1">
                    <Badge variant="secondary" className="text-xs">resource.create</Badge>
                    <Badge variant="secondary" className="text-xs">resource.read</Badge>
                    <Badge variant="secondary" className="text-xs">resource.update</Badge>
                    <Badge variant="secondary" className="text-xs">resource.delete</Badge>
                  </div>
                </div>
                <div className="border rounded-lg p-3">
                  <h5 className="font-medium text-sm mb-1">List Operations</h5>
                  <p className="text-xs text-muted-foreground mb-2">Permissions for listing and querying collections of resources</p>
                  <div className="flex flex-wrap gap-1">
                    <Badge variant="secondary" className="text-xs">resource.list</Badge>
                    <Badge variant="secondary" className="text-xs">resource.search</Badge>
                  </div>
                </div>
                <div className="border rounded-lg p-3">
                  <h5 className="font-medium text-sm mb-1">Special Operations</h5>
                  <p className="text-xs text-muted-foreground mb-2">Resource-specific actions and administrative operations</p>
                  <div className="flex flex-wrap gap-1">
                    <Badge variant="secondary" className="text-xs">resource.manage</Badge>
                    <Badge variant="secondary" className="text-xs">resource.assign</Badge>
                    <Badge variant="secondary" className="text-xs">resource.revoke</Badge>
                    <Badge variant="secondary" className="text-xs">resource.export</Badge>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-2">Custom Permissions</h4>
              <p className="text-sm text-muted-foreground">
                Custom permissions can be created for specific business logic or operations not covered by standard API endpoints. 
                These can be edited and deleted as needed.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex gap-2">
        <Button
          onClick={handleSeedEndpointPermissions}
          variant="outline"
          disabled={seedFetcher.state === 'submitting'}
        >
          <Database className="h-4 w-4 mr-2" />
          {seedFetcher.state === 'submitting' ? 'Seeding...' : 'Seed Endpoint Permissions'}
        </Button>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Permission
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Permission</DialogTitle>
              <DialogDescription>
                Create a custom permission for specific operations.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleCreatePermission}>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Name
                  </Label>
                  <div className="col-span-3">
                    <Input
                      id="name"
                      value={selectedResource && selectedAction ? 
                        `${selectedResource === 'custom' ? '[custom_resource]' : selectedResource}.${selectedAction === 'custom_action' ? '[custom_action]' : selectedAction}` 
                        : ''}
                      placeholder="Will be auto-generated based on resource and action"
                      className="bg-muted"
                      disabled
                      readOnly
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Permission name will be automatically generated as: resource.action
                    </p>
                  </div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="displayName" className="text-right">
                    Display Name
                  </Label>
                  <Input
                    id="displayName"
                    name="displayName"
                    placeholder="e.g., Custom Action"
                    className="col-span-3"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="resource" className="text-right">
                    Resource
                  </Label>
                  <div className="col-span-3">
                    <Select
                      value={selectedResource}
                      onValueChange={(value) => {
                        setSelectedResource(value);
                        setSelectedAction(''); // Reset action when resource changes
                      }}
                      required
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a resource" />
                      </SelectTrigger>
                      <SelectContent>
                        {permissionsData && Object.keys(permissionsData.endpoints).map((resource) => (
                          <SelectItem key={resource} value={resource}>
                            {resource}
                          </SelectItem>
                        ))}
                        <SelectItem value="custom">
                          Custom Resource
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <input type="hidden" name="resource" value={selectedResource} />
                  </div>
                </div>
                {selectedResource === 'custom' && (
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="customResource" className="text-right">
                      Custom Resource
                    </Label>
                    <Input
                      id="customResource"
                      name="customResource"
                      placeholder="e.g., custom_resource"
                      className="col-span-3"
                      required
                    />
                  </div>
                )}
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="action" className="text-right">
                    Action
                  </Label>
                  <div className="col-span-3">
                    <Select
                      value={selectedAction}
                      onValueChange={setSelectedAction}
                      required
                      disabled={!selectedResource}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={selectedResource ? "Select an action" : "Select a resource first"} />
                      </SelectTrigger>
                      <SelectContent>
                        {selectedResource && selectedResource !== 'custom' && permissionsData?.endpoints[selectedResource] && (
                          // Get unique actions for the selected resource
                          Array.from(new Set(
                            permissionsData.endpoints[selectedResource]
                              .flatMap(endpoint => endpoint.operations.map(op => op.action))
                          )).map((action) => (
                            <SelectItem key={action} value={action}>
                              {action}
                            </SelectItem>
                          ))
                        )}
                        {selectedResource === 'custom' && [
                          'create', 'read', 'update', 'delete', 'list', 'manage', 'assign', 'revoke', 'export'
                        ].map((action) => (
                          <SelectItem key={action} value={action}>
                            {action}
                          </SelectItem>
                        ))}
                        <SelectItem value="custom_action">
                          Custom Action
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <input type="hidden" name="action" value={selectedAction} />
                  </div>
                </div>
                {selectedAction === 'custom_action' && (
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="customAction" className="text-right">
                      Custom Action
                    </Label>
                    <Input
                      id="customAction"
                      name="customAction"
                      placeholder="e.g., custom_action"
                      className="col-span-3"
                      required
                    />
                  </div>
                )}
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="description" className="text-right">
                    Description
                  </Label>
                  <Textarea
                    id="description"
                    name="description"
                    placeholder="Describe what this permission allows"
                    className="col-span-3"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button
                  type="submit"
                  disabled={fetcher.state === 'submitting'}
                >
                  {fetcher.state === 'submitting' ? 'Creating...' : 'Create Permission'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Endpoint Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            API Endpoints Overview
          </CardTitle>
          <CardDescription>
            Available API endpoints and their operations that can have permissions assigned.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Accordion className="w-full">
            {Object.entries(permissionsData.endpoints).map(([resource, endpoints]) => (
              <AccordionItem key={resource}>
                <AccordionTrigger className="text-left">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{resource}</Badge>
                    <span className="text-sm text-muted-foreground">
                      ({endpoints.length} endpoint{endpoints.length !== 1 ? 's' : ''})
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3">
                    {endpoints.map((endpoint, index) => (
                      <div key={index} className="border rounded-lg p-3">
                        <div className="font-mono text-sm font-medium mb-2">
                          {endpoint.endpoint}
                        </div>
                        <div className="grid gap-2">
                          {endpoint.operations.map((operation, opIndex) => (
                            <div key={opIndex} className="flex items-center gap-2 text-sm">
                              <Badge variant="secondary" className="text-xs">
                                {operation.method}
                              </Badge>
                              <span className="font-medium">{operation.action}</span>
                              <span className="text-muted-foreground">-</span>
                              <span className="text-muted-foreground">{operation.description}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </CardContent>
      </Card>

      {/* Permissions List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Current Permissions
          </CardTitle>
          <CardDescription>
            All available permissions grouped by resource.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Accordion className="w-full">
            {Object.entries(permissionsData.permissions).map(([resource, permissions]) => (
              <AccordionItem key={resource}>
                <AccordionTrigger className="text-left">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{resource}</Badge>
                    <span className="text-sm text-muted-foreground">
                      ({permissions.length} permission{permissions.length !== 1 ? 's' : ''})
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3">
                    {permissions.map((permission) => (
                      <div key={permission.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium">{permission.displayName}</h4>
                            {permission.isSystemPermission && (
                              <Badge variant="secondary">System</Badge>
                            )}
                          </div>
                          {!permission.isSystemPermission && (
                            <div className="flex gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => openEditDialog(permission)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button variant="outline" size="sm">
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>Delete Permission</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      Are you sure you want to delete the permission &quot;{permission.displayName}&quot;?
                                      This action cannot be undone.
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() => handleDeletePermission(permission)}
                                    >
                                      Delete
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </div>
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground mb-2">
                          <span className="font-mono">{permission.name}</span>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {permission.description}
                        </p>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </CardContent>
      </Card>

      {/* Edit Permission Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Permission</DialogTitle>
            <DialogDescription>
              Update the display name and description for this permission.
            </DialogDescription>
          </DialogHeader>
          {editingPermission && (
            <form onSubmit={handleUpdatePermission}>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="edit-name"
                    value={editingPermission.name}
                    className="col-span-3"
                    disabled
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-displayName" className="text-right">
                    Display Name
                  </Label>
                  <Input
                    id="edit-displayName"
                    name="displayName"
                    defaultValue={editingPermission.displayName}
                    className="col-span-3"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-description" className="text-right">
                    Description
                  </Label>
                  <Textarea
                    id="edit-description"
                    name="description"
                    defaultValue={editingPermission.description}
                    className="col-span-3"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button
                  type="submit"
                  disabled={fetcher.state === 'submitting'}
                >
                  {fetcher.state === 'submitting' ? 'Updating...' : 'Update Permission'}
                </Button>
              </DialogFooter>
            </form>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}