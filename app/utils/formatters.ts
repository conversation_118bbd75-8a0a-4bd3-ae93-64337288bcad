import { formatCurrency as formatCurrencyValue, DEFAULT_CURRENCY } from '~/lib/utils/currencyUtils'
import { useBusinessStore } from '~/lib/stores/businessStore'

export function formatCurrency(value: number, currency?: string, short: boolean = false): string {
  const currentCurrency = currency || useBusinessStore.getState().currentBusiness?.currency || DEFAULT_CURRENCY
  
  if (short) {
    // Short format for charts (K, M, B notation)
    const symbol = getCurrencySymbol(currentCurrency)
    if (value >= 1000000000) {
      return `${symbol}${(value / 1000000000).toFixed(1)}B`
    } else if (value >= 1000000) {
      return `${symbol}${(value / 1000000).toFixed(1)}M`
    } else if (value >= 1000) {
      return `${symbol}${(value / 1000).toFixed(1)}K`
    }
    return formatCurrencyValue(value, currentCurrency)
  }
  
  return formatCurrencyValue(value, currentCurrency)
}

export function formatNumber(value: number, locale: string = 'id-ID'): string {
  // Handle NaN, undefined, null, and invalid numbers
  if (typeof value !== 'number' || isNaN(value) || !isFinite(value)) {
    return new Intl.NumberFormat(locale).format(0)
  }

  return new Intl.NumberFormat(locale).format(value)
}

export function parseCurrency(value: string): number {
  return Number(value.replace(/[^\d.-]/g, ''))
}

// Helper function to get currency symbol
function getCurrencySymbol(currencyCode: string): string {
  const symbols: Record<string, string> = {
    'IDR': 'Rp',
    'USD': '$',
    'EUR': '€',
    'GBP': '£',
    'JPY': '¥',
    'SGD': 'S$',
    'MYR': 'RM',
    'THB': '฿',
    'PHP': '₱',
    'VND': '₫'
  }
  return symbols[currencyCode] || currencyCode
}
