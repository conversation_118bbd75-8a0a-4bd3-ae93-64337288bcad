import { json, type ActionFunctionArgs } from "@remix-run/node";
import { getSession } from "~/lib/auth.session.server";
import { CogsServiceServer } from "~/lib/services/cogsService.server";

export async function action({ request, params }: ActionFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const { id } = params;
  if (!id) {
    return json({ error: "Product ID is required" }, { status: 400 });
  }

  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    const body = await request.json();
    const { businessId, laborCosts, overheadCosts } = body;
    
    if (!businessId) {
      return json({ error: "Business ID is required" }, { status: 400 });
    }

    // Validate input
    const laborCostsNum = parseFloat(laborCosts || '0');
    const overheadCostsNum = parseFloat(overheadCosts || '0');
    
    if (isNaN(laborCostsNum) || laborCostsNum < 0) {
      return json({ error: "Labor costs must be a valid positive number" }, { status: 400 });
    }
    
    if (isNaN(overheadCostsNum) || overheadCostsNum < 0) {
      return json({ error: "Overhead costs must be a valid positive number" }, { status: 400 });
    }

    // Calculate COGS
    const calculation = await CogsServiceServer.calculateProductCogs(
      id,
      businessId,
      session.user.id,
      laborCostsNum,
      overheadCostsNum
    );

    return json({ calculation });
  } catch (error) {
    console.error("Failed to calculate COGS:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to calculate COGS";
    return json({ error: errorMessage }, { status: 500 });
  }
}
