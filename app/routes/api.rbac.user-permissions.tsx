import { json, type LoaderFunctionArgs } from '@remix-run/node'
import { RBACService } from '~/lib/services/rbacService.server'
import { requireAuth } from '~/lib/auth.session.server'

export async function loader({ request }: LoaderFunctionArgs) {
  await requireAuth(request)
  
  const url = new URL(request.url)
  const userId = url.searchParams.get('userId')
  const businessId = url.searchParams.get('businessId')

  if (!userId) {
    return json({ error: 'Missing userId parameter' }, { status: 400 })
  }

  try {
    const permissions = await RBACService.getUserPermissions(
      userId,
      businessId || undefined
    )
    return json(permissions)
  } catch (error) {
    console.error('Error getting user permissions:', error)
    return json({ error: 'Failed to get user permissions' }, { status: 500 })
  }
}