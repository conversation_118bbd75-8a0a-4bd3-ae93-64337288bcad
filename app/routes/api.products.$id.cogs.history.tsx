import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { getSession } from "~/lib/auth.session.server";
import { CogsServiceServer } from "~/lib/services/cogsService.server";

export async function loader({ request, params }: LoaderFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const { id } = params;
  if (!id) {
    return json({ error: "Product ID is required" }, { status: 400 });
  }

  const url = new URL(request.url);
  const businessId = url.searchParams.get("businessId");
  const limitParam = url.searchParams.get("limit");
  
  if (!businessId) {
    return json({ error: "Business ID is required" }, { status: 400 });
  }

  const limit = limitParam ? parseInt(limitParam, 10) : 10;
  if (isNaN(limit) || limit < 1 || limit > 100) {
    return json({ error: "Limit must be between 1 and 100" }, { status: 400 });
  }

  try {
    const history = await CogsServiceServer.getProductCogsHistory(
      id, 
      businessId, 
      session.user.id,
      limit
    );
    
    return json({ history });
  } catch (error) {
    console.error("Failed to get COGS history:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to get COGS history";
    return json({ error: errorMessage }, { status: 500 });
  }
}
