import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { getSession } from "~/lib/auth.session.server";
import { ProductServiceServer } from "~/lib/services/productService.server";

export async function loader({ request, params }: LoaderFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const { id } = params;
  if (!id) {
    return json({ error: "Product ID is required" }, { status: 400 });
  }

  const url = new URL(request.url);
  const businessId = url.searchParams.get("businessId");
  
  if (!businessId) {
    return json({ error: "Business ID is required" }, { status: 400 });
  }

  try {
    const product = await ProductServiceServer.getByIdAndBusiness(
      id, 
      businessId, 
      session.user.id
    );
    
    if (!product) {
      return json({ error: "Product not found" }, { status: 404 });
    }
    
    return json({ product });
  } catch (error) {
    console.error("Failed to load product:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to load product";
    return json({ error: errorMessage }, { status: 500 });
  }
}
