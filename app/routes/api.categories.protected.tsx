import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { getSession } from "~/lib/auth.session.server";
import { CategoryServiceServer } from "~/lib/services/categoryService.server";
import type { CategoryType } from "~/lib/types/inventory";
import { createProtectedRoute, RBAC_CONFIGS } from "~/lib/middleware/autoRBAC.server";

// Original loader function without RBAC checks
async function categoriesLoader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const url = new URL(request.url);
  const businessId = url.searchParams.get("businessId");
  const type = url.searchParams.get("type") as CategoryType;
  
  if (!businessId) {
    return json({ error: "Business ID is required" }, { status: 400 });
  }

  if (!type || !['ingredient', 'product', 'supplier', 'customer'].includes(type)) {
    return json({ error: "Valid category type is required" }, { status: 400 });
  }

  try {
    const categories = await CategoryServiceServer.getAllByBusinessAndType(
      businessId, 
      session.user.id, 
      type
    );
    
    return json({ categories });
  } catch (error) {
    console.error("Failed to load categories:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to load categories";
    return json({ error: errorMessage }, { status: 500 });
  }
}

// Original action function without RBAC checks
async function categoriesAction({ request }: ActionFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    const body = await request.json();
    const { businessId, name, description, type, parentId, color, sortOrder } = body;
    
    if (!businessId) {
      return json({ error: "Business ID is required" }, { status: 400 });
    }

    if (!name || name.trim().length === 0) {
      return json({ error: "Category name is required" }, { status: 400 });
    }

    if (!type || !['ingredient', 'product', 'supplier', 'customer'].includes(type)) {
      return json({ error: "Valid category type is required" }, { status: 400 });
    }

    // Validate name length
    if (name.length > 100) {
      return json({ error: "Category name must be less than 100 characters" }, { status: 400 });
    }

    // Validate description length
    if (description && description.length > 500) {
      return json({ error: "Description must be less than 500 characters" }, { status: 400 });
    }

    // Validate color format
    if (color && !/^#[0-9A-F]{6}$/i.test(color)) {
      return json({ error: "Color must be a valid hex color code" }, { status: 400 });
    }

    // Validate sort order
    if (sortOrder !== undefined && (isNaN(sortOrder) || sortOrder < 0)) {
      return json({ error: "Sort order must be a positive number" }, { status: 400 });
    }

    const category = await CategoryServiceServer.create(
      businessId,
      session.user.id,
      {
        name: name.trim(),
        description: description?.trim(),
        type,
        parentId,
        color,
        sortOrder,
      }
    );
    
    return json({ category });
  } catch (error) {
    console.error("Failed to create category:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to create category";
    return json({ error: errorMessage }, { status: 500 });
  }
}

// Create protected route with automatic RBAC
const { loader, action } = createProtectedRoute(
  RBAC_CONFIGS.categories,
  {
    loader: categoriesLoader,
    action: categoriesAction,
  }
);

export { loader, action };