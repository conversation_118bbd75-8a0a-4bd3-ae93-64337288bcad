import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { getSession } from "~/lib/auth.session.server";
import { CogsServiceServer } from "~/lib/services/cogsService.server";

export async function loader({ request, params }: LoaderFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const { id } = params;
  if (!id) {
    return json({ error: "Product ID is required" }, { status: 400 });
  }

  const url = new URL(request.url);
  const businessId = url.searchParams.get("businessId");
  
  if (!businessId) {
    return json({ error: "Business ID is required" }, { status: 400 });
  }

  try {
    const breakdown = await CogsServiceServer.getCogsBreakdown(
      id, 
      businessId, 
      session.user.id
    );
    
    return json({ breakdown });
  } catch (error) {
    console.error("Failed to get COGS breakdown:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to get COGS breakdown";
    return json({ error: errorMessage }, { status: 500 });
  }
}
