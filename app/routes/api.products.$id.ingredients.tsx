import { json, type ActionFunctionArgs } from "@remix-run/node";
import { getSession } from "~/lib/auth.session.server";
import { ProductServiceServer } from "~/lib/services/productService.server";

export async function action({ request, params }: ActionFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const { id: productId } = params;
  if (!productId) {
    return json({ error: "Product ID is required" }, { status: 400 });
  }

  const formData = await request.formData();
  const action = formData.get("_action") as string;

  if (!action) {
    return json({ error: "Action is required" }, { status: 400 });
  }

  try {
    switch (action) {
      case "add": {
        const businessId = formData.get("businessId") as string;
        const ingredientId = formData.get("ingredientId") as string;
        const quantityNeeded = formData.get("quantityNeeded") as string;
        
        if (!businessId || !ingredientId || !quantityNeeded) {
          return json({ 
            error: "Business ID, Ingredient ID, and Quantity are required" 
          }, { status: 400 });
        }

        const quantity = parseFloat(quantityNeeded);
        if (isNaN(quantity) || quantity <= 0) {
          return json({ error: "Quantity must be a positive number" }, { status: 400 });
        }

        const productIngredient = await ProductServiceServer.addIngredientToProduct(
          productId,
          ingredientId,
          quantity,
          businessId,
          session.user.id
        );
        
        return json({ productIngredient });
      }

      case "update": {
        const businessId = formData.get("businessId") as string;
        const ingredientId = formData.get("ingredientId") as string;
        const quantityNeeded = formData.get("quantityNeeded") as string;
        
        if (!businessId || !ingredientId || !quantityNeeded) {
          return json({ 
            error: "Business ID, Ingredient ID, and Quantity are required" 
          }, { status: 400 });
        }

        const quantity = parseFloat(quantityNeeded);
        if (isNaN(quantity) || quantity <= 0) {
          return json({ error: "Quantity must be a positive number" }, { status: 400 });
        }

        await ProductServiceServer.updateProductIngredient(
          productId,
          ingredientId,
          quantity,
          businessId,
          session.user.id
        );
        
        return json({ success: true });
      }

      case "remove": {
        const businessId = formData.get("businessId") as string;
        const ingredientId = formData.get("ingredientId") as string;
        
        if (!businessId || !ingredientId) {
          return json({ 
            error: "Business ID and Ingredient ID are required" 
          }, { status: 400 });
        }

        await ProductServiceServer.removeIngredientFromProduct(
          productId,
          ingredientId,
          businessId,
          session.user.id
        );
        
        return json({ success: true });
      }

      default:
        return json({ error: "Invalid action" }, { status: 400 });
    }
  } catch (error) {
    console.error("Product ingredient action failed:", error);
    const errorMessage = error instanceof Error ? error.message : "Operation failed";
    return json({ error: errorMessage }, { status: 500 });
  }
}
