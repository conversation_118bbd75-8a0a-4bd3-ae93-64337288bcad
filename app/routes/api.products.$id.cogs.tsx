import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { getSession } from "~/lib/auth.session.server";
import { CogsServiceServer } from "~/lib/services/cogsService.server";

export async function loader({ request, params }: LoaderFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const { id } = params;
  if (!id) {
    return json({ error: "Product ID is required" }, { status: 400 });
  }

  const url = new URL(request.url);
  const businessId = url.searchParams.get("businessId");
  
  if (!businessId) {
    return json({ error: "Business ID is required" }, { status: 400 });
  }

  try {
    const cogs = await CogsServiceServer.getProductCogs(
      id, 
      businessId, 
      session.user.id
    );
    
    if (!cogs) {
      return json({ error: "COGS not found" }, { status: 404 });
    }
    
    return json({ cogs });
  } catch (error) {
    console.error("Failed to load product COGS:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to load product COGS";
    return json({ error: errorMessage }, { status: 500 });
  }
}

export async function action({ request, params }: ActionFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const { id } = params;
  if (!id) {
    return json({ error: "Product ID is required" }, { status: 400 });
  }

  const method = request.method;

  try {
    if (method === "POST") {
      // Create or update COGS
      const body = await request.json();
      const { businessId, laborCosts, overheadCosts, calculationMethod } = body;
      
      if (!businessId) {
        return json({ error: "Business ID is required" }, { status: 400 });
      }

      // Validate input
      const laborCostsNum = parseFloat(laborCosts || '0');
      const overheadCostsNum = parseFloat(overheadCosts || '0');
      
      if (isNaN(laborCostsNum) || laborCostsNum < 0) {
        return json({ error: "Labor costs must be a valid positive number" }, { status: 400 });
      }
      
      if (isNaN(overheadCostsNum) || overheadCostsNum < 0) {
        return json({ error: "Overhead costs must be a valid positive number" }, { status: 400 });
      }

      // Calculate COGS
      const calculationResult = await CogsServiceServer.calculateProductCogs(
        id,
        businessId,
        session.user.id,
        laborCostsNum,
        overheadCostsNum
      );

      // Save COGS
      const cogs = await CogsServiceServer.saveProductCogs(
        id,
        businessId,
        session.user.id,
        calculationResult,
        calculationMethod || 'manual'
      );

      return json({ cogs, calculation: calculationResult });
    }

    if (method === "PUT") {
      // Update existing COGS
      const body = await request.json();
      const { businessId, laborCosts, overheadCosts, calculationMethod } = body;
      
      if (!businessId) {
        return json({ error: "Business ID is required" }, { status: 400 });
      }

      // Validate input
      const laborCostsNum = parseFloat(laborCosts || '0');
      const overheadCostsNum = parseFloat(overheadCosts || '0');
      
      if (isNaN(laborCostsNum) || laborCostsNum < 0) {
        return json({ error: "Labor costs must be a valid positive number" }, { status: 400 });
      }
      
      if (isNaN(overheadCostsNum) || overheadCostsNum < 0) {
        return json({ error: "Overhead costs must be a valid positive number" }, { status: 400 });
      }

      // Calculate updated COGS
      const calculationResult = await CogsServiceServer.calculateProductCogs(
        id,
        businessId,
        session.user.id,
        laborCostsNum,
        overheadCostsNum
      );

      // Update COGS
      const cogs = await CogsServiceServer.saveProductCogs(
        id,
        businessId,
        session.user.id,
        calculationResult,
        'manual_update'
      );

      return json({ cogs, calculation: calculationResult });
    }

    if (method === "DELETE") {
      // Delete COGS (set to inactive)
      const body = await request.json();
      const { businessId } = body;
      
      if (!businessId) {
        return json({ error: "Business ID is required" }, { status: 400 });
      }

      // Get existing COGS
      const existingCogs = await CogsServiceServer.getProductCogs(
        id,
        businessId,
        session.user.id
      );

      if (!existingCogs) {
        return json({ error: "COGS not found" }, { status: 404 });
      }

      // Set COGS to inactive (soft delete)
      await CogsServiceServer.saveProductCogs(
        id,
        businessId,
        session.user.id,
        {
          ingredientCosts: 0,
          laborCosts: 0,
          overheadCosts: 0,
          totalCogs: 0,
          calculationMethod: 'manual'
        },
        'deleted'
      );

      return json({ success: true });
    }

    return json({ error: "Method not allowed" }, { status: 405 });
  } catch (error) {
    console.error("COGS action failed:", error);
    const errorMessage = error instanceof Error ? error.message : "Operation failed";
    return json({ error: errorMessage }, { status: 500 });
  }
}
