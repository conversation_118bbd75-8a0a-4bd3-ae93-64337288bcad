import type { LoaderFunctionArgs, ActionFunctionArgs } from '@remix-run/node';
import { json, redirect } from '@remix-run/node';
import { useLoaderData, useActionData, Form } from '@remix-run/react';
import { useState } from 'react';
import { InvitationService } from '~/lib/services/invitationService.server';
import { getSession } from '~/lib/auth.session.server';
import { authClient } from '~/lib/auth.client';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { Badge } from '~/components/ui/badge';
import { CheckCircle, Clock, Building2, Shield } from 'lucide-react';

export async function loader({ params }: LoaderFunctionArgs) {
  const token = params.token!;
  
  try {
    const invitation = await InvitationService.getInvitationByToken(token);
    
    if (!invitation) {
      throw new Error('Invitation not found');
    }
    
    return json({ invitation });
  } catch (error: any) {
    return json({ error: error.message }, { status: 404 });
  }
}

export async function action({ request, params }: ActionFunctionArgs) {
  const token = params.token!;
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: 'You must be logged in to accept an invitation' }, { status: 401 });
  }
  
  try {
    await InvitationService.acceptInvitation(token, session.user.id);
    return redirect('/dashboard?invitation=accepted');
  } catch (error: any) {
    return json({ error: error.message }, { status: 400 });
  }
}

export default function InvitationPage() {
  const data = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const [isAccepting, setIsAccepting] = useState(false);
  
  if ('error' in data) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="text-red-600">Invalid Invitation</CardTitle>
            <CardDescription>
              This invitation link is not valid or has expired.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-sm text-muted-foreground mb-4">
              {data.error}
            </p>
            <Button asChild>
              <a href="/">Go to Homepage</a>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  const { invitation } = data;
  const isExpired = new Date() > new Date(invitation.expiresAt);
  const isValid = invitation.status === 'pending' && !isExpired;
  
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-lg">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {isValid ? (
              <CheckCircle className="h-12 w-12 text-green-600" />
            ) : (
              <Clock className="h-12 w-12 text-orange-600" />
            )}
          </div>
          <CardTitle>
            {isValid ? 'Business Invitation' : 'Invitation Expired'}
          </CardTitle>
          <CardDescription>
            {isValid 
              ? `You've been invited to join ${invitation.businessName}`
              : 'This invitation is no longer valid'
            }
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Invitation Details */}
          <div className="space-y-4">
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <Building2 className="h-5 w-5 text-blue-600" />
              <div>
                <p className="font-medium">{invitation.businessName}</p>
                <p className="text-sm text-muted-foreground">Business</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <Shield className="h-5 w-5 text-purple-600" />
              <div>
                <p className="font-medium">{invitation.roleDisplayName}</p>
                <p className="text-sm text-muted-foreground">Role</p>
              </div>
            </div>
            
            <div className="text-center">
              <Badge variant={isValid ? 'default' : 'destructive'}>
                {invitation.status} {isExpired && '(Expired)'}
              </Badge>
              <p className="text-sm text-muted-foreground mt-2">
                Expires: {new Date(invitation.expiresAt).toLocaleDateString()}
              </p>
            </div>
          </div>
          
          {/* Action feedback */}
          {actionData && 'error' in actionData && (
            <Alert variant="destructive">
              <AlertDescription>{actionData.error}</AlertDescription>
            </Alert>
          )}
          
          {/* Actions */}
          <div className="space-y-3">
            {isValid ? (
              <Form method="post" onSubmit={() => setIsAccepting(true)}>
                <Button 
                  type="submit" 
                  className="w-full" 
                  disabled={isAccepting}
                >
                  {isAccepting ? 'Accepting...' : 'Accept Invitation'}
                </Button>
              </Form>
            ) : (
              <Button asChild className="w-full" variant="outline">
                <a href="/">Go to Homepage</a>
              </Button>
            )}
            
            <Button asChild variant="ghost" className="w-full">
              <a href="/login">Login with Different Account</a>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
