import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { getSession } from "~/lib/auth.session.server";
import { BusinessServiceServer } from "~/lib/services/businessService.server";
import { RBACService } from "~/lib/services/rbacService.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const businesses = await BusinessServiceServer.getAllByUser(session.user.id);
    return json({ businesses });
  } catch (error) {
    console.error("Failed to load businesses:", error);
    return json({ error: "Failed to load businesses" }, { status: 500 });
  }
}

export async function action({ request }: ActionFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const formData = await request.formData();
  const action = formData.get("_action") as string;

  try {
    switch (action) {
      case "create": {
        const data = {
          name: formData.get("name") as string,
          description: formData.get("description") as string || undefined,
          note: formData.get("note") as string || undefined,
          currency: formData.get("currency") as string,
          logo: formData.get("logo") as string || undefined,
        };
        
        const business = await BusinessServiceServer.create(session.user.id, data);
        
        // Automatically assign business_owner role to the creator
        await RBACService.assignBusinessOwnerRole(session.user.id, business.id);
        
        return json({ business });
      }

      case "update": {
        const id = formData.get("id") as string;
        const data = {
          name: formData.get("name") as string,
          description: formData.get("description") as string || undefined,
          note: formData.get("note") as string || undefined,
          currency: formData.get("currency") as string,
          logo: formData.get("logo") as string || undefined,
        };
        
        await BusinessServiceServer.update(id, session.user.id, data);
        return json({ success: true });
      }

      case "delete": {
        const id = formData.get("id") as string;
        await BusinessServiceServer.delete(id, session.user.id);
        return json({ success: true });
      }

      default:
        return json({ error: "Invalid action" }, { status: 400 });
    }
  } catch (error) {
    console.error("Business action failed:", error);
    return json({ error: "Operation failed" }, { status: 500 });
  }
}
