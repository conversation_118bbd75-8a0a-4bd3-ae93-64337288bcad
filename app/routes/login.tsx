import { redirect } from "@remix-run/node";
import type { LoaderFunctionArgs } from "@remix-run/node";
import { getSession } from "~/lib/auth.session.server";

/**
 * Login route that redirects to the home page.
 * The authentication system uses modals, so this route just ensures
 * users are redirected to the main app where they can authenticate.
 */
export async function loader({ request }: LoaderFunctionArgs) {
  // Check if user is already authenticated
  const session = await getSession(request);
  
  if (session?.user?.id) {
    // User is already authenticated, redirect to home
    return redirect("/");
  }
  
  // User is not authenticated, redirect to home where they can use the auth modal
  return redirect("/");
}

// This component should never render since we always redirect
export default function LoginPage() {
  return null;
}