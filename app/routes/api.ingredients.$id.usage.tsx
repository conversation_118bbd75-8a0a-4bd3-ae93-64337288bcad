import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { getSession } from "~/lib/auth.session.server";
import { IngredientServiceServer } from "~/lib/services/ingredientService.server";
import { createProtectedLoader, RBAC_CONFIGS } from "~/lib/middleware/autoRBAC.server";

async function ingredientUsageLoader({ request, params }: LoaderFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const { id } = params;
  if (!id) {
    return json({ error: "Ingredient ID is required" }, { status: 400 });
  }

  const url = new URL(request.url);
  const businessId = url.searchParams.get("businessId");
  
  if (!businessId) {
    return json({ error: "Business ID is required" }, { status: 400 });
  }

  try {
    const ingredientUsage = await IngredientServiceServer.getWithUsage(
      id, 
      businessId
    );
    
    if (!ingredientUsage) {
      return json({ error: "Ingredient not found" }, { status: 404 });
    }
    
    return json(ingredientUsage);
  } catch (error) {
    console.error("Failed to load ingredient usage:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to load ingredient usage";
    return json({ error: errorMessage }, { status: 500 });
  }
}

export const loader = createProtectedLoader(RBAC_CONFIGS.ingredients, ingredientUsageLoader);
