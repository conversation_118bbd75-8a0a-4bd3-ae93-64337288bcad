import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { getSession } from "~/lib/auth.session.server";
import { CategoryServiceServer } from "~/lib/services/categoryService.server";
import type { CategoryType } from "~/lib/types/inventory";

export async function loader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const url = new URL(request.url);
  const businessId = url.searchParams.get("businessId");
  const type = url.searchParams.get("type") as CategoryType;
  
  if (!businessId) {
    return json({ error: "Business ID is required" }, { status: 400 });
  }

  if (!type || !['ingredient', 'product', 'supplier', 'customer'].includes(type)) {
    return json({ error: "Valid category type is required" }, { status: 400 });
  }

  try {
    const categories = await CategoryServiceServer.getHierarchicalCategories(
      businessId, 
      session.user.id, 
      type
    );
    
    return json({ categories });
  } catch (error) {
    console.error("Failed to load hierarchical categories:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to load hierarchical categories";
    return json({ error: errorMessage }, { status: 500 });
  }
}
