import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { getSession } from "~/lib/auth.session.server";
import { ProductServiceServer } from "~/lib/services/productService.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const url = new URL(request.url);
  const businessId = url.searchParams.get("businessId");
  
  if (!businessId) {
    return json({ error: "Business ID is required" }, { status: 400 });
  }

  try {
    const count = await ProductServiceServer.countByBusiness(
      businessId, 
      session.user.id
    );
    
    return json({ count });
  } catch (error) {
    console.error("Failed to get product count:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to get product count";
    return json({ error: errorMessage }, { status: 500 });
  }
}
