import type { LoaderFunctionArgs } from '@remix-run/node';
import { json } from '@remix-run/node';
import { Outlet, useParams } from '@remix-run/react';
import { Card, CardContent } from '~/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Shield, Settings } from 'lucide-react';
import { Link } from '@remix-run/react';
import { requireBusinessAccess } from '~/lib/middleware/rbac.server';

export async function loader({ request, params }: LoaderFunctionArgs) {
  const businessId = params.businessId!;
  
  // Require business access for settings
  await requireBusinessAccess(request, businessId);
  
  return json({ businessId });
}

export default function SettingsLayout() {
  const { businessId } = useParams();
  
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Settings className="h-8 w-8" />
            Business Settings
          </h1>
          <p className="text-muted-foreground">
            Manage your business configuration and permissions
          </p>
        </div>
      </div>
      
      <Tabs defaultValue="rbac" className="space-y-6">
        <TabsList>
          <TabsTrigger value="rbac" asChild>
            <Link to={`/business/${businessId}/settings/rbac`} className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Role Management
            </Link>
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="rbac">
          <Card>
            <CardContent className="p-0">
              <Outlet />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}