import { redirect, type MetaFunction, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useRevalidator, useSearchParams } from "@remix-run/react";
import { useState, useCallback, useMemo, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Plus, Search, Filter, X, Package, Shield } from "lucide-react";

import { useRBAC } from "~/lib/hooks/useRBAC";
import { useRouteProtection } from "~/lib/hooks/useRouteProtection";

import { getSession } from "~/lib/auth.session.server";
import { IngredientServiceServer } from "~/lib/services/ingredientService.server";
import { BusinessServiceServer } from "~/lib/services/businessService.server";
import { CategoryServiceServer } from "~/lib/services/categoryService.server";
import { createProtectedLoader, RBAC_CONFIGS } from "~/lib/middleware/autoRBAC.server";
import { withRB<PERSON> } from "~/lib/utils/rbacLoader.server";
import { json } from "@remix-run/node";
import { IngredientManagementSheet } from "~/components/inventory/IngredientManagementSheet";
import { IngredientDetailSheet } from "~/components/inventory/IngredientDetailSheet";
import { PermissionsSheet } from "~/components/inventory/PermissionsSheet";
import { getPermissionConfig } from "~/lib/utils/permissionConfigs";
import type { IngredientWithCategory, Category, Ingredient, CategoryWithChildren } from "~/lib/types/inventory";

import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";

export const meta: MetaFunction = () => {
  return [
    { title: "Ingredients - KWACI Grow" },
    { name: "description", content: "Manage your business ingredients" },
  ];
};

interface LoaderData {
  ingredients: IngredientWithCategory[];
  businessId: string;
  businessName: string;
  categories: CategoryWithChildren[];
}

async function getBusinessId(args: LoaderFunctionArgs): Promise<string> {
  const session = await getSession(args.request);

  if (!session?.user) {
    throw new Response("Unauthorized", { status: 401 });
  }

  // First, try to get business ID from query parameters (client-side business switcher)
  const url = new URL(args.request.url);
  const businessIdFromQuery = url.searchParams.get('businessId');

  if (businessIdFromQuery) {
    // Verify user has access to this business
    const business = await BusinessServiceServer.getByIdAndUser(businessIdFromQuery, session.user.id);
    if (business) {
      return business.id;
    }
  }

  // Fallback to user's default business
  const business = await BusinessServiceServer.getDefaultByUser(session.user.id);

  if (!business) {
    throw new Response("No business found. Please create a business first.", { status: 404 });
  }

  return business.id;
}

// Create protected loader that enforces ingredients.read permission
export const loader = createProtectedLoader(
  {
    ...RBAC_CONFIGS.ingredients,
    getBusinessId,
  },
  async ({ request }) => {
    const session = await getSession(request);
    
    if (!session?.user) {
      throw new Response("Unauthorized", { status: 401 });
    }

    try {
      // Get business ID using the same logic as the RBAC middleware
      const businessId = await getBusinessId({ request });

      // Get business details for display
      const business = await BusinessServiceServer.getByIdAndUser(businessId, session.user.id);

      if (!business) {
        throw new Response("Business not found or access denied.", { status: 404 });
      }

      // Get ingredients for the business with category names
      const ingredients = await IngredientServiceServer.getAllByBusinessWithCategories(
        businessId
      );

      // Try to get hierarchical categories for ingredients
      // If user doesn't have categories.read permission, return empty array
      let categories: CategoryWithChildren[] = [];
      try {
        categories = await CategoryServiceServer.getHierarchicalCategories(
          businessId,
          session.user.id,
          'ingredient'
        );
      } catch (categoryError: unknown) {
         // Check if it's a permission error
         if (categoryError instanceof Error && (categoryError.message?.includes('Access denied') || categoryError.message?.includes('insufficient permissions'))) {
           categories = [];
         } else {
           // Re-throw other errors
           throw categoryError;
         }
       }

      const data = {
        ingredients,
        businessId: businessId,
        businessName: business.name,
        categories,
      };

      // Add RBAC data to the response
      const rbac = await withRBAC(request, businessId);
      
      return json({ ...data, rbac });
    } catch (error) {
      // Check if it's a permission error and redirect to unauthorized page
      if (error instanceof Error && (error.message?.includes('Access denied') || error.message?.includes('insufficient permissions'))) {
        throw redirect('/unauthorized');
      }
      
      throw new Response("Failed to load ingredients", { status: 500 });
    }
  }
);

export default function IngredientsPage() {
  const loaderData = useLoaderData<LoaderData>();
  const { ingredients = [], businessId = '', businessName = '', categories: allCategories = [] } = loaderData || {};
  const { t } = useTranslation('inventory');
  const { t: tCommon } = useTranslation('common');
  const revalidator = useRevalidator();
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchTerm, setSearchTerm] = useState("");
  const { ingredients: ingredientsPermissions, categories: categoriesPermissions, hasPermission } = useRBAC();

  // Client-side route protection for navigation
  useRouteProtection({
    permissions: ['ingredients.read'],
    preserveUrl: true
  });

  // Filter state
  const [showFilters, setShowFilters] = useState(false);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>("");
  const [categories, setCategories] = useState<Category[]>([]);
  const [categoriesLoading, setCategoriesLoading] = useState(false);

  // Ref to track if an edit operation just completed to prevent unwanted drawer opening
  const editOperationJustCompleted = useRef(false);
  // Ref to track if any sheet/modal interaction is happening
  const sheetInteractionInProgress = useRef(false);
  
  // Edit state
  const [editingIngredient, setEditingIngredient] = useState<Ingredient | null>(null);
  
  // Permissions sheet state
  const [showPermissionsSheet, setShowPermissionsSheet] = useState(false);

  // Fetch categories for filtering
  useEffect(() => {
    const fetchCategories = async () => {
      if (!businessId) return;

      setCategoriesLoading(true);
      try {
        const response = await fetch(`/api/ingredient-categories?businessId=${businessId}`);
        if (response.ok) {
          const data = await response.json();
          setCategories(data.categories || []);
        } else {
          console.error('Failed to fetch categories');
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      } finally {
        setCategoriesLoading(false);
      }
    };

    fetchCategories();
  }, [businessId]);

  // Get the ingredient ID from URL params for sheet state
  const detailIngredientId = searchParams.get('detail');
  const selectedIngredient = useMemo(() =>
    ingredients?.find(ing => ing.id === detailIngredientId) || null,
    [ingredients, detailIngredientId]
  );

  // Set up mutation observer to detect sheet state changes
  useEffect(() => {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'data-state') {
          const target = mutation.target as Element;
          const dataState = target.getAttribute('data-state');

          if (dataState === 'open') {
            sheetInteractionInProgress.current = true;
          } else if (dataState === 'closed') {
            sheetInteractionInProgress.current = true;
            // Clear the flag after a delay to prevent drawer opening immediately after sheet closes
            setTimeout(() => {
              sheetInteractionInProgress.current = false;
            }, 500);
          }
        }
      });
    });

    // Observe the document for sheet state changes
    observer.observe(document.body, {
      attributes: true,
      subtree: true,
      attributeFilter: ['data-state']
    });

    return () => {
      observer.disconnect();
    };
  }, []);

  // Sheet state management - memoized to prevent unnecessary re-renders
  const openIngredientDetail = useCallback((ingredientId: string) => {
    // Don't open sheet if an edit operation just completed or sheet interaction is in progress
    if (editOperationJustCompleted.current || sheetInteractionInProgress.current) {
      editOperationJustCompleted.current = false;
      sheetInteractionInProgress.current = false;
      return;
    }

    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('detail', ingredientId);
    setSearchParams(newSearchParams);
  }, [searchParams, setSearchParams]);

  const closeIngredientDetail = useCallback(() => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.delete('detail');
    setSearchParams(newSearchParams);
  }, [searchParams, setSearchParams]);

  // Stable ingredient change handler that doesn't interfere with drawer state
  const handleIngredientChange = useCallback(() => {
    // Mark that an edit operation just completed
    editOperationJustCompleted.current = true;

    // Clear the flag after a short delay to allow for any pending events
    setTimeout(() => {
      editOperationJustCompleted.current = false;
    }, 500);

    // Revalidate the data
    revalidator.revalidate();
  }, [revalidator]);

  const handleEditIngredient = useCallback((ingredient: Ingredient) => {
    closeIngredientDetail();
    setEditingIngredient(ingredient);
  }, [closeIngredientDetail]);
  
  const handleEditComplete = useCallback(() => {
    setEditingIngredient(null);
    handleIngredientChange();
  }, [handleIngredientChange]);

  const handleDeleteIngredient = useCallback(async (ingredient: Ingredient) => {
    if (!ingredientsPermissions.canDelete) {
      alert(t('common.noPermission'));
      return;
    }
    
    if (confirm(t('ingredients.delete.confirm'))) {
      try {
        const formData = new FormData();
        formData.append('_action', 'delete');
        formData.append('id', ingredient.id);
        formData.append('businessId', businessId);

        const response = await fetch(`/api/ingredients`, {
          method: 'POST',
          body: formData,
        });

        if (response.ok) {
          closeIngredientDetail();
          revalidator.revalidate();
        } else {
          console.error('Failed to delete ingredient');
        }
      } catch (error) {
        console.error('Error deleting ingredient:', error);
      }
    }
  }, [t, businessId, closeIngredientDetail, revalidator, ingredientsPermissions.canDelete]);

  // Filter ingredients based on search term and category - memoized to prevent unnecessary re-calculations
  const filteredIngredients = useMemo(() => {
    if (!ingredients || !Array.isArray(ingredients)) return [];
    return ingredients.filter(ingredient => {
      // Search filter
      const matchesSearch = !searchTerm ||
        ingredient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (ingredient.notes && ingredient.notes.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (ingredient.categoryName && ingredient.categoryName.toLowerCase().includes(searchTerm.toLowerCase()));

      // Category filter
      const matchesCategory = !selectedCategoryId || ingredient.categoryId === selectedCategoryId;

      return matchesSearch && matchesCategory;
    });
  }, [ingredients, searchTerm, selectedCategoryId]);

  // Filter management functions
  const toggleFilters = useCallback(() => {
    setShowFilters(prev => !prev);
  }, []);

  const clearFilters = useCallback(() => {
    setSelectedCategoryId("");
    setSearchTerm("");
  }, []);

  const handleCategoryFilterChange = useCallback((categoryId: string) => {
    setSelectedCategoryId(categoryId);
  }, []);

  // Check if any filters are active
  const hasActiveFilters = useMemo(() => {
    return selectedCategoryId !== "" || searchTerm !== "";
  }, [selectedCategoryId, searchTerm]);

  // Get selected category name for display
  const selectedCategoryName = useMemo(() => {
    if (!selectedCategoryId) return null;
    const category = categories.find(cat => cat.id === selectedCategoryId);
    return category?.name || null;
  }, [selectedCategoryId, categories]);

  const formatCurrency = useCallback((amount: string | number) => {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount;
    if (isNaN(num)) return 'Rp 0';
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(num);
  }, []);

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('ingredients.title')}</h1>
          <p className="text-muted-foreground">
            {t('ingredients.subtitle', { businessName })}
          </p>
        </div>
        <div className="flex gap-2">
          {ingredientsPermissions.canCreate && (
            <IngredientManagementSheet
              mode="create"
              businessId={businessId}
              onIngredientChange={handleIngredientChange}
              allCategories={allCategories}
            >
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                {t('ingredients.addNew')}
              </Button>
            </IngredientManagementSheet>
          )}
          {hasPermission('system.view_permissions') && (
              <Button
                variant="outline"
                onClick={() => setShowPermissionsSheet(true)}
              >
                <Shield className="mr-2 h-4 w-4" />
                {t('permissions.title', 'Permissions')}
              </Button>
            )}
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">{t('common.searchAndFilter')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Search and Filter Toggle Row */}
            <div className="flex flex-col gap-4 md:flex-row">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={t('ingredients.searchPlaceholder')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2">
                <Button
                  variant={showFilters ? "default" : "outline"}
                  onClick={toggleFilters}
                >
                  <Filter className="mr-2 h-4 w-4" />
                  {t('common.filters')}
                  {hasActiveFilters && (
                    <Badge variant="secondary" className="ml-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs">
                      {(selectedCategoryId ? 1 : 0)}
                    </Badge>
                  )}
                </Button>
                {hasActiveFilters && (
                  <Button variant="ghost" size="sm" onClick={clearFilters}>
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>

            {/* Filter Controls */}
            {showFilters && (
              <div className="border-t pt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {/* Category Filter */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      {t('ingredients.fields.category')}
                    </label>
                    <Select
                      value={selectedCategoryId || "all"}
                      onValueChange={(value) => handleCategoryFilterChange(value === "all" ? "" : value)}
                      disabled={categoriesLoading}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue>
                          {categoriesLoading ? (
                            <span className="text-muted-foreground">Loading categories...</span>
                          ) : selectedCategoryName ? (
                            <div className="flex items-center gap-2">
                              {(() => {
                                const selectedCategory = categories.find(cat => cat.id === selectedCategoryId);
                                return selectedCategory?.color ? (
                                  <div
                                    className="w-3 h-3 rounded-full flex-shrink-0"
                                    style={{ backgroundColor: selectedCategory.color }}
                                  />
                                ) : null;
                              })()}
                              <span>{selectedCategoryName}</span>
                            </div>
                          ) : (
                            <span className="text-muted-foreground">All Categories</span>
                          )}
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        {/* All Categories option */}
                        <SelectItem value="all">
                          <span className="text-muted-foreground">All Categories</span>
                        </SelectItem>

                        {/* Category options */}
                        {categories.map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            <div className="flex items-center gap-2">
                              {category.color && (
                                <div
                                  className="w-3 h-3 rounded-full flex-shrink-0"
                                  style={{ backgroundColor: category.color }}
                                />
                              )}
                              <span>{category.name}</span>
                              {category.description && (
                                <span className="text-xs text-muted-foreground ml-2">
                                  {category.description}
                                </span>
                              )}
                            </div>
                          </SelectItem>
                        ))}

                        {/* Empty state */}
                        {categories.length === 0 && !categoriesLoading && (
                          <SelectItem value="empty" disabled>
                            <span className="text-muted-foreground">No categories available</span>
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Active Filters Display */}
                {hasActiveFilters && (
                  <div className="mt-4 pt-4 border-t">
                    <div className="flex items-center gap-2 flex-wrap">
                      <span className="text-sm text-muted-foreground">Active filters:</span>
                      {selectedCategoryName && (
                        <Badge variant="secondary" className="flex items-center gap-1">
                          Category: {selectedCategoryName}
                          <button
                            onClick={() => setSelectedCategoryId("")}
                            className="ml-1 hover:bg-muted rounded-full p-0.5"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </Badge>
                      )}
                      {searchTerm && (
                        <Badge variant="secondary" className="flex items-center gap-1">
                          Search: &quot;{searchTerm}&quot;
                          <button
                            onClick={() => setSearchTerm("")}
                            className="ml-1 hover:bg-muted rounded-full p-0.5"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </Badge>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>



      {/* Ingredients Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t('ingredients.list')}</CardTitle>
          <CardDescription>
            {t('ingredients.listDescription', { count: filteredIngredients.length })}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredIngredients.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">
                {hasActiveFilters ? t('ingredients.noSearchResults') : t('ingredients.noIngredients')}
              </p>
              {hasActiveFilters && (
                <Button variant="outline" onClick={clearFilters} className="mb-4">
                  <X className="mr-2 h-4 w-4" />
                  Clear Filters
                </Button>
              )}
              {!hasActiveFilters && ingredientsPermissions.canCreate && (
                <IngredientManagementSheet
                  mode="create"
                  businessId={businessId}
                  onIngredientChange={handleIngredientChange}
                  allCategories={allCategories}
                >
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    {t('ingredients.addFirst')}
                  </Button>
                </IngredientManagementSheet>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('ingredients.fields.name')}</TableHead>
                  <TableHead>{t('ingredients.fields.category')}</TableHead>
                  <TableHead>{t('ingredients.fields.usage')}</TableHead>
                  <TableHead>{t('ingredients.fields.notes')}</TableHead>
                  <TableHead>{t('ingredients.fields.unit')}</TableHead>
                  <TableHead>{t('ingredients.fields.baseUnitCost')}</TableHead>
                  <TableHead>{t('ingredients.fields.supplierInfo')}</TableHead>
                  <TableHead className="text-right">{t('common.actions')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredIngredients.map((ingredient) => (
                  <TableRow
                    key={ingredient.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={(e) => {
                      // Prevent opening sheet if clicking on interactive elements or during sheet interactions
                      if (e.target instanceof Element &&
                          (e.target.closest('button') ||
                           e.target.closest('[role="button"]') ||
                           e.target.closest('input') ||
                           e.target.closest('textarea') ||
                           e.target.closest('[data-radix-collection-item]') ||
                           e.target.closest('[data-state]') || // Radix UI elements
                           e.target.closest('[data-radix-popper-content-wrapper]') ||
                           e.target.closest('.sheet-content') ||
                           e.target.closest('[role="dialog"]'))) {
                        return;
                      }

                      // Additional check for sheet interactions
                      if (sheetInteractionInProgress.current || editOperationJustCompleted.current) {
                        return;
                      }

                      openIngredientDetail(ingredient.id);
                    }}
                  >
                    <TableCell className="font-medium">{ingredient.name}</TableCell>
                    <TableCell>
                      {categoriesPermissions.canRead && ingredient.categoryName ? (
                        <div className="flex items-center gap-2">
                          {ingredient.categoryColor && (
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: ingredient.categoryColor }}
                            />
                          )}
                          <Badge variant="outline">{ingredient.categoryName}</Badge>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        {/* Usage count */}
                        <div className="flex items-center gap-1 text-sm">
                          <Package className="h-3 w-3 text-muted-foreground" />
                          <span className="text-muted-foreground">
                            {ingredient.usageCount === 0
                              ? 'Not used'
                              : `Used in ${ingredient.usageCount} product${ingredient.usageCount === 1 ? '' : 's'}`
                            }
                          </span>
                        </div>
                        {/* Status badge */}
                        <Badge
                          variant={ingredient.isActive ? "default" : "secondary"}
                          className={ingredient.isActive ? "bg-green-100 text-green-800 hover:bg-green-100" : ""}
                        >
                          {ingredient.isActive ? tCommon('status.active') : tCommon('status.inactive')}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell className="text-muted-foreground">
                      {ingredient.notes || '-'}
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">{ingredient.unit}</Badge>
                    </TableCell>
                    <TableCell>{formatCurrency(ingredient.baseUnitCost)}</TableCell>
                    <TableCell className="text-muted-foreground">
                      {ingredient.supplierInfo || '-'}
                    </TableCell>
                    <TableCell className="text-right">
                      {ingredientsPermissions.canUpdate && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            setEditingIngredient(ingredient);
                          }}
                        >
                          {t('common.edit')}
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Ingredient Detail Sheet */}
      <IngredientDetailSheet
        ingredient={selectedIngredient}
        businessId={businessId}
        isOpen={!!detailIngredientId}
        onClose={closeIngredientDetail}
        onEdit={handleEditIngredient}
        onDelete={handleDeleteIngredient}
      />
      
      {/* Edit Ingredient Sheet */}
      <IngredientManagementSheet
        mode="edit"
        businessId={businessId}
        onIngredientChange={handleIngredientChange}
        editingIngredient={editingIngredient || undefined}
        onEditComplete={handleEditComplete}
        isOpen={!!editingIngredient}
        onClose={() => setEditingIngredient(null)}
        allCategories={allCategories}
      >
        <div />
      </IngredientManagementSheet>
      
      {/* Permissions Sheet */}
      <PermissionsSheet
        open={showPermissionsSheet}
        onOpenChange={setShowPermissionsSheet}
        routeName={getPermissionConfig('ingredients').routeName}
        title={t('permissions.title', 'Permissions Information')}
        description={t('permissions.description', 'View all permissions used in the ingredients module and their purposes.')}
        permissions={getPermissionConfig('ingredients').permissions}
      />
    </div>
  );
}
