import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { getSession } from "~/lib/auth.session.server";
import { IngredientServiceServer } from "~/lib/services/ingredientService.server";
import { createProtectedRoute, RBAC_CONFIGS } from "~/lib/middleware/autoRBAC.server";
import type { IngredientFilters } from "~/lib/types/inventory";

// Original loader function without RBAC checks
async function ingredientsLoader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const url = new URL(request.url);
  const businessId = url.searchParams.get("businessId");
  
  if (!businessId) {
    return json({ error: "Business ID is required" }, { status: 400 });
  }

  try {
    // Parse filters from query parameters
    const filters: IngredientFilters = {};
    
    const search = url.searchParams.get("search");
    if (search) filters.search = search;

    const unit = url.searchParams.get("unit");
    if (unit) filters.unit = unit;

    const minCost = url.searchParams.get("minCost");
    if (minCost) filters.minCost = parseFloat(minCost);

    const maxCost = url.searchParams.get("maxCost");
    if (maxCost) filters.maxCost = parseFloat(maxCost);

    const isActive = url.searchParams.get("isActive");
    if (isActive) filters.isActive = isActive === 'true';

    const ingredients = await IngredientServiceServer.getAllByBusinessWithCategories(
      businessId,
      filters
    );
    
    return json({ ingredients });
  } catch (error) {
    console.error("Failed to load ingredients:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to load ingredients";
    return json({ error: errorMessage }, { status: 500 });
  }
}

// Original action function without RBAC checks
async function ingredientsAction({ request }: ActionFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const formData = await request.formData();
  const action = formData.get("_action") as string;

  try {
    switch (action) {
      case "create": {
        const businessId = formData.get("businessId") as string;
        
        if (!businessId) {
          return json({ error: "Business ID is required" }, { status: 400 });
        }

        const data = {
          name: formData.get("name") as string,
          baseUnitCost: formData.get("baseUnitCost") as string,
          baseUnitQuantity: formData.get("baseUnitQuantity") as string,
          unit: formData.get("unit") as string,
          categoryId: formData.get("categoryId") as string || undefined,
          supplierInfo: formData.get("supplierInfo") as string || undefined,
          notes: formData.get("notes") as string || undefined,
          isActive: formData.get("isActive") === 'true',
        };

        // Validate required fields
        if (!data.name || !data.unit || !data.baseUnitCost || !data.baseUnitQuantity) {
          return json({ error: "Name, unit, base unit cost, and base unit quantity are required" }, { status: 400 });
        }

        // Validate base unit cost is a valid number
        const baseUnitCost = parseFloat(data.baseUnitCost);
        if (isNaN(baseUnitCost) || baseUnitCost < 0) {
          return json({ error: "Base unit cost must be a valid positive number" }, { status: 400 });
        }

        // Validate base unit quantity is a valid number
        const baseUnitQuantity = parseFloat(data.baseUnitQuantity);
        if (isNaN(baseUnitQuantity) || baseUnitQuantity <= 0) {
          return json({ error: "Base unit quantity must be a valid positive number" }, { status: 400 });
        }

        // Check if ingredient name already exists in business
        const nameExists = await IngredientServiceServer.nameExistsInBusiness(
          data.name, 
          businessId
        );
        
        if (nameExists) {
          return json({ error: "An ingredient with this name already exists in this business" }, { status: 400 });
        }

        const ingredient = await IngredientServiceServer.create(
          businessId,
          {
            ...data,
            baseUnitCost: baseUnitCost.toString(),
            baseUnitQuantity: baseUnitQuantity.toString(),
          }
        );
        
        return json({ ingredient });
      }

      case "update": {
        const id = formData.get("id") as string;
        const businessId = formData.get("businessId") as string;
        
        if (!id || !businessId) {
          return json({ error: "Ingredient ID and Business ID are required" }, { status: 400 });
        }

        const data = {
          name: formData.get("name") as string,
          baseUnitCost: formData.get("baseUnitCost") as string,
          baseUnitQuantity: formData.get("baseUnitQuantity") as string,
          unit: formData.get("unit") as string,
          categoryId: formData.get("categoryId") as string || undefined,
          supplierInfo: formData.get("supplierInfo") as string || undefined,
          notes: formData.get("notes") as string || undefined,
          isActive: formData.get("isActive") === 'true',
        };

        // Validate required fields
        if (!data.name || !data.unit || !data.baseUnitCost || !data.baseUnitQuantity) {
          return json({ error: "Name, unit, base unit cost, and base unit quantity are required" }, { status: 400 });
        }

        // Validate base unit cost is a valid number
        const baseUnitCost = parseFloat(data.baseUnitCost);
        if (isNaN(baseUnitCost) || baseUnitCost < 0) {
          return json({ error: "Base unit cost must be a valid positive number" }, { status: 400 });
        }

        // Validate base unit quantity is a valid number
        const baseUnitQuantity = parseFloat(data.baseUnitQuantity);
        if (isNaN(baseUnitQuantity) || baseUnitQuantity <= 0) {
          return json({ error: "Base unit quantity must be a valid positive number" }, { status: 400 });
        }

        // Check if ingredient name already exists in business (excluding current ingredient)
        const nameExists = await IngredientServiceServer.nameExistsInBusiness(
          data.name, 
          businessId,
          id
        );
        
        if (nameExists) {
          return json({ error: "An ingredient with this name already exists in this business" }, { status: 400 });
        }

        await IngredientServiceServer.update(
          id,
          businessId,
          {
            ...data,
            baseUnitCost: baseUnitCost.toString(),
            baseUnitQuantity: baseUnitQuantity.toString(),
          }
        );
        
        return json({ success: true });
      }

      case "delete": {
        const id = formData.get("id") as string;
        const businessId = formData.get("businessId") as string;
        
        if (!id || !businessId) {
          return json({ error: "Ingredient ID and Business ID are required" }, { status: 400 });
        }

        await IngredientServiceServer.delete(id, businessId);
        return json({ success: true });
      }

      default:
        return json({ error: "Invalid action" }, { status: 400 });
    }
  } catch (error) {
    console.error("Ingredient action failed:", error);
    const errorMessage = error instanceof Error ? error.message : "Operation failed";
    return json({ error: errorMessage }, { status: 500 });
  }
}

// Create protected route with automatic RBAC
const { loader, action } = createProtectedRoute(
  RBAC_CONFIGS.ingredients,
  {
    loader: ingredientsLoader,
    action: ingredientsAction,
  }
);

export { loader, action };
