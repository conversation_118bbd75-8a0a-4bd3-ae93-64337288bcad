import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { getSession } from "~/lib/auth.session.server";
import { CategoryServiceServer } from "~/lib/services/categoryService.server";

export async function loader({ request, params }: LoaderFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const { id } = params;
  if (!id) {
    return json({ error: "Category ID is required" }, { status: 400 });
  }

  const url = new URL(request.url);
  const businessId = url.searchParams.get("businessId");
  
  if (!businessId) {
    return json({ error: "Business ID is required" }, { status: 400 });
  }

  try {
    const category = await CategoryServiceServer.getById(
      id, 
      businessId, 
      session.user.id
    );
    
    if (!category) {
      return json({ error: "Category not found" }, { status: 404 });
    }
    
    return json({ category });
  } catch (error) {
    console.error("Failed to load category:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to load category";
    return json({ error: errorMessage }, { status: 500 });
  }
}

export async function action({ request, params }: ActionFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const { id } = params;
  if (!id) {
    return json({ error: "Category ID is required" }, { status: 400 });
  }

  const method = request.method;

  try {
    if (method === "PUT") {
      // Update category
      const body = await request.json();
      const { businessId, name, description, parentId, color, sortOrder, isActive } = body;
      
      if (!businessId) {
        return json({ error: "Business ID is required" }, { status: 400 });
      }

      // Validate inputs
      if (name !== undefined && (typeof name !== 'string' || name.trim().length === 0)) {
        return json({ error: "Category name must be a non-empty string" }, { status: 400 });
      }

      if (name && name.length > 100) {
        return json({ error: "Category name must be less than 100 characters" }, { status: 400 });
      }

      if (description && description.length > 500) {
        return json({ error: "Description must be less than 500 characters" }, { status: 400 });
      }

      if (color && !/^#[0-9A-F]{6}$/i.test(color)) {
        return json({ error: "Color must be a valid hex color code" }, { status: 400 });
      }

      if (sortOrder !== undefined && (isNaN(sortOrder) || sortOrder < 0)) {
        return json({ error: "Sort order must be a positive number" }, { status: 400 });
      }

      const updateData: any = {};
      if (name !== undefined) updateData.name = name.trim();
      if (description !== undefined) updateData.description = description?.trim();
      if (parentId !== undefined) updateData.parentId = parentId;
      if (color !== undefined) updateData.color = color;
      if (sortOrder !== undefined) updateData.sortOrder = sortOrder;
      if (isActive !== undefined) updateData.isActive = isActive;

      const category = await CategoryServiceServer.update(
        id,
        businessId,
        session.user.id,
        updateData
      );

      return json({ category });
    }

    if (method === "DELETE") {
      // Delete category
      const body = await request.json();
      const { businessId } = body;
      
      if (!businessId) {
        return json({ error: "Business ID is required" }, { status: 400 });
      }

      await CategoryServiceServer.delete(
        id,
        businessId,
        session.user.id
      );

      return json({ success: true });
    }

    return json({ error: "Method not allowed" }, { status: 405 });
  } catch (error) {
    console.error("Category action failed:", error);
    const errorMessage = error instanceof Error ? error.message : "Operation failed";
    return json({ error: errorMessage }, { status: 500 });
  }
}
