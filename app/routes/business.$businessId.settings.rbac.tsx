/* eslint-disable @typescript-eslint/no-explicit-any */
import type { ActionFunctionArgs, LoaderFunctionArgs } from '@remix-run/node';
import { json } from '@remix-run/node';
import { useLoaderData, useActionData, Form, useNavigation } from '@remix-run/react';
import { useState, useMemo } from 'react';
import { requireBusinessOwner } from '~/lib/middleware/rbac.server';
import { withRBAC } from '~/lib/utils/rbacLoader.server';
import { RBACService } from '~/lib/services/rbacService.server';
import { InvitationService } from '~/lib/services/invitationService.server';
import { getSession } from '~/lib/auth.session.server';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Trash2, UserPlus, Shield, Users, Building2 } from 'lucide-react';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { Separator } from '~/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table';
import { PermissionsManagement } from '~/components/rbac/PermissionsManagement';

export async function loader({ request, params }: LoaderFunctionArgs) {
  const businessId = params.businessId!;

  // Require business owner access for current business
  await requireBusinessOwner(request, businessId);

  // Get current user session
  const session = await getSession(request);
  const currentUserId = session.user!.id;

  // Get RBAC data for the current user
  const rbac = await withRBAC(request, businessId);

  // Get all businesses the current user has access to
  const accessibleBusinesses = await RBACService.getUserBusinesses(currentUserId);

  // Get users for all accessible businesses (for cross-business role assignment)
  const allBusinessUsers = new Map<string, any[]>();
  for (const business of accessibleBusinesses) {
    try {
      // Check if user has business owner or role management permissions for this business
      const canManageRoles = await RBACService.hasAnyPermission(
        currentUserId,
        ['business.manage_users', 'users.assign_roles'],
        business.id
      ) || await RBACService.hasRole(currentUserId, 'business_owner', business.id);

      if (canManageRoles) {
        const users = await RBACService.getBusinessUsers(business.id);
        allBusinessUsers.set(business.id, users);
      }
    } catch (error) {
      // If user doesn't have access to this business, skip it
      console.warn(`User ${currentUserId} cannot access business ${business.id}:`, error);
    }
  }

  // Get users for current business (fallback)
  const businessUsers = allBusinessUsers.get(businessId) || await RBACService.getBusinessUsers(businessId);

  // Get all available roles and permissions
  const allRoles = await RBACService.getAllRoles();
  const allPermissions = await RBACService.getAllPermissions();

  // Get pending invitations for current business
  const pendingInvitations = await InvitationService.getBusinessInvitations(businessId);

  return json({
    rbac,
    businessUsers,
    allRoles,
    allPermissions,
    businessId,
    accessibleBusinesses,
    allBusinessUsers: Object.fromEntries(allBusinessUsers),
    currentUserId,
    pendingInvitations,
  });
}

export async function action({ request, params }: ActionFunctionArgs) {
  const businessId = params.businessId!;

  // Get current user session
  const session = await getSession(request);
  const currentUserId = session.user!.id;

  const formData = await request.formData();
  const intent = formData.get('intent') as string;

  try {
    switch (intent) {
      case 'assign_role': {
        const userId = formData.get('userId') as string;
        const roleName = formData.get('roleName') as string;
        const targetBusinessId = (formData.get('targetBusinessId') as string) || businessId;

        if (!userId || !roleName) {
          return json({ error: 'User ID and role name are required' }, { status: 400 });
        }

        // Validate that current user has permission to assign roles in target business
        try {
          const canManageRoles = await RBACService.hasAnyPermission(
            currentUserId,
            ['business.manage_users', 'users.assign_roles'],
            targetBusinessId
          ) || await RBACService.hasRole(currentUserId, 'business_owner', targetBusinessId);

          if (!canManageRoles) {
            return json({
              error: 'You do not have permission to assign roles in the selected business'
            }, { status: 403 });
          }
        } catch (error) {
          return json({
            error: 'You do not have access to the selected business'
          }, { status: 403 });
        }

        // Get business name for success message
        const accessibleBusinesses = await RBACService.getUserBusinesses(currentUserId);
        const targetBusiness = accessibleBusinesses.find(b => b.id === targetBusinessId);
        const businessName = targetBusiness?.name || 'selected business';

        await RBACService.assignRole(userId, roleName, currentUserId, targetBusinessId);

        if (targetBusinessId === businessId) {
          return json({ success: `Role '${roleName}' assigned successfully` });
        } else {
          return json({ success: `Role '${roleName}' assigned successfully in ${businessName}` });
        }
      }
      
      case 'remove_role': {
        const userId = formData.get('userId') as string;
        const roleName = formData.get('roleName') as string;
        const targetBusinessId = (formData.get('targetBusinessId') as string) || businessId;

        if (!userId || !roleName) {
          return json({ error: 'User ID and role name are required' }, { status: 400 });
        }

        // Validate that current user has permission to remove roles in target business
        try {
          const canManageRoles = await RBACService.hasAnyPermission(
            currentUserId,
            ['business.manage_users', 'users.assign_roles'],
            targetBusinessId
          ) || await RBACService.hasRole(currentUserId, 'business_owner', targetBusinessId);

          if (!canManageRoles) {
            return json({
              error: 'You do not have permission to remove roles in the selected business'
            }, { status: 403 });
          }
        } catch (error) {
          return json({
            error: 'You do not have access to the selected business'
          }, { status: 403 });
        }

        // Prevent removing business owner role from self in any business
        if (userId === currentUserId && roleName === 'business_owner') {
          return json({ error: 'You cannot remove your own business owner role' }, { status: 400 });
        }

        const success = await RBACService.removeRole(userId, roleName, targetBusinessId);
        if (success) {
          return json({ success: `Role '${roleName}' removed successfully` });
        } else {
          return json({ error: 'Failed to remove role' }, { status: 400 });
        }
      }
      
      case 'invite_user': {
        const email = formData.get('email') as string;
        const roleName = formData.get('roleName') as string;
        const targetBusinessId = (formData.get('targetBusinessId') as string) || businessId;

        if (!email || !roleName) {
          return json({ error: 'Email and role are required' }, { status: 400 });
        }

        // Validate that current user has permission to invite users to target business
        try {
          const canManageRoles = await RBACService.hasAnyPermission(
            currentUserId,
            ['business.manage_users', 'users.assign_roles'],
            targetBusinessId
          ) || await RBACService.hasRole(currentUserId, 'business_owner', targetBusinessId);

          if (!canManageRoles) {
            return json({
              error: 'You do not have permission to invite users to the selected business'
            }, { status: 403 });
          }
        } catch (error) {
          return json({
            error: 'You do not have access to the selected business'
          }, { status: 403 });
        }

        try {
          const invitationToken = await InvitationService.createInvitation({
            email,
            businessId: targetBusinessId,
            roleName,
            invitedBy: currentUserId,
          });

          // Get business name for success message
          const accessibleBusinesses = await RBACService.getUserBusinesses(currentUserId);
          const targetBusiness = accessibleBusinesses.find(b => b.id === targetBusinessId);
          const businessName = targetBusiness?.name || 'selected business';

          // TODO: Send invitation email with token
          // For now, just return success with token (for development)
          return json({
            success: `Invitation sent to ${email} for ${businessName}`,
            invitationToken // Remove this in production
          });
        } catch (error: any) {
          return json({ error: error.message }, { status: 400 });
        }
      }

      case 'cancel_invitation': {
        const invitationId = formData.get('invitationId') as string;

        if (!invitationId) {
          return json({ error: 'Invitation ID is required' }, { status: 400 });
        }

        try {
          await InvitationService.cancelInvitation(invitationId);
          return json({ success: 'Invitation cancelled successfully' });
        } catch (error: any) {
          return json({ error: error.message }, { status: 400 });
        }
      }

      case 'create_role': {
        const name = formData.get('name') as string;
        const description = formData.get('description') as string;
        const permissionIds = formData.getAll('permissionIds') as string[];
        
        if (!name) {
          return json({ error: 'Role name is required' }, { status: 400 });
        }
        
        await RBACService.createRole(
          name,
          name, // displayName
          description || '',
          permissionIds
        );
        
        return json({ success: `Role '${name}' created successfully` });
      }
      
      case 'update_role': {
        const roleId = formData.get('roleId') as string;
        const name = formData.get('name') as string;
        const description = formData.get('description') as string;
        const permissionIds = formData.getAll('permissionIds') as string[];
        
        if (!roleId || !name) {
          return json({ error: 'Role ID and name are required' }, { status: 400 });
        }
        
        await RBACService.updateRole(roleId, {
          name,
          description: description || '',
        });
        
        await RBACService.updateRolePermissions(roleId, permissionIds);
        
        return json({ success: `Role '${name}' updated successfully` });
      }
      
      case 'delete_role': {
        const roleId = formData.get('roleId') as string;
        const roleName = formData.get('roleName') as string;
        
        if (!roleId) {
          return json({ error: 'Role ID is required' }, { status: 400 });
        }
        
        await RBACService.deleteRole(roleId);
        
        return json({ success: `Role '${roleName}' deleted successfully` });
      }
      
      default:
        return json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('RBAC action error:', error);
    return json({ error: 'An error occurred while processing your request' }, { status: 500 });
  }
}

function RBACManagementPage() {
  const {
    rbac: rbacData,
    businessUsers,
    allRoles,
    allPermissions,
    businessId,
    accessibleBusinesses,
    allBusinessUsers,
    currentUserId,
    pendingInvitations
  } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const [selectedUser, setSelectedUser] = useState<string>('');
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [targetBusinessId, setTargetBusinessId] = useState<string>(businessId);
  const [inviteEmail, setInviteEmail] = useState<string>('');
  const [inviteRole, setInviteRole] = useState<string>('');
  const [inviteTargetBusinessId, setInviteTargetBusinessId] = useState<string>(businessId);

  const isSubmitting = navigation.state === 'submitting';

  // Show all roles (both system and custom) as requested by the user
  const assignableRoles = allRoles.filter((role: any) => role.isActive);

  // Get users for the selected target business
  const targetBusinessUsers = useMemo(() => {
    return allBusinessUsers[targetBusinessId] || [];
  }, [allBusinessUsers, targetBusinessId]);

  // Get target business name for display
  const targetBusiness = useMemo(() => {
    return accessibleBusinesses.find((b: any) => b.id === targetBusinessId);
  }, [accessibleBusinesses, targetBusinessId]);

  // Reset selected user when target business changes
  const handleTargetBusinessChange = (newBusinessId: string) => {
    setTargetBusinessId(newBusinessId);
    setSelectedUser(''); // Reset user selection when business changes
  };
  
  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight flex items-center gap-2">
            <Shield className="h-6 w-6" />
            Role Management
          </h2>
          <p className="text-muted-foreground">
            Manage user roles and permissions across your accessible businesses
          </p>
          {accessibleBusinesses.length > 1 && (
            <div className="flex items-center gap-2 mt-2">
              <Badge variant="secondary" className="flex items-center gap-1">
                <Building2 className="h-3 w-3" />
                {accessibleBusinesses.length} businesses accessible
              </Badge>
            </div>
          )}
        </div>
      </div>
      
      {/* Action feedback */}
      {actionData && 'error' in actionData && actionData.error && (
        <Alert variant="destructive">
          <AlertDescription>{actionData.error}</AlertDescription>
        </Alert>
      )}
      
      {actionData && 'success' in actionData && actionData.success && (
        <Alert>
          <AlertDescription>{actionData.success}</AlertDescription>
        </Alert>
      )}
      
      {actionData && 'info' in actionData && actionData.info && (
        <Alert>
          <AlertDescription>{actionData.info}</AlertDescription>
        </Alert>
      )}
      
      <Tabs defaultValue="users" className="space-y-6">
        <TabsList>
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Users & Roles
          </TabsTrigger>
          <TabsTrigger value="roles" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Role Details
          </TabsTrigger>
          <TabsTrigger value="manage-roles" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Manage Roles
          </TabsTrigger>
          <TabsTrigger value="permissions" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Permissions Management
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="users" className="space-y-6">
          {/* User Management Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserPlus className="h-5 w-5" />
                Assign Roles
              </CardTitle>
              <CardDescription>
                Assign roles to users across your accessible businesses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form method="post" className="space-y-4">
                <input type="hidden" name="intent" value="assign_role" />
                <input type="hidden" name="targetBusinessId" value={targetBusinessId} />

                {/* Business Selector */}
                {accessibleBusinesses.length > 1 && (
                  <div className="space-y-2">
                    <Label htmlFor="targetBusiness">Target Business</Label>
                    <Select value={targetBusinessId} onValueChange={handleTargetBusinessChange}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose target business" />
                      </SelectTrigger>
                      <SelectContent>
                        {accessibleBusinesses.map((business: any) => (
                          <SelectItem key={business.id} value={business.id}>
                            {business.name} {business.id === businessId && '(Current)'}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {targetBusiness && targetBusinessId !== businessId && (
                      <p className="text-sm text-muted-foreground">
                        Assigning role in: <strong>{targetBusiness.name}</strong>
                      </p>
                    )}
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="userId">Select User</Label>
                    <Select name="userId" value={selectedUser} onValueChange={setSelectedUser}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a user" />
                      </SelectTrigger>
                      <SelectContent>
                        {targetBusinessUsers.map((user: any) => (
                          <SelectItem key={user.id} value={user.id}>
                            {user.name} ({user.email})
                            {targetBusinessId !== businessId && (
                              <span className="text-muted-foreground"> - {targetBusiness?.name}</span>
                            )}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {targetBusinessUsers.length === 0 && (
                      <p className="text-sm text-muted-foreground">
                        No users found in selected business or you don't have permission to manage users there.
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="roleName">Select Role</Label>
                    <Select name="roleName" value={selectedRole} onValueChange={setSelectedRole}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a role" />
                      </SelectTrigger>
                      <SelectContent>
                        {assignableRoles.map((role: any) => (
                          <SelectItem key={role.id} value={role.name}>
                            {role.displayName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-end">
                    <Button
                      type="submit"
                      disabled={!selectedUser || !selectedRole || isSubmitting || targetBusinessUsers.length === 0}
                      className="w-full"
                    >
                      {isSubmitting ? 'Assigning...' : 'Assign Role'}
                    </Button>
                  </div>
                </div>
              </Form>
            </CardContent>
          </Card>
          
          {/* Current Users Table */}
          <Card>
            <CardHeader>
              <CardTitle>Current Users</CardTitle>
              <CardDescription>
                Users currently assigned to your business and their roles
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Roles</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {businessUsers.map((user: any) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">{user.name}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {user.roles.map((userRole: any) => (
                            <Badge key={`${user.id}-${userRole.role.name}`} variant="secondary">
                              {userRole.role.displayName}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          {user.roles.map((userRole: any) => (
                            <Form key={`${user.id}-${userRole.role.name}`} method="post" className="inline">
                              <input type="hidden" name="intent" value="remove_role" />
                              <input type="hidden" name="userId" value={user.id} />
                              <input type="hidden" name="roleName" value={userRole.role.name} />
                              <Button
                                type="submit"
                                variant="outline"
                                size="sm"
                                disabled={isSubmitting || (user.id === rbacData?.userId && userRole.role.name === 'business_owner')}
                                className="h-6 px-2"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </Form>
                          ))}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
          
          {/* Invite New User Section */}
          <Card>
            <CardHeader>
              <CardTitle>Invite New User</CardTitle>
              <CardDescription>
                Send an invitation to a new user to join your accessible businesses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form method="post" className="space-y-4">
                <input type="hidden" name="intent" value="invite_user" />
                <input type="hidden" name="targetBusinessId" value={inviteTargetBusinessId} />

                {/* Business Selector for Invitations */}
                {accessibleBusinesses.length > 1 && (
                  <div className="space-y-2">
                    <Label htmlFor="inviteTargetBusiness">Target Business</Label>
                    <Select value={inviteTargetBusinessId} onValueChange={setInviteTargetBusinessId}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose target business" />
                      </SelectTrigger>
                      <SelectContent>
                        {accessibleBusinesses.map((business: any) => (
                          <SelectItem key={business.id} value={business.id}>
                            {business.name} {business.id === businessId && '(Current)'}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      type="email"
                      name="email"
                      value={inviteEmail}
                      onChange={(e) => setInviteEmail(e.target.value)}
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="roleName">Initial Role</Label>
                    <Select name="roleName" value={inviteRole} onValueChange={setInviteRole}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a role" />
                      </SelectTrigger>
                      <SelectContent>
                        {assignableRoles.map((role: any) => (
                          <SelectItem key={role.id} value={role.name}>
                            {role.displayName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-end">
                    <Button
                      type="submit"
                      disabled={!inviteEmail || !inviteRole || isSubmitting}
                      className="w-full"
                    >
                      {isSubmitting ? 'Sending...' : 'Send Invitation'}
                    </Button>
                  </div>
                </div>
              </Form>
            </CardContent>
          </Card>

          {/* Pending Invitations Section */}
          {pendingInvitations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Pending Invitations</CardTitle>
                <CardDescription>
                  Users who have been invited but haven&apos;t accepted yet
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Email</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Business</TableHead>
                      <TableHead>Invited By</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Expires</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {pendingInvitations.map((invitation: any) => (
                      <TableRow key={invitation.id}>
                        <TableCell>{invitation.email}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{invitation.roleDisplayName}</Badge>
                        </TableCell>
                        <TableCell>{invitation.businessName}</TableCell>
                        <TableCell>{invitation.invitedByName}</TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              invitation.status === 'pending' ? 'default' :
                              invitation.status === 'accepted' ? 'secondary' :
                              invitation.status === 'expired' ? 'destructive' : 'outline'
                            }
                          >
                            {invitation.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {new Date(invitation.expiresAt).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          {invitation.status === 'pending' && (
                            <Form method="post" className="inline">
                              <input type="hidden" name="intent" value="cancel_invitation" />
                              <input type="hidden" name="invitationId" value={invitation.id} />
                              <Button
                                type="submit"
                                variant="outline"
                                size="sm"
                                disabled={isSubmitting}
                              >
                                Cancel
                              </Button>
                            </Form>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}
        </TabsContent>
        
        <TabsContent value="roles" className="space-y-6">
          {/* Role Details */}
          <div className="grid gap-6">
            {allRoles.map((role: any) => (
              <Card key={role.id}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>{role.displayName}</span>
                    <Badge variant={role.isSystemRole ? "default" : "secondary"}>
                      {role.isSystemRole ? 'System Role' : 'Custom Role'}
                    </Badge>
                  </CardTitle>
                  {role.description && (
                    <CardDescription>{role.description}</CardDescription>
                  )}
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">Permissions</h4>
                      <div className="flex flex-wrap gap-2">
                        {role.permissions.map((permission: any) => (
                          <Badge key={permission.id} variant="outline">
                            {permission.displayName}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div>
                      <h4 className="font-medium mb-2">Users with this role</h4>
                      <div className="space-y-1">
                        {businessUsers
                          .filter((user: any) => user.roles.some((userRole: any) => userRole.role.name === role.name))
                          .map((user: any) => (
                            <div key={user.id} className="text-sm text-muted-foreground">
                              {user.name} ({user.email})
                            </div>
                          ))
                        }
                        {businessUsers.filter((user: any) => user.roles.some((userRole: any) => userRole.role.name === role.name)).length === 0 && (
                          <div className="text-sm text-muted-foreground italic">
                            No users assigned to this role
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="manage-roles" className="space-y-6">
          {/* Create New Role */}
          <Card>
            <CardHeader>
              <CardTitle>Create New Role</CardTitle>
              <CardDescription>
                Create a custom role with specific permissions for your business
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form method="post" className="space-y-4">
                <input type="hidden" name="intent" value="create_role" />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Role Name</Label>
                    <Input
                      type="text"
                      name="name"
                      placeholder="e.g., Custom Manager"
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Input
                      type="text"
                      name="description"
                      placeholder="Brief description of the role"
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label>Permissions</Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-48 overflow-y-auto border rounded-md p-3">
                    {allPermissions.map((permission: any) => (
                      <div key={permission.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          name="permissionIds"
                          value={permission.id}
                          id={`create-${permission.id}`}
                          className="rounded"
                        />
                        <Label htmlFor={`create-${permission.id}`} className="text-sm">
                          {permission.displayName}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
                
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? 'Creating...' : 'Create Role'}
                </Button>
              </Form>
            </CardContent>
          </Card>
          
          {/* Manage Existing Roles */}
          <Card>
            <CardHeader>
              <CardTitle>Manage Existing Roles</CardTitle>
              <CardDescription>
                Edit or delete custom roles (system roles cannot be modified)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {allRoles.filter((role: any) => !role.isSystemRole).map((role: any) => (
                  <div key={role.id} className="border rounded-lg p-4 space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">{role.displayName}</h4>
                        {role.description && (
                          <p className="text-sm text-muted-foreground">{role.description}</p>
                        )}
                      </div>
                      <Form method="post" className="inline">
                        <input type="hidden" name="intent" value="delete_role" />
                        <input type="hidden" name="roleId" value={role.id} />
                        <input type="hidden" name="roleName" value={role.name} />
                        <Button
                          type="submit"
                          variant="destructive"
                          size="sm"
                          disabled={isSubmitting}
                          onClick={(e) => {
                            if (!confirm(`Are you sure you want to delete the role "${role.displayName}"? This will remove it from all users.`)) {
                              e.preventDefault();
                            }
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </Form>
                    </div>
                    
                    {/* Edit Role Form */}
                    <Form method="post" className="space-y-4">
                      <input type="hidden" name="intent" value="update_role" />
                      <input type="hidden" name="roleId" value={role.id} />
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor={`name-${role.id}`}>Role Name</Label>
                          <Input
                            type="text"
                            name="name"
                            id={`name-${role.id}`}
                            defaultValue={role.name}
                            required
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor={`description-${role.id}`}>Description</Label>
                          <Input
                            type="text"
                            name="description"
                            id={`description-${role.id}`}
                            defaultValue={role.description || ''}
                          />
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <Label>Permissions</Label>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-48 overflow-y-auto border rounded-md p-3">
                          {allPermissions.map((permission: any) => {
                            const isChecked = role.permissions.some((p: any) => p.id === permission.id);
                            return (
                              <div key={permission.id} className="flex items-center space-x-2">
                                <input
                                  type="checkbox"
                                  name="permissionIds"
                                  value={permission.id}
                                  id={`edit-${role.id}-${permission.id}`}
                                  defaultChecked={isChecked}
                                  className="rounded"
                                />
                                <Label htmlFor={`edit-${role.id}-${permission.id}`} className="text-sm">
                                  {permission.displayName}
                                </Label>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                      
                      <Button type="submit" disabled={isSubmitting}>
                        {isSubmitting ? 'Updating...' : 'Update Role'}
                      </Button>
                    </Form>
                  </div>
                ))}
                
                {allRoles.filter((role: any) => !role.isSystemRole).length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No custom roles created yet.</p>
                    <p className="text-sm">Create your first custom role above.</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="permissions" className="space-y-6">
          <PermissionsManagement businessId={businessId} />
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default RBACManagementPage;