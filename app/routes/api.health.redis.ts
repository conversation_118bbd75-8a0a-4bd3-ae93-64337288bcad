/**
 * Redis Health Check Endpoint
 * 
 * This endpoint provides health status for Redis connection and rate limiting functionality.
 * Useful for monitoring and debugging Redis-related issues.
 */

import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { checkRedisHealth, redis, rateLimiters } from "~/lib/redis.server";

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    const url = new URL(request.url);
    const detailed = url.searchParams.get('detailed') === 'true';
    
    // Basic Redis health check
    const isHealthy = await checkRedisHealth();
    
    if (!detailed) {
      return json({
        status: isHealthy ? 'healthy' : 'unhealthy',
        redis: isHealthy,
        timestamp: new Date().toISOString(),
      });
    }
    
    // Detailed health check
    const healthData: any = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      redis: {
        connected: isHealthy,
        url: process.env.UPSTASH_REDIS_URL ? 'configured' : 'not configured',
        token: process.env.UPSTASH_REDIS_TOKEN ? 'configured' : 'not configured',
      },
      rateLimiting: {
        enabled: true,
        limiters: Object.keys(rateLimiters),
      },
    };
    
    if (isHealthy) {
      try {
        // Test Redis operations
        const testKey = `health-check:${Date.now()}`;
        await redis.set(testKey, 'test', { ex: 10 });
        const testValue = await redis.get(testKey);
        await redis.del(testKey);
        
        healthData.redis.operations = {
          set: true,
          get: testValue === 'test',
          delete: true,
        };
        
        // Get Redis info (if available)
        try {
          const info = await redis.info();
          healthData.redis.info = {
            version: info.match(/redis_version:([^\r\n]+)/)?.[1] || 'unknown',
            uptime: info.match(/uptime_in_seconds:([^\r\n]+)/)?.[1] || 'unknown',
            connected_clients: info.match(/connected_clients:([^\r\n]+)/)?.[1] || 'unknown',
          };
        } catch (infoError) {
          healthData.redis.info = 'not available';
        }
        
      } catch (operationError) {
        healthData.redis.operations = {
          error: 'Failed to perform Redis operations',
        };
      }
    }
    
    return json(healthData);
    
  } catch (error) {
    console.error('Health check error:', error);
    
    return json({
      status: 'error',
      error: 'Health check failed',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}

// Also handle POST requests for testing
export async function action({ request }: LoaderFunctionArgs) {
  return loader({ request });
}
