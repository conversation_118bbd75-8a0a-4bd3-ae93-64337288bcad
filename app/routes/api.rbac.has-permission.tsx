import { json, type LoaderFunctionArgs } from '@remix-run/node'
import { RBACService } from '~/lib/services/rbacService.server'
import { requireAuth } from '~/lib/auth.session.server'

export async function loader({ request }: LoaderFunctionArgs) {
  await requireAuth(request)
  
  const url = new URL(request.url)
  const userId = url.searchParams.get('userId')
  const permission = url.searchParams.get('permission')
  const businessId = url.searchParams.get('businessId')

  if (!userId || !permission) {
    return json({ error: 'Missing required parameters' }, { status: 400 })
  }

  try {
    const hasPermission = await RBACService.hasPermission(
      userId,
      permission,
      businessId || undefined
    )

    return json({ hasPermission })
  } catch (error) {
    console.error('Error checking permission:', error)
    return json({ error: 'Failed to check permission' }, { status: 500 })
  }
}