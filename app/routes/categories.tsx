import { json, type LoaderFunctionArgs, type MetaFunction } from "@remix-run/node";
import { useLoaderD<PERSON>, useRevalidator, useSearchParams } from "@remix-run/react";
import { useState, useCallback, useMemo, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Plus, Search, Filter, X, FolderTree, Tag, AlertTriangle } from "lucide-react";

import { getSession } from "~/lib/auth.session.server";
import { CategoryServiceServer } from "~/lib/services/categoryService.server";
import { BusinessServiceServer } from "~/lib/services/businessService.server";
import { withRBAC } from "~/lib/utils/rbacLoader.server";
import { Protected } from "~/lib/hooks/useRBAC";
import { useRouteProtection } from "~/lib/hooks/useRouteProtection";
import { createProtectedLoader, RBAC_CONFIGS } from "~/lib/middleware/autoRBAC.server";
import { CategoryManagementSheet } from "../components/inventory/CategoryManagementSheet";
import { CategoryDetailSheet } from "../components/inventory/CategoryDetailSheet";
import type { CategoryWithChildren, Category, CategoryType } from "~/lib/types/inventory";
import type { RBACData } from "~/lib/utils/rbacLoader.server";

import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";

export const meta: MetaFunction = () => {
  return [
    { title: "Categories - KWACI Grow" },
    { name: "description", content: "Manage your business categories" },
  ];
};

interface LoaderData {
  categories: CategoryWithChildren[];
  businessId: string;
  businessName: string;
  rbac: RBACData | null;
}

async function getBusinessId(args: LoaderFunctionArgs): Promise<string> {
  const session = await getSession(args.request);

  if (!session?.user) {
    throw new Response("Unauthorized", { status: 401 });
  }

  // Get user's default business
  const business = await BusinessServiceServer.getDefaultByUser(session.user.id);

  if (!business) {
    throw new Response("No business found. Please create a business first.", { status: 404 });
  }

  return business.id;
}

// Create protected loader that enforces categories.read permission
export const loader = createProtectedLoader(
  {
    ...RBAC_CONFIGS.categories,
    getBusinessId,
  },
  async ({ request }) => {
    const session = await getSession(request);

    if (!session?.user) {
      throw new Response("Unauthorized", { status: 401 });
    }

    // Get user's default business
    const business = await BusinessServiceServer.getDefaultByUser(session.user.id);

    if (!business) {
      throw new Response("No business found. Please create a business first.", { status: 404 });
    }

    // Note: RBAC permission check for 'categories.read' is handled by createProtectedLoader

    try {
      // Get hierarchical categories for all types
      const allTypes: CategoryType[] = ['ingredient', 'product', 'supplier', 'customer'];
      const allCategories: CategoryWithChildren[] = [];

      for (const type of allTypes) {
        const categoriesForType = await CategoryServiceServer.getHierarchicalCategories(
          business.id,
          session.user.id,
          type
        );
        allCategories.push(...categoriesForType);
      }

      // Include RBAC data for client-side permission checks
      const rbac = await withRBAC(request, business.id);

      return json<LoaderData>({
        categories: allCategories,
        businessId: business.id,
        businessName: business.name,
        rbac,
      });
    } catch (error) {
      console.error("Failed to load categories:", error);
      throw new Response("Failed to load categories", { status: 500 });
    }
  }
);

export default function CategoriesPage() {
  const loaderData = useLoaderData<LoaderData>();
  const { t } = useTranslation('inventory');
  const { t: tCommon } = useTranslation('common');
  const { t: tAuth } = useTranslation('auth');
  const revalidator = useRevalidator();
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchTerm, setSearchTerm] = useState("");

  // Filter state
  const [showFilters, setShowFilters] = useState(false);
  const [selectedType, setSelectedType] = useState<CategoryType | "all">("all");

  // Ref to track if an edit operation just completed to prevent unwanted drawer opening
  const editOperationJustCompleted = useRef(false);
  // Ref to track if any sheet/modal interaction is happening
  const sheetInteractionInProgress = useRef(false);
  
  // Edit state
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);

  // Get the category ID from URL params for sheet state
  const detailCategoryId = searchParams.get('detail');
  
  // Client-side route protection
  useRouteProtection({
    permissions: ['categories.read'],
    preserveUrl: true,
    redirectTo: '/unauthorized'
  });

  // Set up mutation observer to detect sheet state changes
  useEffect(() => {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'data-state') {
          const target = mutation.target as Element;
          const dataState = target.getAttribute('data-state');

          if (dataState === 'open') {
            sheetInteractionInProgress.current = true;
          } else if (dataState === 'closed') {
            sheetInteractionInProgress.current = true;
            // Clear the flag after a delay to prevent drawer opening immediately after sheet closes
            setTimeout(() => {
              sheetInteractionInProgress.current = false;
            }, 500);
          }
        }
      });
    });

    // Observe the document for sheet state changes
    observer.observe(document.body, {
      attributes: true,
      subtree: true,
      attributeFilter: ['data-state']
    });

    return () => {
      observer.disconnect();
    };
  }, []);

  // Memoized selected category
  const selectedCategory = useMemo(() => {
    if (!loaderData?.categories || !Array.isArray(loaderData.categories)) {
      return null;
    }
    
    const findCategory = (cats: CategoryWithChildren[]): Category | null => {
      // Safety check: ensure cats is an array
      if (!Array.isArray(cats)) {
        return null;
      }

      for (const cat of cats) {
        if (cat.id === detailCategoryId) return cat;
        if (cat.children && cat.children.length > 0) {
          const found = findCategory(cat.children);
          if (found) return found;
        }
      }
      return null;
    };
    return findCategory(loaderData.categories);
  }, [loaderData?.categories, detailCategoryId]);

  // Sheet state management - memoized to prevent unnecessary re-renders
  const openCategoryDetail = useCallback((categoryId: string) => {
    // Don't open sheet if an edit operation just completed or sheet interaction is in progress
    if (editOperationJustCompleted.current || sheetInteractionInProgress.current) {
      editOperationJustCompleted.current = false;
      sheetInteractionInProgress.current = false;
      return;
    }

    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('detail', categoryId);
    setSearchParams(newSearchParams);
  }, [searchParams, setSearchParams]);

  const closeCategoryDetail = useCallback(() => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.delete('detail');
    setSearchParams(newSearchParams);
  }, [searchParams, setSearchParams]);

  // Stable category change handler that doesn't interfere with drawer state
  const handleCategoryChange = useCallback(() => {
    // Mark that an edit operation just completed
    editOperationJustCompleted.current = true;

    // Clear the flag after a short delay to allow for any pending events
    setTimeout(() => {
      editOperationJustCompleted.current = false;
    }, 500);

    // Revalidate the data
    revalidator.revalidate();
  }, [revalidator]);

  const handleEditCategory = useCallback((category: Category) => {
    // Close the detail sheet first and wait for URL to update
    closeCategoryDetail();
    // Use setTimeout to ensure the URL update completes before opening edit sheet
    setTimeout(() => {
      setEditingCategory(category);
    }, 50);
  }, [closeCategoryDetail]);

  // Filter categories based on search term and type
  const filteredCategories = useMemo(() => {
    if (!loaderData?.categories || !Array.isArray(loaderData.categories)) {
      return [];
    }
    
    const filterRecursive = (cats: CategoryWithChildren[]): CategoryWithChildren[] => {
      return cats.filter(category => {
        const matchesSearch = searchTerm === "" || 
          category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (category.description && category.description.toLowerCase().includes(searchTerm.toLowerCase()));
        
        const matchesType = selectedType === "all" || category.type === selectedType;
        
        const hasMatchingChildren = category.children && category.children.length > 0 && 
          filterRecursive(category.children).length > 0;
        
        return (matchesSearch && matchesType) || hasMatchingChildren;
      }).map(category => ({
        ...category,
        children: category.children ? filterRecursive(category.children) : []
      }));
    };
    
    return filterRecursive(loaderData.categories);
  }, [loaderData?.categories, searchTerm, selectedType]);

  // Note: Server-side authorization is handled by createProtectedLoader

  // Check for authorization errors or missing data
  if (!loaderData || !loaderData.categories || !Array.isArray(loaderData.categories)) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <div className="flex items-center justify-center w-16 h-16 bg-red-100 rounded-full">
          <AlertTriangle className="w-8 h-8 text-red-600" />
        </div>
        <div className="text-center space-y-2">
          <h2 className="text-xl font-semibold text-gray-900">
            {tAuth('errors.authorization.accessDenied')}
          </h2>
          <p className="text-gray-600 max-w-md">
            {tAuth('errors.authorization.accessDeniedDescription')}
          </p>
          <p className="text-sm text-gray-500">
            {tAuth('errors.authorization.insufficientPermissions')}
          </p>
        </div>
      </div>
    );
  }

  // Destructure data after validation
  const { categories, businessId, businessName } = loaderData;

  // Flatten categories for table display
  const flattenCategories = (cats: CategoryWithChildren[], level = 0): (CategoryWithChildren & { level: number })[] => {
    const result: (CategoryWithChildren & { level: number })[] = [];
    
    for (const cat of cats) {
      result.push({ ...cat, level });
      if (cat.children && cat.children.length > 0) {
        result.push(...flattenCategories(cat.children, level + 1));
      }
    }
    
    return result;
  };

  const flatCategories = flattenCategories(filteredCategories);

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'ingredient': return 'bg-blue-100 text-blue-800';
      case 'product': return 'bg-green-100 text-green-800';
      case 'supplier': return 'bg-purple-100 text-purple-800';
      case 'customer': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'ingredient': return t('categories.types.ingredient');
      case 'product': return t('categories.types.product');
      case 'supplier': return t('categories.types.supplier');
      case 'customer': return t('categories.types.customer');
      default: return type;
    }
  };

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">{t('categories.title')}</h1>
          <p className="text-muted-foreground">
            {t('categories.description', { businessName })}
          </p>
        </div>
        {!editingCategory && (
          <Protected permissions={['categories.create']}>
            <CategoryManagementSheet
              mode="create"
              businessId={businessId}
              onCategoryChange={handleCategoryChange}
              allCategories={categories}
            >
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                {t('categories.actions.create')}
              </Button>
            </CategoryManagementSheet>
          </Protected>
        )}
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder={t('categories.search.placeholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9"
              />
            </div>
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className={showFilters ? "bg-muted" : ""}
            >
              <Filter className="mr-2 h-4 w-4" />
              {tCommon('buttons.filters')}
            </Button>
          </div>
          
          {showFilters && (
            <div className="flex items-center gap-4 pt-4 border-t">
              <div className="flex items-center gap-2">
                <label className="text-sm font-medium">{t('categories.filters.type')}:</label>
                <Select value={selectedType} onValueChange={(value) => setSelectedType(value as CategoryType | "all")}>
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{tCommon('filters.all')}</SelectItem>
                    <SelectItem value="ingredient">{t('categories.types.ingredient')}</SelectItem>
                    <SelectItem value="product">{t('categories.types.product')}</SelectItem>
                    <SelectItem value="supplier">{t('categories.types.supplier')}</SelectItem>
                    <SelectItem value="customer">{t('categories.types.customer')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {(searchTerm || selectedType !== "all") && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setSearchTerm("");
                    setSelectedType("all");
                  }}
                >
                  <X className="mr-1 h-3 w-3" />
                  {tCommon('buttons.clearFilters')}
                </Button>
              )}
            </div>
          )}
        </CardHeader>
      </Card>

      {/* Categories Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FolderTree className="h-5 w-5" />
            {t('categories.list.title')}
          </CardTitle>
          <CardDescription>
            {t('categories.list.description', { count: flatCategories.length })}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {flatCategories.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <Tag className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {searchTerm || selectedType !== "all" 
                  ? t('categories.empty.noResults')
                  : t('categories.empty.noCategories')
                }
              </h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm || selectedType !== "all"
                  ? t('categories.empty.noResultsDescription')
                  : t('categories.empty.noCategoriesDescription')
                }
              </p>
              {!searchTerm && selectedType === "all" && !editingCategory && (
                <Protected permissions={['categories.create']}>
                  <CategoryManagementSheet
                    mode="create"
                    businessId={businessId}
                    onCategoryChange={handleCategoryChange}
                    allCategories={categories}
                  >
                    <Button>
                      <Plus className="mr-2 h-4 w-4" />
                      {t('categories.actions.createFirst')}
                    </Button>
                  </CategoryManagementSheet>
                </Protected>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('categories.table.name')}</TableHead>
                  <TableHead>{t('categories.table.type')}</TableHead>
                  <TableHead>{t('categories.table.description')}</TableHead>
                  <TableHead>{t('categories.table.status')}</TableHead>
                  <TableHead className="w-[100px]">{tCommon('table.actions')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {flatCategories.map((category) => (
                  <TableRow
                    key={category.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => openCategoryDetail(category.id)}
                  >
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div style={{ marginLeft: `${category.level * 20}px` }} className="flex items-center gap-2">
                          {category.level > 0 && (
                            <div className="w-4 h-4 border-l border-b border-muted-foreground/30 -mb-2" />
                          )}
                          {category.color && (
                            <div
                              className="w-3 h-3 rounded-full border border-gray-300"
                              style={{ backgroundColor: category.color }}
                            />
                          )}
                          <span className="font-medium">{category.name}</span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary" className={getTypeColor(category.type)}>
                        {getTypeLabel(category.type)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className="text-muted-foreground">
                        {category.description || "-"}
                      </span>
                    </TableCell>
                    <TableCell>
                      <Badge variant={category.isActive ? "default" : "secondary"}>
                        {category.isActive ? tCommon('status.active') : tCommon('status.inactive')}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Protected permissions={['categories.edit']}>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEditCategory(category);
                            }}
                          >
                            {tCommon('buttons.edit')}
                          </Button>
                        </Protected>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Category Detail Sheet - only show when not editing */}
      {selectedCategory && !editingCategory && (
        <CategoryDetailSheet
          category={selectedCategory}
          businessId={businessId}
          isOpen={!!detailCategoryId && !editingCategory}
          onClose={closeCategoryDetail}
          onEdit={handleEditCategory}
          onCategoryChange={handleCategoryChange}
          allCategories={categories}
        />
      )}

      {/* Category Management Sheet for editing */}
      {editingCategory && (
        <CategoryManagementSheet
          mode="edit"
          businessId={businessId}
          onCategoryChange={handleCategoryChange}
          editingCategory={editingCategory}
          onEditComplete={() => setEditingCategory(null)}
          isOpen={!!editingCategory}
          onClose={() => setEditingCategory(null)}
          allCategories={categories}
        >
          <div />
        </CategoryManagementSheet>
      )}
    </div>
  );
}