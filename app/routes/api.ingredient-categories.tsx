import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { getSession } from "~/lib/auth.session.server";
import { CategoryServiceServer } from "~/lib/services/categoryService.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const url = new URL(request.url);
  const businessId = url.searchParams.get("businessId");
  
  if (!businessId) {
    return json({ error: "Business ID is required" }, { status: 400 });
  }

  try {
    // Get only ingredient categories for the business
    const categories = await CategoryServiceServer.getAllByBusinessAndType(
      businessId, 
      session.user.id,
      'ingredient'
    );
    
    return json({ categories });
  } catch (error) {
    console.error("Failed to load ingredient categories:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to load ingredient categories";
    return json({ error: errorMessage }, { status: 500 });
  }
}
