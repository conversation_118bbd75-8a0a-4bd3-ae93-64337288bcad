/**
 * API endpoint for extending user sessions
 */

import { json, type ActionFunctionArgs } from "@remix-run/node";
import { getSession } from "~/lib/auth.session.server";
import { auth } from "~/lib/auth.server";
import type { SessionExtensionRequest, SessionExtensionResponse } from "~/lib/session/types";

/**
 * Handle session extension requests
 */
export async function action({ request }: ActionFunctionArgs) {
  // Only allow POST requests
  if (request.method !== 'POST') {
    return json(
      { 
        success: false, 
        error: 'Method not allowed' 
      } as SessionExtensionResponse, 
      { status: 405 }
    );
  }

  try {
    // Get current session
    const currentSession = await getSession(request);
    
    if (!currentSession?.user || !currentSession?.session) {
      return json(
        { 
          success: false, 
          error: 'No active session found' 
        } as SessionExtensionResponse, 
        { status: 401 }
      );
    }

    // Parse request body
    let requestData: SessionExtensionRequest;
    try {
      requestData = await request.json();
    } catch (error) {
      return json(
        { 
          success: false, 
          error: 'Invalid request body' 
        } as SessionExtensionResponse, 
        { status: 400 }
      );
    }

    // Validate session ID matches current session
    if (requestData.sessionId !== currentSession.session.id) {
      return json(
        { 
          success: false, 
          error: 'Session ID mismatch' 
        } as SessionExtensionResponse, 
        { status: 400 }
      );
    }

    // Calculate new expiration time
    // Default extension is the same as the original session duration (30 days)
    const extensionDuration = requestData.extensionDuration || (30 * 24 * 60 * 60); // 30 days in seconds
    const newExpiresAt = Date.now() + (extensionDuration * 1000); // Convert to milliseconds

    // Update session through Better Auth
    try {
      // Use Better Auth's session update mechanism
      const updatedSession = await auth.api.updateSession({
        headers: request.headers,
        body: {
          sessionId: currentSession.session.id,
          expiresAt: new Date(newExpiresAt),
        }
      });

      if (!updatedSession) {
        throw new Error('Failed to update session');
      }

      // Calculate remaining time
      const remainingTime = Math.floor((newExpiresAt - Date.now()) / 1000);

      return json({
        success: true,
        newExpiresAt,
        remainingTime,
      } as SessionExtensionResponse);

    } catch (authError) {
      console.error('[SessionExtension] Better Auth update failed:', authError);
      
      return json(
        { 
          success: false, 
          error: 'Failed to extend session' 
        } as SessionExtensionResponse, 
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('[SessionExtension] Unexpected error:', error);
    
    return json(
      { 
        success: false, 
        error: 'Internal server error' 
      } as SessionExtensionResponse, 
      { status: 500 }
    );
  }
}

/**
 * Handle GET requests - return session information
 */
export async function loader({ request }: ActionFunctionArgs) {
  try {
    const currentSession = await getSession(request);
    
    if (!currentSession?.user || !currentSession?.session) {
      return json(
        { 
          success: false, 
          error: 'No active session found' 
        } as SessionExtensionResponse, 
        { status: 401 }
      );
    }

    const session = currentSession.session;
    const expiresAt = new Date(session.expiresAt).getTime();
    const remainingTime = Math.floor((expiresAt - Date.now()) / 1000);

    return json({
      success: true,
      newExpiresAt: expiresAt,
      remainingTime: Math.max(0, remainingTime),
    } as SessionExtensionResponse);

  } catch (error) {
    console.error('[SessionExtension] Error getting session info:', error);
    
    return json(
      { 
        success: false, 
        error: 'Failed to get session information' 
      } as SessionExtensionResponse, 
      { status: 500 }
    );
  }
}
