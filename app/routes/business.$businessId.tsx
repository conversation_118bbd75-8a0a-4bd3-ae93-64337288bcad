import type { LoaderFunctionArgs } from '@remix-run/node';
import { json } from '@remix-run/node';
import { Outlet, useLoaderData } from '@remix-run/react';
import { requireBusinessAccess } from '~/lib/middleware/rbac.server';
import { withRBAC } from '~/lib/utils/rbacLoader.server';
import { BusinessServiceServer } from '~/lib/services/businessService.server';
import { getSession } from '~/lib/auth.session.server';

export async function loader({ request, params }: LoaderFunctionArgs) {
  const businessId = params.businessId!;
  
  // Require business access
  await requireBusinessAccess(request, businessId);
  
  // Get session for user info
  const session = await getSession(request);
  if (!session?.user) {
    throw new Response('Unauthorized', { status: 401 });
  }
  
  // Get business details
  const business = await BusinessServiceServer.getByIdAndUser(businessId, session.user.id);
  if (!business) {
    throw new Response('Business not found', { status: 404 });
  }
  
  // Get RBAC data
  const rbac = await withRBAC(request, businessId);
  
  return json({ business, rbac });
}

export default function BusinessLayout() {
  const { business } = useLoaderData<typeof loader>();
  
  return (
    <div className="min-h-screen">
      {/* Business context is now available to all child routes */}
      <Outlet context={{ business }} />
    </div>
  );
}