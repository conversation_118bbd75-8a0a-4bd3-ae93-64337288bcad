import { json, type ActionFunctionArgs } from "@remix-run/node";
import { getSession } from "~/lib/auth.session.server";
import { CogsServiceServer } from "~/lib/services/cogsService.server";

export async function action({ request, params }: ActionFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const { id } = params;
  if (!id) {
    return json({ error: "Ingredient ID is required" }, { status: 400 });
  }

  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    const body = await request.json();
    const { businessId } = body;
    
    if (!businessId) {
      return json({ error: "Business ID is required" }, { status: 400 });
    }

    // Trigger COGS recalculation for all products using this ingredient
    await CogsServiceServer.recalculateCogsForIngredientChange(
      id,
      businessId,
      session.user.id
    );

    return json({ success: true, message: "COGS recalculation triggered successfully" });
  } catch (error) {
    console.error("Failed to recalculate COGS:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to recalculate COGS";
    return json({ error: errorMessage }, { status: 500 });
  }
}
