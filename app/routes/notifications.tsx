import type { LoaderFunctionArgs, ActionFunctionArgs } from '@remix-run/node';
import { json, redirect } from '@remix-run/node';
import { useLoaderData, useActionData, Form, useNavigation } from '@remix-run/react';
import React, { useState } from 'react';
import { getSession } from '~/lib/auth.session.server';
import { InvitationService } from '~/lib/services/invitationService.server';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { Badge } from '~/components/ui/badge';
import { Separator } from '~/components/ui/separator';
import { 
  Bell, 
  Building2, 
  Shield, 
  User, 
  Calendar, 
  Check, 
  X, 
  Clock,
  Mail,
  AlertTriangle
} from 'lucide-react';

export async function loader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return redirect('/login');
  }

  try {
    // Get pending invitations for the current user
    const pendingInvitations = await InvitationService.getUserPendingInvitations(session.user.email);
    
    return json({
      user: session.user,
      pendingInvitations,
    });
  } catch (error) {
    console.error('Failed to load notifications:', error);
    return json({ 
      user: session.user,
      pendingInvitations: [],
      error: 'Failed to load notifications'
    });
  }
}

export async function action({ request }: ActionFunctionArgs) {
  const session = await getSession(request);

  if (!session?.user) {
    console.error('Notifications action: No user session found');
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  const formData = await request.formData();
  const intent = formData.get('intent') as string;
  const invitationId = formData.get('invitationId') as string;

  console.log(`Notifications action: ${intent} for invitation ${invitationId} by user ${session.user.id}`);

  if (!invitationId) {
    console.error('Notifications action: No invitation ID provided');
    return json({ error: 'Invitation ID is required' }, { status: 400 });
  }

  try {
    switch (intent) {
      case 'accept_invitation': {
        console.log(`Accepting invitation ${invitationId} for user ${session.user.id}`);
        await InvitationService.acceptInvitationById(invitationId, session.user.id);
        console.log(`Successfully accepted invitation ${invitationId}`);
        return json({ success: 'Invitation accepted successfully! You now have access to the business.' });
      }

      case 'decline_invitation': {
        console.log(`Declining invitation ${invitationId}`);
        await InvitationService.declineInvitation(invitationId);
        console.log(`Successfully declined invitation ${invitationId}`);
        return json({ success: 'Invitation declined.' });
      }

      default:
        console.error(`Notifications action: Invalid intent ${intent}`);
        return json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error: any) {
    console.error(`Notifications action error:`, error);
    return json({ error: error.message }, { status: 400 });
  }
}

export default function NotificationsPage() {
  const { user, pendingInvitations, error } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();

  const isSubmitting = navigation.state === 'submitting';

  // Get the invitation ID being processed from form data
  const submittingInvitationId = navigation.formData?.get('invitationId') as string;

  // Helper function to get days until expiration
  const getDaysUntilExpiration = (expiresAt: string) => {
    const now = new Date();
    const expiration = new Date(expiresAt);
    const diffTime = expiration.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // Helper function to get urgency badge
  const getUrgencyBadge = (expiresAt: string) => {
    const days = getDaysUntilExpiration(expiresAt);
    if (days <= 1) {
      return <Badge variant="destructive" className="flex items-center gap-1">
        <AlertTriangle className="h-3 w-3" />
        Expires {days === 0 ? 'today' : 'tomorrow'}
      </Badge>;
    } else if (days <= 3) {
      return <Badge variant="secondary" className="flex items-center gap-1">
        <Clock className="h-3 w-3" />
        Expires in {days} days
      </Badge>;
    }
    return <Badge variant="outline" className="flex items-center gap-1">
      <Calendar className="h-3 w-3" />
      Expires in {days} days
    </Badge>;
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-4xl">
      {/* Header */}
      <div className="flex items-center gap-3 mb-8">
        <Bell className="h-8 w-8 text-blue-600" />
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Notifications</h1>
          <p className="text-muted-foreground">
            Manage your business invitations and notifications
          </p>
        </div>
      </div>

      {/* Action feedback */}
      {actionData && 'error' in actionData && (
        <Alert variant="destructive" className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{actionData.error}</AlertDescription>
        </Alert>
      )}

      {actionData && 'success' in actionData && (
        <Alert className="mb-6 border-green-200 bg-green-50">
          <Check className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">{actionData.success}</AlertDescription>
        </Alert>
      )}

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Pending Invitations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Business Invitations
            {pendingInvitations.length > 0 && (
              <Badge variant="default">{pendingInvitations.length}</Badge>
            )}
          </CardTitle>
          <CardDescription>
            You have {pendingInvitations.length} pending business invitation{pendingInvitations.length !== 1 ? 's' : ''}
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          {pendingInvitations.length === 0 ? (
            /* Empty State */
            <div className="text-center py-12">
              <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No pending invitations</h3>
              <p className="text-muted-foreground">
                You're all caught up! New business invitations will appear here.
              </p>
            </div>
          ) : (
            /* Invitations List */
            <div className="space-y-4">
              {pendingInvitations.map((invitation: any, index: number) => (
                <div key={invitation.id}>
                  <Card className="border-l-4 border-l-blue-500">
                    <CardContent className="pt-6">
                      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                        {/* Invitation Details */}
                        <div className="space-y-3 flex-1">
                          <div className="flex items-start gap-3">
                            <Building2 className="h-5 w-5 text-blue-600 mt-0.5" />
                            <div>
                              <h3 className="font-semibold text-lg">{invitation.businessName}</h3>
                              <p className="text-sm text-muted-foreground">
                                You've been invited to join this business
                              </p>
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 ml-8">
                            <div className="flex items-center gap-2">
                              <Shield className="h-4 w-4 text-purple-600" />
                              <span className="text-sm">
                                <strong>Role:</strong> {invitation.roleDisplayName}
                              </span>
                            </div>
                            
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4 text-green-600" />
                              <span className="text-sm">
                                <strong>Invited by:</strong> {invitation.invitedByName}
                              </span>
                            </div>
                          </div>
                          
                          <div className="ml-8">
                            {getUrgencyBadge(invitation.expiresAt)}
                          </div>
                        </div>
                        
                        {/* Action Buttons */}
                        <div className="flex flex-col sm:flex-row gap-2 lg:min-w-[200px]">
                          <Form method="post" className="flex-1">
                            <input type="hidden" name="intent" value="accept_invitation" />
                            <input type="hidden" name="invitationId" value={invitation.id} />
                            <Button
                              type="submit"
                              className="w-full"
                              disabled={isSubmitting && submittingInvitationId === invitation.id}
                            >
                              {isSubmitting && submittingInvitationId === invitation.id ? (
                                <>
                                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                                  Accepting...
                                </>
                              ) : (
                                <>
                                  <Check className="h-4 w-4 mr-2" />
                                  Accept
                                </>
                              )}
                            </Button>
                          </Form>
                          
                          <Form method="post" className="flex-1">
                            <input type="hidden" name="intent" value="decline_invitation" />
                            <input type="hidden" name="invitationId" value={invitation.id} />
                            <Button
                              type="submit"
                              variant="outline"
                              className="w-full"
                              disabled={isSubmitting && submittingInvitationId === invitation.id}
                            >
                              {isSubmitting && submittingInvitationId === invitation.id ? (
                                <>
                                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                                  Declining...
                                </>
                              ) : (
                                <>
                                  <X className="h-4 w-4 mr-2" />
                                  Decline
                                </>
                              )}
                            </Button>
                          </Form>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  
                  {index < pendingInvitations.length - 1 && (
                    <Separator className="my-4" />
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
