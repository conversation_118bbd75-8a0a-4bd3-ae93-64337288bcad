import type { LoaderFunctionArgs } from '@remix-run/node';
import { json } from '@remix-run/node';
import { useLoaderData } from '@remix-run/react';
import { requireInventoryAccess } from '~/lib/middleware/rbac.server';
import { withRBAC } from '~/lib/utils/rbacLoader.server';
import { useRBAC, Protected } from '~/lib/hooks/useRBAC';

export async function loader({ request, params }: LoaderFunctionArgs) {
  const businessId = params.businessId!;
  
  // Require inventory read access for this route
  await requireInventoryAccess(request, businessId, 'read');
  
  // Get RBAC data for the component
  const rbac = await withRBAC(request, businessId);
  
  // Mock inventory data - replace with actual data fetching
  const inventory = [
    { id: '1', name: 'Tomatoes', quantity: 50, unit: 'kg' },
    { id: '2', name: 'Onions', quantity: 30, unit: 'kg' },
    { id: '3', name: 'Carrots', quantity: 25, unit: 'kg' },
  ];
  
  return json({ inventory, rbac });
}

export default function InventoryPage() {
  const { inventory } = useLoaderData<typeof loader>();
  const rbac = useRBAC();
  
  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Inventory Management</h1>
        
        <Protected permissions={['inventory.create']}>
          <button className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
            Add New Item
          </button>
        </Protected>
      </div>
      
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium">Current Inventory</h2>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Item Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Quantity
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Unit
                </th>
                <Protected permissions={['inventory.update', 'inventory.delete']}>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </Protected>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {inventory.map((item) => (
                <tr key={item.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {item.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.quantity}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.unit}
                  </td>
                  <Protected permissions={['inventory.update', 'inventory.delete']}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      <Protected permissions={['inventory.update']}>
                        <button className="text-blue-600 hover:text-blue-900">
                          Edit
                        </button>
                      </Protected>
                      <Protected permissions={['inventory.delete']}>
                        <button className="text-red-600 hover:text-red-900">
                          Delete
                        </button>
                      </Protected>
                    </td>
                  </Protected>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      
      {/* Debug info - remove in production */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-8 p-4 bg-gray-100 rounded">
          <h3 className="font-bold mb-2">Debug: Current User Permissions</h3>
          <div className="text-sm">
            <p><strong>User ID:</strong> {rbac.userId}</p>
            <p><strong>Business ID:</strong> {rbac.businessId}</p>
            <p><strong>Roles:</strong> {rbac?.roles?.join(', ') || 'None'}</p>
            <p><strong>Permissions:</strong> {rbac?.permissions?.join(', ') || 'None'}</p>
            <div className="mt-2">
              <p><strong>Inventory Permissions:</strong></p>
              <ul className="ml-4">
                <li>Can Read: {rbac.inventory?.canRead ? '✅' : '❌'}</li>
                <li>Can Create: {rbac.inventory?.canCreate ? '✅' : '❌'}</li>
                <li>Can Update: {rbac.inventory?.canUpdate ? '✅' : '❌'}</li>
                <li>Can Delete: {rbac.inventory?.canDelete ? '✅' : '❌'}</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}