import { json, type LoaderFunctionArgs } from '@remix-run/node'
import { RBACService } from '~/lib/services/rbacService.server'
import { requireAuth } from '~/lib/auth.session.server'

export async function loader({ request }: LoaderFunctionArgs) {
  await requireAuth(request)
  
  const url = new URL(request.url)
  const userId = url.searchParams.get('userId')
  const businessId = url.searchParams.get('businessId')

  if (!userId || !businessId) {
    return json({ error: 'Missing required parameters' }, { status: 400 })
  }

  try {
    const isOwner = await RBACService.isBusinessOwner(userId, businessId)
    return json({ isOwner })
  } catch (error) {
    console.error('Error checking business ownership:', error)
    return json({ error: 'Failed to check business ownership' }, { status: 500 })
  }
}