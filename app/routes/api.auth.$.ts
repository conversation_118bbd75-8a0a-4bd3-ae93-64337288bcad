/**
 * Enhanced Better Auth API route with Redis rate limiting and CORS support
 *
 * This is an enhanced version of the auth route that includes:
 * - Redis-based rate limiting for all authentication endpoints
 * - CORS headers for mobile apps and external clients
 * - Comprehensive error handling with internationalization
 * - Request logging for debugging and monitoring
 * - Bearer token support for API clients
 * - Session storage in Redis for improved performance
 *
 * To use this enhanced version:
 * 1. Rename this file to `api.auth.$.ts` (replace the existing one)
 * 2. Update the CORS configuration in cors.server.ts with your domains
 * 3. Configure Redis environment variables (UPSTASH_REDIS_URL, UPSTASH_REDIS_TOKEN)
 * 4. Test with your mobile app or external client
 */

import type { LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node";
import { auth } from "~/lib/auth.server";
import { addCorsHeaders, handlePreflight, extractBearerToken, createCorsErrorResponse } from "~/lib/cors.server";
import { checkAuthRateLimit, addRateLimitHeaders, logRateLimitEvent } from "~/lib/rateLimit.server";
import { checkRedisHealth } from "~/lib/redis.server";

// CORS configuration for external applications
const corsOptions = {
  allowedOrigins: [
    'http://localhost:3000',
    'http://localhost:5174',
    // Add your production domain
    // 'https://your-production-domain.com',
    // Add mobile app schemes if needed
    // 'your-app://auth-callback',
    // For development, you might want to allow all origins
    // '*',
  ],
  allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Cookie', 'X-Requested-With'],
  allowCredentials: true,
  maxAge: 86400, // 24 hours
};

/**
 * Enhanced request handler with Redis rate limiting, CORS, and error handling
 */
async function handleAuthRequest(request: Request): Promise<Response> {
  const origin = request.headers.get('Origin');
  const url = new URL(request.url);
  const path = url.pathname;

  try {
    // Log request for debugging (remove in production)
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Auth API] ${request.method} ${request.url}`, {
        origin,
        userAgent: request.headers.get('User-Agent'),
        contentType: request.headers.get('Content-Type'),
        path,
      });
    }

    // Check Redis health before proceeding
    const redisHealthy = await checkRedisHealth();
    if (!redisHealthy) {
      console.warn('[Auth API] Redis is not available, proceeding without rate limiting');
    }

    // Check rate limiting (only if Redis is healthy)
    if (redisHealthy) {
      const rateLimitResult = await checkAuthRateLimit(request);

      if (!rateLimitResult.allowed && rateLimitResult.response) {
        // Add CORS headers to rate limit response
        const corsResponse = addCorsHeaders(rateLimitResult.response, origin, corsOptions);

        // Log rate limit event
        const identifier = request.headers.get('cf-connecting-ip') ||
                          request.headers.get('x-real-ip') ||
                          request.headers.get('x-forwarded-for')?.split(',')[0]?.trim() ||
                          'unknown';
        logRateLimitEvent(identifier, path, false, rateLimitResult.remaining, rateLimitResult.limit);

        return corsResponse;
      }
    }

    // Handle Bearer token authentication for API clients
    const bearerToken = extractBearerToken(request);
    if (bearerToken) {
      // Add the Bearer token as a cookie for Better Auth compatibility
      const cookieHeader = `better-auth.session_token=${bearerToken}`;
      const headers = new Headers(request.headers);

      // Merge existing cookies with the Bearer token
      const existingCookies = headers.get('Cookie');
      if (existingCookies) {
        headers.set('Cookie', `${existingCookies}; ${cookieHeader}`);
      } else {
        headers.set('Cookie', cookieHeader);
      }

      // Create a new request with the modified headers
      request = new Request(request.url, {
        method: request.method,
        headers,
        body: request.body,
        duplex: 'half' as RequestDuplex,
      });
    }

    // Process the request through Better Auth
    const response = await auth.handler(request);

    // Add CORS headers to the response
    let corsResponse = addCorsHeaders(response, origin, corsOptions);

    // Add rate limit headers if Redis is healthy
    if (redisHealthy) {
      const rateLimitResult = await checkAuthRateLimit(request);
      corsResponse = addRateLimitHeaders(corsResponse, rateLimitResult);
    }

    // Log response for debugging (remove in production)
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Auth API] Response ${corsResponse.status}`, {
        headers: Object.fromEntries(corsResponse.headers.entries()),
      });
    }

    return corsResponse;

  } catch (error) {
    console.error('[Auth API] Error:', error);

    // Return a standardized error response with CORS headers
    return createCorsErrorResponse(
      'Internal server error',
      500,
      'SERVER_ERROR',
      origin,
      corsOptions
    );
  }
}

/**
 * Handle GET requests (OAuth callbacks, session checks, etc.)
 */
export async function loader({ request }: LoaderFunctionArgs) {
  // Handle preflight requests
  if (request.method === 'OPTIONS') {
    return handlePreflight(request, corsOptions);
  }
  
  return handleAuthRequest(request);
}

/**
 * Handle POST requests (login, register, logout, etc.)
 */
export async function action({ request }: ActionFunctionArgs) {
  // Handle preflight requests
  if (request.method === 'OPTIONS') {
    return handlePreflight(request, corsOptions);
  }
  
  return handleAuthRequest(request);
}

/**
 * Handle all other HTTP methods
 */
export async function handleRequest(request: Request): Promise<Response> {
  // Handle preflight requests
  if (request.method === 'OPTIONS') {
    return handlePreflight(request, corsOptions);
  }
  
  return handleAuthRequest(request);
}

// Export the handler for other HTTP methods if needed
export { handleRequest as DELETE, handleRequest as PUT, handleRequest as PATCH };
