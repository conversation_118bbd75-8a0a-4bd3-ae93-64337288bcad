import type { LoaderFunctionArgs } from '@remix-run/node';
import { json } from '@remix-run/node';
import { getSession } from '~/lib/auth.session.server';
import { RBACService } from '~/lib/services/rbacService.server';

export async function loader({ request }: LoaderFunctionArgs) {
  console.log('🔍 Debug Session Route - Starting...');
  
  try {
    // Test session retrieval
    const session = await getSession(request);
    console.log('📝 Session result:', session ? 'Found' : 'Not found');
    
    if (!session) {
      return json({
        error: 'No session found',
        headers: Object.fromEntries(request.headers.entries()),
        cookies: request.headers.get('cookie') || 'No cookies'
      });
    }
    
    console.log('👤 User ID:', session.user.id);
    console.log('📧 User Email:', session.user.email);
    
    // Test RBAC for the specific business
    const businessId = '26b99509-b432-4e5d-8930-c8bc28c8ed0e';
    const userId = session.user.id;
    
    console.log('🔍 Testing RBAC for business:', businessId);
    
    const hasBusinessOwnerRole = await RBACService.hasRole(userId, 'business_owner', businessId);
    console.log('🎭 Has business_owner role:', hasBusinessOwnerRole);
    
    const isBusinessOwner = await RBACService.isBusinessOwner(userId, businessId);
    console.log('👑 Is business owner:', isBusinessOwner);
    
    const userPermissions = await RBACService.getUserPermissions(userId, businessId);
    console.log('🔐 User permissions count:', userPermissions.permissions.length);
    
    return json({
      success: true,
      session: {
        userId: session.user.id,
        email: session.user.email,
        name: session.user.name
      },
      rbac: {
        hasBusinessOwnerRole,
        isBusinessOwner,
        permissionsCount: userPermissions.permissions.length,
        permissions: userPermissions.permissions.slice(0, 5), // First 5 permissions
        roles: userPermissions.roles
      },
      businessId
    });
    
  } catch (error) {
    console.error('❌ Debug session error:', error);
    return json({
      error: 'Debug session failed',
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}

export default function DebugSession() {
  return (
    <div>
      <h1>Debug Session Route</h1>
      <p>Check the JSON response for session debug information.</p>
    </div>
  );
}