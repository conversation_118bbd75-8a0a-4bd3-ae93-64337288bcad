import type { LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node";
import { auth } from "~/lib/auth.server";

// Handle GET requests (for OAuth callbacks, session checks, etc.)
export async function loader({ request }: LoaderFunctionArgs) {
  return auth.handler(request);
}

// Handle POST requests (for login, register, logout, etc.)
export async function action({ request }: ActionFunctionArgs) {
  return auth.handler(request);
}
