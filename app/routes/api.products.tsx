import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { getSession } from "~/lib/auth.session.server";
import { ProductServiceServer } from "~/lib/services/productService.server";
import { createProtectedRoute, RBAC_CONFIGS } from "~/lib/middleware/autoRBAC.server";
import type { ProductFilters, ProductIngredientFormData } from "~/lib/types/inventory";

// Original loader function without RBAC checks
async function productsLoader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const url = new URL(request.url);
  const businessId = url.searchParams.get("businessId");
  
  if (!businessId) {
    return json({ error: "Business ID is required" }, { status: 400 });
  }

  try {
    // Parse filters from query parameters
    const filters: ProductFilters = {};
    
    const search = url.searchParams.get("search");
    if (search) filters.search = search;
    
    const category = url.searchParams.get("category");
    if (category) filters.category = category;
    
    const status = url.searchParams.get("status");
    if (status) filters.status = status as 'active' | 'inactive' | 'discontinued';
    
    const minPrice = url.searchParams.get("minPrice");
    if (minPrice) filters.minPrice = parseFloat(minPrice);
    
    const maxPrice = url.searchParams.get("maxPrice");
    if (maxPrice) filters.maxPrice = parseFloat(maxPrice);

    const products = await ProductServiceServer.getAllByBusiness(
      businessId, 
      session.user.id, 
      filters
    );
    
    return json({ products });
  } catch (error) {
    console.error("Failed to load products:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to load products";
    return json({ error: errorMessage }, { status: 500 });
  }
}

// Original action function without RBAC checks
async function productsAction({ request }: ActionFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const formData = await request.formData();
  const action = formData.get("_action") as string;

  try {
    switch (action) {
      case "create": {
        const businessId = formData.get("businessId") as string;
        
        if (!businessId) {
          return json({ error: "Business ID is required" }, { status: 400 });
        }

        const data = {
          name: formData.get("name") as string,
          description: formData.get("description") as string || undefined,
          sellingPrice: formData.get("sellingPrice") as string,
          category: formData.get("category") as string || undefined,
          status: formData.get("status") as 'active' | 'inactive' | 'discontinued',
          ingredients: [] as ProductIngredientFormData[],
        };

        // Parse ingredients from form data
        const ingredientEntries = Array.from(formData.entries())
          .filter(([key]) => key.startsWith('ingredients['))
        
        const ingredientsMap = new Map<number, Partial<ProductIngredientFormData>>()
        
        ingredientEntries.forEach(([key, value]) => {
          const match = key.match(/ingredients\[(\d+)\]\[(\w+)\]/)
          if (match) {
            const index = parseInt(match[1])
            const field = match[2] as keyof ProductIngredientFormData
            
            if (!ingredientsMap.has(index)) {
              ingredientsMap.set(index, {})
            }
            
            const ingredient = ingredientsMap.get(index)!
            ingredient[field] = value as string
          }
        })
        
        data.ingredients = Array.from(ingredientsMap.values())
          .filter(ingredient => ingredient.ingredientId && ingredient.quantityNeeded)
          .map(ingredient => ({
            ingredientId: ingredient.ingredientId!,
            quantityNeeded: ingredient.quantityNeeded!,
          }))

        // Validate required fields
        if (!data.name || data.status === undefined) {
          return json({ error: "Name and status are required" }, { status: 400 });
        }

        // COGS per cup validation removed - will be calculated automatically

        // Validate ingredients quantities
        for (const ingredient of data.ingredients) {
          const quantity = parseFloat(ingredient.quantityNeeded);
          if (isNaN(quantity) || quantity <= 0) {
            return json({ error: "All ingredient quantities must be valid positive numbers" }, { status: 400 });
          }
        }

        // Check if product name already exists in business
        const nameExists = await ProductServiceServer.nameExistsInBusiness(
          data.name, 
          businessId, 
          session.user.id
        );
        
        if (nameExists) {
          return json({ error: "A product with this name already exists in this business" }, { status: 400 });
        }

        const product = await ProductServiceServer.create(
          businessId,
          session.user.id,
          {
            ...data,
            isActive: data.status === 'active',
            // cogsPerCup removed - will be calculated automatically
            ingredients: data.ingredients.map(ingredient => ({
              ingredientId: ingredient.ingredientId,
              quantityNeeded: parseFloat(ingredient.quantityNeeded).toString(),
            })),
          }
        );
        
        return json({ product });
      }

      case "update": {
        const id = formData.get("id") as string;
        const businessId = formData.get("businessId") as string;
        
        if (!id || !businessId) {
          return json({ error: "Product ID and Business ID are required" }, { status: 400 });
        }

        const data = {
          name: formData.get("name") as string,
          description: formData.get("description") as string || undefined,
          sellingPrice: formData.get("sellingPrice") as string,
          category: formData.get("category") as string || undefined,
          status: formData.get("status") as 'active' | 'inactive' | 'discontinued',
          ingredients: [] as ProductIngredientFormData[],
        };

        // Parse ingredients from form data (same logic as create)
        const ingredientEntries = Array.from(formData.entries())
          .filter(([key]) => key.startsWith('ingredients['))
        
        const ingredientsMap = new Map<number, Partial<ProductIngredientFormData>>()
        
        ingredientEntries.forEach(([key, value]) => {
          const match = key.match(/ingredients\[(\d+)\]\[(\w+)\]/)
          if (match) {
            const index = parseInt(match[1])
            const field = match[2] as keyof ProductIngredientFormData
            
            if (!ingredientsMap.has(index)) {
              ingredientsMap.set(index, {})
            }
            
            const ingredient = ingredientsMap.get(index)!
            ingredient[field] = value as string
          }
        })
        
        data.ingredients = Array.from(ingredientsMap.values())
          .filter(ingredient => ingredient.ingredientId && ingredient.quantityNeeded)
          .map(ingredient => ({
            ingredientId: ingredient.ingredientId!,
            quantityNeeded: ingredient.quantityNeeded!,
          }))

        // Validate required fields
        if (!data.name || data.status === undefined) {
          return json({ error: "Name and status are required" }, { status: 400 });
        }

        // COGS per cup validation removed - will be calculated automatically

        // Validate ingredients quantities
        for (const ingredient of data.ingredients) {
          const quantity = parseFloat(ingredient.quantityNeeded);
          if (isNaN(quantity) || quantity <= 0) {
            return json({ error: "All ingredient quantities must be valid positive numbers" }, { status: 400 });
          }
        }

        // Check if product name already exists in business (excluding current product)
        const nameExists = await ProductServiceServer.nameExistsInBusiness(
          data.name, 
          businessId, 
          session.user.id,
          id
        );
        
        if (nameExists) {
          return json({ error: "A product with this name already exists in this business" }, { status: 400 });
        }

        await ProductServiceServer.update(
          id,
          businessId,
          session.user.id,
          {
            ...data,
            isActive: data.status === 'active',
            // cogsPerCup removed - will be calculated automatically
            ingredients: data.ingredients.map(ingredient => ({
              ingredientId: ingredient.ingredientId,
              quantityNeeded: parseFloat(ingredient.quantityNeeded).toString(),
            })),
          }
        );
        
        return json({ success: true });
      }

      case "delete": {
        const id = formData.get("id") as string;
        const businessId = formData.get("businessId") as string;
        
        if (!id || !businessId) {
          return json({ error: "Product ID and Business ID are required" }, { status: 400 });
        }

        await ProductServiceServer.delete(id, businessId, session.user.id);
        return json({ success: true });
      }

      default:
        return json({ error: "Invalid action" }, { status: 400 });
    }
  } catch (error) {
    console.error("Product action failed:", error);
    const errorMessage = error instanceof Error ? error.message : "Operation failed";
    return json({ error: errorMessage }, { status: 500 });
  }
}

// Create protected route with automatic RBAC
const { loader, action } = createProtectedRoute(
  RBAC_CONFIGS.products,
  {
    loader: productsLoader,
    action: productsAction,
  }
);

export { loader, action };
