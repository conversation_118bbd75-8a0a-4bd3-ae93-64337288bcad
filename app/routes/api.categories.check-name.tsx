import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { getSession } from "~/lib/auth.session.server";
import { CategoryServiceServer } from "~/lib/services/categoryService.server";
import type { CategoryType } from "~/lib/types/inventory";

export async function loader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const url = new URL(request.url);
  const businessId = url.searchParams.get("businessId");
  const name = url.searchParams.get("name");
  const type = url.searchParams.get("type") as CategoryType;
  const excludeId = url.searchParams.get("excludeId");
  
  if (!businessId) {
    return json({ error: "Business ID is required" }, { status: 400 });
  }

  if (!name) {
    return json({ error: "Category name is required" }, { status: 400 });
  }

  if (!type || !['ingredient', 'product', 'supplier', 'customer'].includes(type)) {
    return json({ error: "Valid category type is required" }, { status: 400 });
  }

  try {
    const exists = await CategoryServiceServer.nameExistsInBusiness(
      name,
      type,
      businessId,
      session.user.id,
      excludeId || undefined
    );
    
    return json({ exists });
  } catch (error) {
    console.error("Failed to check category name:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to check category name";
    return json({ error: errorMessage }, { status: 500 });
  }
}
