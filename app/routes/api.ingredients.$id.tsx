import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { getSession } from "~/lib/auth.session.server";
import { IngredientServiceServer } from "~/lib/services/ingredientService.server";

export async function loader({ request, params }: LoaderFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const { id } = params;
  if (!id) {
    return json({ error: "Ingredient ID is required" }, { status: 400 });
  }

  const url = new URL(request.url);
  const businessId = url.searchParams.get("businessId");
  
  if (!businessId) {
    return json({ error: "Business ID is required" }, { status: 400 });
  }

  try {
    const ingredient = await IngredientServiceServer.getByIdAndBusiness(
      id, 
      businessId
    );
    
    if (!ingredient) {
      return json({ error: "Ingredient not found" }, { status: 404 });
    }
    
    return json({ ingredient });
  } catch (error) {
    console.error("Failed to load ingredient:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to load ingredient";
    return json({ error: errorMessage }, { status: 500 });
  }
}
