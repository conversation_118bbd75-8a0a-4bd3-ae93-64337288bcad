import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { getSession } from "~/lib/auth.session.server";
import { ProductServiceServer } from "~/lib/services/productService.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const url = new URL(request.url);
  const businessId = url.searchParams.get("businessId");
  const name = url.searchParams.get("name");
  
  if (!businessId || !name) {
    return json({ error: "Business ID and name are required" }, { status: 400 });
  }

  const excludeId = url.searchParams.get("excludeId") || undefined;

  try {
    const exists = await ProductServiceServer.nameExistsInBusiness(
      name,
      businessId, 
      session.user.id,
      excludeId
    );
    
    return json({ exists });
  } catch (error) {
    console.error("Failed to check product name:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to check product name";
    return json({ error: errorMessage }, { status: 500 });
  }
}
