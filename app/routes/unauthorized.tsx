import { Link, useSearchParams } from '@remix-run/react';
import { useTranslation } from 'react-i18next';
import { AlertTriangle, Home, ArrowLeft, ExternalLink } from 'lucide-react';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';

export default function UnauthorizedPage() {
  const { t } = useTranslation('auth');
  const [searchParams] = useSearchParams();
  const redirectTo = searchParams.get('redirectTo');

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 px-4 relative">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
            <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {t('errors.authorization.accessDenied')}
          </CardTitle>
          <CardDescription className="text-gray-600 dark:text-gray-400">
            {t('errors.authorization.accessDeniedDescription')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {redirectTo && (
            <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <p className="text-sm text-blue-800 dark:text-blue-200 text-center">
                {t('errors.authorization.originalUrlAttempted', { url: redirectTo })}
              </p>
            </div>
          )}

          <p className="text-sm text-gray-500 dark:text-gray-400 text-center">
            {t('errors.authorization.insufficientPermissions')}
          </p>

          <div className="flex flex-col gap-2">
            <Button asChild className="w-full">
              <Link to="/" className="flex items-center justify-center gap-2">
                <Home className="h-4 w-4" />
                {t('errors.authorization.goToDashboard')}
              </Link>
            </Button>

            {redirectTo && (
              <Button asChild variant="outline" className="w-full">
                <Link to={redirectTo} className="flex items-center justify-center gap-2">
                  <ExternalLink className="h-4 w-4" />
                  Try Again
                </Link>
              </Button>
            )}

            <Button
              variant="outline"
              className="w-full flex items-center justify-center gap-2"
              onClick={() => window.history.back()}
            >
              <ArrowLeft className="h-4 w-4" />
              {t('errors.authorization.goBack')}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}