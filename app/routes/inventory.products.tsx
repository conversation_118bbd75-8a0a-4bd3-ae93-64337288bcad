import {
  json,
  type LoaderFunctionArgs,
  type MetaFunction,
} from "@remix-run/node";
import {
  useLoaderData,
  useRevalidator,
  useSearchParams,
} from "@remix-run/react";
import { useState, useCallback, useMemo, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Plus, Search, Filter, X, Shield } from "lucide-react";

import { getSession } from "~/lib/auth.session.server";
import { ProductServiceServer } from "~/lib/services/productService.server";
import {
  createProtectedLoader,
  RBAC_CONFIGS,
} from "~/lib/middleware/autoRBAC.server";
import { withRBAC } from "~/lib/utils/rbacLoader.server";
import { useRBAC } from "~/lib/hooks/useRBAC";
import { useRouteProtection } from "~/lib/hooks/useRouteProtection";

import { BusinessServiceServer } from "~/lib/services/businessService.server";
import { CategoryService } from "~/lib/services/categoryService";
import { ProductCreateSheet } from "~/components/inventory/ProductCreateSheet";
import { ProductDetailSheet } from "~/components/inventory/ProductDetailSheet";
import type {
  ProductWithCategory,
  CategoryWithChildren,
} from "~/lib/types/inventory";
import type { RBACData } from "~/lib/utils/rbacLoader.server";

import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import { ProductManagementSheet } from "~/components/inventory/ProductManagementSheet";
import { PermissionsSheet } from "~/components/inventory/PermissionsSheet";
import { getPermissionConfig } from "~/lib/utils/permissionConfigs";

export const meta: MetaFunction = () => {
  return [
    { title: "Products - KWACI Grow" },
    { name: "description", content: "Manage your business products" },
  ];
};

interface LoaderData {
  products: ProductWithCategory[];
  businessId: string;
  businessName: string;
  rbac?: RBACData;
}

async function getBusinessId(args: LoaderFunctionArgs): Promise<string> {
  const session = await getSession(args.request);

  if (!session?.user) {
    throw new Response("Unauthorized", { status: 401 });
  }

  // First, try to get business ID from query parameters (client-side business switcher)
  const url = new URL(args.request.url);
  const businessIdFromQuery = url.searchParams.get('businessId');

  if (businessIdFromQuery) {
    // Verify user has access to this business
    const business = await BusinessServiceServer.getByIdAndUser(businessIdFromQuery, session.user.id);
    if (business) {
      return business.id;
    }
  }

  // Fallback to user's default business
  const business = await BusinessServiceServer.getDefaultByUser(
    session.user.id
  );

  if (!business) {
    throw new Response("No business found. Please create a business first.", {
      status: 404,
    });
  }

  return business.id;
}

async function productsLoader(args: LoaderFunctionArgs) {
  const { request } = args;
  const session = await getSession(request);

  if (!session?.user) {
    throw new Response("Unauthorized", { status: 401 });
  }

  try {
    // Get business ID using the same logic as the RBAC middleware
    const businessId = await getBusinessId(args);

    // Get business details for display
    const business = await BusinessServiceServer.getByIdAndUser(businessId, session.user.id);

    if (!business) {
      throw new Response("Business not found or access denied.", {
        status: 404,
      });
    }

    // Get products for the business
    const products = await ProductServiceServer.getAllByBusiness(
      businessId,
      session.user.id
    );

    // Get RBAC data
    const rbacData = await withRBAC(request, businessId);

    return json<LoaderData>({
      products,
      businessId: businessId,
      businessName: business.name,
      rbac: rbacData || undefined,
    });
  } catch (error) {
    console.error("Failed to load products:", error);
    throw new Response("Failed to load products", { status: 500 });
  }
}

export const loader = createProtectedLoader(
  {
    ...RBAC_CONFIGS.products,
    getBusinessId,
  },
  productsLoader
);

export default function ProductsPage() {
  // Protect against client-side navigation
  useRouteProtection({
    permissions: ["products.read"],
    preserveUrl: true,
    redirectTo: "/unauthorized",
  });

  const { products, businessId, businessName } = useLoaderData<LoaderData>();
  const {
    products: productsPermissions,
    categories: categoriesPermissions,
    cogs: cogsPermissions,
    hasPermission,
  } = useRBAC();
  const { t } = useTranslation("inventory");
  const revalidator = useRevalidator();
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategoryId, setSelectedCategoryId] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [categories, setCategories] = useState<CategoryWithChildren[]>([]);
  const [categoriesLoading, setCategoriesLoading] = useState(false);
  const [showPermissionsSheet, setShowPermissionsSheet] = useState(false);

  // Refs to track operations and prevent unwanted drawer opening
  const editOperationJustCompleted = useRef(false);
  const sheetInteractionInProgress = useRef(false);

  // Get the product ID from URL params for drawer state
  const detailProductId = searchParams.get("detail");
  const selectedProduct = useMemo(
    () => products?.find((prod) => prod.id === detailProductId) || null,
    [products, detailProductId]
  );

  // Set up mutation observer to detect sheet state changes
  useEffect(() => {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (
          mutation.type === "attributes" &&
          mutation.attributeName === "data-state"
        ) {
          const target = mutation.target as Element;
          const dataState = target.getAttribute("data-state");

          if (dataState === "open") {
            sheetInteractionInProgress.current = true;
          } else if (dataState === "closed") {
            sheetInteractionInProgress.current = true;
            // Clear the flag after a delay to prevent drawer opening immediately after sheet closes
            setTimeout(() => {
              sheetInteractionInProgress.current = false;
            }, 500);
          }
        }
      });
    });

    // Observe the document for sheet state changes
    observer.observe(document.body, {
      attributes: true,
      subtree: true,
      attributeFilter: ["data-state"],
    });

    return () => {
      observer.disconnect();
    };
  }, []);

  // Load categories for filtering - only if user has categories.read permission
  useEffect(() => {
    if (!categoriesPermissions.canRead) {
      return;
    }

    const loadCategories = async () => {
      setCategoriesLoading(true);
      try {
        const hierarchicalCategories =
          await CategoryService.getHierarchicalCategories(
            businessId,
            "product"
          );
        setCategories(hierarchicalCategories);
      } catch (error) {
        console.error("Failed to load categories:", error);
      } finally {
        setCategoriesLoading(false);
      }
    };

    loadCategories();
  }, [businessId, categoriesPermissions.canRead]);

  // Drawer state management - memoized to prevent unnecessary re-renders
  const openProductDetail = useCallback(
    (productId: string) => {
      // Don't open drawer if an edit operation just completed or sheet interaction is in progress
      if (
        editOperationJustCompleted.current ||
        sheetInteractionInProgress.current
      ) {
        editOperationJustCompleted.current = false;
        sheetInteractionInProgress.current = false;
        return;
      }

      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set("detail", productId);
      setSearchParams(newSearchParams);
    },
    [searchParams, setSearchParams]
  );

  const closeProductDetail = useCallback(() => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.delete("detail");
    setSearchParams(newSearchParams);
  }, [searchParams, setSearchParams]);

  const handleDeleteProduct = useCallback(
    async (product: ProductWithCategory) => {
      if (confirm(t("products.delete.confirm"))) {
        try {
          const response = await fetch(
            `/api/products?id=${product.id}&businessId=${businessId}`,
            {
              method: "DELETE",
            }
          );

          if (response.ok) {
            closeProductDetail();
            revalidator.revalidate();
          } else {
            console.error("Failed to delete product");
          }
        } catch (error) {
          console.error("Error deleting product:", error);
        }
      }
    },
    [t, businessId, closeProductDetail, revalidator]
  );

  // Stable product change handler that doesn't interfere with drawer state
  const handleProductChange = useCallback(() => {
    // Mark that an edit operation just completed
    editOperationJustCompleted.current = true;

    // Clear the flag after a short delay to allow for any pending events
    setTimeout(() => {
      editOperationJustCompleted.current = false;
    }, 500);

    // Revalidate the data
    revalidator.revalidate();
  }, [revalidator]);

  // Filter products based on search term and category - memoized to prevent unnecessary re-calculations
  const filteredProducts = useMemo(() => {
    if (!products) return [];
    return products.filter((product) => {
      // Search filter
      const matchesSearch =
        !searchTerm ||
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (product.description &&
          product.description
            .toLowerCase()
            .includes(searchTerm.toLowerCase())) ||
        (product.categoryName &&
          product.categoryName
            .toLowerCase()
            .includes(searchTerm.toLowerCase()));

      // Category filter - only apply if user has categories.read permission
      const matchesCategory =
        !categoriesPermissions.canRead ||
        !selectedCategoryId ||
        product.categoryId === selectedCategoryId;

      return matchesSearch && matchesCategory;
    });
  }, [products, searchTerm, selectedCategoryId, categoriesPermissions.canRead]);

  const formatCurrency = useCallback((amount: string | number) => {
    const num = typeof amount === "string" ? parseFloat(amount) : amount;
    if (isNaN(num)) return "Rp 0";
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
    }).format(num);
  }, []);

  // Filter management functions
  const toggleFilters = useCallback(() => {
    setShowFilters((prev) => !prev);
  }, []);

  const clearFilters = useCallback(() => {
    if (categoriesPermissions.canRead) {
      setSelectedCategoryId("");
    }
    setSearchTerm("");
  }, [categoriesPermissions.canRead]);

  const handleCategoryFilterChange = useCallback(
    (categoryId: string) => {
      if (categoriesPermissions.canRead) {
        setSelectedCategoryId(categoryId);
      }
    },
    [categoriesPermissions.canRead]
  );

  // Check if any filters are active
  const hasActiveFilters = useMemo(() => {
    const categoryFilterActive =
      categoriesPermissions.canRead && selectedCategoryId !== "";
    return categoryFilterActive || searchTerm !== "";
  }, [selectedCategoryId, searchTerm, categoriesPermissions.canRead]);

  // Get selected category name and color for display
  const selectedCategory = useMemo(() => {
    if (!categoriesPermissions.canRead || !selectedCategoryId) return null;
    const findCategory = (
      cats: CategoryWithChildren[]
    ): CategoryWithChildren | null => {
      for (const cat of cats) {
        if (cat.id === selectedCategoryId) return cat;
        if (cat.children && cat.children.length > 0) {
          const found = findCategory(cat.children);
          if (found) return found;
        }
      }
      return null;
    };
    return findCategory(categories);
  }, [selectedCategoryId, categories, categoriesPermissions.canRead]);

  // Helper function to render categories hierarchically with color indicators
  const renderCategoryOptions = useCallback(
    (categories: CategoryWithChildren[], level = 0): JSX.Element[] => {
      const options: JSX.Element[] = [];

      categories.forEach((category) => {
        const indent = "  ".repeat(level);
        options.push(
          <SelectItem key={category.id} value={category.id}>
            <div className="flex items-center gap-2">
              {category.color && (
                <div
                  className="w-3 h-3 rounded-full flex-shrink-0"
                  style={{ backgroundColor: category.color }}
                />
              )}
              <span>
                {indent}
                {category.name}
              </span>
            </div>
          </SelectItem>
        );

        if (category.children && category.children.length > 0) {
          options.push(...renderCategoryOptions(category.children, level + 1));
        }
      });

      return options;
    },
    []
  );

  // Removed getStatusBadgeVariant - now using isActive boolean

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {t("products.title")}
          </h1>
          <p className="text-muted-foreground">
            {t("products.subtitle", { businessName })}
          </p>
        </div>
        <div className="flex items-center gap-2">
          {productsPermissions.canCreate && (
            <ProductCreateSheet
              businessId={businessId}
              onProductChange={handleProductChange}
            >
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                {t("products.addNew")}
              </Button>
            </ProductCreateSheet>
          )}

          {/* Permissions button - protected by system.view_permissions */}
          {hasPermission("system.view_permissions") && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowPermissionsSheet(true)}
            >
              <Shield className="h-4 w-4 mr-2" />
              {t("common.permissions", "Permissions")}
            </Button>
          )}
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">
            {t("common.searchAndFilter")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Search and Filter Toggle Row */}
            <div className="flex flex-col gap-4 md:flex-row">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={t("products.searchPlaceholder")}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2">
                <Button
                  variant={showFilters ? "default" : "outline"}
                  onClick={toggleFilters}
                >
                  <Filter className="mr-2 h-4 w-4" />
                  {t("common.filters")}
                  {hasActiveFilters && (
                    <Badge
                      variant="secondary"
                      className="ml-2 px-1 py-0 text-xs"
                    >
                      {
                        [
                          categoriesPermissions.canRead &&
                            selectedCategoryId &&
                            "Category",
                          searchTerm && "Search",
                        ].filter(Boolean).length
                      }
                    </Badge>
                  )}
                </Button>
                {hasActiveFilters && (
                  <Button variant="ghost" size="sm" onClick={clearFilters}>
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>

            {/* Filter Controls */}
            {showFilters && (
              <div className="border-t pt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {/* Category Filter - only show if user has categories.read permission */}
                  {categoriesPermissions.canRead && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        {t("products.fields.category")}
                      </label>
                      <Select
                        value={selectedCategoryId || "all"}
                        onValueChange={(value) =>
                          handleCategoryFilterChange(
                            value === "all" ? "" : value
                          )
                        }
                        disabled={categoriesLoading}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue>
                            {categoriesLoading ? (
                              <span className="text-muted-foreground">
                                Loading categories...
                              </span>
                            ) : selectedCategory ? (
                              <div className="flex items-center gap-2">
                                {selectedCategory.color && (
                                  <div
                                    className="w-3 h-3 rounded-full flex-shrink-0"
                                    style={{
                                      backgroundColor: selectedCategory.color,
                                    }}
                                  />
                                )}
                                <Badge variant="outline" className="text-xs">
                                  {selectedCategory.name}
                                </Badge>
                              </div>
                            ) : (
                              <span className="text-muted-foreground">
                                All categories
                              </span>
                            )}
                          </SelectValue>
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All categories</SelectItem>
                          {renderCategoryOptions(categories)}
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Products Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t("products.list")}</CardTitle>
          <CardDescription>
            {t("products.listDescription", { count: filteredProducts.length })}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredProducts.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">
                {searchTerm
                  ? t("products.noSearchResults")
                  : t("products.noProducts")}
              </p>
              {!searchTerm && productsPermissions.canCreate && (
                <ProductCreateSheet
                  businessId={businessId}
                  onProductChange={handleProductChange}
                >
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    {t("products.addFirst")}
                  </Button>
                </ProductCreateSheet>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t("products.fields.name")}</TableHead>
                  <TableHead>{t("products.fields.description")}</TableHead>
                  {categoriesPermissions.canRead && (
                    <TableHead>{t("products.fields.category")}</TableHead>
                  )}
                  {cogsPermissions.canRead && (
                    <TableHead>{t("products.fields.cogsPerCup")}</TableHead>
                  )}
                  <TableHead>{t("products.fields.status")}</TableHead>
                  <TableHead className="text-right">
                    {t("common.actions")}
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredProducts.map((product) => (
                  <TableRow
                    key={product.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={(e) => {
                      // Prevent opening drawer if clicking on interactive elements or during sheet interactions
                      if (
                        e.target instanceof Element &&
                        (e.target.closest("button") ||
                          e.target.closest('[role="button"]') ||
                          e.target.closest("input") ||
                          e.target.closest("textarea") ||
                          e.target.closest("[data-radix-collection-item]") ||
                          e.target.closest("[data-state]") || // Radix UI elements
                          e.target.closest(
                            "[data-radix-popper-content-wrapper]"
                          ) ||
                          e.target.closest(".sheet-content") ||
                          e.target.closest('[role="dialog"]'))
                      ) {
                        return;
                      }

                      // Additional check for sheet interactions
                      if (
                        sheetInteractionInProgress.current ||
                        editOperationJustCompleted.current
                      ) {
                        return;
                      }

                      openProductDetail(product.id);
                    }}
                  >
                    <TableCell className="font-medium">
                      {product.name}
                    </TableCell>
                    <TableCell className="text-muted-foreground">
                      {product.description || "-"}
                    </TableCell>
                    {categoriesPermissions.canRead && product.categoryName && (
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {product.categoryColor && (
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: product.categoryColor }}
                            />
                          )}
                          <Badge variant="outline">
                            {product.categoryName}
                          </Badge>
                        </div>
                      </TableCell>
                    )}
                    {cogsPermissions.canRead && (
                      <TableCell>
                        {product.cogsPerCup
                          ? formatCurrency(product.cogsPerCup)
                          : "-"}
                      </TableCell>
                    )}
                    <TableCell>
                      <Badge
                        variant={product.isActive ? "default" : "secondary"}
                      >
                        {t(
                          `products.status.${
                            product.isActive ? "active" : "inactive"
                          }`
                        )}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      {productsPermissions.canUpdate && (
                        <ProductManagementSheet
                          mode="edit"
                          businessId={businessId}
                          productId={product.id}
                          onProductChange={handleProductChange}
                          allCategories={categories}
                        >
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                            }}
                          >
                            {t("common.edit")}
                          </Button>
                        </ProductManagementSheet>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Product Detail Sheet */}
      <ProductDetailSheet
        product={selectedProduct}
        businessId={businessId}
        isOpen={!!detailProductId}
        onClose={closeProductDetail}
        onDelete={handleDeleteProduct}
      />

      {/* Permissions Sheet */}
      <PermissionsSheet
        open={showPermissionsSheet}
        onOpenChange={setShowPermissionsSheet}
        routeName={getPermissionConfig("products").routeName}
        title={t("permissions.title", "Permissions Information")}
        description={t(
          "permissions.description",
          "View all permissions used in the products module and their purposes."
        )}
        permissions={getPermissionConfig("products").permissions}
      />
    </div>
  );
}
