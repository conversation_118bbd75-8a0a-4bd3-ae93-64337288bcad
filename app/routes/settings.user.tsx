import type { LoaderFunctionArgs } from '@remix-run/node';
import { json } from '@remix-run/node';
import { useLoaderData } from '@remix-run/react';
import { getSession } from '~/lib/auth.session.server';
import { RBACService } from '~/lib/services/rbacService.server';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { User, Shield, Key } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table';

export async function loader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    throw new Response('Unauthorized', { status: 401 });
  }

  const userId = session.user.id;
  
  // Get user's permissions flattened
  const flattenedPermissions = await RBACService.getUserPermissionsFlattened(userId);
  
  return json({
    user: session.user,
    flattenedPermissions
  });
}

export default function UserSettingsPage() {
  const { user, flattenedPermissions } = useLoaderData<typeof loader>();

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <User className="h-8 w-8" />
            User Settings
          </h1>
          <p className="text-muted-foreground">
            View your assigned roles and permissions
          </p>
        </div>
      </div>

      {/* User Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            User Information
          </CardTitle>
          <CardDescription>
            Your account details and basic information
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div className="text-sm font-medium text-muted-foreground">Name</div>
              <p className="text-sm">{user.name}</p>
            </div>
            <div>
              <div className="text-sm font-medium text-muted-foreground">Email</div>
              <p className="text-sm">{user.email}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Assigned Roles */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Assigned Roles
          </CardTitle>
          <CardDescription>
            Roles assigned to you across different business contexts
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* All Roles */}
           {flattenedPermissions.roles.length > 0 ? (
             <div className="flex flex-wrap gap-2">
               {flattenedPermissions.roles.map((roleInfo, index: number) => (
                 <div key={index} className="flex flex-wrap gap-1">
                   {roleInfo.contexts.map((context: string, contextIndex: number) => (
                     <Badge 
                       key={contextIndex} 
                       variant={context === 'Global' ? 'default' : 'secondary'} 
                       className={context === 'Global' ? 'bg-blue-100 text-blue-800 hover:bg-blue-200' : 'bg-green-100 text-green-800 hover:bg-green-200'}
                     >
                       {roleInfo.role} ({context})
                     </Badge>
                   ))}
                 </div>
               ))}
             </div>
           ) : (
             <p className="text-sm text-muted-foreground">No roles assigned</p>
           )}
        </CardContent>
      </Card>

      {/* Permissions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            Permissions
          </CardTitle>
          <CardDescription>
            Detailed permissions granted through your roles, organized by context
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Context Explanation */}
          <div className="mb-6 p-4 bg-muted/50 rounded-lg">
            <h4 className="font-medium mb-2">Understanding Permission Context</h4>
            <div className="text-sm text-muted-foreground space-y-2">
              <p>
                Permissions are organized by their scope:
              </p>
              <ul className="list-disc list-inside space-y-1 ml-2">
                <li>
                  <strong className="text-blue-700">Global:</strong> System-wide permissions that apply across all businesses. 
                  These are typically assigned to super administrators.
                </li>
                <li>
                  <strong className="text-green-700">Business-specific:</strong> Permissions that only apply within a specific business context. 
                  Most user permissions fall into this category.
                </li>
              </ul>
            </div>
          </div>

          {/* All Permissions */}
          {flattenedPermissions.permissions.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Permission</TableHead>
                  <TableHead>Contexts</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {flattenedPermissions.permissions.map((permissionInfo, index: number) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">
                      {permissionInfo.permission}
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {permissionInfo.contexts.map((context: string, contextIndex: number) => (
                           <Badge 
                             key={contextIndex} 
                             variant={context === 'Global' ? 'default' : 'secondary'} 
                             className={context === 'Global' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}
                           >
                             {context}
                           </Badge>
                         ))}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <p className="text-sm text-muted-foreground">No permissions assigned</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}