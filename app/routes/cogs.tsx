import { json, type MetaFunction, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { useTranslation } from "react-i18next";
import { getSession } from "~/lib/auth.session.server";
import { BusinessServiceServer } from "~/lib/services/businessService.server";
import { createProtectedLoader, RBAC_CONFIGS } from "~/lib/middleware/autoRBAC.server";
import { withRBAC } from "~/lib/utils/rbacLoader.server";
import type { RBACData } from "~/lib/utils/rbacLoader.server";
import { usePermissionProtection } from "~/lib/hooks/useRouteProtection";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Calculator } from "lucide-react";
import { COGSCalculator } from "~/components/cogs/COGSCalculator";

export const meta: MetaFunction = () => {
  return [
    { title: "COGS Calculator - KWACI Grow" },
    { name: "description", content: "Calculate Cost of Goods Sold for your products with precision" },
  ];
};

interface LoaderData {
  businessId: string;
  businessName: string;
  rbac: RBACData | null;
}

async function getBusinessId(args: LoaderFunctionArgs): Promise<string> {
  const session = await getSession(args.request);

  if (!session?.user) {
    throw new Response("Unauthorized", { status: 401 });
  }

  // First, try to get business ID from query parameters (client-side business switcher)
  const url = new URL(args.request.url);
  const businessIdFromQuery = url.searchParams.get('businessId');

  if (businessIdFromQuery) {
    // Verify user has access to this business
    const business = await BusinessServiceServer.getByIdAndUser(businessIdFromQuery, session.user.id);
    if (business) {
      return business.id;
    }
  }

  // Fallback to user's default business
  const business = await BusinessServiceServer.getDefaultByUser(session.user.id);

  if (!business) {
    throw new Response("No business found. Please create a business first.", { status: 404 });
  }

  return business.id;
}

// Create protected loader that enforces cogs.read permission
export const loader = createProtectedLoader(
  {
    ...RBAC_CONFIGS.cogs,
    getBusinessId,
  },
  async ({ request }) => {
    const session = await getSession(request);

    if (!session?.user) {
      throw new Response("Unauthorized", { status: 401 });
    }

    // Get user's default business
    const business = await BusinessServiceServer.getDefaultByUser(session.user.id);

    if (!business) {
      throw new Response("No business found. Please create a business first.", { status: 404 });
    }

    // Note: RBAC permission check for 'cogs.read' is handled by createProtectedLoader

    // Include RBAC data for client-side permission checks
    const rbac = await withRBAC(request, business.id);

    return json<LoaderData>({
      businessId: business.id,
      businessName: business.name,
      rbac,
    });
  }
);

export default function COGSPage() {
  const { businessName } = useLoaderData<LoaderData>();
  const { t } = useTranslation(['common', 'cogs']);

  // Client-side authorization check for client-side navigation
  // This ensures users who navigate via client-side routing are also authorized
  usePermissionProtection(['cogs.read']);

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col gap-4">
        <div className="flex items-center gap-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
            <Calculator className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              {t('cogs:title')}
            </h1>
            <p className="text-lg text-muted-foreground">
              {t('cogs:description')} - {businessName}
            </p>
          </div>
        </div>
      </div>

      {/* COGS Calculator Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            {t('cogs:calculator.title')}
          </CardTitle>
          <CardDescription>
            {t('cogs:calculator.description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <COGSCalculator />
        </CardContent>
      </Card>
    </div>
  );
}
