import type { LoaderFunctionArgs } from '@remix-run/node';
import { json } from '@remix-run/node';
import { getSession } from '~/lib/auth.session.server';
import { InvitationService } from '~/lib/services/invitationService.server';

export async function loader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request);
  
  if (!session?.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const count = await InvitationService.getUserInvitationCount(session.user.email);
    return json({ count });
  } catch (error) {
    console.error('Failed to get notification count:', error);
    return json({ count: 0 });
  }
}
