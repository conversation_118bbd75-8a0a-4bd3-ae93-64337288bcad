import type { MetaFunction } from "@remix-run/node";

export const meta: MetaFunction = () => {
  return [
    { title: "Test Breadcrumb - KWACI Grow" },
    { name: "description", content: "Testing breadcrumb functionality" },
  ];
};

export default function TestBreadcrumb() {
  return (
    <div className="flex flex-1 flex-col gap-4 p-4">
      <div className="grid auto-rows-min gap-4 md:grid-cols-3">
        <div className="aspect-video rounded-xl bg-muted/50" />
        <div className="aspect-video rounded-xl bg-muted/50" />
        <div className="aspect-video rounded-xl bg-muted/50" />
      </div>
      <div className="min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min">
        <div className="p-6">
          <h1 className="text-2xl font-bold mb-4">Test Breadcrumb Page</h1>
          <p className="text-muted-foreground">
            This page is used to test the breadcrumb functionality. 
            The breadcrumb should show: KWACI Grow {'>'}  Test Breadcrumb
          </p>
        </div>
      </div>
    </div>
  );
}