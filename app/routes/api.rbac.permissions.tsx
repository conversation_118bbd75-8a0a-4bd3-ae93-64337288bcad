import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from '@remix-run/node';
import { db } from '~/lib/db/connection';
import { permissions, type NewPermission } from '~/lib/db/schema';
import { eq } from 'drizzle-orm';
import { EndpointPermissionService } from '~/lib/services/endpointPermissionService.server';
import { getSession } from '~/lib/auth.session.server';
import { RBACService } from '~/lib/services/rbacService.server';

export async function loader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request);
  if (!session?.user?.id) {
    throw new Response('Unauthorized', { status: 401 });
  }
  
  const url = new URL(request.url);
  const businessId = url.searchParams.get('businessId');

  if (!businessId) {
    throw new Response('Business ID is required', { status: 400 });
  }

  // Check if user has permission to read permissions
  const canReadPermissions = await RBACService.hasPermission(session.user.id, 'rbac_permissions.read', businessId);
  if (!canReadPermissions) {
    throw new Response('Forbidden', { status: 403 });
  }

  try {
    // Get all permissions grouped by resource
    const permissionsGrouped = await EndpointPermissionService.getPermissionsGroupedByResource();
    
    // Get endpoint information
    const endpointsByResource = EndpointPermissionService.getEndpointsByResource();
    
    return json({
      success: true,
      data: {
        permissions: permissionsGrouped,
        endpoints: endpointsByResource,
      },
    });
  } catch (error) {
    console.error('Error fetching permissions:', error);
    return json(
      { success: false, error: 'Failed to fetch permissions' },
      { status: 500 }
    );
  }
}

export async function action({ request }: ActionFunctionArgs) {
  const session = await getSession(request);
  if (!session?.user?.id) {
    return json(
      { success: false, error: 'Unauthorized' },
      { status: 401 }
    );
  }
  
  const formData = await request.formData();
  const intent = formData.get('intent') as string;
  const businessId = formData.get('businessId') as string;

  if (!businessId) {
    return json(
      { success: false, error: 'Business ID is required' },
      { status: 400 }
    );
  }

  try {
    switch (intent) {
      case 'create': {
        // Check if user has permission to create permissions
        const canCreatePermissions = await RBACService.hasPermission(session.user.id, 'rbac_permissions.create', businessId);
        if (!canCreatePermissions) {
          return json(
            { success: false, error: 'You do not have permission to create permissions' },
            { status: 403 }
          );
        }

        const name = formData.get('name') as string;
        const displayName = formData.get('displayName') as string;
        const description = formData.get('description') as string;
        const resource = formData.get('resource') as string;
        const action = formData.get('action') as string;

        if (!name || !displayName || !resource || !action) {
          return json(
            { success: false, error: 'Name, display name, resource, and action are required' },
            { status: 400 }
          );
        }

        // Check if permission already exists
        const existingPermission = await db
          .select()
          .from(permissions)
          .where(eq(permissions.name, name))
          .limit(1);

        if (existingPermission.length > 0) {
          return json(
            { success: false, error: 'Permission with this name already exists' },
            { status: 400 }
          );
        }

        const newPermission: NewPermission = {
          name,
          displayName,
          description: description || `${action} ${resource}`,
          resource,
          action,
          isSystemPermission: false,
        };

        await db.insert(permissions).values(newPermission);

        return json({
          success: true,
          message: 'Permission created successfully',
        });
      }

      case 'update': {
        // Check if user has permission to update permissions
        const canUpdatePermissions = await RBACService.hasPermission(session.user.id, 'rbac_permissions.update', businessId);
        if (!canUpdatePermissions) {
          return json(
            { success: false, error: 'You do not have permission to update permissions' },
            { status: 403 }
          );
        }

        const permissionId = formData.get('permissionId') as string;
        const displayName = formData.get('displayName') as string;
        const description = formData.get('description') as string;

        if (!permissionId || !displayName) {
          return json(
            { success: false, error: 'Permission ID and display name are required' },
            { status: 400 }
          );
        }

        // Check if permission exists and is not a system permission
        const existingPermission = await db
          .select()
          .from(permissions)
          .where(eq(permissions.id, permissionId))
          .limit(1);

        if (existingPermission.length === 0) {
          return json(
            { success: false, error: 'Permission not found' },
            { status: 404 }
          );
        }

        if (existingPermission[0].isSystemPermission) {
          return json(
            { success: false, error: 'Cannot update system permissions' },
            { status: 400 }
          );
        }

        await db
          .update(permissions)
          .set({
            displayName,
            description: description || existingPermission[0].description,
          })
          .where(eq(permissions.id, permissionId));

        return json({
          success: true,
          message: 'Permission updated successfully',
        });
      }

      case 'delete': {
        // Check if user has permission to delete permissions
        const canDeletePermissions = await RBACService.hasPermission(session.user.id, 'rbac_permissions.delete', businessId);
        if (!canDeletePermissions) {
          return json(
            { success: false, error: 'You do not have permission to delete permissions' },
            { status: 403 }
          );
        }

        const permissionId = formData.get('permissionId') as string;

        if (!permissionId) {
          return json(
            { success: false, error: 'Permission ID is required' },
            { status: 400 }
          );
        }

        // Check if permission exists and is not a system permission
        const existingPermission = await db
          .select()
          .from(permissions)
          .where(eq(permissions.id, permissionId))
          .limit(1);

        if (existingPermission.length === 0) {
          return json(
            { success: false, error: 'Permission not found' },
            { status: 404 }
          );
        }

        if (existingPermission[0].isSystemPermission) {
          return json(
            { success: false, error: 'Cannot delete system permissions' },
            { status: 400 }
          );
        }

        await db.delete(permissions).where(eq(permissions.id, permissionId));

        return json({
          success: true,
          message: 'Permission deleted successfully',
        });
      }

      case 'seed-endpoint-permissions': {
        // Check if user has permission to create permissions
        const canCreatePermissions = await RBACService.hasPermission(session.user.id, 'rbac_permissions.create', businessId);
        if (!canCreatePermissions) {
          return json(
            { success: false, error: 'You do not have permission to seed permissions' },
            { status: 403 }
          );
        }

        await EndpointPermissionService.seedEndpointPermissions();

        return json({
          success: true,
          message: 'Endpoint permissions seeded successfully',
        });
      }

      default:
        return json(
          { success: false, error: 'Invalid intent' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error in permissions action:', error);
    return json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}