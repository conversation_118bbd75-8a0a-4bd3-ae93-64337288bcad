import { db } from './app/lib/db/connection';
import { businesses, userRoles, roles, users } from './app/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { RBACService } from './app/lib/services/rbacService.server';
import { BusinessServiceServer } from './app/lib/services/businessService.server';

async function debugTestUser() {
  const testUserId = '3f92d9b7-0758-43b6-9c5e-a5e8d976400c';
  
  console.log('🔍 Debugging test user:', testUserId);
  
  // Check if user exists
  const [user] = await db.select().from(users).where(eq(users.id, testUserId)).limit(1);
  console.log('\n👤 User:', user ? `${user.name} (${user.email})` : 'NOT FOUND');
  
  if (!user) {
    console.log('❌ User not found!');
    return;
  }
  
  // Check all businesses
  console.log('\n🏢 All businesses in database:');
  const allBusinesses = await db.select().from(businesses);
  allBusinesses.forEach((b: any, i: number) => {
    console.log(`  ${i + 1}. ${b.name} (ID: ${b.id}, Owner: ${b.userId})`);
  });
  
  // Check businesses owned by test user
  console.log('\n🏢 Businesses owned by test user:');
  const ownedBusinesses = await db.select().from(businesses).where(eq(businesses.userId, testUserId));
  if (ownedBusinesses.length === 0) {
    console.log('  ❌ No businesses owned by test user');
  } else {
    ownedBusinesses.forEach((b: any, i: number) => {
      console.log(`  ${i + 1}. ${b.name} (ID: ${b.id})`);
    });
  }
  
  // Check user roles
  console.log('\n👥 User roles:');
  const testUserRoles = await db.select({
    role: roles,
    userRole: userRoles,
    business: businesses
  })
  .from(userRoles)
  .innerJoin(roles, eq(userRoles.roleId, roles.id))
  .leftJoin(businesses, eq(userRoles.businessId, businesses.id))
  .where(and(
    eq(userRoles.userId, testUserId),
    eq(userRoles.isActive, true)
  ));
  
  if (testUserRoles.length === 0) {
    console.log('  ❌ No active roles found for test user');
  } else {
    testUserRoles.forEach((r: any, i: number) => {
      console.log(`  ${i + 1}. Role: ${r.role.name}, Business: ${r.business?.name || 'SYSTEM-WIDE'} (ID: ${r.userRole.businessId || 'N/A'})`);
    });
  }
  
  // Check businesses accessible via RBAC
  console.log('\n🔐 Businesses accessible via RBAC:');
  try {
    const accessibleBusinesses = await RBACService.getUserBusinesses(testUserId);
    if (accessibleBusinesses.length === 0) {
      console.log('  ❌ No businesses accessible via RBAC');
    } else {
      accessibleBusinesses.forEach((b: any, i: number) => {
        console.log(`  ${i + 1}. ${b.name} (ID: ${b.id})`);
      });
    }
  } catch (error: any) {
    console.log('  ❌ Error getting accessible businesses:', error.message);
  }
  
  // Check default business
  console.log('\n🎯 Default business:');
  try {
    const defaultBusiness = await BusinessServiceServer.getDefaultByUser(testUserId);
    if (!defaultBusiness) {
      console.log('  ❌ No default business found');
    } else {
      console.log(`  ✅ ${defaultBusiness.name} (ID: ${defaultBusiness.id})`);
    }
  } catch (error: any) {
    console.log('  ❌ Error getting default business:', error.message);
  }
  
  // Check permissions for first accessible business
  const accessibleBusinesses = await RBACService.getUserBusinesses(testUserId);
  if (accessibleBusinesses.length > 0) {
    const firstBusiness = accessibleBusinesses[0];
    console.log(`\n🔑 Permissions for ${firstBusiness.name}:`);
    try {
      const permissions = await RBACService.getUserPermissions(testUserId, firstBusiness.id);
      console.log('  Roles:', permissions.roles);
      console.log('  Permissions:', permissions.permissions);
      console.log('  Has categories.read:', permissions.permissions.includes('categories.read'));
    } catch (error: any) {
      console.log('  ❌ Error getting permissions:', error.message);
    }
  }
}

debugTestUser().catch(console.error).finally(() => process.exit(0));