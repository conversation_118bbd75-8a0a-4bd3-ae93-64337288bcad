# Internationalization (i18n) Implementation

This document describes the internationalization implementation using react-i18next in the KWACI Grow application.

## Overview

The application now supports two languages:
- **English (en)** - Default/fallback language
- **Bahasa Indonesia (id)** - Secondary language

## Implementation Details

### Dependencies Added
- `react-i18next` - React integration for i18next
- `i18next` - Core internationalization framework
- `i18next-browser-languagedetector` - Language detection and persistence

### File Structure

```
app/
├── lib/
│   └── i18n.ts                 # i18n configuration
├── locales/
│   ├── en/                     # English translations
│   │   ├── common.json         # Shared UI elements
│   │   ├── auth.json           # Authentication related
│   │   ├── navigation.json     # Sidebar, breadcrumbs
│   │   └── dashboard.json      # Dashboard specific
│   └── id/                     # Indonesian translations
│       ├── common.json
│       ├── auth.json
│       ├── navigation.json
│       └── dashboard.json
└── components/
    └── ui/
        ├── language-switcher.tsx  # Enhanced language switcher
        └── loading-spinner.tsx    # Loading component
```

### Key Features

1. **Namespace-based Organization**: Translations are organized by feature/component
2. **Language Detection**: Automatically detects user's preferred language
3. **Persistence**: Language preference is saved in localStorage
4. **Dynamic Language Switching**: Users can toggle between languages
5. **Fallback Support**: Falls back to English if translation is missing
6. **SSR Compatible**: Works with Remix's server-side rendering

### Configuration

The i18n configuration is in `app/lib/i18n.ts`:
- Fallback language: English
- Default namespace: 'common'
- Detection order: localStorage → navigator language
- Suspense enabled for loading translations

### Components Updated

1. **LanguageSwitcher**: Enhanced to use react-i18next's changeLanguage
2. **Root Layout**: Wrapped with I18nextProvider and Suspense
3. **HeaderContent**: Breadcrumbs and auth buttons
4. **AppSidebar**: Navigation items and project names
5. **Dashboard**: All user-facing text
6. **Navigation Components**: Menu items and actions

## Usage

### Basic Translation
```tsx
import { useTranslation } from 'react-i18next';

function MyComponent() {
  const { t } = useTranslation('common');
  return <h1>{t('appName')}</h1>;
}
```

### Multiple Namespaces
```tsx
const { t } = useTranslation(['common', 'dashboard']);
return (
  <div>
    <h1>{t('common:appName')}</h1>
    <p>{t('dashboard:sections.currentBusiness')}</p>
  </div>
);
```

### Interpolation
```tsx
const { t } = useTranslation('common');
return <p>{t('messages.welcome', { name: user.name })}</p>;
```

### Language Switching
```tsx
const { i18n } = useTranslation();
const switchLanguage = () => {
  i18n.changeLanguage(i18n.language === 'en' ? 'id' : 'en');
};
```

## Translation Namespaces

### common.json
- App name and branding
- Common buttons and actions
- Form labels
- General messages

### auth.json
- Authentication forms
- Login/register text
- User account related

### navigation.json
- Sidebar navigation
- Menu items
- Breadcrumbs
- Project actions

### dashboard.json
- Dashboard-specific content
- Business management
- Quick actions
- Getting started guides

## Language Switcher

The language switcher is located in the header and:
- Shows current language state
- Provides tooltips in current language
- Persists selection across sessions
- Updates all UI text immediately

## Best Practices

1. **Always use translation keys** instead of hardcoded text
2. **Organize by feature** using appropriate namespaces
3. **Provide meaningful key names** that describe the content
4. **Use interpolation** for dynamic content
5. **Test both languages** to ensure completeness
6. **Keep translations consistent** across similar contexts

## Adding New Translations

1. Add the key to the appropriate namespace in both `en/` and `id/` folders
2. Use the translation in your component with `useTranslation`
3. Test both languages to ensure proper display

## Future Enhancements

1. **Additional Languages**: Easy to add more languages by creating new locale folders
2. **Pluralization**: i18next supports complex pluralization rules
3. **Date/Number Formatting**: Can be integrated with locale-specific formatting
4. **Translation Management**: Consider tools like Crowdin or Lokalise for larger teams
5. **SEO**: Implement language-specific meta tags and URLs
