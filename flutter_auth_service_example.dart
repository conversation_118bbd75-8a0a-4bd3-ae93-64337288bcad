/**
 * Complete Flutter Authentication Service Example
 * 
 * This file demonstrates how to integrate with your Better Auth API
 * from a Flutter mobile application.
 * 
 * Dependencies to add to pubspec.yaml:
 * dependencies:
 *   http: ^1.1.0
 *   flutter_secure_storage: ^9.0.0
 *   shared_preferences: ^2.2.2
 */

import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

// Models
class User {
  final String id;
  final String email;
  final String name;
  final String? image;
  final bool emailVerified;
  final DateTime createdAt;
  final DateTime updatedAt;

  User({
    required this.id,
    required this.email,
    required this.name,
    this.image,
    required this.emailVerified,
    required this.createdAt,
    required this.updatedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      email: json['email'],
      name: json['name'],
      image: json['image'],
      emailVerified: json['emailVerified'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }
}

class AuthSession {
  final String id;
  final String userId;
  final String token;
  final DateTime expiresAt;
  final String? ipAddress;
  final String? userAgent;

  AuthSession({
    required this.id,
    required this.userId,
    required this.token,
    required this.expiresAt,
    this.ipAddress,
    this.userAgent,
  });

  factory AuthSession.fromJson(Map<String, dynamic> json) {
    return AuthSession(
      id: json['id'],
      userId: json['userId'],
      token: json['token'],
      expiresAt: DateTime.parse(json['expiresAt']),
      ipAddress: json['ipAddress'],
      userAgent: json['userAgent'],
    );
  }
}

class AuthResult {
  final User? user;
  final AuthSession? session;
  final String? error;
  final String? errorCode;

  AuthResult({this.user, this.session, this.error, this.errorCode});

  bool get isSuccess => user != null && session != null;
  bool get hasError => error != null;
}

// Custom exceptions
class AuthException implements Exception {
  final String message;
  final String? code;
  final int? statusCode;

  AuthException(this.message, {this.code, this.statusCode});

  @override
  String toString() => 'AuthException: $message${code != null ? ' ($code)' : ''}';
}

// Main Authentication Service
class AuthService {
  static const String _baseUrl = 'https://your-domain.com/api/auth'; // Update this
  static const String _tokenKey = 'session_token';
  
  // Secure storage for tokens
  static const _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: IOSAccessibility.first_unlock_this_device,
    ),
  );

  // Singleton pattern
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  // Current session cache
  AuthResult? _currentSession;

  // Get stored session token
  Future<String?> getSessionToken() async {
    try {
      return await _secureStorage.read(key: _tokenKey);
    } catch (e) {
      print('Error reading session token: $e');
      return null;
    }
  }

  // Store session token securely
  Future<void> setSessionToken(String token) async {
    try {
      await _secureStorage.write(key: _tokenKey, value: token);
    } catch (e) {
      print('Error storing session token: $e');
    }
  }

  // Remove session token
  Future<void> removeSessionToken() async {
    try {
      await _secureStorage.delete(key: _tokenKey);
      _currentSession = null;
    } catch (e) {
      print('Error removing session token: $e');
    }
  }

  // Get HTTP headers with authentication
  Future<Map<String, String>> _getHeaders() async {
    final token = await getSessionToken();
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  // Handle HTTP errors and convert to AuthException
  AuthException _handleHttpError(http.Response response) {
    try {
      final errorData = jsonDecode(response.body);
      return AuthException(
        errorData['message'] ?? 'Unknown error occurred',
        code: errorData['code'],
        statusCode: response.statusCode,
      );
    } catch (e) {
      return AuthException(
        'HTTP ${response.statusCode}: ${response.reasonPhrase}',
        statusCode: response.statusCode,
      );
    }
  }

  // User Registration
  Future<AuthResult> register({
    required String email,
    required String password,
    required String name,
    String? image,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/sign-up/email'),
        headers: await _getHeaders(),
        body: jsonEncode({
          'email': email,
          'password': password,
          'name': name,
          if (image != null) 'image': image,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        
        final user = User.fromJson(data['user']);
        final session = AuthSession.fromJson(data['session']);
        
        // Store session token
        await setSessionToken(session.token);
        
        // Cache the session
        _currentSession = AuthResult(user: user, session: session);
        
        return _currentSession!;
      } else {
        final exception = _handleHttpError(response);
        return AuthResult(error: exception.message, errorCode: exception.code);
      }
    } on SocketException {
      return AuthResult(error: 'No internet connection', errorCode: 'NETWORK_ERROR');
    } catch (e) {
      return AuthResult(error: 'Network error: $e', errorCode: 'UNKNOWN_ERROR');
    }
  }

  // User Login
  Future<AuthResult> login({
    required String email,
    required String password,
    bool rememberMe = true,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/sign-in/email'),
        headers: await _getHeaders(),
        body: jsonEncode({
          'email': email,
          'password': password,
          'rememberMe': rememberMe,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        
        final user = User.fromJson(data['user']);
        final session = AuthSession.fromJson(data['session']);
        
        // Store session token
        await setSessionToken(session.token);
        
        // Cache the session
        _currentSession = AuthResult(user: user, session: session);
        
        return _currentSession!;
      } else {
        final exception = _handleHttpError(response);
        return AuthResult(error: exception.message, errorCode: exception.code);
      }
    } on SocketException {
      return AuthResult(error: 'No internet connection', errorCode: 'NETWORK_ERROR');
    } catch (e) {
      return AuthResult(error: 'Network error: $e', errorCode: 'UNKNOWN_ERROR');
    }
  }

  // Get Current Session
  Future<AuthResult?> getCurrentSession({bool forceRefresh = false}) async {
    // Return cached session if available and not forcing refresh
    if (_currentSession != null && !forceRefresh) {
      return _currentSession;
    }

    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/get-session'),
        headers: await _getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        
        final user = User.fromJson(data['user']);
        final session = AuthSession.fromJson(data['session']);
        
        // Cache the session
        _currentSession = AuthResult(user: user, session: session);
        
        return _currentSession;
      } else if (response.statusCode == 401) {
        // Session expired, remove token
        await removeSessionToken();
        return null;
      } else {
        throw _handleHttpError(response);
      }
    } on SocketException {
      // Return cached session if network is unavailable
      return _currentSession;
    } catch (e) {
      print('Error getting current session: $e');
      return null;
    }
  }

  // Logout
  Future<void> logout() async {
    try {
      // Try to logout on server
      await http.post(
        Uri.parse('$_baseUrl/sign-out'),
        headers: await _getHeaders(),
      );
    } catch (e) {
      print('Error during server logout: $e');
    } finally {
      // Always remove local token and cache
      await removeSessionToken();
    }
  }

  // Request Password Reset
  Future<bool> requestPasswordReset({
    required String email,
    String? redirectTo,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/forget-password'),
        headers: await _getHeaders(),
        body: jsonEncode({
          'email': email,
          if (redirectTo != null) 'redirectTo': redirectTo,
        }),
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error requesting password reset: $e');
      return false;
    }
  }

  // Reset Password
  Future<bool> resetPassword({
    required String token,
    required String newPassword,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/reset-password'),
        headers: await _getHeaders(),
        body: jsonEncode({
          'token': token,
          'password': newPassword,
        }),
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error resetting password: $e');
      return false;
    }
  }

  // Check if user is authenticated
  Future<bool> isAuthenticated() async {
    final session = await getCurrentSession();
    return session?.isSuccess ?? false;
  }

  // Get current user (convenience method)
  Future<User?> getCurrentUser() async {
    final session = await getCurrentSession();
    return session?.user;
  }
}

// Error message helper
class AuthErrorMessages {
  static Map<String, String> getErrorMessage(String? errorCode) {
    switch (errorCode) {
      case 'USER_ALREADY_EXISTS':
        return {
          'title': 'Account Exists',
          'message': 'An account with this email already exists. Please try signing in instead.',
        };
      case 'INVALID_CREDENTIALS':
        return {
          'title': 'Invalid Credentials',
          'message': 'Please check your email and password and try again.',
        };
      case 'EMAIL_NOT_VERIFIED':
        return {
          'title': 'Email Not Verified',
          'message': 'Please verify your email address before signing in.',
        };
      case 'WEAK_PASSWORD':
        return {
          'title': 'Weak Password',
          'message': 'Please choose a stronger password with at least 8 characters.',
        };
      case 'NETWORK_ERROR':
        return {
          'title': 'Connection Error',
          'message': 'Please check your internet connection and try again.',
        };
      case 'RATE_LIMITED':
        return {
          'title': 'Too Many Attempts',
          'message': 'Please wait a moment before trying again.',
        };
      default:
        return {
          'title': 'Error',
          'message': 'An unexpected error occurred. Please try again.',
        };
    }
  }
}
