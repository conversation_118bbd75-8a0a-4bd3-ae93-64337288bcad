import { db } from './app/lib/db/connection';
import { roles, permissions, rolePermissions } from './app/lib/db/schema';
import { eq, and } from 'drizzle-orm';

async function removeCategoriesPermissionFromTestRole() {
  console.log('🔧 Removing categories.read permission from TEST_READ_CREATE_INGREDIENT role...');
  
  // Find the TEST_READ_CREATE_INGREDIENT role
  const [role] = await db.select().from(roles).where(eq(roles.name, 'TEST_READ_CREATE_INGREDIENT')).limit(1);
  
  if (!role) {
    console.log('❌ TEST_READ_CREATE_INGREDIENT role not found!');
    return;
  }
  
  console.log('✅ Found role:', role.name, '(ID:', role.id + ')');
  
  // Find the categories.read permission
  const [permission] = await db.select().from(permissions).where(eq(permissions.name, 'categories.read')).limit(1);
  
  if (!permission) {
    console.log('❌ categories.read permission not found!');
    return;
  }
  
  console.log('✅ Found permission:', permission.name, '(ID:', permission.id + ')');
  
  // Check if the role has this permission
  const [existing] = await db.select().from(rolePermissions)
    .where(and(
      eq(rolePermissions.roleId, role.id),
      eq(rolePermissions.permissionId, permission.id)
    ))
    .limit(1);
  
  if (!existing) {
    console.log('ℹ️ Role does not have categories.read permission');
    return;
  }
  
  // Remove the permission from the role
  await db.delete(rolePermissions)
    .where(and(
      eq(rolePermissions.roleId, role.id),
      eq(rolePermissions.permissionId, permission.id)
    ));
  
  console.log('✅ Successfully removed categories.read permission from TEST_READ_CREATE_INGREDIENT role');
  
  // Verify the removal
  const [verification] = await db.select().from(rolePermissions)
    .where(and(
      eq(rolePermissions.roleId, role.id),
      eq(rolePermissions.permissionId, permission.id)
    ))
    .limit(1);
  
  if (!verification) {
    console.log('✅ Verification successful - permission removal confirmed');
  } else {
    console.log('❌ Verification failed - permission still found');
  }
}

removeCategoriesPermissionFromTestRole().catch(console.error).finally(() => process.exit(0));