import { db } from './app/lib/db/connection.js';
import { users, userRoles, roles, businesses } from './app/lib/db/schema.js';
import { eq } from 'drizzle-orm';

console.log('=== Debug User Authentication ===');

// Check if <PERSON> exists and get his ID
const johnUser = await db
  .select()
  .from(users)
  .where(eq(users.email, '<EMAIL>'))
  .limit(1);

if (johnUser.length === 0) {
  console.log('❌ John user not found!');
  process.exit(1);
}

const userId = johnUser[0].id;
console.log('✅ John user found:', {
  id: userId,
  name: john<PERSON><PERSON>[0].name,
  email: john<PERSON><PERSON>[0].email
});

// Check the specific business
const businessId = 'c2dc351a-f97f-406b-84c6-fd4d3c1fb1c9';
const business = await db
  .select()
  .from(businesses)
  .where(eq(businesses.id, businessId))
  .limit(1);

if (business.length === 0) {
  console.log('❌ Business not found!');
  process.exit(1);
}

console.log('✅ Business found:', {
  id: business[0].id,
  name: business[0].name,
  ownerId: business[0].userId
});

// Check if John has business_owner role for this business
const userRole = await db
  .select({
    roleName: roles.name,
    businessId: userRoles.businessId,
    isActive: userRoles.isActive
  })
  .from(userRoles)
  .innerJoin(roles, eq(userRoles.roleId, roles.id))
  .where(
    eq(userRoles.userId, userId) &&
    eq(userRoles.businessId, businessId) &&
    eq(roles.name, 'business_owner')
  )
  .limit(1);

console.log('🔍 Business owner role check:', userRole);

if (userRole.length === 0) {
  console.log('❌ John does not have business_owner role for this business!');
} else {
  console.log('✅ John has business_owner role:', userRole[0]);
}

// Check all roles for this user and business
const allUserRoles = await db
  .select({
    roleName: roles.name,
    businessId: userRoles.businessId,
    isActive: userRoles.isActive
  })
  .from(userRoles)
  .innerJoin(roles, eq(userRoles.roleId, roles.id))
  .where(
    eq(userRoles.userId, userId) &&
    eq(userRoles.businessId, businessId)
  );

console.log('📋 All roles for this user and business:', allUserRoles);

console.log('=== Debug Complete ===');