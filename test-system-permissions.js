#!/usr/bin/env node

/**
 * Comprehensive System Permissions Test
 * 
 * This script tests all documented permission types:
 * - CRUD Operations (create, read, update, delete)
 * - List Operations (list, search)
 * - Special Operations (manage, assign, revoke, export, calculate)
 */

import process from 'process';
import { db } from './app/lib/db/connection.ts';
import { permissions, roles, rolePermissions } from './app/lib/db/schema.ts';
import { eq } from 'drizzle-orm';

// Expected permission patterns from documentation
const EXPECTED_PERMISSION_TYPES = {
  CRUD: ['create', 'read', 'update', 'delete'],
  LIST: ['list', 'search'],
  SPECIAL: ['manage', 'assign', 'revoke', 'export', 'calculate', 'manage_users', 'assign_roles']
};

// Resources that should have permissions
const EXPECTED_RESOURCES = [
  'business', 'businesses',
  'inventory', 'products', 'ingredients', 
  'categories',
  'cogs',
  'users', 'rbac_users',
  'roles', 'rbac_roles',
  'rbac_permissions',
  'auth',
  'health'
];

class PermissionTester {
  constructor() {
    this.results = {
      total: 0,
      passed: 0,
      failed: 0,
      errors: []
    };
  }

  log(message, type = 'info') {
    const prefix = {
      info: '📋',
      success: '✅',
      error: '❌',
      warning: '⚠️'
    }[type];
    console.log(`${prefix} ${message}`);
  }

  test(description, testFn) {
    this.results.total++;
    try {
      const result = testFn();
      if (result) {
        this.results.passed++;
        this.log(`${description}`, 'success');
      } else {
        this.results.failed++;
        this.log(`${description}`, 'error');
        this.results.errors.push(description);
      }
    } catch (error) {
      this.results.failed++;
      this.log(`${description} - Error: ${error.message}`, 'error');
      this.results.errors.push(`${description}: ${error.message}`);
    }
  }

  async getAllPermissions() {
    return await db.select().from(permissions);
  }

  async testCRUDOperations(allPermissions) {
    this.log('\n🔍 Testing CRUD Operations...', 'info');
    
    const crudPermissions = allPermissions.filter(p => 
      EXPECTED_PERMISSION_TYPES.CRUD.includes(p.action)
    );

    // Test each CRUD operation exists
    EXPECTED_PERMISSION_TYPES.CRUD.forEach(action => {
      const permissionsWithAction = crudPermissions.filter(p => p.action === action);
      this.test(
        `CRUD: ${action} operations exist (found ${permissionsWithAction.length})`,
        () => permissionsWithAction.length > 0
      );
    });

    // Test CRUD operations follow naming convention
    crudPermissions.forEach(permission => {
      this.test(
        `CRUD: ${permission.name} follows resource.action naming`,
        () => permission.name === `${permission.resource}.${permission.action}`
      );
    });

    return crudPermissions;
  }

  async testListOperations(allPermissions) {
    this.log('\n📋 Testing List Operations...', 'info');
    
    const listPermissions = allPermissions.filter(p => 
      EXPECTED_PERMISSION_TYPES.LIST.includes(p.action)
    );

    // Test list operations exist
    EXPECTED_PERMISSION_TYPES.LIST.forEach(action => {
      const permissionsWithAction = listPermissions.filter(p => p.action === action);
      this.test(
        `LIST: ${action} operations exist (found ${permissionsWithAction.length})`,
        () => permissionsWithAction.length >= 0 // Allow 0 as some might not be implemented yet
      );
    });

    // Test that read permissions exist as alternative to list
    const readPermissions = allPermissions.filter(p => p.action === 'read');
    this.test(
      `LIST: 'read' operations exist as list alternative (found ${readPermissions.length})`,
      () => readPermissions.length > 0
    );

    return listPermissions;
  }

  async testSpecialOperations(allPermissions) {
    this.log('\n⚡ Testing Special Operations...', 'info');
    
    const specialPermissions = allPermissions.filter(p => 
      EXPECTED_PERMISSION_TYPES.SPECIAL.includes(p.action)
    );

    // Test special operations exist
    EXPECTED_PERMISSION_TYPES.SPECIAL.forEach(action => {
      const permissionsWithAction = specialPermissions.filter(p => p.action === action);
      this.test(
        `SPECIAL: ${action} operations exist (found ${permissionsWithAction.length})`,
        () => permissionsWithAction.length >= 0
      );
    });

    // Test specific special operations that should exist
    const requiredSpecialOps = [
      'business.manage_users',
      'users.assign_roles',
      'cogs.calculate'
    ];

    requiredSpecialOps.forEach(permissionName => {
      const exists = allPermissions.some(p => p.name === permissionName);
      this.test(
        `SPECIAL: Required operation '${permissionName}' exists`,
        () => exists
      );
    });

    return specialPermissions;
  }

  async testResourceCoverage(allPermissions) {
    this.log('\n🏗️ Testing Resource Coverage...', 'info');
    
    const foundResources = [...new Set(allPermissions.map(p => p.resource))];
    
    // Test that expected resources have permissions
    EXPECTED_RESOURCES.forEach(resource => {
      const hasPermissions = foundResources.includes(resource);
      this.test(
        `RESOURCE: '${resource}' has permissions`,
        () => hasPermissions
      );
    });

    // Test that each resource has at least read permission
    foundResources.forEach(resource => {
      const hasReadPermission = allPermissions.some(p => 
        p.resource === resource && (p.action === 'read' || p.action === 'list')
      );
      this.test(
        `RESOURCE: '${resource}' has read/list permission`,
        () => hasReadPermission
      );
    });

    return foundResources;
  }

  async testSystemVsCustomPermissions(allPermissions) {
    this.log('\n🔐 Testing System vs Custom Permissions...', 'info');
    
    const systemPermissions = allPermissions.filter(p => p.isSystemPermission);
    const customPermissions = allPermissions.filter(p => !p.isSystemPermission);

    this.test(
      `System permissions exist (found ${systemPermissions.length})`,
      () => systemPermissions.length > 0
    );

    this.test(
      `Custom/endpoint permissions exist (found ${customPermissions.length})`,
      () => customPermissions.length >= 0
    );

    // Test that system permissions follow expected patterns
    const systemPermissionNames = systemPermissions.map(p => p.name);
    const expectedSystemPermissions = [
      'business.create', 'business.read', 'business.update', 'business.delete',
      'inventory.read', 'inventory.create', 'inventory.update', 'inventory.delete',
      'users.read', 'users.create', 'users.update', 'users.delete'
    ];

    expectedSystemPermissions.forEach(permissionName => {
      this.test(
        `System permission '${permissionName}' exists`,
        () => systemPermissionNames.includes(permissionName)
      );
    });

    return { systemPermissions, customPermissions };
  }

  async testRolePermissionAssignments() {
    this.log('\n👥 Testing Role-Permission Assignments...', 'info');
    
    const allRoles = await db.select().from(roles);
    const systemRoles = allRoles.filter(r => r.isSystemRole);

    // Test that system roles exist
    const expectedRoles = ['super_admin', 'business_owner', 'business_manager', 'inventory_manager', 'staff', 'viewer'];
    expectedRoles.forEach(roleName => {
      const roleExists = systemRoles.some(r => r.name === roleName);
      this.test(
        `System role '${roleName}' exists`,
        () => roleExists
      );
    });

    // Test that roles have permission assignments
    for (const role of systemRoles) {
      const rolePermissionCount = await db
        .select()
        .from(rolePermissions)
        .where(eq(rolePermissions.roleId, role.id));
      
      this.test(
        `Role '${role.name}' has permission assignments (${rolePermissionCount.length})`,
        () => rolePermissionCount.length > 0
      );
    }

    return systemRoles;
  }

  async testNamingConventions(allPermissions) {
    this.log('\n📝 Testing Naming Conventions...', 'info');
    
    // Test resource.action naming convention
    allPermissions.forEach(permission => {
      const followsConvention = /^[a-z_]+\.[a-z_]+$/.test(permission.name);
      this.test(
        `Permission '${permission.name}' follows resource.action convention`,
        () => followsConvention
      );
    });

    // Test that name matches resource.action
    allPermissions.forEach(permission => {
      const expectedName = `${permission.resource}.${permission.action}`;
      this.test(
        `Permission '${permission.name}' matches resource.action (${expectedName})`,
        () => permission.name === expectedName
      );
    });
  }

  printSummary() {
    this.log('\n📊 Test Summary', 'info');
    console.log(`Total Tests: ${this.results.total}`);
    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(`Success Rate: ${((this.results.passed / this.results.total) * 100).toFixed(1)}%`);

    if (this.results.errors.length > 0) {
      this.log('\n❌ Failed Tests:', 'error');
      this.results.errors.forEach(error => {
        console.log(`  • ${error}`);
      });
    }

    return this.results.failed === 0;
  }

  async runAllTests() {
    this.log('🚀 Starting System Permissions Test Suite', 'info');
    
    try {
      // Get all permissions from database
      const allPermissions = await this.getAllPermissions();
      this.log(`Found ${allPermissions.length} total permissions in database`, 'info');

      // Run all test suites
      await this.testCRUDOperations(allPermissions);
      await this.testListOperations(allPermissions);
      await this.testSpecialOperations(allPermissions);
      await this.testResourceCoverage(allPermissions);
      await this.testSystemVsCustomPermissions(allPermissions);
      await this.testRolePermissionAssignments();
      await this.testNamingConventions(allPermissions);

      // Print summary
      const allTestsPassed = this.printSummary();
      
      if (allTestsPassed) {
        this.log('\n🎉 All system permissions are working correctly!', 'success');
        process.exit(0);
      } else {
        this.log('\n⚠️ Some permission tests failed. Please review the issues above.', 'warning');
        process.exit(1);
      }
    } catch (error) {
      this.log(`Fatal error during testing: ${error.message}`, 'error');
      console.error(error);
      process.exit(1);
    }
  }
}

// Run the tests
const tester = new PermissionTester();
tester.runAllTests();